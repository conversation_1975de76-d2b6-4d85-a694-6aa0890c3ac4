<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.StepRunMapper">

    <select id="checkUnique" resultType="int" parameterType="com.datalink.fdop.base.api.model.vo.StepRun">
        SELECT COUNT(1)
        FROM zjdata.step_run
        WHERE ver_id = #{stepRun.verId} AND factory_id = #{stepRun.factoryId} AND flow_item_id = #{stepRun.flowItemId}
            AND effective_date = #{stepRun.effectiveDate} AND batches = #{stepRun.batches}
            AND product_code = #{stepRun.productCode} AND product_ver = #{stepRun.productVer}
            AND lstar_code = #{stepRun.lstarCode} limit 1
    </select>

</mapper>


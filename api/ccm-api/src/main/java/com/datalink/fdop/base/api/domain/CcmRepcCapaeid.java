package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "zjdata", value = "ccm_repc_capaeid")
@ApiModel("替代机组-报工替代")
public class CcmRepcCapaeid {

    @ApiModelProperty(value = "制造工厂")
    @NotBlank(message = "制造工厂不能为空")
    @Excel(name = "制造工厂")
    private String factoryId;

    @ApiModelProperty(value = "旧设备ID")
    @NotBlank(message = "旧设备ID不能为空")
    @Excel(name = "旧设备ID")
    private String oldEquipId;

    @ApiModelProperty(value = "旧设备组")
    @NotBlank(message = "旧设备组不能为空")
    @Excel(name = "旧设备组")
    private String oldEquipGroupId;

    @ApiModelProperty(value = "新设备ID")
    @Excel(name = "新设备ID")
    private String equipId;

    @ApiModelProperty(value = "新设备组")
    @Excel(name = "新设备组")
    private String equipGroupId;

    @ApiModelProperty(value = "负责部门")
    @Excel(name = "负责部门")
    private String resDept;

    @Excel(name = "是否启用", readConverterExp = "true=启用,false=不启用")
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;

    @ApiModelProperty(value = "更改人")
    private String updateBy;

    @ApiModelProperty(value = "更改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewVarcost;

import java.util.List;

public interface DwsNewVarcostService extends IService<DwsNewVarcost> {

    PageDataInfo<DwsNewVarcost> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwsNewVarcost> listAll();

    List<DwsNewVarcost> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);
}
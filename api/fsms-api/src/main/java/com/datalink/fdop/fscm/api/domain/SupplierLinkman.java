package com.datalink.fdop.fscm.api.domain;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(schema = "zjdata", value = "f_d_supplier_linkman")
@ExcelIgnoreUnannotated
@ApiModel("供应商联系人存储表")
public class SupplierLinkman {
  @ApiModelProperty("供应商编码")
  @ExcelProperty("供应商编码")
  private String supplierCode;
  @ApiModelProperty("制造商代码")
  @ExcelProperty("制造商代码")
  private String manufCode;
  @ApiModelProperty("制造商类别")
  @ExcelProperty("制造商类别")
  private String manufType;
  @ApiModelProperty("联系人序号")
  @ExcelProperty("联系人序号")
  private String serialNum;
  @ApiModelProperty("联系人姓名")
  @ExcelProperty("联系人姓名")
  private String name;
  @ApiModelProperty("联系电话")
  @ExcelProperty("联系电话")
  private String phone;
  @ApiModelProperty("联系邮箱")
  @ExcelProperty("联系邮箱")
  private String email;
  @ApiModelProperty("联系人部门")
  @ExcelProperty("联系人部门")
  private String department;
  @ApiModelProperty("联系人岗位")
  @ExcelProperty("联系人岗位")
  private String post;
  @ApiModelProperty("是否默认联系人")
  @ExcelProperty("是否默认联系人")
  private String defaultLinkman;
  @TableField(exist = false)
  @ApiModelProperty("供应商名称")
  @ExcelProperty("供应商名称")
  private String supplierName;
  @TableField(exist = false)
  @ApiModelProperty("制造商名称")
  @ExcelProperty("制造商名称")
  private String manufName;


}

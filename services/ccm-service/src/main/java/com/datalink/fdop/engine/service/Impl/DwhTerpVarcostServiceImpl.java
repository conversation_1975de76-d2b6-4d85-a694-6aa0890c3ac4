package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpVarcost;
import com.datalink.fdop.engine.mapper.DwhTerpVarcostMapper;
import com.datalink.fdop.engine.service.DwhTerpVarcostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhTerpVarcostServiceImpl extends ServiceImpl<DwhTerpVarcostMapper, DwhTerpVarcost> implements DwhTerpVarcostService {

    @Autowired
    private DwhTerpVarcostMapper dwhTerpVarcostMapper;

    @Override
    public PageDataInfo<DwhTerpVarcost> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwhTerpVarcost> page = PageUtils.getPage(DwhTerpVarcost.class);
        IPage<DwhTerpVarcost> iPage = dwhTerpVarcostMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhTerpVarcost> listAll() {
        return dwhTerpVarcostMapper.listAll();
    }

    @Override
    public List<DwhTerpVarcost> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwhTerpVarcostMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
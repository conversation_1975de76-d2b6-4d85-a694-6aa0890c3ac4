package com.datalink.fdop.element.api.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/16 15:13
 */
@Data
@ApiModel("数据实体表增强类，包含实体编码，名称，描述")
public class DataEntityTableVo extends DataEntityTable {

    @ApiModelProperty(value = "实体编码")
    private String entityCode;

    @ApiModelProperty(value = "实体名称")
    private String entityName;

    @ApiModelProperty(value = "实体描述")
    private String entityDesc;

}

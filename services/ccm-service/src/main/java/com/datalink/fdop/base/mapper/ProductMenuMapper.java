package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.base.api.domain.CostCenterMenu;
import com.datalink.fdop.base.api.domain.ProductMenu;
import com.datalink.fdop.base.api.model.CostCenterTree;
import com.datalink.fdop.base.api.model.ProductTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-09-06 11:28
 */
public interface ProductMenuMapper extends BaseMapper<ProductMenu> {

    int bacthUpdatePidById(@Param("menuId") String menuId);

    int deleteByIds(@Param("ids") List<String> ids);

    List<ProductTree> selectMenuTree(@Param("sort") String sort);

    List<String> selectByPids(@Param("ids") List<String> ids);

    ProductMenu selectById(@Param("id") String id);

}

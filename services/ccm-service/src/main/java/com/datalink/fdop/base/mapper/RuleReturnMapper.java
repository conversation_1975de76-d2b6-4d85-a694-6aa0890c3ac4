package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.base.model.RuleReturn;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

public interface RuleReturnMapper extends BaseMapper<RuleReturn> {

    String selectLastVer();

    BigDecimal countByFour(@Param("verCode") String verCode,@Param("time") String time,
                           @Param("kostlCode") String kostlCode,@Param("saknrCode") String saknrCode);

}

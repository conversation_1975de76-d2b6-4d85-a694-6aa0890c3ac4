<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwhFinalRateStructurePMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwhFinalRateStructureP">
        SELECT 
            *
        FROM dwh.dwh_final_rate_structure_p node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and node.control_area_id = #{controlAreaId}
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != ''">
                and node.year_month >= #{yearMonthFrom}
            </if>
            <if test="yearMonthTo != null and yearMonthTo != ''">
                and node.year_month &lt;= #{yearMonthTo}
            </if>
            <if test="costCenterType != null and costCenterType != ''">
                and node.cost_center_type in
                <foreach item="item" collection="costCenterType.split(',')" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.ver_id,node.value_type,node.control_area_id,
        node.year_month,node.cost_center_id,node.cost_structure_id
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwhFinalRateStructureP">
        SELECT
        *
        FROM dwh.dwh_final_rate_structure_p node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and node.control_area_id = #{controlAreaId}
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != '' ">
                and node.year_month >= #{yearMonthFrom}
            </if>
            <if test="yearMonthTo != null and yearMonthTo != ''">
                and node.year_month &lt;= #{yearMonthTo}
            </if>
            <if test="costCenterType != null and costCenterType != ''">
                and node.cost_center_type in
                <foreach item="item" collection="costCenterType.split(',')" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.ver_id,node.value_type,node.control_area_id,
        node.year_month,node.cost_center_id,node.cost_structure_id
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwhFinalMpsMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwhFinalMps">
        SELECT *
        FROM dwh.dwh_final_mps node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and CONCAT(node.mps_year, node.mps_month) between #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
        ORDER BY node.ver_id, node.plant_id,node.product_id,node.mps_year,node.mps_month,node.mps_type
        ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwhFinalMps">
        SELECT *
        FROM dwh.dwh_final_mps node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and CONCAT(node.mps_year, node.mps_month) between #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
        ORDER BY node.ver_id, node.plant_id,node.product_id,node.mps_year,node.mps_month,node.mps_type
        ${sort}
    </select>

</mapper>


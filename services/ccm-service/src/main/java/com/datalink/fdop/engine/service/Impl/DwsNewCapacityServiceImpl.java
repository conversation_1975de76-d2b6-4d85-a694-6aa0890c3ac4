package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewCapacity;
import com.datalink.fdop.engine.mapper.DwsNewCapacityMapper;
import com.datalink.fdop.engine.service.DwsNewCapacityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewCapacityServiceImpl extends ServiceImpl<DwsNewCapacityMapper, DwsNewCapacity> implements DwsNewCapacityService {

    @Autowired
    private DwsNewCapacityMapper dwsNewCapacityMapper;

    @Override
    public PageDataInfo<DwsNewCapacity> overview(
            String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwsNewCapacity> page = PageUtils.getPage(DwsNewCapacity.class);
        IPage<DwsNewCapacity> iPage = dwsNewCapacityMapper.selectAll(verId, plantId,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewCapacity> listAll() {
        return dwsNewCapacityMapper.listAll();
    }

    @Override
    public List<DwsNewCapacity> selectNoPage(String verId, String plantId, String sort, SearchVo searchVo) {
        return dwsNewCapacityMapper.selectNoPage(verId, plantId, sort, searchVo);
    }


}

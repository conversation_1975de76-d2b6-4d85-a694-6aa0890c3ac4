package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.engine.service.TechCompareService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-03-15 16:46
 */
@RestController
@RequestMapping("/fccm/TechCompare")
public class TechCompareController {

    @Autowired
    private TechCompareService techCompareService;

    @ApiOperation("查")
    @Log(title = "FCCM功能报表")
    @PostMapping("/query")
    public R<PageDataInfo> query(@RequestParam("factoryId") String factoryId,
                                 @RequestParam(required = false, value = "productId") String productId,
                                 @RequestParam(required = false, value = "startTime") String startTime,
                                 @RequestParam(required = false, value = "endTime") String endTime,
                                 @RequestParam(required = false, defaultValue = "ASC") String sort,
                                 @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(techCompareService.query(factoryId, productId, startTime, endTime, sort, searchVo));
    }


}

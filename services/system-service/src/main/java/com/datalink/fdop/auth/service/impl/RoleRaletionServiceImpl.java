package com.datalink.fdop.auth.service.impl;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.auth.service.RoleRelationService;
import com.datalink.fdop.auth.mapper.RoleRelationMapper;
import com.datalink.fdop.system.api.domain.SysRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/5/7 13:37
 */
@Service
public class RoleRaletionServiceImpl implements RoleRelationService {

    @Autowired
    public RoleRelationMapper roleRaletionMapper;

    @Override
    public PageDataInfo<SysRole> showNotAdded(Long objectId, String code, String name, List<Long> roleIds) {
        List<VlabelItem<SysRole>> vlabelItems = roleRaletionMapper.showNotAddedSysRole(roleIds,code, name);
        List<SysRole> sysRoles = vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
        return PageUtils.getPageInfo(sysRoles,roleRaletionMapper.showNotAddedSysRoleCount(roleIds,code, name));
    }

    @Override
    public PageDataInfo<SysRole> showAdded(Long objectId, String code, String name, List<Long> roleIds) {
        List<VlabelItem<SysRole>> vlabelItems = roleRaletionMapper.showAddedSysRole(roleIds,code, name);
        List<SysRole> sysRoles = vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
        return PageUtils.getPageInfo(sysRoles,roleRaletionMapper.showAddedSysRoleCount(roleIds,code, name));
    }

    @Override
    @Transactional
    public R add(Long objectId, List<Long> roleIds) {
        int i = roleRaletionMapper.saveRelation(objectId, roleIds);
        if (i < 1) {
            throw new ServiceException(Status.SAVE_FAILED);
        }
        return R.ok(null, Status.SAVE_SUCCEED.getMsg());
    }

    @Override
    @Transactional
    public R del(Long objectId, List<Long> roleIds) {
            int i=roleRaletionMapper.delRelation(objectId,roleIds);
            if (i<1){
                throw new ServiceException(Status.DELETE_FAILED);
            }
            return R.ok(null,Status.DELETE_SUCCEED.getMsg());
    }

    //--------------------组----------------------------

    @Override
    public PageDataInfo<SysRole> groupShowNotAdded(Long groupId, String code, String name, List<Long> roleIds) {
        List<VlabelItem<SysRole>> vlabelItems = roleRaletionMapper.showNotAddedSysRole(roleIds,code, name);
        List<SysRole> sysRoles = vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
        return PageUtils.getPageInfo(sysRoles,roleRaletionMapper.showNotAddedSysRoleCount(roleIds,code, name));
    }

    @Override
    public PageDataInfo<SysRole> groupShowAdded(Long groupId, String code, String name, List<Long> roleIds) {
        List<VlabelItem<SysRole>> vlabelItems = roleRaletionMapper.showAddedSysRole(roleIds,code, name);
        List<SysRole> sysRoles = vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
        return PageUtils.getPageInfo(sysRoles,roleRaletionMapper.showAddedSysRoleCount(roleIds,code, name));
    }

    @Override
    public R groupAdd(Long groupId, List<Long> roleIds) {
            int i=roleRaletionMapper.saveGroupRelation(groupId,roleIds);
            if (i<1){
                throw new ServiceException(Status.SAVE_FAILED);
            }
            return R.ok(null,Status.SAVE_SUCCEED.getMsg());
    }

    @Override
    public R groupDel(Long groupId, List<Long> roleIds) {
            int i=roleRaletionMapper.delGroupRelation(groupId,roleIds);
            if (i<1){
                throw new ServiceException(Status.DELETE_FAILED);
            }
            return R.ok(null, Status.DELETE_SUCCEED.getMsg());

    }

    @Override
    public List<Long> findRoleByObject(Long ObjectId) {
        List<Long> list = roleRaletionMapper.showNotAdded(ObjectId);
        return list;
    }

    @Override
    public List<Long> findRoleByGroup(Long groupId) {
        return roleRaletionMapper.groupShowNotAdded(groupId);
    }
}

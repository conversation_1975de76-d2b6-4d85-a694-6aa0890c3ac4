package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.engine.api.domain.DwdFlow;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwdFlowMapper extends BaseMapper<DwdFlow> {

    IPage<DwdFlow> selectAll(
            @Param("plantId") String plantId, @Param("productId") String productId,
            @Param("page") IPage<DwdFlow> page, @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwdFlow> selectNoPage(@Param("plantId") String plantId,
                               @Param("productId") String productId,
                               @Param("sort") String sort,
                               @Param("searchVo") SearchVo searchVo);
}

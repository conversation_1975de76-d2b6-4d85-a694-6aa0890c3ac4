package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalCapacity;
import com.datalink.fdop.engine.mapper.DwhFinalCapacityMapper;
import com.datalink.fdop.engine.service.DwhFinalCapacityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalCapacityServiceImpl extends ServiceImpl<DwhFinalCapacityMapper, DwhFinalCapacity> implements DwhFinalCapacityService {

    @Autowired
    private DwhFinalCapacityMapper dwhFinalCapacityMapper;

    @Override
    public PageDataInfo<DwhFinalCapacity> overview(
            String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwhFinalCapacity> page = PageUtils.getPage(DwhFinalCapacity.class);
        IPage<DwhFinalCapacity> iPage = dwhFinalCapacityMapper.selectAll(verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalCapacity> listAll() {
        return dwhFinalCapacityMapper.listAll();
    }

    @Override
    public List<DwhFinalCapacity> selectNoPage(String verId, String plantId, String sort, SearchVo searchVo) {
        return dwhFinalCapacityMapper.selectNoPage(verId, plantId, sort, searchVo);
    }

}

package com.datalink.fdop.seatunnel.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.seatunnel.api.RemoteSeaTunnelRestApiService;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelSubmitConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.Job;
import com.datalink.fdop.seatunnel.api.restapi.domain.JobInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class RemoteSeaTunnelRestApiFallbackFactory implements FallbackFactory<RemoteSeaTunnelRestApiService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteSeaTunnelRestApiFallbackFactory.class);

    @Override
    public RemoteSeaTunnelRestApiService create(Throwable throwable) {
        log.error("SeaTunnel Rest Api 服务调用失败:{}", throwable.getMessage());
        return new RemoteSeaTunnelRestApiService() {


            @Override
            public R<JobInfo> submitJob(SeaTunnelSubmitConfig modelConfig) {
                return R.fail("SeaTunnel任务提交失败 " +throwable.getMessage());
            }

            @Override
            public R<Job> getJob(String jobId) {
                return R.fail("获取SeaTunnel任务失败 " +throwable.getMessage());
            }

            @Override
            public R<List<Job>> finishedJobs(String state) {
                return  R.fail("获取SeaTunnel任务列表失败 " +throwable.getMessage());
            }
        };
    }

}

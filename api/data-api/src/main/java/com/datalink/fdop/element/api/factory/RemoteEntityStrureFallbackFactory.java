package com.datalink.fdop.element.api.factory;


import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.RemoteEntityStrureService;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/17 15:14
 */
public class RemoteEntityStrureFallbackFactory  implements FallbackFactory<RemoteEntityStrureService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteEntityStrureFallbackFactory.class);
    @Override
    public RemoteEntityStrureService create(Throwable cause) {
        log.error("数据元素服务调用失败:{}", cause.getMessage());
        return new RemoteEntityStrureService() {

            @Override
            public R<List<DataEntityStructureVo>> selectStructureById(Long dataEntityId) {
                return R.fail("查询数据实体的表结构失败"+cause.getMessage());
            }

            @Override
            public R<List<DataEntityStructureVo>> selectStructureByIdAndTenantId(Long tenantId, Long dataEntityId) {
                return R.fail("查询数据实体的表结构失败"+cause.getMessage());
            }
        };
    }
}

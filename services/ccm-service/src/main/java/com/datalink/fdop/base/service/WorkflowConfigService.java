package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.WorkflowConfig;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface WorkflowConfigService extends IService<WorkflowConfig> {

    String checkUnique(String code);

    int deleteByIds(List<String> ids);

    PageDataInfo<WorkflowConfig> overview(String sort, SearchVo searchVo);

    boolean updateBatchStatus(Boolean ccmFunLock, List<WorkflowConfig> codeList);

}

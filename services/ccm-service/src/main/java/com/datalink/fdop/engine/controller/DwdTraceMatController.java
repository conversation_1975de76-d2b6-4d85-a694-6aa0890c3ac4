package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwdTraceMat;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdTraceMatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwdTraceMat")
@Api(tags = "同步视图-物料账记数据")
public class DwdTraceMatController extends BaseController {

    @Autowired
    private DwdTraceMatService dwdTraceMatService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "queryParam", value = "查询参数", required = false, dataTypeClass = QueryParam.class, paramType = "body"),
    })
    @Log(title = "同步视图-物料账记数据")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String yearMonth = MapUtils.getAsString(params, "yearMonth");
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String companyId = MapUtils.getAsString(params, "companyId");
        PageDataInfo<DwdTraceMat> overview = dwdTraceMatService.overview(yearMonth, controlAreaId, companyId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdTraceMat.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "同步视图-物料账记数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String yearMonth = MapUtils.getAsString(params, "yearMonth");
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String companyId = MapUtils.getAsString(params, "companyId");
        List<DwdTraceMat> list = dwdTraceMatService.selectNoPage(yearMonth, controlAreaId, companyId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdTraceMat> util = new ExcelUtil<>(DwdTraceMat.class);
        util.exportExcel(response, list, "同步视图-物料账记数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdTraceMat> util = new ExcelUtil<>(DwdTraceMat.class);
        util.importTemplateExcel(response, "同步视图-物料账记模板");
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId() {
        return R.ok(dataDictionaryService.listCompanyId("dwd.dwd_trace_mat", null));
    }

}
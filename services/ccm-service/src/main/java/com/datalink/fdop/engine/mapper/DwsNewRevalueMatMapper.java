package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwsNewRevalueMat;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwsNewRevalueMatMapper extends BaseMapper<DwsNewRevalueMat> {

    IPage<DwsNewRevalueMat> selectAll(@Param("yearMonth") String yearMonth,
            @Param("verId") String verId,
            @Param("controlAreaId") String controlAreaId, @Param("companyId") String companyId,
            @Param("page") IPage<DwsNewRevalueMat> page, @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwsNewRevalueMat> selectNoPage(@Param("yearMonth") String yearMonth,
                                        @Param("verId") String verId,
                                        @Param("controlAreaId") String controlAreaId,
                                        @Param("companyId") String companyId,
                                        @Param("sort") String sort,
                                        @Param("searchVo") SearchVo searchVo);
}
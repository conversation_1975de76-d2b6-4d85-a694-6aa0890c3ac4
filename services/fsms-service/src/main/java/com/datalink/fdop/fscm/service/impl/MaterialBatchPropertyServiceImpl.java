package com.datalink.fdop.fscm.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.fscm.api.domain.MaterialBatchProperty;
import com.datalink.fdop.fscm.mapper.MaterialBatchPropertyMapper;
import com.datalink.fdop.fscm.service.MaterialBatchPropertyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2022/11/17 14:55
 */
@Service
@Transactional
public class MaterialBatchPropertyServiceImpl extends ServiceImpl<MaterialBatchPropertyMapper, MaterialBatchProperty> implements MaterialBatchPropertyService {


    @Autowired
    private MaterialBatchPropertyMapper materialBatchPropertyMapper;

    @Override
    public Boolean checkDataRepetition(MaterialBatchProperty materialBatchProperty) {

        MaterialBatchProperty data = materialBatchPropertyMapper.selectOne(new QueryWrapper<MaterialBatchProperty>()
                .lambda()
                .eq(MaterialBatchProperty::getMaterialCode,materialBatchProperty.getMaterialCode())
                .eq(MaterialBatchProperty::getFactoryCode,materialBatchProperty.getFactoryCode())
                .eq(MaterialBatchProperty::getBatchNumber,materialBatchProperty.getBatchNumber())
                .eq(MaterialBatchProperty::getAttributeName,materialBatchProperty.getAttributeName())
                .eq(MaterialBatchProperty::getSerialNum,materialBatchProperty.getSerialNum())
        );
        if (data!=null) {
            return true;
        }else {
            return false;
        }
    }

    @Override
    public void delData(MaterialBatchProperty materialBatchProperty) {
        materialBatchPropertyMapper.delete(new QueryWrapper<MaterialBatchProperty>()
                .lambda()
                .eq(MaterialBatchProperty::getMaterialCode,materialBatchProperty.getMaterialCode())
                .eq(MaterialBatchProperty::getFactoryCode,materialBatchProperty.getFactoryCode())
                .eq(MaterialBatchProperty::getBatchNumber,materialBatchProperty.getBatchNumber())
                .eq(MaterialBatchProperty::getAttributeName,materialBatchProperty.getAttributeName())
                .eq(MaterialBatchProperty::getSerialNum,materialBatchProperty.getSerialNum())
        );
    }
}

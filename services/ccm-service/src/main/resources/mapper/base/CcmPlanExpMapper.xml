<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CcmPlanExpMapper">

    <delete id="deleteBykey">
        delete from zjdata.ccm_plan_exp
        WHERE control_area_id = #{controlAreaId} and ver_id=#{verId} and value_type =#{valueType}
            and year = #{year} and month = #{month} and cost_element_id = #{costElementId} and
            cost_center_id = #{costCenterId}
    </delete>

    <select id="checkIdUnique" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_plan_exp
        WHERE control_area_id = #{controlAreaId} and ver_id=#{verId} and value_type =#{valueType}
          and year = #{year} and month = #{month} and cost_element_id = #{costElementId} and
            cost_center_id = #{costCenterId}
        limit 1
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CcmPlanExp">
        update zjdata.ccm_plan_exp
        set
            <if test="currency != null">currency = #{currency},</if>
            <if test="origCostElementId != null">orig_cost_element_id = #{origCostElementId},</if>
            <if test="revalueElementId != null">revalue_element_id = #{revalueElementId},</if>
            <if test="amount != null ">amount = #{amount},</if>
            <if test="enable != null">enable = #{enable},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="plantId != null">plant_id = #{plantId},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE control_area_id = #{controlAreaId} and ver_id=#{verId} and value_type =#{valueType}
        and year = #{year} and month = #{month} and cost_element_id = #{costElementId} and
        cost_center_id = #{costCenterId}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.CcmPlanExp">
        SELECT node.*
        FROM zjdata.ccm_plan_exp node
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
            <if test="companyId != null and companyId != ''">
                and node.company_id in
                <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and CONCAT(year, month) between  #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
        ORDER BY node.control_area_id, node.ver_id, node.value_type,
        node.year,node.month,node.cost_element_id,node.cost_center_id
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.base.api.domain.CcmPlanExp">
        SELECT node.*
        FROM zjdata.ccm_plan_exp node
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
            <if test="companyId != null and companyId != ''">
                and node.company_id in
                <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and CONCAT(year, month) between  #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
        ORDER BY node.control_area_id, node.ver_id, node.value_type,
        node.year,node.month,node.cost_element_id,node.cost_center_id
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_plan_exp
        SET enable = #{enable}
        where (control_area_id, ver_id,value_type,year,month,cost_element_id,cost_center_id) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (
             #{item.controlAreaId},#{item.verId},#{item.valueType},#{item.year},
             #{item.month},#{item.costElementId},#{item.costCenterId}
            )
        </foreach>
    </update>

</mapper>


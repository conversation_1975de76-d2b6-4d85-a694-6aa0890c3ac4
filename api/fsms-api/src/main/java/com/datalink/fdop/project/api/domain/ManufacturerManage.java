package com.datalink.fdop.project.api.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(schema = "zjdata",value = "p_d_manufacturer_manage")
@ApiModel(value = "制造商管理启用情况存储表")
public class ManufacturerManage {


  @ApiModelProperty(value = "序号")
  private String serialNum;
  @ApiModelProperty(value = "描述")
  @TableField(value ="\"desc\"" )
  private String desc;
  @ApiModelProperty(value = "状态")
  private String statu;



}

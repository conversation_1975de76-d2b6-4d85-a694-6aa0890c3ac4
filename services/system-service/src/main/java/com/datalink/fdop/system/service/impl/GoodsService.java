package com.datalink.fdop.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.system.api.domain.Goods;
import com.datalink.fdop.system.mapper.GoodsMapper;
import com.datalink.fdop.system.service.IGoodsService;
import org.apache.dolphinscheduler.fdop.api.RemoteResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 14:25
 */
@Service
public class GoodsService implements IGoodsService {

    @Autowired
    private RemoteResourceService remoteResourceService;

    @Autowired
    private GoodsMapper goodsMapper;

    @Transactional
    @Override
    public int create(String goodsSn, String productNo, String goodsName, Double marketPrice, Double shopPrice, Integer warnStock, Integer goodsStock, String goodsUnit, String goodsTips, MultipartFile img) {
        Goods goods = new Goods(goodsSn, productNo, goodsName, marketPrice, shopPrice, warnStock, goodsStock, goodsUnit, goodsTips);

        int insert = goodsMapper.insert(goods);
        if (insert > 0 && img != null) {
            // 获取路径
            goods.setGoodsImg(getImageUrl(goods.getGoodsId(), img));
            goodsMapper.updateById(goods);
        }

        return insert;
    }

    @Transactional
    @Override
    public int update(Integer goodsId, String goodsSn, String productNo, String goodsName, Double marketPrice, Double shopPrice, Integer warnStock, Integer goodsStock, String goodsUnit, String goodsTips, MultipartFile img) {
        if (goodsMapper.selectById(goodsId) == null) {
            throw new ServiceException("商品不存在");
        }
        Goods goods = new Goods(goodsId, goodsSn, productNo, goodsName, marketPrice, shopPrice, warnStock, goodsStock, goodsUnit, goodsTips);
        // 获取路径
        if (img != null) {
            goods.setGoodsImg(getImageUrl(goodsId, img));
        }
        return goodsMapper.updateById(goods);
    }

    @Transactional
    @Override
    public int delete(List<Integer> ids) {
        return goodsMapper.deleteBatchIds(ids);
    }

    @Override
    public List<Goods> list(Goods goods) {
        if (goods == null) {
            goods = new Goods();
        }
        List<Goods> tenantList = goodsMapper.selectList(Wrappers.<Goods>lambdaQuery()
                .like(goods.getGoodsId() != null, Goods::getGoodsId, goods.getGoodsId())
                .like(StringUtils.isNotEmpty(goods.getGoodsSn()), Goods::getGoodsSn, goods.getGoodsSn())
                .like(StringUtils.isNotEmpty(goods.getProductNo()), Goods::getProductNo, goods.getProductNo())
                .like(StringUtils.isNotEmpty(goods.getGoodsName()), Goods::getGoodsName, goods.getGoodsName())
                .orderByDesc(Goods::getGoodsId)
        );
        return tenantList;
    }

    @Override
    public int uploadImg(Integer id, MultipartFile img) {
        Goods goods = goodsMapper.selectById(id);
        if (goodsMapper.selectById(id) == null) {
            throw new ServiceException("商品不存在");
        }
        // 获取路径
        goods.setGoodsImg(getImageUrl(id, img));

        return goodsMapper.updateById(goods);
    }

    private String getImageUrl(Integer id, MultipartFile img) {
        String originalFilename = img.getOriginalFilename();
        try {
            R uploadResourceR = remoteResourceService.uploadResource(id, "FILE", originalFilename, null, null, img, SecurityUtils.getTenantId());
            if (uploadResourceR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(uploadResourceR.getMsg());
            }
            return uploadResourceR.getData().toString();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Goods viewImg(Integer id) {
        Goods goods = goodsMapper.selectById(id);
        if (goodsMapper.selectById(id) == null) {
            throw new ServiceException("商品不存在");
        }
        return goods;
    }
}

package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalVarcost;
import com.datalink.fdop.engine.mapper.DwhFinalVarcostMapper;
import com.datalink.fdop.engine.service.DwhFinalVarcostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalVarcostServiceImpl extends ServiceImpl<DwhFinalVarcostMapper, DwhFinalVarcost> implements DwhFinalVarcostService {

    @Autowired
    private DwhFinalVarcostMapper dwhFinalVarcostMapper;

    @Override
    public PageDataInfo<DwhFinalVarcost> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwhFinalVarcost> page = PageUtils.getPage(DwhFinalVarcost.class);
        IPage<DwhFinalVarcost> iPage = dwhFinalVarcostMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalVarcost> listAll() {
        return dwhFinalVarcostMapper.listAll();
    }

    @Override
    public List<DwhFinalVarcost> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwhFinalVarcostMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
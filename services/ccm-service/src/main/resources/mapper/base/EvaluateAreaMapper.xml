<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.EvaluateAreaMapper">

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_evaluate_area
        WHERE evaluate_area_id = #{id} limit 1
    </select>

    <select id="checkEvaluateAreaGroup" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_evaluate_area
        WHERE evaluate_area_group = #{evaluateAreaGroup} limit 1
    </select>

    <select id="select" parameterType="String" resultType="com.datalink.fdop.base.api.domain.EvaluateArea">
        SELECT *
        FROM zjdata.ccm_evaluate_area
        WHERE evaluate_area_id = #{id}
    </select>

    <delete id="deleteById" parameterType="String">
        delete from zjdata.ccm_evaluate_area
        where evaluate_area_id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from zjdata.ccm_evaluate_area
        WHERE evaluate_area_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="pageList" resultType="com.datalink.fdop.base.api.domain.EvaluateArea">
        SELECT a.*
        from zjdata.ccm_evaluate_area  a
        <where>
            <if test="a!=null and a!=''">
                   AND ${a}
            </if>
        </where>
        order by evaluate_area_id
        <if test="sort != null and sort != ''">
            ${sort}
        </if>
    </select>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.EvaluateArea">
        SELECT a.*
        from zjdata.ccm_evaluate_area  a
        order by evaluate_area_id desc
    </select>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_evaluate_area
            SET enable = #{item.enable}
            WHERE evaluate_area_id = #{item.evaluateAreaId}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_evaluate_area (
        evaluate_area_id, evaluate_area_desc, evaluate_area_text,company_id,evaluate_area_group, enable,create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.evaluateAreaId},#{item.evaluateAreaDesc},#{item.evaluateAreaText},
            #{item.companyId},#{item.evaluateAreaGroup},
            #{item.enable},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>


</mapper>


package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRateActivityA;

import java.util.List;

public interface DwhFinalRateActivityAService extends IService<DwhFinalRateActivityA> {

    PageDataInfo<DwhFinalRateActivityA> overview(
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo);

    List<DwhFinalRateActivityA> selectNoPage(String controlAreaId,
                                             String verId,
                                             String yearMonthFrom,
                                             String yearMonthTo,
                                             String sort,
                                             SearchVo searchVo);
}
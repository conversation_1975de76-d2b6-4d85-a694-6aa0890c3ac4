package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalPlanPidExp;

import java.util.List;

public interface DwhFinalPlanPidExpService extends IService<DwhFinalPlanPidExp> {

    PageDataInfo<DwhFinalPlanPidExp> overview(
            String verId, String companyId, String yearMonthFrom,
            String yearMonthTo, String sort, SearchVo searchVo);

    List<DwhFinalPlanPidExp> selectNoPage(String verId,
                                          String companyId,
                                          String yearMonthFrom,
                                          String yearMonthTo,
                                          String sort,
                                          SearchVo searchVo);
}
/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.datalink.fdop.tenant.provider;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.datalink.fdop.tenant.api.domain.dto.DataSourceInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DataSourceProvider {
    private static final Logger logger = LoggerFactory.getLogger(DataSourceProvider.class);

    private static final Map<Long, DruidDataSource> uniqueId2dataSourcetMap = new ConcurrentHashMap<>();

    private static class DataSourceProviderHolder {
        private static final DataSourceProvider INSTANCE = new DataSourceProvider();
    }

    public static DataSourceProvider getInstance() {
        return DataSourceProviderHolder.INSTANCE;
    }

    public DruidDataSource getDataSource(Long tenantId, DataSourceInfo dataSourceInfo) {
        logger.info("tenantId {}", tenantId);

        DruidDataSource druidDataSource = uniqueId2dataSourcetMap.computeIfAbsent(tenantId, $ -> {
            DruidDataSource dataSource = JdbcDataSourceProvider.createJdbcDataSource(dataSourceInfo);
            if (null == dataSource) {
                throw new RuntimeException("数据源信息异常");
            }
            return dataSource;
        });
        if (checkDataSourceFailContinuousStatus(druidDataSource)) {
            // 连接池创建连接线程已死亡，关闭连接池，重新创建连接池
            this.remoteDataSource(tenantId);
            druidDataSource = uniqueId2dataSourcetMap.get(tenantId);
            if (druidDataSource == null) {
                throw new RuntimeException("数据库连接为空");
            }
        }
        return druidDataSource;
    }

    public void remoteDataSource(Long dataSourceId) {
        logger.info("dataSourceId {}", dataSourceId);
        // 关闭DataSource连接
        DruidDataSource druidDataSource = uniqueId2dataSourcetMap.get(dataSourceId);
        if (druidDataSource == null) {
            return;
        }
        druidDataSource.close();
        // 删除缓存的DataSource
        uniqueId2dataSourcetMap.remove(dataSourceId);
    }

    /**
     * 检测线程是否存活
     *
     * @param dataSource 德鲁伊数据源
     * @return 是否存活
     */
    public boolean checkDataSourceFailContinuousStatus(DruidDataSource dataSource) {
        if (!dataSource.isBreakAfterAcquireFailure()) {
            logger.info("this data source does not open `BreakAfterAcquireFailure`");
            return false;
        }
        boolean failContinuous = dataSource.isFailContinuous(); // fail over retry attempts
        boolean createConnectionThreadIsAlive = true;
        try {
            DruidDataSource.CreateConnectionThread createConnectionThread = getCreateConnectionThread(dataSource);
            if (createConnectionThread == null) {
                logger.error("Get dataSource: " + dataSource + "'s createConnectionThread is null.");
                return false;
            }
            createConnectionThreadIsAlive = createConnectionThread.isAlive();
        } catch (Exception e) {
            logger.error("Get dataSource: " + dataSource + "'s createConnectionThread fails.", e);
            return false;
        }
        return failContinuous && !createConnectionThreadIsAlive;
    }

    public DruidDataSource.CreateConnectionThread getCreateConnectionThread(DruidDataSource dataSource) throws NoSuchFieldException, IllegalAccessException {
        Field createConnectionThread = dataSource.getClass().getSuperclass().getDeclaredField("createConnectionThread");
        createConnectionThread.setAccessible(true);
        logger.info("Get dataSource: {}'s createConnectionThread: {}", dataSource, JSON.toJSONString(createConnectionThread));
        return (DruidDataSource.CreateConnectionThread) createConnectionThread.get(dataSource);
    }

}

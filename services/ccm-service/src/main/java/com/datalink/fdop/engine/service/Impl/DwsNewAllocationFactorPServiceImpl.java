package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationFactorP;
import com.datalink.fdop.engine.mapper.DwsNewAllocationFactorPMapper;
import com.datalink.fdop.engine.service.DwsNewAllocationFactorPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewAllocationFactorPServiceImpl extends ServiceImpl<DwsNewAllocationFactorPMapper, DwsNewAllocationFactorP> implements DwsNewAllocationFactorPService {

    @Autowired
    private DwsNewAllocationFactorPMapper dwsNewAllocationFactorPMapper;

    @Override
    public PageDataInfo<DwsNewAllocationFactorP> overview(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String allocationType,
            String sort,
            SearchVo searchVo) {
        Page<DwsNewAllocationFactorP> page = PageUtils.getPage(DwsNewAllocationFactorP.class);
        IPage<DwsNewAllocationFactorP> iPage = dwsNewAllocationFactorPMapper.selectAll(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, costCenterType, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewAllocationFactorP> selectNoPage(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String costCenterType, String sort, SearchVo searchVo) {
        return dwsNewAllocationFactorPMapper.selectNoPage(controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, costCenterType, sort, searchVo);
    }
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.engine.api.domain.DwdSptEg;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdSptEgService extends IService<DwdSptEg> {
    
    PageDataInfo<DwdSptEg> overview(String plantId, String equipGroupId, String sort, SearchVo searchVo);
    
    List<DwdSptEg> selectNoPage(String plantId, String equipGroupId, String sort, SearchVo searchVo);
}
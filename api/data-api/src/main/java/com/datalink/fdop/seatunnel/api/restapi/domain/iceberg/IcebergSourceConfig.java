package com.datalink.fdop.seatunnel.api.restapi.domain.iceberg;

import com.alibaba.fastjson.annotation.JSONField;
import com.datalink.fdop.seatunnel.api.restapi.domain.SourceConfig;
import lombok.Data;

/**
 * Iceberg源配置信息
 */
@Data
public class IcebergSourceConfig extends CommonIcebergConfig implements SourceConfig {

    /**
     * 引用SeaTunnel 上下文配置 sink/transform 中定义的result_table_name
     */
    @JSONField(name = "source_table_name")
    private String sourceTableName;

}

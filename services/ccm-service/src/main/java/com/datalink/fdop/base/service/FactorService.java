package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.Factor;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface FactorService extends IService<Factor> {

    boolean insert(Factor factor);

    String checkIdUnique(String id, String date);

    PageDataInfo<Factor> list(Factor factor);

    int deleteByList(List<Factor> list);

    String importData(List<Factor> list, Boolean isUpdateSupport, String operName);

    boolean copy(List<Factor> list);

    PageDataInfo<Factor> overview(String menuId, String sort, SearchVo searchVo);

    PageDataInfo<Factor> select(String startCode, String endCode, String sort);

    List<Factor> getAll();

    int updateByKey(Factor factor);

    boolean updateBatchStatus(List<Factor> statusList);

}

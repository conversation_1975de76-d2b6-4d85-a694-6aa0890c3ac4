<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.VarMapper">

    <select id="selectTaskCodeByName" resultType="java.lang.Long">
        select code from dolphinscheduler.t_ds_task_definition where name = #{name} limit 1
    </select>

    <select id="selectPostingLogHead" resultType="com.datalink.fdop.engine.api.domain.PostingLogHead">
        select a.* from fccm.posting_log_${str}_head a
        where a.business_type = '${businessType}' and a.ver_id = '${verId}' and a.company_id = '${companyId}'
          and a.year = ${year} and a.month = ${month} and a.del_flag != true limit 1
    </select>

    <select id="selectPostingLogItem" resultType="com.datalink.fdop.engine.api.domain.PostingLogItem">
        select a.* from fccm.posting_log_${str}_item a
        where a.business_type = '${businessType}' and a.ver_id = '${verId}' and a.factory_id = '${factoryId}'
          and a.year = ${year} and a.month = ${month} and a.company_id = '${companyId}'
          and a.document_code = ${documentCode} and a.del_flag != true
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="selectVarPpvHead" resultType="com.datalink.fdop.engine.api.domain.VarPpvHead">
        select a.* from fccm.var_ppv_${str}_head a
        where a.ver_id = '${verId}' and a.company_id = '${companyId}' and a.year = ${year}
        and a.month = ${month} and a.del_flag != true
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        <if test="end != null and end != ''">limit 1</if>
    </select>

    <select id="selectVarPpvItem" resultType="com.datalink.fdop.engine.api.domain.VarPpvItem">
        select a.* from fccm.var_ppv_${str}_item a
        where a.ver_id = '${varPpvHead.verId}' and a.company_id = '${varPpvHead.companyId}' and a.factory_id = '${varPpvHead.factoryId}'
          and a.year = ${varPpvHead.year} and a.month = ${varPpvHead.month}
          and a.material_id = '${varPpvHead.materialId}' and a.del_flag != true
    </select>

    <select id="selectVarMpv" resultType="com.datalink.fdop.engine.api.domain.VarMpv">
        select a.* from fccm.var_mpv_${str} a
        where a.ver_id = '${verId}' and a.company_id = '${companyId}' and a.year = ${year}
        and a.month = ${month} and a.del_flag != true
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        <if test="end != null and end != ''">limit 1</if>
    </select>

    <select id="varSaveHead">
        insert into <if test="tableName == 'ppv'">fccm.var_${tableName}_${str}_head</if>
        <if test="tableName == 'mpv'">fccm.var_${tableName}_${str}</if>
        (select a.* from <if test="tableName == 'ppv'">fccm.var_${tableName}_${str1}_head</if>
        <if test="tableName == 'mpv'">fccm.var_${tableName}_${str1}</if> a
        where a.ver_id = '${verId}' and a.company_id = '${companyId}'
        and a.year = ${year} and a.month = ${month} and a.del_flag != true)
    </select>

    <select id="varSaveItem">
        insert into fccm.var_ppv_${str}_item
        (select b.* from fccm.var_ppv_${str1}_item b join fccm.var_ppv_${str1}_head a
        on a.year = b.year and a.month = b.month and a.ver_id = b.ver_id
        and a.factory_id = b.factory_id and a.company_id = b.company_id and a.material_id = b.material_id
        where a.ver_id = '${verId}' and a.company_id = '${companyId}'
        and a.year = ${year} and a.month = ${month} and a.del_flag != true)
    </select>

    <select id="postingSaveHead">
        insert into fccm.posting_log_${str}_head
        (select a.* from fccm.posting_log_${str1}_head a
            where a.business_type = '${businessType}' and a.ver_id = '${verId}' and a.company_id = '${companyId}'
            and a.year = ${year} and a.month = ${month} and a.del_flag != true)
    </select>

    <select id="postingSaveItem">
        insert into fccm.posting_log_${str}_item
        (select b.* from fccm.posting_log_${str1}_item b join fccm.posting_log_${str1}_head a
            on a.business_type = b.business_type and a.ver_id = b.ver_id
            and a.company_id = b.company_id and a.factory_id = b.factory_id
            and a.year = b.year and a.month = b.month and a.document_code = b.document_code
            where a.business_type = '${businessType}' and a.ver_id = '${verId}' and a.company_id = '${companyId}'
            and a.year = ${year} and a.month = ${month} and a.del_flag != true)
    </select>

    <select id="varDeleteHead">
        update <if test="tableName == 'ppv'">fccm.var_${tableName}_${str}_head</if>
        <if test="tableName == 'mpv'">fccm.var_${tableName}_${str}</if>
        set del_flag = true
        where ver_id = '${verId}' and company_id = '${companyId}'
        and year = ${year} and month = ${month} and del_flag != true
    </select>

    <select id="varDeleteItem">
        update fccm.var_ppv_${str}_item
        set del_flag = true
        where ver_id = '${verId}' and company_id = '${companyId}'
        and year = ${year} and month = ${month} and del_flag != true
    </select>

    <select id="postingDeleteHead">
        update fccm.posting_log_${str}_head
        set del_flag = true
        where business_type = '${businessType}' and ver_id = '${verId}' and company_id = '${companyId}'
          and year = ${year} and month = ${month} and del_flag != true
    </select>

    <select id="postingDeleteItem">
        update fccm.posting_log_${str}_item
        set del_flag = true
        where business_type = '${businessType}' and ver_id = '${verId}' and company_id = '${companyId}'
          and year = ${year} and month = ${month} and del_flag != true
    </select>

</mapper>
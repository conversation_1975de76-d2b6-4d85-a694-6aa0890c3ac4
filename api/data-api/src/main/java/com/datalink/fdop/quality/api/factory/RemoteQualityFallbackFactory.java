package com.datalink.fdop.quality.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.quality.api.RemoteQualityService;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.quality.api.model.dto.DataEntityQuality;
import com.datalink.fdop.quality.api.model.vo.CheckRegexVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据治理模块降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteQualityFallbackFactory implements FallbackFactory<RemoteQualityService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteQualityFallbackFactory.class);

    @Override
    public RemoteQualityService create(Throwable throwable) {
        log.error("数据质量模块调用失败:{}", throwable.getMessage());
        return new RemoteQualityService() {

            @Override
            public R update(DataQuality dataQuality) {
                return R.fail("修改数据质量失败：" + throwable.getMessage());
            }

            @Override
            public R<DataQuality> selectById(Long id) {
                return R.fail("查询数据质量失败：" + throwable.getMessage());
            }

            @Override
            public R<String> getRegex(Long id, String prefix) {
                return R.fail("获取正则表达式规则失败：" + throwable.getMessage());
            }

            @Override
            public R<Boolean> checkRegex(CheckRegexVo checkRegexVo) {
                return R.fail("校验正则表达式规则失败：" + throwable.getMessage());
            }

            @Override
            public R<List<DataEntityQuality>> parseEntityQuality(Long dataEntityId) {
                return R.fail("解析实体中的规则：" + throwable.getMessage());
            }

            @Override
            public R<List<DataEntityQuality>> parseElementQuality(Long dataElementId) {
                return R.fail("解析元素中的规则：" + throwable.getMessage());
            }

        };
    }
}

package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-10-29 10:17
 */
@Data
@TableName(schema = "zjdata", value = "ccm_activity")
@ApiModel("作业类型")
public class Activity {

    @Excel(name = "管理范围", sort = 1)
    @ApiModelProperty(value = "管理范围")
    @NotBlank(message = "管理范围不能为空")
    private String controlAreaId;

    @ApiModelProperty(value = "管理范围描述")
    @TableField(exist = false)
    private String controlAreaDesc;

    @Excel(name = "作业类型", sort = 2)
    @NotBlank(message = "作业类型不能为空")
    @ApiModelProperty(value = "作业类型")
    private String activityId;

    @Excel(name = "有效期从", sort = 3, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "有效期从")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date dateFrom;

    @Excel(name = "有效期至", sort = 4, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "有效期至")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @NotNull(message = "有效期至不能为空")
    private Date dateTo;

    @Excel(name = "作业类型描述", sort = 5)
    @ApiModelProperty(value = "作业类型描述")
    private String activityDesc;

    @Excel(name = "作业类型文本", sort = 6)
    @ApiModelProperty(value = "作业类型文本")
    private String activityText;

    @Excel(name = "作业类型单位", sort = 7)
    @ApiModelProperty(value = "作业类型单位")
    private String unit;

    @Excel(name = "成本要素", sort = 8)
    @ApiModelProperty(value = "成本要素")
    private String costElementId;

    @TableField(exist = false)
    @ApiModelProperty(value = "成本要素描述")
    private String costElementDesc;

    @Excel(name = "是否启用", sort = 13, readConverterExp = "true=启用,false=不启用")
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更改人")
    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;

}

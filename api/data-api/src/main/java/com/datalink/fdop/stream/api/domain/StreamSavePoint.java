package com.datalink.fdop.stream.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName(schema = "zjdata", value = "s_c_stream_savepoint")
@ApiModel("Flink流任务保存点")
public class StreamSavePoint extends BaseField {

    @ApiModelProperty(value = "SavePointId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "序号")
    private Integer sort;

    @ApiModelProperty(value = "流任务ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long streamId;

    @ApiModelProperty(value = "保存点存储路径")
    private String savePointPath;


}

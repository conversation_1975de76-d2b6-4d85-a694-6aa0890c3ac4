package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.base.api.domain.CapacityMenu;
import com.datalink.fdop.base.api.model.CapacityTree;
import com.datalink.fdop.base.api.model.WorkCenterTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CapacityMenuMapper extends BaseMapper<CapacityMenu> {

    int batchUpdatePidByIds(@Param("list") List<String> childrenList, @Param("pid") String pid);

    List<CapacityTree> selectMenuTree(@Param("sort") String sort);

    List<String> selectIdsByPid(@Param("pid") String pid);

}

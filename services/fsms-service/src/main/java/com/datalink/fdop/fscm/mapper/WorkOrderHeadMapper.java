package com.datalink.fdop.fscm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.fscm.api.domain.WorkOrderHead;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderQueryShowVo;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderQueryVo;
import com.datalink.fdop.project.api.model.vo.OrderQueryShowVo;
import com.datalink.fdop.project.api.model.vo.OrderQueryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:32
 */
@Mapper
@Transactional
public interface WorkOrderHeadMapper extends BaseMapper<WorkOrderHead> {



    Page<OrderQueryShowVo> queryData(Page<OrderQueryShowVo> page, @Param("object") OrderQueryVo orderQueryVo, @Param("flag") String flag);

    Page<WorkOrderQueryShowVo> queryWorkOrderData(Page<WorkOrderQueryShowVo> page, @Param("workOrderQueryVo") WorkOrderQueryVo workOrderQueryVo, @Param("delFlag") String delFlag,@Param("temporary") String temporary);


    @Select("select post_quantity from zjdata.f_d_post_certificate_row where work_order_num=#{workOrderNum} and work_order_row_num=#{workOrderRowNum} and move_type in ('101','102')")
    List<BigDecimal> findPostQuantity(@Param("workOrderNum") String workOrderNum, @Param("workOrderRowNum") Long workOrderRowNum);

    @Select("SELECT is_turnkey FROM zjdata.p_d_processing where process =#{process}")
    String findIsTurnkey(String process);
}

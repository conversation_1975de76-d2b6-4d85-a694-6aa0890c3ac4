package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRecoverEnt;
import com.datalink.fdop.engine.mapper.DwhFinalRecoverEntMapper;
import com.datalink.fdop.engine.service.DwhFinalRecoverEntService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRecoverEntServiceImpl extends ServiceImpl<DwhFinalRecoverEntMapper, DwhFinalRecoverEnt> implements DwhFinalRecoverEntService {

    @Autowired
    private DwhFinalRecoverEntMapper dwhFinalRecoverEntMapper;

    @Override
    public PageDataInfo<DwhFinalRecoverEnt> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwhFinalRecoverEnt> page = PageUtils.getPage(DwhFinalRecoverEnt.class);
        IPage<DwhFinalRecoverEnt> iPage = dwhFinalRecoverEntMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRecoverEnt> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwhFinalRecoverEntMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo); // 新增方法实现
    }
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.FlowItemMapper">

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.FlowItem" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_flow_item
        WHERE step_no = #{stepNo} AND technology_id = #{technologyId} AND technology_ver = #{technologyVer}
          AND del_flag = false limit 1
    </select>

    <update id="deleteByIds" parameterType="Long">
        update zjdata.ccm_flow_item
        SET del_flag = true
        WHERE
        <foreach collection="flowItemList" item="flowItem" open="(" separator=" or " close=")">
            step_no = #{flowItem.stepNo} AND technology_id = #{flowItem.technologyId} AND technology_ver = #{flowItem.technologyVer}
        </foreach>
        and del_flag = false
    </update>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.FlowItem" resultType="com.datalink.fdop.base.api.domain.FlowItem">
        SELECT *
        FROM zjdata.ccm_flow_item
        WHERE del_flag = false
        <if test="flowItem.stepNo != null">
            AND step_no = #{flowItem.stepNo}
        </if>
        <if test="flowItem.technologyId != null and flowItem.technologyId != ''">AND technology_id = #{flowItem.technologyId}</if>
        <if test="flowItem.technologyVer != null">AND technology_ver = #{flowItem.technologyVer}</if>
        ORDER BY technology_id, technology_ver desc
    </select>

    <update id="updateByKey">
        update zjdata.ccm_flow_item
        set
        <if test="routerId != null">router_id = #{routerId},</if>
        <if test="routerVer != null">router_ver = #{routerVer},</if>
        <if test="layerId != null">layer_id = #{layerId},</if>
        <if test="stageId != null">stage_id = #{stageId},</if>
        <if test="stepId != null">step_id = #{stepId},</if>
        <if test="stepType != null">step_type = #{stepType},</if>
        <if test="recipeId != null">recipe_id = #{recipeId},</if>
        <if test="workCenterId != null">work_center_id = #{workCenterId},</if>
        <if test="baseUnit != null">base_unit = #{baseUnit},</if>
        <if test="baseQty != null">base_qty = #{baseQty},</if>
        <if test="unit1 != null">unit1 = #{unit1},</if>
        <if test="qty1 != null">qty1 = #{qty1},</if>
        <if test="unit2 != null">unit2 = #{unit2},</if>
        <if test="qty2 != null">qty2 = #{qty2},</if>
        <if test="yieldRate != null">yield_rate = #{yieldRate},</if>
        <if test="weight != null">weight = #{weight},</if>
        <if test="text != null">text = #{text},</if>
        <if test="outsourcing != null">outsourcing = #{outsourcing},</if>
        <if test="enable != null">enable = #{enable},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        update_time = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where step_no = #{stepNo} AND technology_id = #{technologyId} AND technology_ver = #{technologyVer}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.FlowItem">
        SELECT ls.* from zjdata.ccm_flow_item ls
        WHERE ls.del_flag = false
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("ls",searchCondition)})
        </if>
        ORDER BY technology_id, technology_ver ${sort}
    </select>

    <select id="selectItemByHead" resultType="com.datalink.fdop.base.api.model.FlowTree">
        select a.*, 'ITEM' as menu_type from zjdata.ccm_flow_item a
        where a.technology_id = #{technologyId} AND a.technology_ver = #{technologyVer} and a.del_flag = false
    </select>

</mapper>


package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalCapacity;

import java.util.List;

public interface DwhFinalCapacityService extends IService<DwhFinalCapacity> {

    PageDataInfo<DwhFinalCapacity> overview(
            String verId, String plantId,  String sort, SearchVo searchVo);

    List<DwhFinalCapacity> listAll();

    List<DwhFinalCapacity> selectNoPage(String verId, String plantId, String sort, SearchVo searchVo);
}

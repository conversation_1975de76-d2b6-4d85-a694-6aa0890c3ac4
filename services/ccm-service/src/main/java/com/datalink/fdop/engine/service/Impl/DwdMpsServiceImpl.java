package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdMps;
import com.datalink.fdop.engine.api.domain.DwhFinalCapacity;
import com.datalink.fdop.engine.mapper.DwdMpsMapper;
import com.datalink.fdop.engine.mapper.DwhFinalCapacityMapper;
import com.datalink.fdop.engine.service.DwdMpsService;
import com.datalink.fdop.engine.service.DwhFinalCapacityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdMpsServiceImpl extends ServiceImpl<DwdMpsMapper, DwdMps> implements DwdMpsService {

    @Autowired
    private DwdMpsMapper dwdMpsMapper;

    @Override
    public PageDataInfo<DwdMps> overview(
            String verId, String plantId, String yearMonthFrom, String yearMonthTo,
            String sort, SearchVo searchVo) {
        Page<DwdMps> page = PageUtils.getPage(DwdMps.class);
        IPage<DwdMps> iPage = dwdMpsMapper.selectAll(verId, plantId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdMps> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwdMpsMapper.selectNoPage(verId, plantId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}

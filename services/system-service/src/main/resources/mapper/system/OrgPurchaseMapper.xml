<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.system.mapper.OrgPurchaseMapper">

    <select id="checkPurchaseUnique"  resultType="int">
        SELECT COUNT(1)
        FROM zjdata.org_purchase
        WHERE purchase_code = #{code} and company_id =#{companyId} limit 1
    </select>

    <select id="checkPurchase" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.org_purchase
        WHERE id &lt;&gt; #{id} AND purchase_code = #{code} and company_id =#{companyId}AND del_flag = false limit 1
    </select>

    <update id="deletePurchaseById" parameterType="Long">
        update zjdata.org_purchase
        SET del_flag = true
        WHERE id = #{id}
        and del_flag = false
    </update>

    <update id="deletePurchaseByIds" parameterType="Long">
        update zjdata.org_purchase
        SET del_flag = true
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag = false
    </update>

    <select id="selectPurchaseList" resultType="com.datalink.fdop.system.api.domain.OrgPurchase">
        SELECT op.*, cp.company_code as company_code, cp.company_description as company_description
        FROM zjdata.org_purchase op
        left join zjdata.org_company cp on op.company_id = cp.id
        WHERE op.del_flag = false
        <if test="orgPurchase.id != null and orgPurchase.id != 0L">
            AND op.id = #{orgPurchase.id}
        </if>
        <if test="orgPurchase.purchaseCode != null and orgPurchase.purchaseCode != ''">
            AND op.purchase_code like '%${orgPurchase.purchaseCode}%'
        </if>
        <if test="orgPurchase.purchaseDescription != null and orgPurchase.purchaseDescription != ''">
            AND op.purchase_description like '%${orgPurchase.purchaseDescription}%'
        </if>
        ORDER BY op.id desc
    </select>

    <update id="updateCompanyByDeleteCompanys" parameterType="Long">
        update zjdata.org_purchase
        <set>
            company_id = null,
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        WHERE del_flag = false AND
        company_id IN
        <foreach collection="companyIds" item="companyId" open="(" separator="," close=")">
            #{companyId}
        </foreach>
    </update>

    <select id="query" resultType="com.datalink.fdop.system.api.domain.OrgPurchase">
        SELECT purchase.*, company.company_code as company_code, company.company_description as company_description
        FROM zjdata.org_purchase purchase
        left join zjdata.org_company company on purchase.company_id = company.id
        WHERE purchase.del_flag = false
        <if test="searchVo.purchaseCodes != null and searchVo.purchaseCodes.size != 0">
            AND purchase.purchase_code IN
            <foreach collection="searchVo.purchaseCodes" item="purchaseCode" separator="," open="(" close=")">
                #{purchaseCode}
            </foreach>
        </if>
        <if test="searchVo.purchaseDescriptions != null and searchVo.purchaseDescriptions.size != 0">
            AND purchase.purchase_description IN
            <foreach collection="searchVo.purchaseDescriptions" item="purchaseDescription" separator="," open="(" close=")">
                #{purchaseDescription}
            </foreach>
        </if>

        <if test="searchVo.companyCodes != null and searchVo.companyCodes.size != 0">
            AND company.company_code IN
            <foreach collection="searchVo.companyCodes" item="companyCode" separator="," open="(" close=")">
                #{companyCode}
            </foreach>
        </if>
        <if test="searchVo.companyDescriptions != null and searchVo.companyDescriptions.size != 0">
            AND company.company_description IN
            <foreach collection="searchVo.companyDescriptions" item="companyDescription" separator="," open="(" close=")">
                #{companyDescription}
            </foreach>
        </if>

        <if test="searchVo.enables != null">
            AND purchase.enable = #{searchVo.enables}
        </if>
        ORDER BY purchase.id desc
    </select>

</mapper>
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdPrice;

import java.util.List;

public interface DwdPriceService extends IService<DwdPrice> {

    PageDataInfo<DwdPrice> overview(String materialId, String plantId, String sort, SearchVo searchVo);

    List<DwdPrice> selectNoPage(String materialId, String plantId, String sort, SearchVo searchVo);
}
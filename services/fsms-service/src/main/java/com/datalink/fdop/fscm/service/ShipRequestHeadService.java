package com.datalink.fdop.fscm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.fscm.api.domain.ShipRequestHead;
import com.datalink.fdop.fscm.api.domain.ShipRequestRow;
import com.datalink.fdop.fscm.api.model.vo.SaveShipRequestVo;
import com.datalink.fdop.fscm.api.model.vo.ShipRequestPostCertificateVo;
import com.datalink.fdop.fscm.api.model.vo.ShipRequestQueryVo;
import com.datalink.fdop.fscm.api.model.vo.ShipRequestShowVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/15 10:07
 */
public interface ShipRequestHeadService extends IService<ShipRequestHead> {
    List<ShipRequestShowVo> queryData( ShipRequestQueryVo shipRequestQueryVo, String delFlag, String temporary);

    void checkData(List<ShipRequestShowVo> shipRequestShowVos);

    void saveData(ShipRequestPostCertificateVo shipRequestPostCertificateVo);

    void saveCheck(List<ShipRequestRow> shipRequestRows);

    void updateRepertory(SaveShipRequestVo saveShipRequestVo);
}

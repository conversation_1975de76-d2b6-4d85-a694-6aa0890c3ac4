<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.AllocationMethodMenuMapper">

    <update id="bacthUpdatePidById" parameterType="String">
        update zjdata.ccm_allocation_method_menu
        set p_menu_id = (select p_menu_id from zjdata.ccm_allocation_method_menu where menu_id = #{menuId} limit 1)
        where p_menu_id = #{menuId} and del_flag = false
    </update>

    <update id="deleteByIds" parameterType="String">
        update zjdata.ccm_allocation_method_menu
        set del_flag = true
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag = false
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.AllocationMethodTree">
        select null as control_area_id, null as allocation_method_id, null as allocation_segment_id, a.*, 'MENU' as menu_type,
               null as date_from, null as date_to, null as allocation_method_text, null as allocation_method_desc,
               null as allocation_segment_desc, null as allocation_segment_text, null as enable
        from zjdata.ccm_allocation_method_menu a
        where a.del_flag != true
        UNION
        select b.control_area_id as control_area_id, b.allocation_method_id as allocation_method_id, null as allocation_segment_id, null as menu_id, b.menu_id as p_menu_id, null as menu_desc, null as menu_text,
               b.del_flag as del_flag, 'MID' as menu_type,
               b.date_from as date_from, b.date_to as date_to,
               b.allocation_method_text as allocation_method_text, b.allocation_method_desc as allocation_method_desc,
               null as allocation_segment_desc, null as allocation_segment_text, b.enable as enable
        from zjdata.ccm_allocation_method b
        where b.del_flag != true
        UNION
        select c.control_area_id as control_area_id, c.allocation_method_id as allocation_method_id, c.allocation_segment_id as allocation_segment_id, null as menu_id, null as p_menu_id, null as menu_desc, null as menu_text,
               c.del_flag as del_flag, 'NODE' as menu_type,
               null as date_from, null as date_to, null as allocation_method_text, null as allocation_method_desc,
               c.allocation_segment_desc as allocation_segment_desc, c.allocation_segment_text as allocation_segment_text, c.enable as enable
        from zjdata.ccm_allocation_division c
        where c.del_flag != true
        order by menu_id ${sort}
    </select>

    <select id="selectByPids" resultType="String">
        select * from zjdata.ccm_allocation_method
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag != true
        order by control_area_id, allocation_method_id desc
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.AllocationMethodMenu">
        select * from zjdata.ccm_allocation_method_menu
        where menu_id = #{id} and del_flag != true limit 1
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CostStructureItemMapper">

    <update id="deleteBykey">
        update zjdata.ccm_cost_structure_item
        set del_flag = true
        where control_area_id = #{controlAreaId} AND cost_structure_id = #{costStructureId}
          and evaluate_area_id = #{evaluateAreaId} AND date_from = '${date}' AND del_flag = false
    </update>

    <delete id="deleteByHead" parameterType="com.datalink.fdop.base.api.domain.CostStructureHead">
        update zjdata.ccm_cost_structure_item
        set del_flag = true
        where control_area_id = #{controlAreaId} AND cost_structure_id = #{costStructureId}
          AND date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}' AND del_flag = false
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_cost_structure_item
        where control_area_id = #{controlAreaId} AND cost_structure_id = #{costStructureId}
          and evaluate_area_id = #{evaluateAreaId} AND date_from = '${date}' AND del_flag = false
    </select>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.CostStructureItem" resultType="com.datalink.fdop.base.api.domain.CostStructureItem">
        SELECT a.*, controlArea.control_area_desc as control_area_desc, evaluateArea.evaluate_area_desc as evaluate_area_desc,
               head.cost_structure_desc as cost_structure_desc, costElementFrom.cost_element_desc as cost_element_from_desc,
               costElementTo.cost_element_desc as cost_element_to_desc, costElementGroup.menu_desc as cost_element_group_desc
        FROM zjdata.ccm_cost_structure_item a
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
        left join zjdata.ccm_cost_structure_head head on head.control_area_id = a.control_area_id and head.del_flag = false
        left join zjdata.ccm_evaluate_area evaluateArea on a.evaluate_area_id = evaluateArea.evaluate_area_id and evaluateArea.del_flag = false
        left join zjdata.ccm_cost_element costElementFrom on a.cost_element_from = costElementFrom.cost_element_id and costElementFrom.del_flag = false
        left join zjdata.ccm_cost_element costElementTo on a.cost_element_to = costElementTo.cost_element_id and costElementTo.del_flag = false
        left join zjdata.ccm_cost_element_menu costElementGroup on a.cost_element_group = costElementGroup.menu_id and costElementGroup.del_flag = false
        where a.del_flag = false
        <if test="costStructureItem.costStructureId != null and costStructureItem.costStructureId != ''">
            AND a.cost_structure_id = #{costStructureItem.costStructureId}
        </if>
        <if test="costStructureItem.controlAreaId != null and costStructureItem.controlAreaId != ''">
            AND a.control_area_id = #{costStructureItem.controlAreaId}
        </if>
        <if test="costStructureItem.evaluateAreaId != null and costStructureItem.evaluateAreaId != ''">
            AND a.evaluate_area_id = #{costStructureItem.evaluateAreaId}
        </if>
        <if test="costStructureItem.dateFrom != null">
            AND a.date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(costStructureItem.dateFrom)}'
        </if>
        ORDER BY control_area_id, cost_structure_id, date_from ,evaluate_area_id desc
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.CostStructureItem">
        SELECT a.*, controlArea.control_area_desc as control_area_desc, evaluateArea.evaluate_area_desc as evaluate_area_desc,
               head.cost_structure_desc as cost_structure_desc, costElementFrom.cost_element_desc as cost_element_from_desc,
               costElementTo.cost_element_desc as cost_element_to_desc, costElementGroup.menu_desc as cost_element_group_desc
        FROM zjdata.ccm_cost_structure_item a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
                 left join zjdata.ccm_cost_structure_head head on head.control_area_id = a.control_area_id and head.del_flag = false
                 left join zjdata.ccm_evaluate_area evaluateArea on a.evaluate_area_id = evaluateArea.evaluate_area_id and evaluateArea.del_flag = false
                 left join zjdata.ccm_cost_element costElementFrom on a.cost_element_from = costElementFrom.cost_element_id and costElementFrom.del_flag = false
                 left join zjdata.ccm_cost_element costElementTo on a.cost_element_to = costElementTo.cost_element_id and costElementTo.del_flag = false
                 left join zjdata.ccm_cost_element_menu costElementGroup on a.cost_element_group = costElementGroup.menu_id and costElementGroup.del_flag = false
        where a.control_area_id = #{controlAreaId} AND a.cost_structure_id = #{costStructureId}
          and a.evaluate_area_id = #{evaluateAreaId} AND a.date_from = '${date}' AND a.del_flag = false limit 1
    </select>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CostStructureItem">
        update zjdata.ccm_cost_structure_item
        set
        <if test="costElementFrom != null">cost_element_from = #{costElementFrom},</if>
        <if test="costElementTo != null">cost_element_to = #{costElementTo},</if>
        <if test="costElementGroup != null">cost_element_group = #{costElementGroup},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId} and cost_structure_id = #{costStructureId}
          and date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}'
          and del_flag = false and evaluate_area_id = #{evaluateAreaId}
    </update>

    <select id="getAll" resultType="com.datalink.fdop.base.api.domain.CostStructureItem">
        SELECT a.*, controlArea.control_area_desc as control_area_desc, evaluateArea.evaluate_area_desc as evaluate_area_desc,
               head.cost_structure_desc as cost_structure_desc, costElementFrom.cost_element_desc as cost_element_from_desc,
               costElementTo.cost_element_desc as cost_element_to_desc, costElementGroup.menu_desc as cost_element_group_desc
        FROM zjdata.ccm_cost_structure_item a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
                 left join zjdata.ccm_cost_structure_head head on head.control_area_id = a.control_area_id and head.del_flag = false
                 left join zjdata.ccm_evaluate_area evaluateArea on a.evaluate_area_id = evaluateArea.evaluate_area_id and evaluateArea.del_flag = false
                 left join zjdata.ccm_cost_element costElementFrom on a.cost_element_from = costElementFrom.cost_element_id and costElementFrom.del_flag = false
                 left join zjdata.ccm_cost_element costElementTo on a.cost_element_to = costElementTo.cost_element_id and costElementTo.del_flag = false
                 left join zjdata.ccm_cost_element_menu costElementGroup on a.cost_element_group = costElementGroup.menu_id and costElementGroup.del_flag = false
        WHERE a.del_flag = false
        ORDER BY control_area_id, cost_structure_id, date_from, evaluate_area_id desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.CostStructureItem">
        SELECT a.*, controlArea.control_area_desc as control_area_desc, evaluateArea.evaluate_area_desc as evaluate_area_desc,
        head.cost_structure_desc as cost_structure_desc, costElementFrom.cost_element_desc as cost_element_from_desc,
        costElementTo.cost_element_desc as cost_element_to_desc, costElementGroup.menu_desc as cost_element_group_desc
        FROM zjdata.ccm_cost_structure_item a
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
        left join zjdata.ccm_cost_structure_head head on head.control_area_id = a.control_area_id and head.del_flag = false
        left join zjdata.ccm_evaluate_area evaluateArea on a.evaluate_area_id = evaluateArea.evaluate_area_id and evaluateArea.del_flag = false
        left join zjdata.ccm_cost_element costElementFrom on a.cost_element_from = costElementFrom.cost_element_id and costElementFrom.del_flag = false
        left join zjdata.ccm_cost_element costElementTo on a.cost_element_to = costElementTo.cost_element_id and costElementTo.del_flag = false
        left join zjdata.ccm_cost_element_menu costElementGroup on a.cost_element_group = costElementGroup.menu_id and costElementGroup.del_flag = false
        WHERE a.del_flag = false
        <if test="searchVo != null and searchVo!= ''">
            and ${searchVo}
        </if>
        ORDER BY control_area_id, cost_structure_id, date_from, evaluate_area_id ${sort}
    </select>

    <select id="query" resultType="com.datalink.fdop.base.api.domain.CostStructureItem">
        SELECT a.*, controlArea.control_area_desc as control_area_desc, evaluateArea.evaluate_area_desc as evaluate_area_desc,
               head.cost_structure_desc as cost_structure_desc, costElementFrom.cost_element_desc as cost_element_from_desc,
               costElementTo.cost_element_desc as cost_element_to_desc, costElementGroup.menu_desc as cost_element_group_desc
        FROM zjdata.ccm_cost_structure_item a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
                 left join zjdata.ccm_cost_structure_head head on head.control_area_id = a.control_area_id and head.del_flag = false
                 left join zjdata.ccm_evaluate_area evaluateArea on a.evaluate_area_id = evaluateArea.evaluate_area_id and evaluateArea.del_flag = false
                 left join zjdata.ccm_cost_element costElementFrom on a.cost_element_from = costElementFrom.cost_element_id and costElementFrom.del_flag = false
                 left join zjdata.ccm_cost_element costElementTo on a.cost_element_to = costElementTo.cost_element_id and costElementTo.del_flag = false
                 left join zjdata.ccm_cost_element_menu costElementGroup on a.cost_element_group = costElementGroup.menu_id and costElementGroup.del_flag = false
        where a.control_area_id = #{controlAreaId} AND a.cost_structure_id = #{costStructureId}
          AND a.date_from = '${date}' AND a.del_flag = false limit 1
    </select>

</mapper>


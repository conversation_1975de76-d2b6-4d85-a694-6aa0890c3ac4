package com.datalink.fdop.permissions.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.permissions.api.domain.RelevancyBack;
import com.datalink.fdop.permissions.api.domain.RelevancyFront;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/20 15:43
 */
public interface IRelevancyService {

    int addPermissionsElement(List<DataElement> dataElementList);

    int deletePermissionsElement(List<Long> ids);

    PageDataInfo<DataElement> selectPermissionsElementList(DataElement dataElement);

    int addRelevancyFront(Long elementId, List<RelevancyFront> relevancyFrontList);

    int deleteRelevancyFront(Long elementId, List<Long> ids);

    int addRelevancyBack(Long elementId, List<RelevancyBack> relevancyBackList);

    int deleteRelevancyBack(Long elementId, List<Long> ids);

    PageDataInfo<RelevancyFront> selectRelevancyFrontList(Long elementId, RelevancyFront relevancyFront);

    PageDataInfo<RelevancyBack> selectRelevancyBackList(Long elementId, RelevancyBack relevancyBack);

}

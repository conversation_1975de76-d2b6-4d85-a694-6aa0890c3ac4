package com.datalink.fdop.fscm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.fscm.api.domain.ProvisionalSingleOut;
import com.datalink.fdop.fscm.api.domain.ProvisionalSingleOutCache;
import com.datalink.fdop.fscm.api.domain.WorkOrderChild;
import com.datalink.fdop.fscm.api.domain.WorkOrderRow;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:34
 */
public interface ProvisionalSingleOutService extends IService<ProvisionalSingleOut> {
    long getProvisionalSingleOutCount(WorkOrderChild workOrderChild);
    long getProvisionalSingleOutCacheCount(ProvisionalSingleOutCache cache);

    List<ProvisionalSingleOut> getProvisionalSingleOutByRow(String workOrderNum, Long workOrderRowNum);

    List<ProvisionalSingleOut> getProvisionalSingleOutByChild(WorkOrderChild orderChildren);

    List<ProvisionalSingleOut> getProvisionalSingleOutByChildGroupBy(WorkOrderChild orderChildren);

    List<ProvisionalSingleOut> getProvisionalSingleOutByRowGroupBy(String workOrderNum, Long workOrderRowNum, Long childrenNum);

    void setVal(ProvisionalSingleOut provisionalSingleOut, WorkOrderChild child, ProvisionalSingleOut oldSingleOut);

    void synchronizationCache(String workOrderNum, Long workOrderRowNum, Long workOrderChildrenNum);

    void synchronizationProvisional(String workOrderNum, List<Long> workOrderRowNums);

    List<ProvisionalSingleOut> getSingleOutByRowGroupBy(WorkOrderRow row);

    void setPieBin(WorkOrderChild workOrderChild);

    void changeRefreshCache(String workOrderNum);

    void delCache(List<ProvisionalSingleOut> provisionalSingleOuts);

    void changeRefreshCache2(ProvisionalSingleOut provisionalSingleOut);
}

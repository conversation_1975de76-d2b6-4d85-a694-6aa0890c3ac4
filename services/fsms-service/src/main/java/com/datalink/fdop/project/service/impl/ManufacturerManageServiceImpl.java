package com.datalink.fdop.project.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.project.api.domain.ManufacturerManage;
import com.datalink.fdop.project.mapper.ManufacturerManageMapper;
import com.datalink.fdop.project.service.ManufacturerManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/11/4 15:00
 */
@Service
public class ManufacturerManageServiceImpl extends ServiceImpl<ManufacturerManageMapper, ManufacturerManage> implements ManufacturerManageService {


    @Autowired
    private ManufacturerManageMapper manageMapper;



    @Override
    public int findFlowCount() {
        return manageMapper.findFlowCount();
    }
}

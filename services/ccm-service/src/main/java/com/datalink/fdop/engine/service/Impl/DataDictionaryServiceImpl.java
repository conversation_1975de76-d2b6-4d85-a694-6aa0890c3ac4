package com.datalink.fdop.engine.service.Impl;

import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.mapper.DataDictionaryMapper;
import com.datalink.fdop.engine.service.DataDictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 通用数据字典Service（关联ODS层描述字段）
 */
@Service
public class DataDictionaryServiceImpl implements DataDictionaryService {

    @Autowired
    private DataDictionaryMapper dataDictionaryMapper;

    @Override
    public List<SelectVo> listPlantId(String orgTableName, SearchVo searchVo) {
        return dataDictionaryMapper.listPlantId(orgTableName, searchVo);
    }

    @Override
    public List<SelectVo> selectEvaluateAreaId(String orgTableName, SearchVo searchVo) {
        return dataDictionaryMapper.selectEvaluateAreaId(orgTableName, searchVo);
    }

    @Override
    public List<SelectVo> listVerId(String orgTableName, SearchVo searchVo) {
        return dataDictionaryMapper.listVerId(orgTableName, searchVo);
    }

    @Override
    public List<SelectVo> listCompanyId(String orgTableName, SearchVo searchVo) {
        return dataDictionaryMapper.listCompanyId(orgTableName, searchVo);
    }

    @Override
    public List<SelectVo> listCompanyId(String orgTableName, String fieldName, SearchVo searchVo) {
        return dataDictionaryMapper.listCompanyIdAndFieldName(orgTableName, fieldName, searchVo);
    }

    @Override
    public List<SelectVo> listManageScope(String orgTableName, SearchVo searchVo) {
        return dataDictionaryMapper.listManageScope(orgTableName, searchVo);
    }

    @Override
    public List<SelectVo> listCostCenterId(String orgTableName, SearchVo searchVo) {
        return dataDictionaryMapper.listCostCenterId(orgTableName, searchVo);
    }

}

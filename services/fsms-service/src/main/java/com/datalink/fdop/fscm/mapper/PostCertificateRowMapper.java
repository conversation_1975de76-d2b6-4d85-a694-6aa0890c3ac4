package com.datalink.fdop.fscm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.fscm.api.domain.PostCertificateRow;
import org.apache.ibatis.annotations.Select;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2022/11/29 10:39
 */
@Transactional
public interface PostCertificateRowMapper extends BaseMapper<PostCertificateRow> {

    @Select("SELECT max(voucher_num) voucher_num from zjdata.f_d_post_certificate_row ")
    String getMaxNum();
}

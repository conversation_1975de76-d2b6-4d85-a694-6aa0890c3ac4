package com.datalink.fdop.drive.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.DataSourceMenu;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/9 21:28
 */
public class DomainAgeUtils {

    public static String getDataSourceAgeString(DataSource dataSource) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataSource.getId() != null ? dataSource.getId() : IdWorker.getId()))
                .append(", pid: " + (dataSource.getPid() != null ? dataSource.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataSource.getCode()) ? ", code: '" + dataSource.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataSource.getName()) ? ", name: '" + dataSource.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataSource.getDescription()) ? ", description: '" + dataSource.getDescription() + "'" : ""))
                .append((dataSource.getType() != null ? ", type: '" + dataSource.getType() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataSource.getDataSourceBasicInfo()) ? ", dataSourceBasicInfo: '" + dataSource.getDataSourceBasicInfo() + "'" : ""))
                .append(", isRead: " + (dataSource.getIsRead() != null ? dataSource.getIsRead() : false))
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataSource.getCreateBy()) ? dataSource.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataSource.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataSource.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getDataSourceMenuAgeStr(DataSourceMenu dataSourceMenu) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataSourceMenu.getId() != null ? dataSourceMenu.getId() : IdWorker.getId()))
                .append(", pid: " + (dataSourceMenu.getPid() != null ? dataSourceMenu.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataSourceMenu.getCode()) ? ", code: '" + dataSourceMenu.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataSourceMenu.getName()) ? ", name: '" + dataSourceMenu.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataSourceMenu.getDescription()) ? ", description: '" + dataSourceMenu.getDescription() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataSourceMenu.getCreateBy()) ? dataSourceMenu.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataSourceMenu.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataSourceMenu.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

}

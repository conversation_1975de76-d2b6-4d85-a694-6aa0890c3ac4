<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.WorkFlowMapper">

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.WorkFlow">
        SELECT code, project_code, version
        FROM dolphinscheduler.t_ds_process_definition
        WHERE id = #{id}
    </select>


    <select id="selectProcessInstanceByProcessDefinitionCodeAndRunning" resultType="com.datalink.fdop.base.api.domain.WorkFlow" flushCache="true">
        SELECT install.id as processInstanceId,
               install.name as processInstanceName,
               install.state as processStatus,
               definition.project_code as projectCode
        FROM dolphinscheduler.t_ds_process_instance install
                 left join dolphinscheduler.t_ds_process_definition definition on install.process_definition_code = definition.code and install.process_definition_version = definition."version"
        WHERE install.process_definition_code = #{processDefinitionCode}
          AND install.process_definition_version = #{version}
          AND install.state = 1
        ORDER BY install.id DESC LIMIT 1
    </select>

    <select id="selectProcessStatusByProcessInstanceId" resultType="int" flushCache="true">
        SELECT state as processStatus
        FROM dolphinscheduler.t_ds_process_instance
        WHERE id = #{processInstanceId}
    </select>


    <select id="selectProcessByProcessInstanceId" resultType="com.datalink.fdop.base.api.domain.WorkFlow" flushCache="true">
        SELECT
            install.id as processInstanceId,
            install.name as processInstanceName,
            install.state as processStatus,
            definition.project_code as projectCode,
            definition.code as code,
            definition.version as version
        FROM dolphinscheduler.t_ds_process_instance install
            left join dolphinscheduler.t_ds_process_definition definition on install.process_definition_code = definition.code and install.process_definition_version = definition."version"
        WHERE install.id = #{processInstanceId}
    </select>

</mapper>


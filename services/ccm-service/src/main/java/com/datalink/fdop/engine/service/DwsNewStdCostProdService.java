package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostProd;

import java.util.List;

public interface DwsNewStdCostProdService extends IService<DwsNewStdCostProd> {

    PageDataInfo<DwsNewStdCostProd> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo);

    List<DwsNewStdCostProd> listAll();

    List<DwsNewStdCostProd> selectNoPage(String controlAreaId, String productId, String verId, String plantId, String date, String sort, SearchVo searchVo);
}
package com.datalink.fdop.element.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseDomain;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.element.api.enums.EntityInsertType;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2022/4/12 11:45
 */
@Data
@TableName(schema = "zjdata", value = "d_e_data_entity_structure")
@ApiModel("数据实体结构")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataEntityStructure extends BaseDomain {

    private static final long serialVersionUID = -5508413529453322173L;

    @ApiModelProperty(value = "实体字段id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "实体id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dataEntityId;

    @NotNull(message = "实体新增类型不能为空")
    @ApiModelProperty(value = "实体新增类型(CUSTOMIZE:自定义,PREDEFINED:预定义,BUILTIN:内置列)")
    private EntityInsertType entityInsertType;

    @ApiModelProperty(value = "编码", required = true)
    @NotNull(message = "编码不能为空")
    @Size(min = 0, max = 60, message = "编码不能超过60个字符")
    @Pattern(regexp = "^[a-zA-Z_]([a-zA-Z0-9_]{0,59})", message = "实体编码只能以字母开头，数字或字母或下划线组成")
    private String code;

    @ApiModelProperty(value = "名称")
    @Size(min = 0, max = 60, message = "名称不能超过60个字符")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @NotNull(message = "实体字段类型不能为空")
    @ApiModelProperty(value = "字段类型")
    private FieldType fieldType;

    @ApiModelProperty(value = "长度")
    private Long length;

    @ApiModelProperty(value = "精度")
    private Long decimalLength;

    @ApiModelProperty(value = "是否主键")
    private Boolean isPk;

    @ApiModelProperty(value = "排序")
    @TableField(exist = false)
    private Integer seq;

    @ApiModelProperty(value = "数据治理-L3名称")
    private String l3Name;

    @ApiModelProperty(value = "数据治理-L3名称")
    private String l4Name;

    @ApiModelProperty(value = "数据治理-L5ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long l5Id;

    @ApiModelProperty(value = "数据治理-L5编码")
    private String l5Code;

    @ApiModelProperty(value = "数据治理-L5名称")
    private String l5Name;

    public DataEntityStructure() {
    }

    public DataEntityStructure(Long dataEntityId, EntityInsertType entityInsertType, String code, String name, FieldType fieldType, Long length, Long decimalLength, Boolean isPk, Integer seq) {
        this.dataEntityId = dataEntityId;
        this.entityInsertType = entityInsertType;
        this.code = code;
        this.name = name;
        this.fieldType = fieldType;
        this.length = length;
        this.decimalLength = decimalLength;
        this.isPk = isPk;
        this.seq = seq;
    }

    public DataEntityStructure(Long id, Long dataEntityId, EntityInsertType entityInsertType, String code, String name, FieldType fieldType, Long length, Long decimalLength, Boolean isPk, Integer seq) {
        this.id = id;
        this.dataEntityId = dataEntityId;
        this.entityInsertType = entityInsertType;
        this.code = code;
        this.name = name;
        this.fieldType = fieldType;
        this.length = length;
        this.decimalLength = decimalLength;
        this.isPk = isPk;
        this.seq = seq;
    }

    public DataEntityStructure(Long id, Long dataEntityId, EntityInsertType entityInsertType, String code, String name, String description, FieldType fieldType, Long length, Long decimalLength, Boolean isPk, Integer seq) {
        this.id = id;
        this.dataEntityId = dataEntityId;
        this.entityInsertType = entityInsertType;
        this.code = code;
        this.name = name;
        this.description = description;
        this.fieldType = fieldType;
        this.length = length;
        this.decimalLength = decimalLength;
        this.isPk = isPk;
        this.seq = seq;
    }

    public DataEntityStructure(DataEntityStructureVo dataEntityStructureVo) {
        this.dataEntityId = dataEntityStructureVo.getDataEntityId();
        this.entityInsertType = dataEntityStructureVo.getEntityInsertType();
        this.code = dataEntityStructureVo.getCode();
        this.name = dataEntityStructureVo.getName();
        this.description = dataEntityStructureVo.getDescription();
        this.fieldType = dataEntityStructureVo.getFieldType();
        this.length = dataEntityStructureVo.getLength();
        this.decimalLength = dataEntityStructureVo.getDecimalLength();
        this.isPk = dataEntityStructureVo.getIsPk();
        this.seq = dataEntityStructureVo.getSeq();
    }

}

package com.datalink.fdop.seatunnel.api.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * SeaTunnel属性字段类
 */
@Data
public class SeaTunnelField {

    /**
     * 属性ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段类型
     */
    private String fieldType;

    /**
     * 字段长度
     */
    private Long length;

    /**
     * 字段小数长度
     */
    private Long decimalLength;

    /**
     * 是否主键
     */
    private Boolean isPk;

    /**
     * 转译类型
     */
    private String transformFieldType;


    public SeaTunnelField() {
    }

    public SeaTunnelField(Long id, String fieldName, String fieldType, Long length, Long decimalLength, Boolean isPk) {
        this.id = id;
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.length = length;
        this.decimalLength = decimalLength;
        this.isPk = isPk;
    }
}

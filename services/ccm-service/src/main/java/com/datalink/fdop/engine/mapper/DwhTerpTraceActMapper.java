package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.engine.api.domain.DwhTerpTraceAct;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwhTerpTraceActMapper extends BaseMapper<DwhTerpTraceAct> {

    IPage<DwhTerpTraceAct> selectAll(
             @Param("plantId") String plantId,
            @Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo,
            IPage<DwhTerpTraceAct> page, @Param("sort") String sort,
                                 @Param("searchVo") SearchVo searchVo);

    List<DwhTerpTraceAct> selectNoPage(@Param("plantId") String plantId,
                                       @Param("dateFrom") String dateFrom,
                                       @Param("dateTo") String dateTo,
                                       @Param("sort") String sort,
                                       @Param("searchVo") SearchVo searchVo);
}

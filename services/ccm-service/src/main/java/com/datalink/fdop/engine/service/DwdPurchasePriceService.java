package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdPurchasePrice;

import java.util.List;

public interface DwdPurchasePriceService extends IService<DwdPurchasePrice> {

    PageDataInfo<DwdPurchasePrice> overview(String materialId, String plantId, String sort, SearchVo searchVo);

    List<DwdPurchasePrice> selectNoPage(String materialId, String plantId, String sort, SearchVo searchVo);
}

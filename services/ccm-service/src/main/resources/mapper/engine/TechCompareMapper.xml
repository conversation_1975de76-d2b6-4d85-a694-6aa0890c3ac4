<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.TechCompareMapper">

    <select id="selectCount" resultType="java.lang.Long">
        select count(*) from fccm.tech_compare where (compare_status != 'Y' and compare_status != 'N') or compare_status is NULL
    </select>

    <update id="updateStatus">
        update fccm.tech_compare
        set compare_status = case
            when step_id = f_step_id and equip_group_id = f_equip_group_id and cost_center_id = f_cost_center_id
                and unit1 = f_unit1 and qty1 = f_qty1 and unit2 = f_unit2 and qty2 = f_qty2 then 'Y'
        else 'N' end where 1=1
    </update>

    <select id="query" resultType="com.datalink.fdop.engine.api.domain.TechCompare">
        select a.* from fccm.tech_compare a
        where a.factory_id = '${factoryId}'
        <if test="productId != null and productId != ''">
            and a.product_id = '${productId}'
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            and a.change_date between date('${startTime}') and date('${endTime}')
        </if>
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        order by change_date ${sort}
    </select>

</mapper>
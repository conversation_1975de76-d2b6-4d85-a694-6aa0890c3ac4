<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.AllocationMethodMapper">

    <resultMap id="AllocationMethodResultMap" type="com.datalink.fdop.base.api.domain.AllocationMethodVo">
        <association property="allocationMethod" javaType="com.datalink.fdop.base.api.domain.AllocationMethod">
            <result property="controlAreaId" column="control_area_id"/>
            <result property="controlAreaDesc" column="control_area_desc"/>
            <result property="allocationMethodId" column="allocation_method_id"/>
            <result property="dateFrom" column="date_from"/>
            <result property="dateTo" column="date_to"/>
            <result property="allocationMethodDesc" column="allocation_method_desc"/>
            <result property="allocationMethodText" column="allocation_method_text"/>
            <result property="valueTypeP" column="value_type_p"/>
            <result property="valueTypeA" column="value_type_a"/>
            <result property="verUsed" column="ver_used"/>
            <result property="enable" column="enable"/>
            <result property="createTime" column="create_time"/>
            <result property="createBy" column="create_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="updateBy" column="update_by"/>
        </association>
    </resultMap>

    <delete id="deleteByKey">
        delete from zjdata.ccm_allocation_method
        where (control_area_id, allocation_method_id) in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.allocationMethodId})
        </foreach>
    </delete>

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.AllocationMethod" resultType="int">
        SELECT COUNT(1) FROM zjdata.ccm_allocation_method WHERE control_area_id = #{controlAreaId} AND allocation_method_id = #{allocationMethodId}
    </select>

    <sql id="selectListSql">
        SELECT node.*, controlArea.control_area_desc as control_area_desc
        FROM zjdata.ccm_allocation_method node
        left join zjdata.ccm_control_area controlArea on node.control_area_id = controlArea.control_area_id
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY allocation_method_id desc
    </sql>

    <select id="selectList"  resultType="com.datalink.fdop.base.api.domain.AllocationMethod">
        <include refid="selectListSql"></include>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.AllocationMethod">
        <include refid="selectListSql"></include>
    </select>


    <select id="selectListByControlAreaIdAndIds" resultMap="AllocationMethodResultMap">
        SELECT node.*, controlArea.control_area_desc as control_area_desc
        FROM zjdata.ccm_allocation_method node
        left join zjdata.ccm_control_area controlArea on node.control_area_id = controlArea.control_area_id
        where node.control_area_id = #{controlAreaId} AND node.allocation_method_id IN
        <foreach item="item" collection="allocationMethodIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAllocationDivisionVoListByAllocationMethod" resultType="com.datalink.fdop.base.api.domain.AllocationDivisionVo">
        SELECT
            division.control_area_id,
            division.keyid,
            division.allocation_segment_id,
            division.allocation_segment_desc,
            division.allocation_segment_text,
            division.allocation_method_id,
            segments.send_factor,
            segments.send_scale,
            segments.accept_factor,
            segments.accept_scale,
            segments.flowing_element,
            segments.send_cost_center_from,
            segments.send_cost_center_to,
            segments.send_cost_center_group,
            segments.send_cost_element_from,
            segments.send_cost_element_to,
            segments.send_cost_element_group,
            segments.accept_cost_center_from,
            segments.accept_cost_center_to,
            segments.accept_cost_center_group
        FROM
            zjdata.ccm_allocation_division division
                LEFT JOIN zjdata.ccm_allocation_segments segments ON
                        division.control_area_id = segments.control_area_id
                    AND division.keyid = segments.keyid
        WHERE division."enable" = true
        AND division.control_area_id = #{controlAreaId} AND division.allocation_method_id = #{allocationMethodId}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.AllocationMethod">
        SELECT node.*, controlArea.control_area_desc as control_area_desc
        FROM zjdata.ccm_allocation_method node
                 left join zjdata.ccm_control_area controlArea on node.control_area_id = controlArea.control_area_id
        where node.control_area_id = #{controlAreaId} AND node.allocation_method_id = #{allocationMethodId}
    </select>

<!--    <select id="selectIdsByPid" resultType="String">-->
<!--        select allocation_method_id from zjdata.ccm_allocation_method-->
<!--        where menu_id = #{pid} and del_flag = false-->
<!--    </select>-->

<!--    <update id="bacthUpdatePidById">-->
<!--        update zjdata.ccm_allocation_method-->
<!--        set menu_id = (select p_menu_id from zjdata.ccm_allocation_method_menu where menu_id = #{menuId} limit 1)-->
<!--        where menu_id = #{menuId} and del_flag = false-->
<!--    </update>-->

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.AllocationMethod">
        update zjdata.ccm_allocation_method
        set
            <if test="dateFrom != null">
                date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}',
            </if>
            <if test="dateTo != null">
                date_to = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateTo)}',
            </if>
            <if test="allocationMethodDesc != null">allocation_method_desc = #{allocationMethodDesc},</if>
            <if test="allocationMethodText != null">allocation_method_text = #{allocationMethodText},</if>
            <if test="valueTypeP != null">value_type_p = #{valueTypeP},</if>
            <if test="valueTypeA != null">value_type_a = #{valueTypeA},</if>
            <if test="verUsed != null">ver_used = #{verUsed},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId} and allocation_method_id = #{allocationMethodId}
    </update>

    <select id="selectControlAreaId" resultType="String" parameterType="String">
        select control_area_id from zjdata.ccm_control_area where control_area_desc = #{id}
    </select>

    <select id="getAll" resultType="com.datalink.fdop.base.api.domain.AllocationMethod">
        SELECT a.*, controlArea.control_area_desc as control_area_desc
        FROM zjdata.ccm_allocation_method a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        ORDER BY control_area_id, allocation_method_id desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.AllocationMethod">
        SELECT a.*, controlArea.control_area_desc as control_area_desc
        FROM zjdata.ccm_allocation_method a
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
        </where>
        ORDER BY control_area_id, allocation_method_id ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_allocation_method
        SET enable = #{enable}
        where (control_area_id, allocation_method_id) in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.allocationMethodId})
        </foreach>
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.AllocationMethodTree">
        select
            allocation_method_id as id,
            control_area_id as control_area_id,
            '-1' as pid,
            'MENU' as menuType,
            allocation_method_id as name,
            allocation_method_text as description
        from
            zjdata.ccm_allocation_method
        <where>
            <if test="controlAreaId != null and controlAreaId != ''">
                and control_area_id = #{controlAreaId}
            </if>
            <if test="code != null and code != ''">
                and allocation_method_id = #{code}
            </if>
        </where>
        order by allocation_method_id ${sort}
    </select>

    <select id="getAllocationMethod" resultType="string">
        select allocation_method_id from zjdata.ccm_allocation_method  where control_area_id = #{controlAreaId} group by allocation_method_id
    </select>

</mapper>


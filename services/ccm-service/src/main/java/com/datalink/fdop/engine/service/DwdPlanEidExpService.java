package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdPlanEidExp;

import java.util.List;

public interface DwdPlanEidExpService extends IService<DwdPlanEidExp> {

    PageDataInfo<DwdPlanEidExp> overview(
            String verId, String plantId, String dateFrom, String dateTo,
            String sort, SearchVo searchVo);

    List<DwdPlanEidExp> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo);

}
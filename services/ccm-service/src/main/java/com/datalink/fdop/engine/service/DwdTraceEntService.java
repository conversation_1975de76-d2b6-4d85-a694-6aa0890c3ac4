package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdTraceEnt;

import java.util.List;

public interface DwdTraceEntService extends IService<DwdTraceEnt> {

    PageDataInfo<DwdTraceEnt> overview(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo);

    List<DwdTraceEnt> selectNoPage(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo);
}
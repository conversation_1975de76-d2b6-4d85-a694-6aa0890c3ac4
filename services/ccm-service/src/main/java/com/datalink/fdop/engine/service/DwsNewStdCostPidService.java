package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostPid;

import java.util.List;

public interface DwsNewStdCostPidService extends IService<DwsNewStdCostPid> {

    PageDataInfo<DwsNewStdCostPid> overview(
            String controlAreaId, String productId, Integer batchQty,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo);

    List<DwsNewStdCostPid> selectNoPage(String controlAreaId,
                                        String productId,
                                        Integer batchQty,
                                        String verId,
                                        String plantId,
                                        String date,
                                        String sort,
                                        SearchVo searchVo);
}
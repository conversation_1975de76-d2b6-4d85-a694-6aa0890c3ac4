package com.datalink.fdop.govern.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkTree;
import com.datalink.fdop.govern.api.domain.DataTag;
import com.datalink.fdop.govern.api.domain.DataTagExport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataTagMapper {

    int insert(@Param("tag") DataTag dataTag);

    /**
     * 补偿新增方法（图库一次性无法新增太多字段，如果字段太多则用这个方法拆分）
     * @param dataTag
     * @return
     */
    int updateInsertById(DataTag dataTag);

    int updateById(DataTag dataTag);

    VlabelItem<DataTag> selectById(Long id);

    VlabelItem<DataTag> selectByCode(@Param("code") String code);

    VlabelItem<DataTag> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    int batchRegister(@Param("ids") List<Long> ids);

    int batchUnRegister(@Param("ids") List<Long> ids);

    IPage<VlabelItem<DataTag>> selectList(IPage<VlabelItem> page, @Param("tag") DataTag dataTag);

    List<DataProcessFrameworkTree> selectTree(@Param("sort") String sort, @Param("code") String code, @Param("name") String name);

    List<SelectVo> selectListAll(@Param("tag") DataTag dataTag);

    IPage<DataTag> overview(@Param("pid") Long pid, @Param("page") Page<DataTag> page, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    List<DataTagExport> getExportList(@Param("pid") Long pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

}

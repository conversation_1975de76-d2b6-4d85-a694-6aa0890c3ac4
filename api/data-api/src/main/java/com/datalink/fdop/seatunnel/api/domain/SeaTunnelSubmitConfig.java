package com.datalink.fdop.seatunnel.api.domain;

import com.datalink.fdop.seatunnel.api.restapi.domain.Environment;
import lombok.Data;

@Data
public class SeaTunnelSubmitConfig {

    /**
     * SeaTunnel 执行环境定义
     */
    private Environment env;

    /**
     * 源 数据模型(数据源/实体)
     */
    private DataModelOptions sourceOptions;

    /**
     * 目标 数据模型(数据源/实体)
     */
    private DataModelOptions sinkOptions;

    public SeaTunnelSubmitConfig() {
    }

    public SeaTunnelSubmitConfig(Environment env, DataModelOptions sourceOptions, DataModelOptions sinkOptions) {
        this.env = env;
        this.sourceOptions = sourceOptions;
        this.sinkOptions = sinkOptions;
    }
}

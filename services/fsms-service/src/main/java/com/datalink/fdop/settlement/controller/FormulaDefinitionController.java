package com.datalink.fdop.settlement.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.PieceConfig;
import com.datalink.fdop.settlement.api.domain.FormulaAllocation;
import com.datalink.fdop.settlement.api.domain.FormulaDefinition;
import com.datalink.fdop.settlement.api.model.vo.FormulaAllocationVo;
import com.datalink.fdop.settlement.api.model.vo.FormulaDefinitionVo;
import com.datalink.fdop.settlement.service.FormulaDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/settlement/formula/definition")
@Transactional
@Api(tags = "公式定义")
public class FormulaDefinitionController {

    @Autowired
    private FormulaDefinitionService formulaDefinitionService;

    @ApiOperation(value = "查询")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) FormulaDefinition formulaDefinition) {
        Page<FormulaDefinition> page = PageUtils.getPage(FormulaDefinition.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        SearchVo searchVo = formulaDefinition.getSearchVo();
        if (searchVo != null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<FormulaDefinition> pageData = formulaDefinitionService.page(page);
        List<FormulaDefinition> records = pageData.getRecords();
        if (searchVo != null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
            page.setTotal(records.size());
            records = records.stream().skip(pageSize * (current - 1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }


    @ApiOperation(value = "新增")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/create")
    public R create(@RequestBody List<FormulaDefinition> formulaDefinitions) {
        if (formulaDefinitionService.saveBatch(formulaDefinitions)) {
            return R.ok("保存成功");
        } else {
            return R.fail("保存失败");
        }
    }

    @ApiOperation(value = "修改")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody List<FormulaDefinition> formulaDefinitions) {
        for (FormulaDefinition f : formulaDefinitions) {
            if (!formulaDefinitionService.update(new UpdateWrapper<FormulaDefinition>().lambda()
                    .set(FormulaDefinition::getFormula, f.getFormula())
                    .set(FormulaDefinition::getProcess, f.getProcess())
                    .set(FormulaDefinition::getFormulaDesc, f.getFormulaDesc())
                    .eq(FormulaDefinition::getFormula, f.getOldFormula())
                    .eq(FormulaDefinition::getProcess, f.getOldProcess())
            )) {
                throw new ServiceException("修改失败");
            }
        }
        return R.ok();
    }

    @ApiOperation(value = "删除")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<FormulaDefinition> formulaDefinitions) {
        for (FormulaDefinition f : formulaDefinitions) {
            formulaDefinitionService.remove(new QueryWrapper<FormulaDefinition>().lambda()
                    .eq(FormulaDefinition::getFormula, f.getFormula())
                    .eq(FormulaDefinition::getProcess, f.getProcess())
            );
        }

        return R.ok();

    }

    @ApiOperation(value = "导出")
    @Log(title = "settlement", businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody(required = false) FormulaDefinition formulaDefinition) throws IOException {
        Page<FormulaDefinition> page = PageUtils.getPage(FormulaDefinition.class);
        SearchVo searchVo = formulaDefinition.getSearchVo();
        if (searchVo != null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<FormulaDefinition> pageData = formulaDefinitionService.page(page);
        List<FormulaDefinition> records = pageData.getRecords();
        if (searchVo != null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
        }
        ExcelUtils.export3Excel(response, records, FormulaDefinition.class, "公式定义");
    }


}

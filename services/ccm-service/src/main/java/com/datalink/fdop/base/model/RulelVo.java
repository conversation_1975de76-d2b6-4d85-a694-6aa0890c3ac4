package com.datalink.fdop.base.model;

import com.datalink.fdop.base.api.domain.AllocationSegments;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-09 10:16
 */
@Data
public class RulelVo {

    private Long id;

    private Long pid;

    private String code;

    private String abbreviation;

    private String description;

    private String version;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endTime;

    private Long bwkeyId;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;

    private Boolean enable;

    private Boolean delFlag;

    private List<AllocationSegments> children = new ArrayList<>();

}

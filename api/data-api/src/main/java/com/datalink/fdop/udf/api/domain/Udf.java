package com.datalink.fdop.udf.api.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.datalink.fdop.udf.api.enums.UdfType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/10/11 10:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "zjdata", value = "u_c_udf")
public class Udf extends BaseField {

    @ApiModelProperty(value = "udf id", required = true)
    @TableId(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @NotNull(message = "请选择一个菜单")
    @ApiModelProperty(value = "父节点id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    @NotNull(message = "udf脚本类型")
    @ApiModelProperty(value = "脚本类型(PYTHON,JAVA)", required = true)
    private UdfType udfType;

    @ApiModelProperty(value = "脚本输入参数", required = true)
    private String scriptInputParam;

    @ApiModelProperty(value = "资源id(java)", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long resourceId;

    @ApiModelProperty(value = "脚本逻辑(python)", required = true)
    private String scriptLogic;

    @ApiModelProperty(value = "脚本输出参数", required = true)
    private String scriptOutputParam;

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CostStructureMenuMapper">

    <update id="bacthUpdatePidById" parameterType="String">
        update zjdata.ccm_cost_structure_menu
        set p_menu_id = (select p_menu_id from zjdata.ccm_cost_structure_menu where menu_id = #{menuId} limit 1)
        where p_menu_id = #{menuId} and del_flag = false
    </update>

    <update id="deleteByIds" parameterType="String">
        update zjdata.ccm_cost_structure_menu
        set del_flag = true
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag = false
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.CostStructureTree">
        select null as control_area_id, null as cost_structure_id, a.*, 'MENU' as menu_type, null as date_from from zjdata.ccm_cost_structure_menu a
        where a.del_flag != true
        UNION
        select b.control_area_id as control_area_id, b.cost_structure_id as cost_structure_id, null as menu_id, b.menu_id as p_menu_id, null as menu_desc, null as menu_text,
               b.del_flag as del_flag, 'NODE' as menu_type, b.date_from as date_from from zjdata.ccm_cost_structure_head b
        where b.del_flag != true
        order by menu_id ${sort}, control_area_id ${sort}, cost_structure_id ${sort}, date_from ${sort}
    </select>

    <select id="selectByPids" resultType="String">
        select * from zjdata.ccm_cost_structure_head
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag != true
        order by control_area_id, cost_structure_id, date_from desc
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.CostStructureMenu">
        select * from zjdata.ccm_cost_structure_menu
        where menu_id = #{id} and del_flag != true limit 1
    </select>

</mapper>
package com.datalink.fdop.datart.api.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/11/24 11:33
 */
@Data
@TableName(schema = "zjdata", value = "f_d_dashboard_menu")
public class DashboardMenu extends BaseField {

    @ApiModelProperty(value = "仪表盘菜单id", required = true)
    @TableId(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @NotNull(message = "请选择一个菜单")
    @ApiModelProperty(value = "父节点id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

}

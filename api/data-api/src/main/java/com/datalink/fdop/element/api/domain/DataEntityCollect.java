package com.datalink.fdop.element.api.domain;


import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import lombok.Data;

import java.util.List;

/**
 * 实体汇总结构
 */
@Data
public class DataEntityCollect {

    /**
     * 实体信息
     */
    private DataEntity dataEntity;

    /**
     * 实体列结构信息
     */
    private List<DataEntityStructureVo> dataEntityStructureList;

    /**
     * 实体表信息(表名，是否只读，数据库表与实体列的映射等)
     */
    private DataEntityTable dataEntityTable;


}

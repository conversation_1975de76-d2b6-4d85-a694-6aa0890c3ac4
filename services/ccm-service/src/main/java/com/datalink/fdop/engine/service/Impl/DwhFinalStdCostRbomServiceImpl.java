package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostRbom;
import com.datalink.fdop.engine.mapper.DwhFinalStdCostRbomMapper;
import com.datalink.fdop.engine.service.DwhFinalStdCostRbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalStdCostRbomServiceImpl extends ServiceImpl<DwhFinalStdCostRbomMapper, DwhFinalStdCostRbom> implements DwhFinalStdCostRbomService {

    @Autowired
    private DwhFinalStdCostRbomMapper dwhFinalStdCostRbomMapper;

    @Override
    public PageDataInfo<DwhFinalStdCostRbom> overview(
            String controlAreaId, String productId,
            String verId, String plantId,String date,
            String sort, SearchVo searchVo) {
        Page<DwhFinalStdCostRbom> page = PageUtils.getPage(DwhFinalStdCostRbom.class);
        IPage<DwhFinalStdCostRbom> iPage = dwhFinalStdCostRbomMapper.selectAll(controlAreaId, productId, verId, plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalStdCostRbom> selectNoPage(String plantId, String verId, String controlAreaId, String productId, String date, String sort, SearchVo searchVo) {
        return dwhFinalStdCostRbomMapper.selectNoPage(plantId, verId, controlAreaId, productId, date, sort, searchVo);
    }


}

package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalSetOrd;
import com.datalink.fdop.engine.mapper.DwhFinalSetOrdMapper;
import com.datalink.fdop.engine.service.DwhFinalSetOrdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalSetOrdServiceImpl extends ServiceImpl<DwhFinalSetOrdMapper, DwhFinalSetOrd> implements DwhFinalSetOrdService {

    @Autowired
    private DwhFinalSetOrdMapper dwhFinalSetOrdMapper;

    @Override
    public PageDataInfo<DwhFinalSetOrd> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwhFinalSetOrd> page = PageUtils.getPage(DwhFinalSetOrd.class);
        IPage<DwhFinalSetOrd> iPage = dwhFinalSetOrdMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalSetOrd> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwhFinalSetOrdMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}
package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRevalueMat;
import com.datalink.fdop.engine.mapper.DwsNewRevalueMatMapper;
import com.datalink.fdop.engine.service.DwsNewRevalueMatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRevalueMatServiceImpl extends ServiceImpl<DwsNewRevalueMatMapper, DwsNewRevalueMat> implements DwsNewRevalueMatService {

    @Autowired
    private DwsNewRevalueMatMapper dwsNewRevalueMatMapper;

    @Override
    public PageDataInfo<DwsNewRevalueMat> overview(String yearMonth, String verId, String controlAreaId, String companyId, String sort, SearchVo searchVo) {
        Page<DwsNewRevalueMat> page = PageUtils.getPage(DwsNewRevalueMat.class);
        IPage<DwsNewRevalueMat> iPage = dwsNewRevalueMatMapper.selectAll(yearMonth, verId, controlAreaId, companyId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRevalueMat> selectNoPage(String yearMonth, String verId, String controlAreaId, String companyId, String sort, SearchVo searchVo) {
        return dwsNewRevalueMatMapper.selectNoPage(yearMonth, verId, controlAreaId, companyId, sort, searchVo);
    }
}
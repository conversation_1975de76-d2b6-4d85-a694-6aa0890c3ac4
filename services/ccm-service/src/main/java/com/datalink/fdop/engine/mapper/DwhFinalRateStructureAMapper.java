package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwhFinalRateStructureA;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwhFinalRateStructureAMapper extends BaseMapper<DwhFinalRateStructureA> {

    IPage<DwhFinalRateStructureA> selectAll(
            @Param("controlAreaId") String controlAreaId,
            @Param("verId") String verId,
            @Param("yearMonthFrom") String yearMonthFrom,
            @Param("yearMonthTo") String yearMonthTo,
            @Param("page") IPage<DwhFinalRateStructureA> page,
            @Param("costCenterType") String costCenterType,
            @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwhFinalRateStructureA> selectNoPage(@Param("controlAreaId") String controlAreaId,
                                              @Param("verId") String verId,
                                              @Param("yearMonthFrom") String yearMonthFrom,
                                              @Param("yearMonthTo") String yearMonthTo,
                                              @Param("costCenterType") String costCenterType,
                                              @Param("sort") String sort,
                                              @Param("searchVo") SearchVo searchVo);
}
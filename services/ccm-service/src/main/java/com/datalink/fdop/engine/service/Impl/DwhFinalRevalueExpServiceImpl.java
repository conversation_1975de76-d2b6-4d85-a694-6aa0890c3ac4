package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRevalueExp;
import com.datalink.fdop.engine.mapper.DwhFinalRevalueExpMapper;
import com.datalink.fdop.engine.service.DwhFinalRevalueExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRevalueExpServiceImpl extends ServiceImpl<DwhFinalRevalueExpMapper, DwhFinalRevalueExp> implements DwhFinalRevalueExpService {

    @Autowired
    private DwhFinalRevalueExpMapper dwhFinalRevalueExpMapper;

    @Override
    public PageDataInfo<DwhFinalRevalueExp> overview(
            String yearMonth, 
            String verId, 
            String controlAreaId, 
            String companyId, 
            String sort, 
            SearchVo searchVo) {
        Page<DwhFinalRevalueExp> page = PageUtils.getPage(DwhFinalRevalueExp.class);
        IPage<DwhFinalRevalueExp> iPage = dwhFinalRevalueExpMapper.selectAll(yearMonth, verId, controlAreaId, companyId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRevalueExp> selectNoPage(String yearMonth, String verId, String controlAreaId, String companyId, String sort, SearchVo searchVo) {
        return dwhFinalRevalueExpMapper.selectNoPage(yearMonth, verId, controlAreaId, companyId, sort, searchVo);
    }
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpFlow;

import java.util.List;

public interface DwhTerpFlowService extends IService<DwhTerpFlow> {

    PageDataInfo<DwhTerpFlow> overview(String verId,String  plantId,String productId,String sort, SearchVo searchVo);

    List<DwhTerpFlow> selectNoPage(String verId,
                                   String plantId,
                                   String productId,
                                   SearchVo searchVo);
}

package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostPbom;
import com.datalink.fdop.engine.mapper.DwhFinalStdCostPbomMapper;
import com.datalink.fdop.engine.service.DwhFinalStdCostPbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalStdCostPbomServiceImpl extends ServiceImpl<DwhFinalStdCostPbomMapper, DwhFinalStdCostPbom> implements DwhFinalStdCostPbomService {

    @Autowired
    private DwhFinalStdCostPbomMapper dwhFinalStdCostPbomMapper;

    @Override
    public PageDataInfo<DwhFinalStdCostPbom> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwhFinalStdCostPbom> page = PageUtils.getPage(DwhFinalStdCostPbom.class);
        IPage<DwhFinalStdCostPbom> iPage = dwhFinalStdCostPbomMapper.selectAll(controlAreaId, productId,verId, plantId,date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalStdCostPbom> listAll() {
        return dwhFinalStdCostPbomMapper.listAll();
    }

    @Override
    public List<DwhFinalStdCostPbom> selectNoPage(String plantId, String verId, String controlAreaId, String productId, String date, String sort, SearchVo searchVo) {
        return dwhFinalStdCostPbomMapper.selectNoPage(plantId, verId, controlAreaId, productId, date, sort, searchVo);
    }


}

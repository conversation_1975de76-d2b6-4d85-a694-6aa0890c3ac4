package com.datalink.fdop.element.api.model.vo;

import com.datalink.fdop.common.core.domain.TreeVo;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/18 10:48
 */
@Data
@AllArgsConstructor
@ApiModel("数据元素菜单、元素、表结构的嵌套结构")
public class DataElementStructureVo extends TreeVo<DataElementStructureVo> {

    @ApiModelProperty(value = "数据元素类型(MAIN:主数据,FIELD:字段,INPUT:录入)")
    private DataElementType dataElementType;

    @ApiModelProperty(value = "是否是树的边")
    private Boolean isTree;

    @ApiModelProperty(value = "父级数据元素id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentDataElementId;

    @ApiModelProperty(value = "字段类型")
    private FieldType fieldType;

    @ApiModelProperty(value = "长度")
    private Long length;

    @ApiModelProperty(value = "精度")
    private Long decimalLength;

    @ApiModelProperty(value = "是否主键")
    private Boolean isPk;

    @ApiModelProperty(value = "映射字段名")
    private String mapFieldName;

    @ApiModelProperty(value = "排序")
    private Integer seq;

    // @ApiModelProperty(value = "是否是嵌套的主数据类型字段")
    // private Boolean isNested;

    public DataElementStructureVo() {
    }

    public DataElementStructureVo(DataEntityStructureVo dataEntityStructureVo) {
        super(dataEntityStructureVo.getId(),
                dataEntityStructureVo.getCode(),
                dataEntityStructureVo.getName(),
                dataEntityStructureVo.getDescription());
        this.dataElementType = dataEntityStructureVo.getDataElementType();
        this.fieldType = dataEntityStructureVo.getFieldType();
        this.length = dataEntityStructureVo.getLength();
        this.decimalLength = dataEntityStructureVo.getDecimalLength();
        this.isPk = dataEntityStructureVo.getIsPk();
        this.seq = dataEntityStructureVo.getSeq();
    }

}

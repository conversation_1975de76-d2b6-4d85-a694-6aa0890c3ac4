package com.datalink.fdop.drive.service;


import com.datalink.fdop.drive.api.domain.dto.Field;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/4 20:02
 */
public interface ITableService {

    void truncate(Long id, String databaseName, String tableName, String condition);

    void addField(Long id, String databaseName, String tableName, List<Field> fields);

    void batchInsert(Long id, String databaseName, String tableName, List<Map<String, Object>> datas);

}


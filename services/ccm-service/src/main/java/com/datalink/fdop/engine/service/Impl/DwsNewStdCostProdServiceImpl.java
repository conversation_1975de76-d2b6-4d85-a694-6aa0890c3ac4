package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostProd;
import com.datalink.fdop.engine.mapper.DwsNewStdCostProdMapper;
import com.datalink.fdop.engine.service.DwsNewStdCostProdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewStdCostProdServiceImpl extends ServiceImpl<DwsNewStdCostProdMapper, DwsNewStdCostProd> implements DwsNewStdCostProdService {

    @Autowired
    private DwsNewStdCostProdMapper dwsNewStdCostProdMapper;

    @Override
    public PageDataInfo<DwsNewStdCostProd> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwsNewStdCostProd> page = PageUtils.getPage(DwsNewStdCostProd.class);
        IPage<DwsNewStdCostProd> iPage = dwsNewStdCostProdMapper.selectAll(controlAreaId, productId, verId, plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewStdCostProd> listAll() {
        return dwsNewStdCostProdMapper.listAll();
    }

    @Override
    public List<DwsNewStdCostProd> selectNoPage(String controlAreaId, String productId, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwsNewStdCostProdMapper.selectNoPage(controlAreaId, productId, verId, plantId, date, sort, searchVo);
    }
}
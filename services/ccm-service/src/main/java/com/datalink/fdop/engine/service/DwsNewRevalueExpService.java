package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRevalueExp;

import java.util.List;

public interface DwsNewRevalueExpService extends IService<DwsNewRevalueExp> {

    PageDataInfo<DwsNewRevalueExp> overview(String yearMonth, String verId, String controlAreaId, String materialId, String sort, SearchVo searchVo);

    List<DwsNewRevalueExp> selectNoPage(String yearMonth, String verId, String controlAreaId, String companyId, String sort, SearchVo searchVo);
}
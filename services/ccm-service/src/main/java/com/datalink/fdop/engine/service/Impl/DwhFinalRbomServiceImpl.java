package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRbom;
import com.datalink.fdop.engine.mapper.DwhFinalRbomMapper;
import com.datalink.fdop.engine.service.DwhFinalRbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRbomServiceImpl extends ServiceImpl<DwhFinalRbomMapper, DwhFinalRbom> implements DwhFinalRbomService {

    @Autowired
    private DwhFinalRbomMapper dwhFinalRbomMapper;

    @Override
    public PageDataInfo<DwhFinalRbom> overview(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        Page<DwhFinalRbom> page = PageUtils.getPage(DwhFinalRbom.class);
        IPage<DwhFinalRbom> iPage = dwhFinalRbomMapper.selectAll(verId, plantId, productId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRbom> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwhFinalRbomMapper.selectNoPage(verId, plantId, productId, sort, searchVo);
    }
}
package com.datalink.fdop.element.api.domain;

import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.element.api.enums.EntityInsertType;
import com.datalink.fdop.element.api.enums.EntityType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DataEntityRelation {

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "目标子实体id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetDataEntityId;

    @ApiModelProperty(value = "目标子实体编码")
    private String targetDataEntityCode;

    @ApiModelProperty(value = "目标子实体名称")
    private String targetDataEntityName;

    @ApiModelProperty(value = "目标子实体类型")
    private EntityType targetDataEntityType;

    @ApiModelProperty(value = "目标实体字段id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetDataEntityStructureId;

    @ApiModelProperty(value = "目标实体字段编码", required = true)
    private String targetDataEntityStructureCode;

    @ApiModelProperty(value = "目标实体字段名称")
    private String targetDataEntityStructureName;

    @ApiModelProperty(value = "目标实体字段描述")
    private String targetDataEntityStructureDescription;

    @ApiModelProperty(value = "目标实体字段字段类型")
    private FieldType targetDataEntityStructureFieldType;

    @ApiModelProperty(value = "目标实体字段长度")
    private Long targetDataEntityStructureLength;

    @ApiModelProperty(value = "目标实体字段精度")
    private Long targetDataEntityStructureDecimalLength;

    @ApiModelProperty(value = "目标实体字段是否主键")
    private Boolean targetDataEntityStructureIsPk;

    @ApiModelProperty(value = "目标实体新增类型")
    private EntityInsertType targetEntityInsertType;

    @ApiModelProperty(value = "来源实体id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sourceDataEntityMenuId;

    @ApiModelProperty(value = "来源实体编码")
    private String sourceDataEntityMenuCode;

    @ApiModelProperty(value = "来源实体名称")
    private String sourceDataEntityMenuName;

    @ApiModelProperty(value = "来源子实体id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sourceDataEntityId;

    @ApiModelProperty(value = "来源子实体编码")
    private String sourceDataEntityCode;

    @ApiModelProperty(value = "来源子实体名称")
    private String sourceDataEntityName;

    @ApiModelProperty(value = "来源子实体类型")
    private EntityType sourceDataEntityType;

    @ApiModelProperty(value = "来源实体字段id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sourceDataEntityStructureId;

    @ApiModelProperty(value = "来源实体字段编码", required = true)
    private String sourceDataEntityStructureCode;

    @ApiModelProperty(value = "来源实体字段名称")
    private String sourceDataEntityStructureName;

    @ApiModelProperty(value = "来源实体字段描述")
    private String sourceDataEntityStructureDescription;

    @ApiModelProperty(value = "来源实体字段字段类型")
    private FieldType sourceDataEntityStructureFieldType;

    @ApiModelProperty(value = "来源实体字段长度")
    private Long sourceDataEntityStructureLength;

    @ApiModelProperty(value = "来源实体字段精度")
    private Long sourceDataEntityStructureDecimalLength;

    @ApiModelProperty(value = "来源实体字段是否主键")
    private Boolean sourceDataEntityStructureIsPk;

    @ApiModelProperty(value = "来源实体新增类型")
    private EntityInsertType sourceEntityInsertType;

    @ApiModelProperty(value = "排序")
    private Integer seq;
    
}

package com.datalink.fdop.monitor.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.enums.MenuType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.monitor.api.domain.MonitorEngineMenu;
import com.datalink.fdop.monitor.api.model.MonitorEngineTree;
import com.datalink.fdop.monitor.mapper.MonitorEngineMapper;
import com.datalink.fdop.monitor.mapper.MonitorEngineMenuMapper;
import com.datalink.fdop.monitor.service.MonitorEngineMenuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-09-06 11:35
 */
@Service
public class MonitorEngineMenuServiceImpl implements MonitorEngineMenuService {

    @Autowired
    private MonitorEngineMapper monitorEngineMapper;

    @Autowired
    private MonitorEngineMenuMapper monitorEngineMenuMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(MonitorEngineMenu monitorEngineMenu) {
        if (monitorEngineMenuMapper.selectByCode(monitorEngineMenu.getCode()) != null) {
            throw new ServiceException(Status.MONITOR_ENGINE_MENU_EXIST);
        }
        monitorEngineMenu.setId(IdWorker.getId());
        int insert = monitorEngineMenuMapper.insertMonitorEngineMenu(monitorEngineMenu);
        // 创建菜单边关系
        if (insert > 0 && monitorEngineMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            monitorEngineMenuMapper.createMonitorEngineMenuEdge(monitorEngineMenu.getPid(), Arrays.asList(monitorEngineMenu.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(MonitorEngineMenu monitorEngineMenu) {
        VlabelItem<MonitorEngineMenu> vlabelItem = monitorEngineMenuMapper.selectById(monitorEngineMenu.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.MONITOR_ENGINE_MENU_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(monitorEngineMenu.getCode()) && monitorEngineMenuMapper.checkCodeIsExists(monitorEngineMenu.getId(), monitorEngineMenu.getCode()) != null) {
            throw new ServiceException(Status.MONITOR_ENGINE_MENU_EXIST);
        }
        int update = monitorEngineMenuMapper.updateById(monitorEngineMenu);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            monitorEngineMenuMapper.deleteMonitorEngineMenuEdge(Arrays.asList(monitorEngineMenu.getId()), vlabelItem.getProperties().getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (monitorEngineMenu.getPid() != -1L) {
                monitorEngineMenuMapper.createMonitorEngineMenuEdge(monitorEngineMenu.getPid(), Arrays.asList(monitorEngineMenu.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<MonitorEngineMenu> vlabelItem = monitorEngineMenuMapper.selectById(id);
            if (vlabelItem == null) {
                continue;
            }
            MonitorEngineMenu monitorEngineMenu = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = monitorEngineMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = monitorEngineMenuMapper.bacthUpdatePidById(menuIdList, monitorEngineMenu.getPid());
                if (update > 0 && monitorEngineMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    monitorEngineMenuMapper.createMonitorEngineMenuEdge(monitorEngineMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> monitorEngineIdList = monitorEngineMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(monitorEngineIdList)) {
                int update = monitorEngineMapper.bacthUpdatePidById(monitorEngineIdList, monitorEngineMenu.getPid());
                if (update > 0 && monitorEngineMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    monitorEngineMapper.createMonitorEngineAndMenuEdge(monitorEngineMenu.getPid(), monitorEngineIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return monitorEngineMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<Long> getChildrenMenuId(List<Long> ids) {
        List<Long> childrenMenuIds = new ArrayList<>();
        List<Long> menuIds = monitorEngineMenuMapper.selectByPids(ids);
        if (CollectionUtils.isNotEmpty(menuIds)) {
            childrenMenuIds.addAll(getChildrenMenuId(menuIds));
        }
        childrenMenuIds.addAll(ids);
        return childrenMenuIds;
    }


    @Override
    public List<MonitorEngineTree> tree(String sort, String code) {
        // 所有的数据集合
        List<MonitorEngineTree> trees = new ArrayList<>();
        // 添加数据元素树
        trees.addAll(monitorEngineMapper.selectMonitorEngineTree(sort, code));
        // 添加数据元素菜单树
        trees.addAll(monitorEngineMenuMapper.selectMenuTree(sort, code));

        // 一级菜单集合
        List<MonitorEngineTree> menuList = new ArrayList<>();

        // 获取第一级节点
        for (MonitorEngineTree tree : trees) {
            if (tree.getPid().equals(-1L)) {
                menuList.add(tree);
            }
        }
        // 递归获取子节点
        for (MonitorEngineTree parent : menuList) {
            recursiveTree(parent, trees);
        }
        return menuList;

    }

    /**
     * 递归获取子节点
     *
     * @param parent
     * @param list
     * @return
     */
    private MonitorEngineTree recursiveTree(MonitorEngineTree parent, List<MonitorEngineTree> list) {
        for (MonitorEngineTree monitorEngineTree : list) {
            if (parent.getId().equals(monitorEngineTree.getPid())) {
                // 如果是菜单就继续递归查询
                if (monitorEngineTree.getMenuType() == MenuType.MENU) {
                    monitorEngineTree = recursiveTree(monitorEngineTree, list);
                }
                parent.getChildren().add(monitorEngineTree);
            }
        }
        return parent;
    }

    @Override
    public List<MonitorEngineTree> menuTree(String sort, String code) {
        // 所有的数据集合
        List<MonitorEngineTree> trees = new ArrayList<>();

        trees.addAll(monitorEngineMenuMapper.selectMenuTree(sort, code));

        trees = trees.stream().filter(actionTree -> actionTree.getMenuType() == MenuType.MENU).collect(Collectors.toList());

        // 一级菜单集合
        List<MonitorEngineTree> menuList = new ArrayList<>();

        // 获取第一级节点
        for (MonitorEngineTree tree : trees) {
            if (tree.getPid().equals(-1L)) {
                menuList.add(tree);
            }
        }
        // 递归获取子节点
        for (MonitorEngineTree parent : menuList) {
            recursiveTree(parent, trees);
        }
        return menuList;
    }
    
}

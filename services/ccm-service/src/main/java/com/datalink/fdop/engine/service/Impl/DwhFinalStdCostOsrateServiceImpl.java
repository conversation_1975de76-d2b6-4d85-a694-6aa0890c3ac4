package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostOsrate;
import com.datalink.fdop.engine.mapper.DwhFinalStdCostOsrateMapper;
import com.datalink.fdop.engine.service.DwhFinalStdCostOsrateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalStdCostOsrateServiceImpl extends ServiceImpl<DwhFinalStdCostOsrateMapper, DwhFinalStdCostOsrate> implements DwhFinalStdCostOsrateService {

    @Autowired
    private DwhFinalStdCostOsrateMapper dwhFinalStdCostOsrateMapper;

    @Override
    public PageDataInfo<DwhFinalStdCostOsrate> overview(
            String controlAreaId, String verId, String plantId, String productId,
            String date, String sort, SearchVo searchVo) {
        Page<DwhFinalStdCostOsrate> page = PageUtils.getPage(DwhFinalStdCostOsrate.class);
        IPage<DwhFinalStdCostOsrate> iPage = dwhFinalStdCostOsrateMapper.selectAll(controlAreaId, verId, plantId, productId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalStdCostOsrate> selectNoPage(String plantId,
                                                    String verId,
                                                    String controlAreaId,
                                                    String productId,
                                                    String date,
                                                    String sort,
                                                    SearchVo searchVo) {
        return dwhFinalStdCostOsrateMapper.selectNoPage(plantId, verId, controlAreaId, productId, date, sort, searchVo);
    }
}
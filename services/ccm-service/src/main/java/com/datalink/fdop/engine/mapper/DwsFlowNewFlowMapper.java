package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.engine.api.domain.DwsFlowNewFlow;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwsFlowNewFlowMapper extends BaseMapper<DwsFlowNewFlow> {

    IPage<DwsFlowNewFlow> selectAll(
            @Param("verId") String verId,
            @Param("plantId") String plantId, @Param("productId") String productId,
            @Param("page") IPage<DwsFlowNewFlow> page, @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwsFlowNewFlow> selectNoPage(@Param("verId") String verId,
                                      @Param("plantId") String plantId,
                                      @Param("productId") String productId,
                                      @Param("sort") String sort,
                                      @Param("searchVo") SearchVo searchVo);
}

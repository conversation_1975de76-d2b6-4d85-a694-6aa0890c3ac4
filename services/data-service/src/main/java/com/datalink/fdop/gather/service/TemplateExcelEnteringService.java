package com.datalink.fdop.gather.service;

import com.datalink.fdop.gather.api.model.TemplateFieldConfigVo;
import com.datalink.fdop.gather.api.domain.Resource;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/7/6 19:52
 */
public interface TemplateExcelEnteringService {

    void saveData(Long userId, Long templateId, Long logId, Map<Integer, String> headMap, List<Map<Integer, String>> cachedDataList);

    void readFile(Long templateId, Long userId, Long logId, int batchCount, String realPath, TemplateFieldConfigVo templateFieldConfigVo, boolean useSkipError, String batchId, Resource resource);

    void readFile(Long templateId, Long userId, Long logId, int batchCount, String realPath, TemplateFieldConfigVo templateFieldConfigVo, boolean useSkipError, String batchId, Resource resource, Long tenantId);

    void getWriteData(Long templateId, Long userid, HttpServletResponse response, Boolean type) throws IOException;

    void getFiles(Long templateId, Long userId, int batchCount, TemplateFieldConfigVo templateFieldConfigVo, boolean useSkipError, List<Resource> resourceList, List<Long> logIds);

    void getFilesByTenantId(Long templateId, Long userId, int batchCount, TemplateFieldConfigVo templateFieldConfigVo, boolean useSkipError, List<Resource> resourceList, List<Long> logIds, Long tenantId);

}

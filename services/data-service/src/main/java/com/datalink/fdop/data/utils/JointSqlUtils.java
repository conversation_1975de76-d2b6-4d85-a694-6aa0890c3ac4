package com.datalink.fdop.data.utils;


import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.permissions.api.enums.ContainType;
import com.datalink.fdop.permissions.api.enums.OperatorType;
import com.datalink.fdop.permissions.api.model.vo.PermissionsElementVo;

public class JointSqlUtils {

    public static String jointSinglePrimarySql(PermissionsElementVo permissionsElementVo){
        String resultSql=" ";
        try {
            String fieldName = permissionsElementVo.getCode();
            String startValue = permissionsElementVo.getStartValue();
            String endValue = permissionsElementVo.getEndValue();
            ContainType containType = permissionsElementVo.getContainType();
            if (StringUtils.isNotEmpty(startValue) ) {

                OperatorType operatorType = permissionsElementVo.getOperatorType();
                switch (operatorType) {
                    case EQ:
                        if (containType==ContainType.CONTAIN) {
                            resultSql += " n." + fieldName + "= " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        } else {
                            resultSql += " not n." + fieldName + "= " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        }
                        break;
                    case NEQ:
                        if (containType==ContainType.CONTAIN) {
                            resultSql += " n." + fieldName + "<> " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        } else {
                            resultSql += " not n." + fieldName + "<> " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        }
                        break;
                    case GT:
                        if (containType==ContainType.CONTAIN) {
                            resultSql += " n." + fieldName + "> " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        } else {
                            resultSql += " not n." + fieldName + "> " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        }
                        break;
                    case LT:
                        if (containType==ContainType.CONTAIN) {
                            resultSql += " n." + fieldName + "< '" + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue) + "' and";
                        } else {
                            resultSql += " not n." + fieldName + "< '" + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue) + "' and";
                        }
                        break;
                    case LE:
                        if (containType==ContainType.CONTAIN) {
                            resultSql += " n." + fieldName + "<= '" + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(),startValue) + "' and";
                        } else {
                            resultSql += " not n." + fieldName + "<= '" + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue) + "' and";
                        }
                        break;
                    case GE:
                        if (containType==ContainType.CONTAIN) {
                            resultSql += " n." + fieldName + ">= " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        } else {
                            resultSql += " not n." + fieldName + ">= " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(),startValue);
                        }
                        break;
                    case LIKE:
                        if (containType==ContainType.CONTAIN) {
                            resultSql += " n." + fieldName + " Contains " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        } else {
                            resultSql += " not n." + fieldName + " Contains " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        }
                        break;
                    case NOT_LIKE:
                        if (containType==ContainType.CONTAIN) {
                            resultSql += " not n." + fieldName + " Contains " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        } else {
                            resultSql += " n." + fieldName + " Contains " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue);
                        }
                        break;
                    case INTERVAL:
                        if (containType==ContainType.CONTAIN) {
                            resultSql += "  (n." + fieldName + " > " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue) + " and n." + fieldName + " < " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), endValue) + ")";
                        } else {
                            resultSql += " not (n." + fieldName + " > " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), startValue) + " and n." + fieldName + " < " + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), endValue) + ")";
                        }
                        break;
                    case IN:
                        String[] split = startValue.split(",");
                        String inStr = "";
                        int i = 0;
                        for (String str : split) {
                            if (i == 0) {
                                inStr = inStr + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), str);
                            } else {
                                inStr = inStr + "," + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), str);
                            }
                            i++;
                        }
                        if (containType==ContainType.CONTAIN) {
                            resultSql += "  (n." + fieldName + " in (" + inStr + ") and";
                        } else {
                            resultSql += " not (n." + fieldName + " in (" + inStr + ") and";
                        }
                        break;
                    case NOT_IN:
                        String[] split1 = startValue.split(",");
                        String inStr1 = "";
                        int i1 = 0;
                        for (String str : split1) {
                            if (i1 == 0) {
                                inStr1 = inStr1 + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), str);
                            } else {
                                inStr1 = inStr1 + "," + DataTypeChangeUtils.changeField(permissionsElementVo.getFieldType(), str);
                            }
                            i1++;
                        }
                        if (containType==ContainType.CONTAIN) {
                            resultSql += "  (n." + fieldName + " not in (" + inStr1 + ") and";
                        } else {
                            resultSql += " not (n." + fieldName + " not in (" + inStr1 + ") and";
                        }
                        break;
                    /*case "全部":
                        if (dataDetailed.getContainIs()) {
                            resultSql += "  1=1 and";
                        } else {
                            return "";
                        }
                        break;*/
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return resultSql;
    }

}

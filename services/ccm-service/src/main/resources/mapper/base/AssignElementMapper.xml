<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.AssignElementMapper">

    <sql id="selectSql">
        SELECT
            control_area_id AS control_area_id,
            cost_element_id AS cost_element_id,
            flowing_element AS flowing_element,
            enable AS enable,
            create_by AS create_by,
            create_time AS create_time,
            update_by AS update_by,
            update_time AS update_time
        FROM zjdata.ccm_assign_element
    </sql>

    <update id="batchDeleteByKey">
        delete from zjdata.ccm_assign_element
        where (control_area_id, cost_element_id) in
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.costElementId})
        </foreach>
    </update>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM zjdata.ccm_assign_element where control_area_id = #{controlAreaId}  and cost_element_id = #{costElementId}
    </select>

    <select id="checkIdExist" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM zjdata.ccm_assign_element where control_area_id = #{controlAreaId}  and cost_element_id = #{costElementId}
    </select>

    <sql id="selectListSql">
        <include refid="selectSql"/> node
        <where>
            <if test="controlAreaId != null and controlAreaId != '' and controlAreaId != '-1'">
                AND node.control_area_id = #{controlAreaId}
            </if>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY cost_element_id ${sort}
    </sql>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.AssignElement">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.AssignElement">
        <include refid="selectListSql"/>
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.AssignElement">
        <include refid="selectSql"/> node where node.cost_element_id = #{costElementId} AND node.control_area_id =#{controlAreaId}
    </select>

    <select id="selectIdsByPid" resultType="String">
        select cost_element_id from (<include refid="selectSql"/>) a where a.menu_id = #{pid}
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.AssignElement">
        update zjdata.ccm_assign_element
        set
            <if test="flowingElement != null">flowing_element = #{flowingElement},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id =#{controlAreaId} and cost_element_id = #{costElementId}
    </update>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_assign_element
        SET enable = #{enable}
        where (control_area_id, cost_element_id) in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.costElementId})
        </foreach>
    </update>

    <select id="selectListByControlAreaIdAndEnable" resultType="com.datalink.fdop.base.api.domain.AssignElement">
        <include refid="selectSql"/> node
        where node.enable = true and node.control_area_id = #{controlAreaId}
    </select>

</mapper>
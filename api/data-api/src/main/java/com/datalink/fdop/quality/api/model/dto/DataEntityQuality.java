package com.datalink.fdop.quality.api.model.dto;

import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/12 9:54
 */
@Data
public class DataEntityQuality {

    @ApiModelProperty(value = "数据元素字段id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "编码", required = true)
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "数据元素类型(MAIN:主数据,FIELD:字段,INPUT:录入)")
    private DataElementType dataElementType;

    @ApiModelProperty(value = "字段类型")
    private FieldType fieldType;

    @ApiModelProperty(value = "字段类型")
    private List<DataQualityDto> dataQualityDtoList = new ArrayList<>();

    public DataEntityQuality() {
    }

    public DataEntityQuality(Long id, String code, String name, String description, DataElementType dataElementType, FieldType fieldType) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.description = description;
        this.dataElementType = dataElementType;
        this.fieldType = fieldType;
    }

    @JsonIgnore
    public String getAgeStr() {
        return "{id:" + this.getId() +
                ", code:'" + this.getCode() + "'" +
                (StringUtils.isNotEmpty(this.getName()) ? ", name: '" + this.getName() + "'" : "") +
                (StringUtils.isNotEmpty(this.getDescription()) ? ", description: '" + this.getDescription() + "'" : "") +
                ", dataElementType:'" + this.getDataElementType() + "'" +
                ", fieldType:'" + this.getFieldType() + "'" +
                (CollectionUtils.isNotEmpty(this.getDataQualityDtoList()) ? ", dataQualityDtoList:"
                        + this.getDataQualityDtoList().stream()
                        .map(dataQualityDto -> dataQualityDto.getAgeStr())
                        .collect(Collectors.joining(",", "[", "]")) : "") +
                "}";
    }

}

package com.datalink.fdop.auth.mapper;

import com.datalink.fdop.auth.api.domain.AuthAction;
import com.datalink.fdop.auth.api.domain.FunctionObject;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.auth.api.domain.MenuDetailed;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/10 9:10
 */
@Repository
@Transactional
@Mapper
public interface FunctionObjectRelationMapper {

    int saveFunctionObject(@Param("ageStr") String ageStr);

    int saveRelation(@Param("id")Long id, @Param("objId")Long objId);

    int bindingMenuRelation(@Param("id")Long id, @Param("tableName")String tableName, @Param("menuIds") List<Long> menuIds);

    int bindingNodeRelation(@Param("id")Long id, @Param("tableName")String tableName, @Param("nodeIds")List<Long> nodeIds);

    int saveDetailed(@Param("ageStr") String ageStr);

    int saveMenuDetailedRelation(@Param("fObjectId")Long fObjectId,@Param("id") Long id);

    int saveNodeDetailedRelation(@Param("fObjectId")Long fObjectId,@Param("id") Long id);

    int delFunctionDetailedEdge(@Param("fObjId")Long fObjId);

    int delFunctionMenuEdge(@Param("fObjId")Long fObjId);

    int delFunctionObjectEdge(@Param("fObjId")Long fObjId,@Param("objId") Long objId);

    List<VlabelItem<MenuDetailed>> findMenuDetailed(@Param("objId")Long objId, @Param("funcCode")String funcCode);

    int delActionRelation(@Param("fObjectId")Long fObjectId);

    int saveAction(@Param("ageStr") String ageStr);

    int saveActionRelation(@Param("fObjectId")Long fObjectId, @Param("id")Long id);

    List<VlabelItem<MenuDetailed>> findNodeDetailed(@Param("objId")Long objId, @Param("funcCode")String funcCode);

    List<VlabelItem<MenuDetailed>> findNodeDetailedByFuncId(@Param("funcObjId")Long funcObjId, @Param("funcElemCode")String funcElemCode);

    List<VlabelItem<MenuDetailed>> findMenuDetailedByFuncId(@Param("funcObjId")Long funcObjId, @Param("funcElemCode")String funcElemCode);

    List<VlabelItem<FunctionObject>> findFObject(Long objId);

    VlabelItem<FunctionObject> findFObjectBycodeAndObjId(@Param("objId")Long objId,@Param("type") String type);


    int delFunctionMenuDetailedEdge(Long fObjectId);

    int delFunctionNodeDetailedEdge(Long fObjectId);

    List<String> getgMenu(@Param("fObjectId") Long fObjectId, @Param("tableName")String tableName);

    List<String> getNode(@Param("fObjectId") Long fObjectId, @Param("tableName")String tableName);

    AuthAction getActionByFObjectId(Long id);

    int delMenuRelation(@Param("fObjectId")Long fObjectId, @Param("tableName")String tableName);

    int delNodeRelation(@Param("fObjectId")Long fObjectId, @Param("tableName")String tableName);

    int delMenuDetailed(@Param("fObjectId")Long fObjectId,@Param("detailedIds") List<Long> detailedIds);

    int delNodeDetailed(@Param("fObjectId")Long fObjectId, @Param("detailedIds")List<Long> detailedIds);

    int updataMenuDetailed(@Param("detailed")MenuDetailed detailed);

    int findMenuDetailedByFuncIdCount(@Param("funcObjId")Long funcObjId, @Param("funcElemCode")String funcElemCode);

    int findNodeDetailedByFuncIdCount(@Param("funcObjId")Long funcObjId, @Param("funcElemCode")String funcElemCode);

    int delNodeRelationByPid(@Param("funcObjId")Long fObjectId, @Param("tableName")String tableName, @Param("pids")List<Long> pids);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.BomItemMapper">

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.BomItem" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_bom_item
        WHERE recipe_id = #{recipeId} AND recipe_ver = #{recipeVer}
          AND material_id = #{materialId} AND control_area_id = #{controlAreaId}
          AND del_flag = false limit 1
    </select>

    <update id="deleteByIds" parameterType="Long">
        update zjdata.ccm_bom_item
        SET del_flag = true
        WHERE
        <foreach collection="bomItemList" item="bomItem" open="(" separator=" or " close=")">
            material_id = #{bomItem.materialId} and recipe_id = #{bomItem.recipeId}
          and recipe_ver = #{bomItem.recipeVer} and control_area_id = #{bomItem.controlAreaId}
        </foreach>
        and del_flag = false
    </update>

    <update id="updateByKey">
        update zjdata.ccm_bom_item
        set
        <if test="baseUnit != null">base_unit = #{baseUnit},</if>
        <if test="baseQty != null">base_qty = #{baseQty},</if>
        <if test="unitUsage != null">unit_usage = #{unitUsage},</if>
        <if test="qtyUsage != null">qty_usage = #{qtyUsage},</if>
        <if test="unitBasic != null">unit_basic = #{unitBasic},</if>
        <if test="qtyBasic != null">qty_basic = #{qtyBasic},</if>
        <if test="scrapRate != null">scrap_rate = #{scrapRate},</if>
        update_by = #{updateBy},
        update_time = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE del_flag = false AND recipe_id = #{recipeId} AND recipe_ver = #{recipeVer}
          AND material_id = #{materialId} AND control_area_id = #{controlAreaId}
    </update>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.BomItem" resultType="com.datalink.fdop.base.api.domain.BomItem">
        SELECT *
        FROM zjdata.ccm_bom_item
        WHERE del_flag = false
        <if test="bomItem.recipeId != null and bomItem.recipeId != ''">
            AND recipe_id = #{bomItem.recipeId}
        </if>
        <if test="bomItem.recipeVer != null and bomItem.recipeVer != ''">
            AND recipe_ver = #{bomItem.recipeVer}
        </if>
        <if test="bomItem.materialId != null and bomItem.materialId != ''">
            AND material_id = #{bomItem.materialId}
        </if>
        <if test="bomItem.controlAreaId != null and bomItem.controlAreaId != ''">
            AND control_area_id = #{bomItem.controlAreaId}
        </if>
        ORDER BY recipe_id, recipe_ver desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.BomItem">
        SELECT ls.*
        FROM zjdata.ccm_bom_item ls
        WHERE ls.del_flag = false
        <if test="a != null and a != ''">
            AND ${a}
        </if>
        ORDER BY recipe_id, recipe_ver ${sort}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.BomItem" parameterType="com.datalink.fdop.base.api.domain.BomItem">
        SELECT *
        FROM zjdata.ccm_bom_item
        WHERE del_flag = false
            AND recipe_id = #{recipeId}
            AND recipe_ver = #{recipeVer}
            AND material_id = #{materialId}
            AND control_area_id = #{controlAreaId}
        limit 1
    </select>

    <select id="selectItemByHead" resultType="com.datalink.fdop.base.api.model.BomTree" parameterType="com.datalink.fdop.base.api.domain.BomHead">
        select a.*, 'ITEM' as menu_type from zjdata.ccm_bom_item a
        where a.recipe_id = #{recipeId} and a.recipe_ver = #{recipeVer} and a.del_flag = false
    </select>

</mapper>


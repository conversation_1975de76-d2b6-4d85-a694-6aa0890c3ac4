<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.ObjMapper">

    <select id="selectByPid" resultType="com.datalink.fdop.base.model.Obj">
        SELECT ls.*, skf.code as send_kostl_from_code,
               skt.code as send_kostl_to_code,
               spf.code as send_pronr_from_code,
               spt.code as send_pronr_to_code,
               ssf.code as send_saknr_from_code,
               sst.code as send_saknr_to_code,
               akf.code as accept_kostl_from_code,
               akt.code as accept_kostl_to_code,
               apf.code as accept_pronr_from_code,
               apt.code as accept_pronr_to_code,
               asf.code as accept_saknr_from_code,
               ast.code as accept_saknr_to_code
        FROM zjdata.p_d_rule_obj ls
                 left join zjdata.p_d_kostl skf on ls.send_kostl_from_id = skf.id
                 left join zjdata.p_d_kostl skt on ls.send_kostl_to_id = skt.id
                 left join zjdata.p_d_pronr spf on ls.send_pronr_from_id = spf.id
                 left join zjdata.p_d_pronr spt on ls.send_pronr_to_id = spt.id
                 left join zjdata.p_d_saknr ssf on ls.send_saknr_from_id = ssf.id
                 left join zjdata.p_d_saknr sst on ls.send_saknr_to_id = sst.id
                 left join zjdata.p_d_kostl akf on ls.accept_kostl_from_id = akf.id
                 left join zjdata.p_d_kostl akt on ls.accept_kostl_to_id = akt.id
                 left join zjdata.p_d_pronr apf on ls.accept_pronr_from_id = apf.id
                 left join zjdata.p_d_pronr apt on ls.accept_pronr_to_id = apt.id
                 left join zjdata.p_d_saknr asf on ls.accept_saknr_from_id = asf.id
                 left join zjdata.p_d_saknr ast on ls.accept_saknr_to_id = ast.id
        where ls.pid = #{pid} limit 1
    </select>

    <select id="selectKostl" resultType="java.lang.Long">
        select id from zjdata.p_d_kostl where code >= #{startCode} and code &lt;= #{endCode}
    </select>

    <select id="selectKostlByPid" resultType="java.lang.Long">
        select id from zjdata.p_d_kostl where pid = #{pid}
    </select>

    <select id="selectSaknr" resultType="java.lang.Long">
        select id from zjdata.p_d_saknr where code >= #{startCode} and code &lt;= #{endCode}
    </select>

    <select id="selectSaknrByPid" resultType="java.lang.Long">
        select id from zjdata.p_d_saknr where pid = #{pid}
    </select>

</mapper>


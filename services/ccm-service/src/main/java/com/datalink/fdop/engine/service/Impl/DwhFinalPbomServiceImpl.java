package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalPbom;
import com.datalink.fdop.engine.mapper.DwhFinalPbomMapper;
import com.datalink.fdop.engine.service.DwhFinalPbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalPbomServiceImpl extends ServiceImpl<DwhFinalPbomMapper, DwhFinalPbom> implements DwhFinalPbomService {

    @Autowired
    private DwhFinalPbomMapper dwhFinalPbomMapper;

    @Override
    public PageDataInfo<DwhFinalPbom> overview(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        Page<DwhFinalPbom> page = PageUtils.getPage(DwhFinalPbom.class);
        IPage<DwhFinalPbom> iPage = dwhFinalPbomMapper.selectAll(verId, plantId, productId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }


    @Override
    public List<DwhFinalPbom> listAll() {
        return dwhFinalPbomMapper.listAll();
    }

    @Override
    public List<DwhFinalPbom> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwhFinalPbomMapper.selectNoPage(verId, plantId, productId, sort, searchVo);
    }


}

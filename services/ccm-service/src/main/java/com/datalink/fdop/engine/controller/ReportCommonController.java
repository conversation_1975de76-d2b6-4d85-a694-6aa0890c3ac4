package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.engine.service.ReportCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/base/reportCommon")
@Api(tags = "CCMS-报表公共接口")
public class ReportCommonController extends BaseController {

    @Autowired
    private ReportCommonService reportCommonService;

    @ApiOperation(value = "查询成本中心类型")
    @GetMapping("/listCostCenterType")
    public R<List<String>> listCostCenterType(@RequestParam(required = false) String companyId,
                                              @RequestParam(required = false) String yearMonthFrom,
                                              @RequestParam(required = false) String yearMonthTo
    ) {
        return R.ok(reportCommonService.listCostCenterType(companyId, yearMonthFrom, yearMonthTo));
    }

    @ApiOperation(value = "查询成本中心和描述")
    @GetMapping("/listCostCenterId")
    public R<List<SelectVo>> listCostCenterId(@RequestParam(required = false) String companyId,
                                              @RequestParam(required = false) String yearMonthFrom,
                                              @RequestParam(required = false) String yearMonthTo) {
        return R.ok(reportCommonService.listCostCenterId(companyId, yearMonthFrom, yearMonthTo));
    }

    @ApiOperation(value = "查询成本要素和描述")
    @GetMapping("/listCostElementId")
    public R<List<SelectVo>> listCostElementId() {
        return R.ok(reportCommonService.listCostElementId());
    }

    @ApiOperation(value = "查询成本组件和描述")
    @GetMapping("/listCostStructureId")
    public R<List<SelectVo>> listCostStructureId() {
        return R.ok(reportCommonService.listCostStructureId());
    }

    @ApiOperation(value = "查询工作区")
    @GetMapping("/listWorkArea")
    public R<List<String>> listWorkArea() {
        return R.ok(reportCommonService.listWorkArea());
    }

    @ApiOperation(value = "查询设备组和描述")
    @GetMapping("/listEquipGroupId")
    public R<List<SelectVo>> listEquipGroupId() {
        return R.ok(reportCommonService.listEquipGroupId());
    }

    @ApiOperation(value = "查询批次类别")
    @GetMapping("/listLotCategory")
    public R<List<String>> listLotCategory() {
        return R.ok(reportCommonService.listLotCategory());
    }

    @ApiOperation(value = "查询批次类型")
    @GetMapping("/listLotType")
    public R<List<String>> listLotType() {
        return R.ok(reportCommonService.listLotType());
    }

    @ApiOperation(value = "查询母批工单类型和描述")
    @GetMapping("/listBaseOrderType")
    public R<List<SelectVo>> listBaseOrderType() {
        return R.ok(reportCommonService.listBaseOrderType());
    }
}

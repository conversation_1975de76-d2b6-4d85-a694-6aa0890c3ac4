package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationFactorA;

import java.util.List;

public interface DwsNewAllocationFactorAService extends IService<DwsNewAllocationFactorA> {

    PageDataInfo<DwsNewAllocationFactorA> overview(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String allocationType,
            String sort,
            SearchVo searchVo);

    List<DwsNewAllocationFactorA> selectNoPage(String controlAreaId,
                                               String verId, String companyId,
                                               String yearMonthFrom, String yearMonthTo,
                                               String allocationType,
                                               String costCenterType, String sort, SearchVo searchVo);
}
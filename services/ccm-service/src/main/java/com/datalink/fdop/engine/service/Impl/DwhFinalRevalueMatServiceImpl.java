package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRevalueMat;
import com.datalink.fdop.engine.mapper.DwhFinalRevalueMatMapper;
import com.datalink.fdop.engine.service.DwhFinalRevalueMatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRevalueMatServiceImpl extends ServiceImpl<DwhFinalRevalueMatMapper, DwhFinalRevalueMat> implements DwhFinalRevalueMatService {

    @Autowired
    private DwhFinalRevalueMatMapper dwhFinalRevalueMatMapper;

    @Override
    public PageDataInfo<DwhFinalRevalueMat> overview(
            String yearMonth, 
            String verId, 
            String controlAreaId, 
            String companyId, 
            String sort, 
            SearchVo searchVo) {
        Page<DwhFinalRevalueMat> page = PageUtils.getPage(DwhFinalRevalueMat.class);
        IPage<DwhFinalRevalueMat> iPage = dwhFinalRevalueMatMapper.selectAll(yearMonth, verId, controlAreaId, companyId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRevalueMat> selectNoPage(String yearMonth, String verId, String controlAreaId, String companyId, String sort, SearchVo searchVo) {
        return dwhFinalRevalueMatMapper.selectNoPage(yearMonth, verId, controlAreaId, companyId, sort, searchVo);
    }
}
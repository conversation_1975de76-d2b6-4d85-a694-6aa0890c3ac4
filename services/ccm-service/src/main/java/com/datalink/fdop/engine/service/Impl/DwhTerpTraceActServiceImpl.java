package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.engine.api.domain.DwhTerpTraceAct;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.mapper.DwhTerpTraceActMapper;
import com.datalink.fdop.engine.service.DwhTerpTraceActService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhTerpTraceActServiceImpl extends ServiceImpl<DwhTerpTraceActMapper, DwhTerpTraceAct> implements DwhTerpTraceActService {

    @Autowired
    private DwhTerpTraceActMapper dwhTerpTraceActMapper;

    @Override
    public PageDataInfo<DwhTerpTraceAct> overview(String plantId, String dateFrom, String dateTo,String sort, SearchVo searchVo) {
        Page<DwhTerpTraceAct> page = PageUtils.getPage(DwhTerpTraceAct.class);
        IPage<DwhTerpTraceAct> iPage = dwhTerpTraceActMapper.selectAll(plantId, dateFrom, dateTo,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhTerpTraceAct> selectNoPage(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        return dwhTerpTraceActMapper.selectNoPage(plantId, dateFrom, dateTo, sort, searchVo);
    }


}

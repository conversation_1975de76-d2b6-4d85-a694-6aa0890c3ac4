package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostStep;
import com.datalink.fdop.engine.mapper.DwhFinalStdCostStepMapper;
import com.datalink.fdop.engine.service.DwhFinalStdCostStepService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalStdCostStepServiceImpl extends ServiceImpl<DwhFinalStdCostStepMapper, DwhFinalStdCostStep> implements DwhFinalStdCostStepService {

    @Autowired
    private DwhFinalStdCostStepMapper dwhFinalStdCostStepMapper;

    @Override
    public PageDataInfo<DwhFinalStdCostStep> overview(
            String plantId, String verId, String controlAreaId, String productId,
            String date,  String sort, SearchVo searchVo) {
        Page<DwhFinalStdCostStep> page = PageUtils.getPage(DwhFinalStdCostStep.class);
        IPage<DwhFinalStdCostStep> iPage = dwhFinalStdCostStepMapper.selectAll(plantId, verId, controlAreaId, productId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalStdCostStep> listAll() {
        return dwhFinalStdCostStepMapper.listAll();
    }

    @Override
    public List<DwhFinalStdCostStep> selectNoPage(String plantId, String verId, String controlAreaId, String productId, String date, String sort, SearchVo searchVo) {
        return dwhFinalStdCostStepMapper.selectNoPage(plantId, verId, controlAreaId, productId, date, sort, searchVo);
    }
}
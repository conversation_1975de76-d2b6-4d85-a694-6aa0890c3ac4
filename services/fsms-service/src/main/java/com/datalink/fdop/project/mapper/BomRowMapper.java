package com.datalink.fdop.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.project.api.domain.BomRow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/11 14:28
 */
@Mapper
public interface BomRowMapper extends BaseMapper<BomRow> {

    @Select("SELECT a.head_id from zjdata.p_d_bom_head a LEFT JOIN zjdata.p_d_bom_row b on a.parent_material_code=b.children_material_code  where b.children_material_code is null")
    List<String> findTopBom();
}

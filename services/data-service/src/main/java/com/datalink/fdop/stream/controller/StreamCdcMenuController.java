package com.datalink.fdop.stream.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.stream.api.domain.StreamCdc;
import com.datalink.fdop.stream.api.domain.StreamCdcMenu;
import com.datalink.fdop.stream.api.domain.StreamCdcTree;
import com.datalink.fdop.stream.service.IStreamCdcMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/stream/cdc/menu")
@RestController
@Api(tags = "流cdc菜单api")
public class StreamCdcMenuController extends BaseController {

    @Autowired
    private IStreamCdcMenuService streamCdcMenuService;

    @ApiOperation("创建流菜单")
    @Log(title = "流", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody StreamCdcMenu streamCdcMenu) {
        return R.toResult(streamCdcMenuService.create(streamCdcMenu));
    }

    @ApiOperation("修改流菜单")
    @Log(title = "流", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody StreamCdcMenu streamCdcMenu) {
        return R.toResult(streamCdcMenuService.update(streamCdcMenu));
    }

    @ApiOperation("删除流菜单")
    @Log(title = "流", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_FLOW_MENU_TO_DELETE);
        }
        return R.toResult(streamCdcMenuService.delete(ids));
    }

    @ApiOperation("总览")
    @Log(title = "流")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<StreamCdc>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                               @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                               @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(streamCdcMenuService.overview(pid, sort, searchVo));
    }


    @ApiOperation("展示数据质量树结构")
    @Log(title = "数据质量")
    @GetMapping(value = "/tree")
    public R<List<StreamCdcTree>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false, defaultValue = "true") Boolean isQueryNode) {
        List<StreamCdcTree> list = streamCdcMenuService.tree(sort, code, isQueryNode);
        return R.ok(list);
    }


    @ApiOperation("查序号")
    @Log(title = "流")
    @GetMapping(value = "/querySerialNumber")
    public R<Integer> querySerialNumber(@RequestParam Boolean menuFlag) {
        return R.ok(streamCdcMenuService.querySerialNumber(menuFlag));
    }

}

package com.datalink.fdop.engine.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-02-28 14:12
 */
public interface SettleService {

    Map<String, Object> fg(String verId, String factoryId, Long year, Long month, SearchVo searchVo, String tableName, String actionFlag);

    Map<String, Object> wip(String verId, String factoryId, Long year, Long month, String businessType, SearchVo searchVo, String tableName, String actionFlag);

    Map<String, Object> event(String verId, String factoryId, Long year, Long month, String businessType, SearchVo searchVo, String tableName, String actionFlag);

    Map<String, Object> posting(String verId, String factoryId, Long year, Long month, String businessType, SearchVo searchVo, String tableName, String actionFlag);

    int settleSave(String verId, String factoryId, Long year, Long month, String businessType, String actionFlag);

    int undoSave(String verId, String factoryId, Long year, Long month, String businessType, String actionFlag);
}

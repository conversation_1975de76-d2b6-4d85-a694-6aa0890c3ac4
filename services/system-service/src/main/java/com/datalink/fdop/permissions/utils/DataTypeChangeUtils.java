package com.datalink.fdop.permissions.utils;

import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.enums.FieldType;

/**
 * <AUTHOR>
 * @Date 2022/7/4 20:32
 */
public class DataTypeChangeUtils {


    public static String changeField(FieldType fieldType,Object data){
        String result="";
        if (data==null) {
            return result;
        }
        if (fieldType==FieldType.数值类型){
            return String.valueOf(data);
        }else if (fieldType==FieldType.整数类型){
            return String.valueOf(data);
        }else if (fieldType==FieldType.浮点类型){
            return String.valueOf(data);
        }else if (fieldType==FieldType.长浮点类型){
            return String.valueOf(data);
        }else if (fieldType==FieldType.字符类型){
            return "'"+ data+"'";
        }else if (fieldType==FieldType.长整数类型){
            return String.valueOf(data);
        }else if (fieldType==FieldType.文本类型){
            return "'"+ data+"'";
        }else if (fieldType==FieldType.日期类型){
            return "'"+ data+"'";
        }else if (fieldType==FieldType.时戳类型){
            return String.valueOf(data);
        }else if (fieldType==FieldType.时间类型){
            return String.valueOf(data);
        }
        return result;
    }


    public static String getFieldProperty(FieldType fieldType){
        String result="TEXT";
        if (fieldType==FieldType.数值类型){
            return "TEXT";
        }else if (fieldType==FieldType.整数类型){
            return "BIGINT";
        }else if (fieldType==FieldType.浮点类型){
            return "TEXT";
        }else if (fieldType==FieldType.长浮点类型){
            return "TEXT";
        }else if (fieldType==FieldType.字符类型){
            return "TEXT";
        }else if (fieldType==FieldType.长整数类型){
            return "BIGINT";
        }else if (fieldType==FieldType.文本类型){
            return "TEXT";
        }else if (fieldType==FieldType.日期类型){
            return "TEXT";
        }else if (fieldType==FieldType.时戳类型){
            return "TEXT";
        }else if (fieldType==FieldType.时间类型){
            return "TEXT";
        }
        return result;
    }




    public static String escapeExprSpecialWord(String keyword) {

        if (StringUtils.isNotBlank(keyword)) {

            String[] fbsArr = { "\\", "$", "(", ")", "*", "+", "[", "]", "?", "^", "{", "}"};

            for (String key : fbsArr) {

                if (keyword.contains(key)) {

                    keyword = keyword.replace(key, "\\" + key);

                }

            }

        }

        return keyword;

    }
}


package com.datalink.fdop.seatunnel.api;


import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.seatunnel.api.domain.DataModelOptions;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelField;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelRunConfig;
import com.datalink.fdop.seatunnel.api.factory.RemoteSeaTunnelFallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(contextId = "remoteSeaTunnelService", path = "/seatunnel/seatunnel/api",
        value = ServiceNameConstants.DATA_SERVICE, fallbackFactory = RemoteSeaTunnelFallbackFactory.class)
public interface RemoteSeaTunnelService {

    @PostMapping("/getDataModelOptionsDataSource")
    R<DataSource> getDataModelOptionsDataSource(@RequestBody DataModelOptions options);

    @ApiOperation("获取数据模型的列信息")
    @PostMapping("/getDataModelOptionsFields")
    R<List<SeaTunnelField>> getDataModelOptionsFields(@RequestBody DataModelOptions options);

    @ApiOperation("生成SeaTunnel运行配置")
    @PostMapping("/generateSeaTunnelRunConfig")
    R<String> generateSeaTunnelRunConfig(@RequestBody SeaTunnelRunConfig runConfig);

    @ApiOperation("生成SeaTunnel运行配置目标为文件服务器")
    @PostMapping("/generateSeaTunnelRunConfigSinkFile")
    R<String> generateSeaTunnelRunConfigSinkFile(@RequestBody SeaTunnelRunConfig runConfig);

    @ApiOperation("SeaTunnel运行配置通配符转换")
    @PostMapping("/transformPlaceholderSeaTunnelRunConfig")
    R<String> transformPlaceholderSeaTunnelRunConfig(@RequestBody SeaTunnelRunConfig runConfig);

}

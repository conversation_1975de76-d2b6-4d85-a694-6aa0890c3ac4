<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.RuleReturnMapper">

    <select id="selectLastVer" resultType="java.lang.String">
        select ver_flag from zjdata.co_allocation where ver_flag like '%A' order by id desc limit 1
    </select>

    <select id="countByFour" resultType="java.math.BigDecimal">
        select sum(money) from zjdata.co_allocation
        where ver_code = #{verCode} and date = #{time} and kostl_code = #{kostlCode} and saknr_code = #{saknrCode}
    </select>

</mapper>


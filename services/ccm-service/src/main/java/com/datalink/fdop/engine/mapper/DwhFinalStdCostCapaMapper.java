package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostCapa;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwhFinalStdCostCapaMapper extends BaseMapper<DwhFinalStdCostCapa> {

    IPage<DwhFinalStdCostCapa> selectAll(
            @Param("plantId") String plantId, @Param("verId") String verId,
            @Param("controlAreaId") String controlAreaId, @Param("productId") String productId,
            @Param("batchQty") Integer batchQty, @Param("baseYearMonth") String baseYearMonth,
            @Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo,
            @Param("page") IPage<DwhFinalStdCostCapa> page, @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwhFinalStdCostCapa> selectNoPage(@Param("plantId") String plantId,
                                           @Param("verId") String verId,
                                           @Param("controlAreaId") String controlAreaId,
                                           @Param("productId") String productId,
                                           @Param("batchQty") Integer batchQty,
                                           @Param("baseYearMonth") String baseYearMonth,
                                           @Param("dateFrom") String dateFrom,
                                           @Param("dateTo") String dateTo,
                                           @Param("sort") String sort,
                                           @Param("searchVo") SearchVo searchVo);
}
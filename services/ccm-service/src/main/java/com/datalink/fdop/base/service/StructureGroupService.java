package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.StructureGroup;
import com.datalink.fdop.base.api.model.StructureGroupTree;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface StructureGroupService extends IService<StructureGroup> {

    boolean insert(StructureGroup structureGroup);

    int deleteByList(List<StructureGroup> list);

//    boolean copy(List<Capacity> list);

    PageDataInfo<StructureGroup> overview(String pid, String sort, SearchVo searchVo);

    List<StructureGroup> selectListNoPage(String pid, String sort, SearchVo searchVo);

    int updateByKey(StructureGroup structureGroup);

    boolean updateBatchStatus(Boolean enable, List<StructureGroup> ids);

    String importData(List<StructureGroup> list, Boolean isUpdateSupport, String operName);

    List<StructureGroup> listAll(String costStructureGroupId,Long costLevel);

    List<StructureGroupTree> tree(String code, String sort, Boolean isQueryNode);

}

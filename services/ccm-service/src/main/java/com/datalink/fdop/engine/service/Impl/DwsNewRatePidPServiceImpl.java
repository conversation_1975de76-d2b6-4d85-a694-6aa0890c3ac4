package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRatePidP;
import com.datalink.fdop.engine.mapper.DwsNewRatePidPMapper;
import com.datalink.fdop.engine.service.DwsNewRatePidPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRatePidPServiceImpl extends ServiceImpl<DwsNewRatePidPMapper, DwsNewRatePidP> implements DwsNewRatePidPService {

    @Autowired
    private DwsNewRatePidPMapper dwsNewRatePidPMapper;

    @Override
    public PageDataInfo<DwsNewRatePidP> overview(
            String verId,
            String plantId,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo) {
        Page<DwsNewRatePidP> page = PageUtils.getPage(DwsNewRatePidP.class);
        IPage<DwsNewRatePidP> iPage = dwsNewRatePidPMapper.selectAll(verId, plantId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRatePidP> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwsNewRatePidPMapper.selectNoPage(verId, plantId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
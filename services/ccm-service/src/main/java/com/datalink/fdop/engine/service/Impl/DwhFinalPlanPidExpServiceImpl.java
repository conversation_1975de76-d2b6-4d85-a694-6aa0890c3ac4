package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalPlanPidExp;
import com.datalink.fdop.engine.mapper.DwhFinalPlanPidExpMapper;
import com.datalink.fdop.engine.service.DwhFinalPlanPidExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalPlanPidExpServiceImpl extends ServiceImpl<DwhFinalPlanPidExpMapper, DwhFinalPlanPidExp> implements DwhFinalPlanPidExpService {

    @Autowired
    private DwhFinalPlanPidExpMapper dwhFinalPlanPidExpMapper;

    @Override
    public PageDataInfo<DwhFinalPlanPidExp> overview(
            String verId, String companyId, String yearMonthFrom,
            String yearMonthTo, String sort, SearchVo searchVo) {
        Page<DwhFinalPlanPidExp> page = PageUtils.getPage(DwhFinalPlanPidExp.class);
        IPage<DwhFinalPlanPidExp> iPage = dwhFinalPlanPidExpMapper.selectAll(verId, companyId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalPlanPidExp> selectNoPage(String verId, String companyId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwhFinalPlanPidExpMapper.selectNoPage(verId, companyId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
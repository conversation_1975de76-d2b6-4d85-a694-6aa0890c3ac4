package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.base.api.domain.Ver;
import com.datalink.fdop.base.api.domain.WorkflowConfig;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WorkflowConfigMapper extends BaseMapper<WorkflowConfig> {

    int checkUnique(@Param("id") String id);

    int deleteByIds(@Param(value = "ids") List<String> ids);

    IPage<WorkflowConfig> selectAll(IPage<WorkflowConfig> page, @Param("sort") String sort, @Param("search") SearchVo search);

    boolean updateBatchStatus(@Param("ccmFunLock") Boolean ccmFunLock, @Param("codeList") List<WorkflowConfig> codeList);
}

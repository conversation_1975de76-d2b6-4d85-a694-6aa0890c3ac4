package com.datalink.fdop.fscm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.fscm.api.domain.Repertory;
import com.datalink.fdop.fscm.api.model.vo.ReceivingExcelVo;
import com.datalink.fdop.fscm.mapper.FSCMTemplateMapper;
import com.datalink.fdop.fscm.service.RepertoryService;
import com.datalink.fdop.fscm.service.TemplateService;
import com.datalink.fdop.project.api.domain.OrderRow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/6 15:56
 */
@Service
@Transactional
public class TemplateServiceImpl implements TemplateService {

    @Autowired
    private FSCMTemplateMapper templateMapper;
    @Autowired
    private RepertoryService repertoryService;


    @Override
    public List<ReceivingExcelVo> checkData(List<ReceivingExcelVo> receivingExcelVos) {
        //数据校验
        receivingExcelVos.stream().forEach(receivingExcelVo -> {
            //1
            String invoices= templateMapper.findInvoice(receivingExcelVo.getOrderNum());
            if (!"0".equals(invoices)) {
                receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"收货单据不是采购订单，请检查！");
            }
            //2
            if (receivingExcelVo.getOrderRowNum()==null) {
                Long orderRowNum= templateMapper.findOrderRowNum(receivingExcelVo.getOrderNum(),receivingExcelVo.getMaterialCode());
                if (orderRowNum==null) {
                    receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"收货单据不是采购订单，请检查！");
                }else {
                    String delFlag= templateMapper.findOrderRowNumDelFlag(receivingExcelVo.getOrderNum(),receivingExcelVo.getMaterialCode(),orderRowNum);
                    if ("0".equals(delFlag)) {
                        receivingExcelVo.setOrderRowNum(orderRowNum);
                    }else {
                        receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"此采购项目已被删除，请检查！");
                    }
                }
            }else {
                String delFlag= templateMapper.findOrderRowNumDelFlag(receivingExcelVo.getOrderNum(),receivingExcelVo.getMaterialCode(),receivingExcelVo.getOrderRowNum());
                if (StringUtils.isEmpty(delFlag)) {
                    receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"此采购项目不存在或收货物料编码有误，请检查！");
                }
                if ("1".equals(delFlag)) {
                    receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"此采购项目已被删除，请检查！");
                }
            }

            //3
            String unit=templateMapper.findUnit(receivingExcelVo.getMaterialCode());
            if (StringUtils.isEmpty(unit)||!unit.equals(receivingExcelVo.getBasicUnit())) {
                receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"收货物料基本单位有误，请检查！");
            }
            //4
            String stockPCode = receivingExcelVo.getStockPCode();
            if (StringUtils.isEmpty("stockPCode")) {
                receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"收货库存地点不存在，请检查！");
            }else {
                String pCode = templateMapper.findStockPCode(stockPCode);
                if (StringUtils.isEmpty(pCode)) {
                    receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"收货库存地点不存在，请检查！");
                }
            }
            String factory=templateMapper.findFactory(stockPCode,receivingExcelVo.getOrderNum(),receivingExcelVo.getOrderRowNum());
            if (StringUtils.isEmpty(factory)) {
                receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"收货库存地点不属于此采购订单行的工厂，请检查！");
            }
            //5
            OrderRow orderRow=templateMapper.findOrderRow(receivingExcelVo.getOrderNum(),receivingExcelVo.getOrderRowNum());
            if (orderRow!=null && orderRow.getDeliveryControl().equals("0")) {
                List<Double> postQuantitys = templateMapper.findPostQuantity(receivingExcelVo.getOrderNum(), receivingExcelVo.getOrderRowNum());
                double postQuantitySum = postQuantitys.stream().mapToDouble(Double::doubleValue).sum();
                double count = Double.valueOf(receivingExcelVo.getQuantity()) + postQuantitySum - orderRow.getOrderQuantityReceive() * Double.valueOf(orderRow.getBasicUnitNum()) / Double.valueOf(orderRow.getBasicUnitNum());
                if (Double.valueOf(orderRow.getExcessiveTolerance())<count) {
                    receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"该采购项目剩余可收货数量小于当前收货数量，请检查！");
                }
            }
            String returnFlag = orderRow.getReturnFlag();
            if (returnFlag.equals("1")) {
                String factoryCode = templateMapper.findFactoryCode(receivingExcelVo.getOrderNum(), receivingExcelVo.getOrderRowNum());
                Repertory repertory = repertoryService.getOne(new QueryWrapper<Repertory>().lambda()
                    .eq(Repertory::getFactoryCode,factoryCode)
                    .eq(Repertory::getStockPCode,receivingExcelVo.getStockPCode())
                    .eq(Repertory::getMaterialCode,receivingExcelVo.getMaterialCode())
                    .eq(Repertory::getBatchNumber,receivingExcelVo.getBatchNumber())
                    .eq(Repertory::getPiece,StringUtils.isNotEmpty(receivingExcelVo.getPiece())?receivingExcelVo.getPiece(): Constants.DATA_DEFAULT_VALUE)
                );
                if (repertory==null||repertory.getUnrestrictedStock()<Double.valueOf(receivingExcelVo.getQuantity())) {
                    receivingExcelVo.setMsg(receivingExcelVo.getMsg()+"短缺非限制库存，无法退货，请检查！");
                }
            }

        });
        return receivingExcelVos;
    }

    @Override
    public String findSupplierCode(String orderNum) {
        return templateMapper.findSupplierCode(orderNum);
    }

    @Override
    public String findStockPDescription(String stockPCode) {
        return templateMapper.findStockPDescription(stockPCode);
    }

    @Override
    public String findSupplierName(String supplierCode) {
        return templateMapper.findSupplierName(supplierCode);
    }

    @Override
    public String findFactoryCode(String orderNum, Long orderRowNum) {
        return templateMapper.findFactoryCode(orderNum,orderRowNum);
    }

    @Override
    public String findReturnFlag(String orderNum, Long orderRowNum) {
        return templateMapper.findReturnFlag( orderNum,  orderRowNum);
    }

    @Override
    public String findMoveTypeDes(String moveType) {
        return templateMapper.findMoveTypeDes(moveType);
    }
}

package com.datalink.fdop.engine.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-03-06 15:53
 */
public interface CostOutService {

    Map<String, Object> getWipOrInv(String verId, String companyId, String factoryId, Long yearMonth, String costType, SearchVo searchVo, String prefix);

    Map<String, Object> getRd(String verId, String companyId, String factoryId, String costCenterId, String wbsId, Long yearMonth, SearchVo searchVo);

    Map<String, Object> getEvent(String verId, String companyId, String factoryId, String eventCode, Long yearMonth, SearchVo searchVo);

    Map<String, Object> getPc(String verId, String companyId, String factoryId, String workOrder, Long yearMonth, SearchVo searchVo);
}

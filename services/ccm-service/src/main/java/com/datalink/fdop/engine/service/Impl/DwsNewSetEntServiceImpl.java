package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewSetEnt;
import com.datalink.fdop.engine.mapper.DwsNewSetEntMapper;
import com.datalink.fdop.engine.service.DwsNewSetEntService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewSetEntServiceImpl extends ServiceImpl<DwsNewSetEntMapper, DwsNewSetEnt> implements DwsNewSetEntService {

    @Autowired
    private DwsNewSetEntMapper dwsNewSetEntMapper;

    @Override
    public PageDataInfo<DwsNewSetEnt> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwsNewSetEnt> page = PageUtils.getPage(DwsNewSetEnt.class);
        IPage<DwsNewSetEnt> iPage = dwsNewSetEntMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewSetEnt> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwsNewSetEntMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}
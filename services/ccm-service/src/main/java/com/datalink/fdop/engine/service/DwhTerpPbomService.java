package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpPbom;

import java.util.List;

public interface DwhTerpPbomService extends IService<DwhTerpPbom> {

    PageDataInfo<DwhTerpPbom> overview(String verId,String  plantId,String productId,String sort, SearchVo searchVo);

    List<DwhTerpPbom> listAll();

    List<DwhTerpPbom> selectNoPage(String verId,
                                   String plantId,
                                   String productId,
                                   String sort,
                                   SearchVo searchVo);
}

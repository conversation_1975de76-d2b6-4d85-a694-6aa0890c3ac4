package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwdPrice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwdPriceMapper extends BaseMapper<DwdPrice> {

    IPage<DwdPrice> selectAll(@Param("materialId") String materialId,
                              @Param("plantId") String plantId,
                              @Param("page") IPage<DwdPrice> page,
                              @Param("sort") String sort,
                              @Param("searchVo") SearchVo searchVo);

    List<DwdPrice> selectNoPage(@Param("materialId") String materialId,
                                @Param("plantId") String plantId,
                                @Param("sort") String sort,
                                @Param("searchVo") SearchVo searchVo);
}
package com.datalink.fdop.datart.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.datart.domain.Source;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SourceMapper extends BaseMapper<Source> {

    int createSource(@Param("source") Source source);

    int updateSource(Source source);

    int deleteSource(@Param("id") String id);

    Source selectById(@Param("id") String id);

    Source selectByName(@Param("name") String name);

    List<Source> listByOrg(@Param("orgId") String orgId, @Param("active") int active);

    Source getSourceDetail(String sourceId);
}

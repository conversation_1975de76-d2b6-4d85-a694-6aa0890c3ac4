package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostOsrate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwsNewStdCostOsrateMapper extends BaseMapper<DwsNewStdCostOsrate> {

    IPage<DwsNewStdCostOsrate> selectAll(
            @Param("controlAreaId") String controlAreaId, @Param("productId") String productId,
            @Param("batchQty") Integer batchQty,
            @Param("verId") String verId, @Param("plantId") String plantId,
            @Param("date") String date,
            @Param("page") IPage<DwsNewStdCostOsrate> page, @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwsNewStdCostOsrate> selectNoPage(@Param("controlAreaId") String controlAreaId,
                                           @Param("productId") String productId,
                                           @Param("batchQty") Integer batchQty,
                                           @Param("verId") String verId,
                                           @Param("plantId") String plantId,
                                           @Param("date") String date,
                                           @Param("sort") String sort,
                                           @Param("searchVo") SearchVo searchVo);
}
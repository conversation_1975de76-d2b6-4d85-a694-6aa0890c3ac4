package com.datalink.fdop.element.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2022/3/28 17:37
 */
public enum DataElementColumnType {

    MAIN_COLUMN(0, "MAIN_COLUMN"),
    OTHER_COLUMN(1, "OTHER_COLUMN"),
    ;

    private final int code;
    @EnumValue
    private final String desc;

    DataElementColumnType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}

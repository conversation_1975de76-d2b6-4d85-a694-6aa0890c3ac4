package com.datalink.fdop.seatunnel.api.restapi.domain.doris;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 通用Doris配置类
 */
@Data
public class CommonDorisConfig {

    /**
     * SeaTunnel插件类型
     */
    @JSONField(name = "plugin_name")
    private String pluginName = "Doris";

    /**
     * Doris FE节点，多个,分割
     */
    private String fenodes;

    /**
     * Mysql查询端口
     */
    @JSONField(name = "query-port")
    private String queryPort;

    private String database;

    /**
     * Doris 用户名
     */
    private String username;

    /**
     * Doris 密码
     */
    private String password;

}

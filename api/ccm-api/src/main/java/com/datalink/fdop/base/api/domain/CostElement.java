package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022-10-27 11:31
 */
@Data
@TableName(schema = "zjdata", value = "ccm_cost_element")
@ApiModel
public class CostElement {

    @Excel(name = "科目表", sort = 1)
    @ApiModelProperty(value = "科目表")
    @NotBlank(message = "科目表不能为空")
    private String chartofa;

    @Excel(name = "成本要素", sort = 2)
    @NotBlank(message = "成本要素不能为空")
    @ApiModelProperty(value = "成本要素")
    private String costElementId;

    @Excel(name = "成本要素描述", sort = 3)
    @ApiModelProperty(value = "成本要素描述")
    private String costElementDesc;

    @Excel(name = "成本要素文本", sort = 4)
    @ApiModelProperty(value = "成本要素文本")
    private String costElementText;

    @Excel(name = "成本要素类型", sort = 5)
    @ApiModelProperty(value = "成本要素类型")
    private String costElementType;

    @ApiModelProperty(value = "所属成本要素分组")
    private String costElementGroupId;

    @TableField(exist = false)
    @ApiModelProperty(value = "所属成本要素分组描述")
    private String costElementGroupDesc;

    @Excel(name = "是否启用", sort = 6, readConverterExp = "true=启用,false=不启用")
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "创建人")
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更改人")
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除")
    private Boolean deleteFlag;

}

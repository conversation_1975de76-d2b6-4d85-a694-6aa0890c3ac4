package com.datalink.fdop.element.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/16 15:13
 */
@Data
@NoArgsConstructor
@TableName(schema = "zjdata", value = "d_e_data_element_Table")
@ApiModel("数据实体关联表")
public class DataElementTable extends BaseField {

    private static final long serialVersionUID = -5508413529453322173L;

    @ApiModelProperty(value = "表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "数据实体id")
    @NotNull(message = "元素id不能用空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dataElementId;

    @NotNull(message = "元素id不能用空")
    @ApiModelProperty(value = "数据源id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dataSourceId;

    @ApiModelProperty(value = "库名")
    private String schemaName;

    @ApiModelProperty(value = "表名")
    private String tableName;

    @ApiModelProperty(value = "创表配置")
    private String createTableConfig;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "表结构")
    private List<DataElementStructure> dataElementStructureList;

}

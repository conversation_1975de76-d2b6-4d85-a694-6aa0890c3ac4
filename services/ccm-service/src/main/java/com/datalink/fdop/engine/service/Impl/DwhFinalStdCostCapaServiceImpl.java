package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostCapa;
import com.datalink.fdop.engine.mapper.DwhFinalStdCostCapaMapper;
import com.datalink.fdop.engine.service.DwhFinalStdCostCapaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalStdCostCapaServiceImpl extends ServiceImpl<DwhFinalStdCostCapaMapper, DwhFinalStdCostCapa> implements DwhFinalStdCostCapaService {

    @Autowired
    private DwhFinalStdCostCapaMapper dwhFinalStdCostCapaMapper;

    @Override
    public PageDataInfo<DwhFinalStdCostCapa> overview(
            String plantId, String verId, String controlAreaId, String productId, Integer batchQty, String baseYearMonth,
            String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        Page<DwhFinalStdCostCapa> page = PageUtils.getPage(DwhFinalStdCostCapa.class);
        IPage<DwhFinalStdCostCapa> iPage = dwhFinalStdCostCapaMapper.selectAll(plantId, verId, controlAreaId, productId, batchQty, baseYearMonth, dateFrom, dateTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalStdCostCapa> selectNoPage(String plantId, String verId, String controlAreaId, String productId, Integer batchQty, String baseYearMonth, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        return dwhFinalStdCostCapaMapper.selectNoPage(plantId, verId, controlAreaId, productId, batchQty,
                baseYearMonth, dateFrom, dateTo, sort, searchVo);
    }


}
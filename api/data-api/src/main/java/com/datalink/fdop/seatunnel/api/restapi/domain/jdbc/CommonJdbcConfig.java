package com.datalink.fdop.seatunnel.api.restapi.domain.jdbc;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 通用Jdbc配置类
 */
@Data
public class CommonJdbcConfig {

    /**
     * SeaTunnel插件类型
     */
    @JSONField(name = "plugin_name")
    private String pluginName = "Jdbc";

    /**
     * SELECT/INSERT SQL语句
     */
    private String query;

    /**
     * Jdbc连接驱动
     */
    private String driver;

    /**
     * Jdbc连接字符串
     */
    private String url;

    /**
     * 数据库用户名
     */
    private String user;

    /**
     * 数据库密码
     */
    private String password;

}

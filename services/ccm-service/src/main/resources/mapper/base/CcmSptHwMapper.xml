<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CcmSptHwMapper">

    <delete id="deleteBykey">
        delete from zjdata.ccm_spt_hw
        WHERE plant_id = #{plantId} and equip_group_id = #{equipGroupId}
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_spt_hw
        WHERE plant_id = #{plantId} and equip_group_id = #{equipGroupId}
        limit 1
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CcmSptHw">
        update zjdata.ccm_spt_hw
        set
            <if test="sptStd != null and sptStd!='' ">spt_std = #{sptStd},</if>
            <if test="enable != null ">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where plant_id=#{plantId} and equip_group_id = #{equipGroupId}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.CcmSptHw">
        SELECT 
            *
        FROM zjdata.ccm_spt_hw a
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
            <if test="plantId != null and plantId != ''">
                and a.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="equipGroupId != null and equipGroupId != ''">
                and a.equip_group_id in
                <foreach item="item" collection="equipGroupId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.plant_id, a.equip_group_id
        ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.base.api.domain.CcmSptHw">
        SELECT
        *
        FROM zjdata.ccm_spt_hw a
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
            <if test="plantId != null and plantId != ''">
                and a.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="equipGroupId != null and equipGroupId != ''">
                and a.equip_group_id in
                <foreach item="item" collection="equipGroupId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.plant_id, a.equip_group_id
        ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_spt_hw
        SET enable = #{enable}
        where (plant_id, equip_group_id) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.plantId},#{item.equipGroupId})
        </foreach>
    </update>

</mapper>
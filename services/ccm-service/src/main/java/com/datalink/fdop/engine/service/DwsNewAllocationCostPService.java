package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationCostP;

import java.util.List;

public interface DwsNewAllocationCostPService extends IService<DwsNewAllocationCostP> {

    /**
     * 分页查询定向分摊
     **/
    PageDataInfo<DwsNewAllocationCostP> selectTargetedNewAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo);

    /**
     * 分页查询费用分摊
     **/
    PageDataInfo<DwsNewAllocationCostP> selectCostNewAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo);

    List<DwsNewAllocationCostP> selectNoPage(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo);
}
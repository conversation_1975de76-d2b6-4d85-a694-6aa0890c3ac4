package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(schema = "zjdata", value = "ccm_plan_eid_exp")
@ApiModel
public class PlanEidExp {

    @Excel(name = "数据集", sort = 1)
    @ApiModelProperty(value = "数据集")
    @NotBlank(message = "数据集不能为空")
    private String verId;

    @Excel(name = "管理范围", sort = 2)
    @ApiModelProperty(value = "管理范围")
    @NotBlank(message = "管理范围不能为空")
    private String controlAreaId;

    @Excel(name = "值类型", sort = 3)
    @ApiModelProperty(value = "值类型")
    @NotBlank(message = "值类型不能为空")
    private String valueType;

    @Excel(name = "币别", sort = 4)
    @ApiModelProperty(value = "币别")
    private String currency;

    @Excel(name = "年度", sort = 5)
    @ApiModelProperty(value = "年度")
    @NotNull(message = "年度不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private String year;

    @Excel(name = "期间", sort = 6)
    @ApiModelProperty(value = "期间")
    @NotNull(message = "期间不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private String month;

    @Excel(name = "原始成本要素", sort = 7)
    @ApiModelProperty(value = "原始成本要素")
    private String origCostElementId;

    @Excel(name = "重估成本要素", sort = 8)
    @ApiModelProperty(value = "重估成本要素")
    private String revalueElementId;

    @Excel(name = "成本要素", sort = 9)
    @ApiModelProperty(value = "成本要素")
    @NotBlank(message = "成本要素不能为空")
    private String costElementId;

    @Excel(name = "设备ID", sort = 10)
    @ApiModelProperty(value = "设备ID")
    @NotBlank(message = "设备ID不能为空")
    private String equipId;

    @Excel(name = "成本中心", sort = 11)
    @ApiModelProperty(value = "成本中心")
    @NotBlank(message = "成本中心不能为空")
    private String costCenterId;

    @Excel(name = "货币值", sort = 12)
    @ApiModelProperty(value = "货币值")
    private BigDecimal amount;

    @Excel(name = "公司代码", sort = 13)
    @ApiModelProperty(value = "公司代码")
    private String companyId;

    @Excel(name = "工厂代码", sort = 14)
    @ApiModelProperty(value = "工厂代码")
    private String plantId;

    @Excel(name = "是否启用", sort = 15, readConverterExp = "true=启用,false=不启用")
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更改人")
    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;
    
}
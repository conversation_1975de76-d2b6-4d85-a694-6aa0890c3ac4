<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.WorkflowConfigMapper">

    <select id="checkUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_workflow_config
        WHERE ccm_fun_code = #{id} limit 1
    </select>

    <delete id="deleteByIds" parameterType="Long">
        delete from zjdata.ccm_workflow_config
        WHERE ccm_fun_code IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.WorkflowConfig">
        SELECT ls.*
        FROM zjdata.ccm_workflow_config ls
        <where>
            <if test="search != null ">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("ls",search)})
            </if>
        </where>
        ORDER BY ls.ccm_fun_code ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_workflow_config
        SET ccm_fun_lock = #{ccmFunLock}
        where ccm_fun_code in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
             #{item.ccmFunCode}
        </foreach>
    </update>

</mapper>


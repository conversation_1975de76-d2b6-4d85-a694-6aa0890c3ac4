<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwdPlanExpMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwdPlanExp">
        SELECT *
        FROM dwd.dwd_plan_exp node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id = #{verId}
            </if>
            <if test="companyId != null and companyId != ''">
                and node.company_id in
                <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != '' ">
                and node.year_month >= #{yearMonthFrom}
            </if>
            <if test="yearMonthTo != null and yearMonthTo != ''">
                and node.year_month &lt;= #{yearMonthTo}
            </if>
        </where>
        ORDER BY node.ver_id,node.value_type,node.control_area_id,
        node.company_id,node.plant_id,node.year_month,node.cost_center_id,
        node.orig_cost_element_id,node.cost_element_id
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwdPlanExp">
        SELECT *
        FROM dwd.dwd_plan_exp node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id = #{verId}
            </if>
            <if test="companyId != null and companyId != ''">
                and node.company_id in
                <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != '' ">
                and node.year_month >= #{yearMonthFrom}
            </if>
            <if test="yearMonthTo != null and yearMonthTo != ''">
                and node.year_month &lt;= #{yearMonthTo}
            </if>
        </where>
        ORDER BY node.ver_id,node.value_type,node.control_area_id,
        node.company_id,node.plant_id,node.year_month,node.cost_center_id,
        node.orig_cost_element_id,node.cost_element_id
    </select>

</mapper>
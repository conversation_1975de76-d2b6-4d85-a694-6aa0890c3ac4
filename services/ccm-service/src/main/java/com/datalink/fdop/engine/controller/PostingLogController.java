package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.engine.api.domain.PostingLogHead2;
import com.datalink.fdop.engine.api.domain.PostingLogItem2;
import com.datalink.fdop.engine.model.vo.PostingLog;
import com.datalink.fdop.engine.service.PostingLogService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-14 14:54
 */
@RestController
@RequestMapping("/fccm/PostingLog")
public class PostingLogController {

    @Autowired
    private PostingLogService postingLogService;

    @ApiOperation("查")
    @Log(title = "FCCM功能报表")
    @PostMapping("/query")
    public R<PageDataInfo> query(@RequestParam(value = "verId") String verId,
                                 @RequestParam(value = "companyId") String companyId,
                                 @RequestParam(value = "actionFlag", required = false) String actionFlag,
                                 @RequestParam(value = "postingDate") String postingDate,
                                 @RequestParam(required = false, defaultValue = "ASC") String sort,
                                 @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(postingLogService.query(verId, companyId, actionFlag, postingDate, sort, searchVo));
    }

    @ApiOperation("暂存")
    @Log(title = "FCCM功能报表")
    @PostMapping("/add")
    public R add(@RequestBody PostingLog postingLog) {
        postingLog.getHead().setActionFlag("1A");
        return R.ok(postingLogService.add(postingLog));
    }

    @ApiOperation("预制")
    @Log(title = "FCCM功能报表")
    @PutMapping("/precast")
    public R precast(@RequestBody PostingLog postingLog) {
        postingLog.getHead().setActionFlag("1B");
        return R.ok(postingLogService.add(postingLog));
    }


    @ApiOperation("增Item")
    @Log(title = "FCCM功能报表")
    @PostMapping("/addItem")
    public R add(@RequestBody PostingLogItem2 postingLogItem2) {
        return R.ok(postingLogService.addItem(postingLogItem2));
    }

    @ApiOperation("改Head")
    @Log(title = "FCCM功能报表")
    @PutMapping("/updateHead")
    public R updateHead(@RequestBody PostingLogHead2 postingLogHead2) {
        return R.ok(postingLogService.updateHead(postingLogHead2));
    }

    @ApiOperation("改Item")
    @Log(title = "FCCM功能报表")
    @PutMapping("/updateItem")
    public R updateItem(@RequestBody PostingLogItem2 postingLogItem2) {
        return R.ok(postingLogService.updateItem(postingLogItem2));
    }

    @ApiOperation("删Item")
    @Log(title = "FCCM功能报表")
    @DeleteMapping("/delItem")
    public R delete(@RequestBody List<PostingLogItem2> list) {
        return R.ok(postingLogService.delItem(list));
    }

    @ApiOperation("正式")
    @Log(title = "FCCM功能报表")
    @PutMapping("/formal")
    public R formal(@RequestParam(value = "id") Long id) {
        return R.ok(postingLogService.updateStatus(id, "1C"));
    }

    @ApiOperation("撤销")
    @Log(title = "FCCM功能报表")
    @PutMapping("/undo")
    public R undo(@RequestParam(value = "id") Long id, @RequestParam(value = "actionFlag") String actionFlag) {
        return R.ok(postingLogService.undo(id, actionFlag));
    }

}

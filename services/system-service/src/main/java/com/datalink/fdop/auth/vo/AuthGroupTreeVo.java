package com.datalink.fdop.auth.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.datalink.fdop.common.core.enums.MenuType;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2022/4/21 15:27
 */
@Data
public class AuthGroupTreeVo implements Serializable {
    private static final long serialVersionUID = 8122885688452792744L;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;
    private MenuType menuType;//0是菜单，1是节点
    private String name;
    private String code;
    private String description;
    private List<AuthGroupTreeVo> children = new ArrayList<AuthGroupTreeVo>();

}

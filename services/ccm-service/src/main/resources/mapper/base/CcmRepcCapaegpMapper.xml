<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CcmRepcCapaegpMapper">

    <delete id="deleteBykey">
        delete
        from zjdata.ccm_repc_capaegp
        WHERE factory_id = #{factoryId}
          and old_equip_group_id = #{oldEquipGroupId}
          and res_dept = #{resDept}
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_repc_capaegp
        WHERE factory_id = #{factoryId}
          and old_equip_group_id = #{oldEquipGroupId}
          and res_dept = #{resDept}
        limit 1
    </select>

    <select id="overview" resultType="com.datalink.fdop.base.api.domain.CcmRepcCapaegp">
        SELECT ls.*
        FROM zjdata.ccm_repc_capaegp ls
        <where>
            <if test="searchVo != null ">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("ls",searchVo)})
            </if>
        </where>
        ORDER BY factory_id,old_equip_group_id,res_dept ${sort}
    </select>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CcmRepcCapaegp">
        update zjdata.ccm_repc_capaegp
        set
        <if test="equipGroupId != null and equipGroupId != '' ">equip_group_id = #{equipGroupId},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where factory_id=#{factoryId} and old_equip_group_id = #{oldEquipGroupId} and res_dept = #{resDept}
    </update>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_repc_capaegp
        SET enable = #{enable}
        where (factory_id, old_equip_group_id, res_dept ) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.factoryId},#{item.oldEquipGroupId},#{item.resDept})
        </foreach>
    </update>
</mapper>
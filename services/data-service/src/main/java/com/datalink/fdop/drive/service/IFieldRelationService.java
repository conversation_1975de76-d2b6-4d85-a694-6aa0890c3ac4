package com.datalink.fdop.drive.service;


import com.datalink.fdop.drive.api.domain.dto.Field;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/6 10:45
 */
public interface IFieldRelationService {

    List<Field> toBase(String dbType, List<Field> fieldList);

    List<Field> toSource(String dbType, List<Field> fieldList);

    List<Field> base2Flink(List<Field> fieldList);

    List<Field> flink2Base(List<Field> fieldList);

    List<LinkedHashMap<String, Object>> test(String sql);

}

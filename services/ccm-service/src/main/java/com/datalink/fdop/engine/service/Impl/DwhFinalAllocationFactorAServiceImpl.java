package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalAllocationFactorA;
import com.datalink.fdop.engine.mapper.DwhFinalAllocationFactorAMapper;
import com.datalink.fdop.engine.service.DwhFinalAllocationFactorAService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalAllocationFactorAServiceImpl extends ServiceImpl<DwhFinalAllocationFactorAMapper, DwhFinalAllocationFactorA> implements DwhFinalAllocationFactorAService {

    @Autowired
    private DwhFinalAllocationFactorAMapper dwhFinalAllocationFactorAMapper;

    @Override
    public PageDataInfo<DwhFinalAllocationFactorA> overview(
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String costCenterId,
            String factorId,
            String allocationType,
            String sort,
            SearchVo searchVo) {
        Page<DwhFinalAllocationFactorA> page = PageUtils.getPage(DwhFinalAllocationFactorA.class);
        IPage<DwhFinalAllocationFactorA> iPage = dwhFinalAllocationFactorAMapper.selectAll(
                controlAreaId, verId, yearMonthFrom, yearMonthTo, costCenterType, costCenterId, factorId, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalAllocationFactorA> selectNoPage(String controlAreaId, String verId, String yearMonthFrom, String yearMonthTo, String costCenterType, String costCenterId, String factorId, String allocationType, SearchVo searchVo) {
        return dwhFinalAllocationFactorAMapper.selectNoPage(controlAreaId, verId, yearMonthFrom, yearMonthTo,
                costCenterType, costCenterId, factorId, allocationType, searchVo);
    }
}
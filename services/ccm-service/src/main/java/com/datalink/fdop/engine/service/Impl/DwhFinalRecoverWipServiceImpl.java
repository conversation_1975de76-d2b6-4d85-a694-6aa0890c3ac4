package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRecoverWip;
import com.datalink.fdop.engine.mapper.DwhFinalRecoverWipMapper;
import com.datalink.fdop.engine.service.DwhFinalRecoverWipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRecoverWipServiceImpl extends ServiceImpl<DwhFinalRecoverWipMapper, DwhFinalRecoverWip> implements DwhFinalRecoverWipService {

    @Autowired
    private DwhFinalRecoverWipMapper dwhFinalRecoverWipMapper;

    @Override
    public PageDataInfo<DwhFinalRecoverWip> overview(String verId,String plantId, String date, String sort, SearchVo searchVo) {
        Page<DwhFinalRecoverWip> page = PageUtils.getPage(DwhFinalRecoverWip.class);
        IPage<DwhFinalRecoverWip> iPage = dwhFinalRecoverWipMapper.selectAll(verId,plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRecoverWip> listAll() {
        return dwhFinalRecoverWipMapper.listAll();
    }

    @Override
    public List<DwhFinalRecoverWip> selectNoPage(String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwhFinalRecoverWipMapper.selectNoPage(verId, plantId, date, sort, searchVo);
    }
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRevalueMat;

import java.util.List;

public interface DwsNewRevalueMatService extends IService<DwsNewRevalueMat> {

    PageDataInfo<DwsNewRevalueMat> overview(String yearMonth, String verId,  String controlAreaId, String companyId, String sort, SearchVo searchVo);

    List<DwsNewRevalueMat> selectNoPage(String yearMonth, String verId, String controlAreaId, String companyId, String sort, SearchVo searchVo);
}
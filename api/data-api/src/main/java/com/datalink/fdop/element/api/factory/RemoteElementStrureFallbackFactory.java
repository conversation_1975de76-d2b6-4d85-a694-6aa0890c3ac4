package com.datalink.fdop.element.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.RemoteElementStrureService;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/5 10:55
 */
public class RemoteElementStrureFallbackFactory  implements FallbackFactory<RemoteElementStrureService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteElementFallbackFactory.class);
    @Override
    public RemoteElementStrureService create(Throwable cause) {
        log.error("数据元素服务调用失败:{}", cause.getMessage());
        return new RemoteElementStrureService() {
            @Override
            public R list(Long dataElementId) {
                return R.fail("查询数据元素的表结构失败"+cause.getMessage());
            }

            @Override
            public R<List<DataElementStructure>> getIterateStructure(Long dataElementId) {
                return R.fail("查询数据元素的级联表结构失败"+cause.getMessage());
            }
        };
    }
}

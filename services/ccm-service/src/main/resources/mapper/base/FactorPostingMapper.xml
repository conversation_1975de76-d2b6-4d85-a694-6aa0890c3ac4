<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.FactorPostingMapper">

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.FactorPosting" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_factor_posting
        WHERE control_area_id = #{controlAreaId} AND ver_id = #{verId}
          AND factor_id = #{factorId} ANd cost_center_id = #{costCenterId} AND year_month = #{yearMonth}
          AND del_flag = false limit 1
    </select>

    <update id="deleteByIds" parameterType="com.datalink.fdop.base.api.domain.FactorPosting">
        update zjdata.ccm_factor_posting
        SET del_flag = true
        WHERE ver_id IN
        <foreach collection="ids" item="id" open="(" separator=" or " close=")">
            control_area_id = #{controlAreaId} AND ver_id = #{verId}
            AND factor_id = #{factorId} ANd cost_center_id = #{costCenterId} AND year_month = #{yearMonth}
        </foreach>
        and del_flag = false
    </update>

    <update id="updateByKey">
        update zjdata.ccm_factor_posting
        set
        <if test="qty != null">qty = #{qty},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE control_area_id = #{controlAreaId} AND ver_id = #{verId}
        AND factor_id = #{factorId} ANd cost_center_id = #{costCenterId} AND year_month = #{yearMonth}
        AND del_flag = false
    </update>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.FactorPosting" resultType="com.datalink.fdop.base.api.domain.FactorPosting">
        SELECT a.*
        FROM zjdata.ccm_factor_posting a
        WHERE a.del_flag = false
        <if test="factorPosting.controlAreaId != null and factorPosting.controlAreaId != ''">
            AND a.control_area_id like '%${factorPosting.controlAreaId}%'
        </if>
        <if test="factorPosting.verId != null and factorPosting.verId != ''">
            AND a.ver_id = '${factorPosting.verId}'
        </if>
        <if test="factorPosting.costCenterId != null and factorPosting.costCenterId != ''">
            AND a.cost_center_id like '%${factorPosting.costCenterId}%'
        </if>
        <if test="factorPosting.factorId != null and factorPosting.factorId != ''">
            AND a.factor_id like '%${factorPosting.factorId}%'
        </if>
        <if test="factorPosting.yearMonth != null">
            AND a.year_month = #{factorPosting.yearMonth}
        </if>
        <if test="factorPosting.enable != null">
            AND a.enable = #{factorPosting.enable}
        </if>
        ORDER BY control_area_id, ver_id, factor_id, cost_center_id, year_month desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.FactorPosting">
        SELECT ls.*
        FROM zjdata.ccm_factor_posting ls
        WHERE ls.del_flag = false
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("ls",searchCondition)})
        </if>
        ORDER BY control_area_id, ver_id, factor_id, cost_center_id, year_month ${sort}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.FactorPosting">
        SELECT *
        FROM zjdata.ccm_factor_posting
        WHERE control_area_id = #{controlAreaId} AND ver_id = #{verId}
          AND factor_id = #{factorId} ANd cost_center_id = #{costCenterId} AND year_month = #{yearMonth}
          AND del_flag = false limit 1
    </select>

</mapper>


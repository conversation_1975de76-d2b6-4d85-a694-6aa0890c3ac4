package com.datalink.fdop.element.api.domain;

import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.element.api.model.DataEntityTree;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 实体导出
 */
@Data
public class DataEntityExport {

    /**
     * 数据实体汇总（包含实体，表、字段、字段与实体列映射关系）
     */
    private List<DataEntityCollect> dataEntityCollectList;

    /**
     * 数据实体所属的菜单树结构
     */
    private List<DataEntityTree> dataEntityTreeList;

    /**
     * 导出时的数据源信息（用于导入时验证）
     */
    private Map<Long, DataSource> dataSourceMap;

    /**
     * 菜单信息（用于导入时自动创建菜单）
     */
    private Map<Long, DataEntityMenu> menuInfoMap;

    /**
     * 导出时间
     */
    private String exportTime;

    /**
     * 导出用户
     */
    private String exportUser;

    /**
     * 导出版本
     */
    private String version = "1.0";

}

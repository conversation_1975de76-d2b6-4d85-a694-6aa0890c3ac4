<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwdMaterialMbewMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwdMaterialMbew">
        SELECT *
        FROM dwd.dwd_material_mbew node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="materialType != null and materialType != ''">
                and node.material_type in
                <foreach item="item" collection="materialType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialGroup != null and materialGroup != ''">
                and node.material_group in
                <foreach item="item" collection="materialGroup.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialId != null and materialId != ''">
                and node.material_id in
                <foreach item="item" collection="materialId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="evaluateAreaId != null and evaluateAreaId != ''">
                and node.evaluate_area_id in
                <foreach item="item" collection="evaluateAreaId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.material_type, node.material_group, node.material_id ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwdMaterialMbew">
        SELECT *
        FROM dwd.dwd_material_mbew node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="materialType != null and materialType != ''">
                and node.material_type in
                <foreach item="item" collection="materialType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialGroup != null and materialGroup != ''">
                and node.material_group in
                <foreach item="item" collection="materialGroup.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialId != null and materialId != ''">
                and node.material_id in
                <foreach item="item" collection="materialId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="evaluateAreaId != null and evaluateAreaId != ''">
                and node.evaluate_area_id in
                <foreach item="item" collection="evaluateAreaId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.material_type, node.material_group, node.material_id ${sort}
    </select>

</mapper>
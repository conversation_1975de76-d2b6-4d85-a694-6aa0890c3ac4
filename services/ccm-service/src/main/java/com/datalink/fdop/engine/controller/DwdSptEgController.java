package com.datalink.fdop.engine.controller;

import com.datalink.fdop.engine.api.domain.DwdSptEg;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdSptEgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/base/dwdSptEg")
@Api(tags = "CCMS-WPH管理（设备组）")
public class DwdSptEgController {

    @Autowired
    private DwdSptEgService dwdSptEgService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "WPH管理（设备组）")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String equipGroupId = MapUtils.getAsString(params, "equipGroupId");
        PageDataInfo<DwdSptEg> overview = dwdSptEgService.overview(plantId, equipGroupId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdSptEg.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "WPH管理（设备组）", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String equipGroupId = MapUtils.getAsString(params, "equipGroupId");
        
        List<DwdSptEg> list = dwdSptEgService.selectNoPage(plantId, equipGroupId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdSptEg> util = new ExcelUtil<>(DwdSptEg.class);
        util.exportExcel(response, list, "设备组均值");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdSptEg> util = new ExcelUtil<>(DwdSptEg.class);
        util.importTemplateExcel(response, "设备组均值");
    }

    @ApiOperation(value = "查询工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwd.dwd_spt_eg", null));
    }

    @ApiOperation(value = "查询设备组（SelectVo）")
    @GetMapping("/listEquipGroupId")
    public R<List<SelectVo>> listEquipGroupId() {
        return R.ok(dwdSptEgService
                .lambdaQuery()
                .select(DwdSptEg::getEquipGroupId, DwdSptEg::getEquipGroupDesc)
                .groupBy(DwdSptEg::getEquipGroupId, DwdSptEg::getEquipGroupDesc)
                .orderByAsc(DwdSptEg::getEquipGroupId).list().stream()
                .filter(dwdSptEg -> dwdSptEg != null && dwdSptEg.getEquipGroupId() != null)
                .map(dwdSptEg -> new SelectVo(dwdSptEg.getEquipGroupId(), dwdSptEg.getEquipGroupDesc()))
                .collect(Collectors.toList()));
    }

}

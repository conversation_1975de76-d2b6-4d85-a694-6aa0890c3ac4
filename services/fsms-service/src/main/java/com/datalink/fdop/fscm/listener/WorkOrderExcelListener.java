package com.datalink.fdop.fscm.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderExcelVo;
import com.datalink.fdop.fscm.execl.AnalysisData;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/9 15:35
 */
public class WorkOrderExcelListener  implements ReadListener<WorkOrderExcelVo>{

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    /**
     * 缓存的数据
     */
    private List<WorkOrderExcelVo> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private AnalysisData analysisData=null;

    public WorkOrderExcelListener(AnalysisData analysisData) {
        this.analysisData=analysisData;
    }


    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(WorkOrderExcelVo data, AnalysisContext context) {
        cachedDataList.add(data);
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        this.analysisData.setWorkOrderExcelVos(cachedDataList);
    }
}

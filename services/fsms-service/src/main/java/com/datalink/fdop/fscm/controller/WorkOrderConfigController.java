package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.WorkOrderConfig;
import com.datalink.fdop.fscm.api.domain.WorkOrderHead;
import com.datalink.fdop.fscm.service.WorkOrderConfigService;
import com.datalink.fdop.fscm.service.WorkOrderHeadService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/21 14:54
 */
@RestController
@RequestMapping("/work/order/config")
@Transactional
@ApiOperation("工单配置")
public class WorkOrderConfigController {

    @Autowired
    private WorkOrderConfigService workOrderConfigService;
    @Autowired
    private WorkOrderHeadService workOrderHeadService;

    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) SearchVo searchVo) {
        Page<WorkOrderConfig> page = PageUtils.getPage(WorkOrderConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderConfig> pageData=workOrderConfigService.page(page,new QueryWrapper<WorkOrderConfig>()
                .lambda()
                .orderByAsc(WorkOrderConfig::getWorkOrderType)
        );
        List<WorkOrderConfig> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
            page.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }


    @ApiOperation(value = "下拉查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/combobox/query")
    public R combobox() {
        Page<WorkOrderConfig> page = PageUtils.getPage(WorkOrderConfig.class);
        Page<WorkOrderConfig> pageData=workOrderConfigService.page(page,new QueryWrapper<WorkOrderConfig>()
                .lambda()
                .orderByAsc(WorkOrderConfig::getWorkOrderType)
                .groupBy(WorkOrderConfig::getWorkOrderType)
        );
        return R.ok(PageUtils.getPageInfo(pageData.getRecords(), (int) pageData.getTotal()));
    }



    @ApiOperation(value = "新增修改")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/createOrUpdate")
    public R create(@RequestBody List<WorkOrderConfig> workOrderConfigs) {
        try {
            if (workOrderConfigService.saveOrUpdateBatch(workOrderConfigs)) {
                return R.ok();
            }else {
                return R.fail("失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("检查数据是否正确!");
        }
    }

    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids) {
        for (Long id : ids) {
            WorkOrderConfig config = workOrderConfigService.getById(id);
            if (config!=null) {
                List<WorkOrderHead> list = workOrderHeadService.list(new QueryWrapper<WorkOrderHead>().lambda().eq(WorkOrderHead::getWorkOrderType,config.getWorkOrderType()));
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new ServiceException("工单类型已有数据，无法删除！");
                }
            }
        }
        if (workOrderConfigService.removeBatchByIds(ids)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody(required = false) SearchVo searchVo) throws IOException {
        Page<WorkOrderConfig> page = PageUtils.getPage(WorkOrderConfig.class);
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderConfig> pageData=workOrderConfigService.page(page,new QueryWrapper<WorkOrderConfig>()
                .lambda()
                .orderByAsc(WorkOrderConfig::getWorkOrderType)
        );
        List<WorkOrderConfig> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
        }
        ExcelUtils.export3Excel(response,records, WorkOrderConfig.class,"工作订单配置");
    }
}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalActualEidExp;

import java.util.List;

public interface DwhFinalActualEidExpService extends IService<DwhFinalActualEidExp> {

    PageDataInfo<DwhFinalActualEidExp> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwhFinalActualEidExp> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);
}
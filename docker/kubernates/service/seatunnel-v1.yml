apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: data-platform
  labels:
    version: v1
    app: seatunnel
  name: seatunnel-v1
spec:
  replicas: 1
  selector:
    matchLabels:
      version: v1
      app: seatunnel
  template:
    metadata:
      labels:
        version: v1
        app: seatunnel
      annotations:
        kubesphere.io/imagepullsecrets: '{"container-gcmnw0":"harbor-data-platform"}'
        logging.kubesphere.io/logsidecar-config: '{}'
    spec:
      containers:
        - name: container-gcmnw0
          imagePullPolicy: Always
          image: 'harbor.semi-tech.com/fsp/data-platform/apache-seatunnel:2.3.10'
          ports:
            - name: http-5801
              protocol: TCP
              containerPort: 5801
            - name: http-18080
              containerPort: 18080
              protocol: TCP
          volumeMounts:
            - name: host-time
              mountPath: /etc/localtime
              readOnly: true
            - name: volume-syzu5f
              mountPath: /opt/soft/seatunnel/logs
            - name: volume-nut0xd
              mountPath: /opt/soft/seatunnel/config/hazelcast.yaml
              subPath: hazelcast.yaml
            - name: volume-a0sgih
              mountPath: /opt/soft/seatunnel/config/jvm_options
              subPath: jvm_options
            - name: volume-393k3o
              mountPath: /opt/soft/seatunnel/config/seatunnel-env.sh
              subPath: seatunnel-env.sh
            - name: volume-f1t55n
              mountPath: /opt/soft/seatunnel/config/seatunnel.yaml
              subPath: seatunnel.yaml
      serviceAccount: default
      terminationGracePeriodSeconds: 30
      initContainers: []
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ''
          name: host-time
        - name: volume-syzu5f
          persistentVolumeClaim:
            claimName: seatunnel-logs
        - name: volume-nut0xd
          configMap:
            name: seatunnel-config
            items:
              - key: hazelcast.yaml
                path: hazelcast.yaml
        - name: volume-a0sgih
          configMap:
            name: seatunnel-config
            items:
              - key: jvm_options
                path: jvm_options
        - name: volume-393k3o
          configMap:
            name: seatunnel-config
            items:
              - key: seatunnel-env.sh
                path: seatunnel-env.sh
        - name: volume-f1t55n
          configMap:
            name: seatunnel-config
            items:
              - key: seatunnel.yaml
                path: seatunnel.yaml
      imagePullSecrets:
        - name: harbor-data-platform
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
---
apiVersion: v1
kind: Service
metadata:
  namespace: data-platform
  labels:
    version: v1
    app: seatunnel
  annotations:
    kubesphere.io/serviceType: statelessservice
    kubesphere.io/description: 数据集成服务
  name: seatunnel
spec:
  sessionAffinity: None
  selector:
    app: seatunnel
  ports:
    - name: http-5801
      protocol: TCP
      port: 5801
      targetPort: 5801
      nodePort: 32007
    - name: http-18080
      protocol: TCP
      port: 18080
      targetPort: 18080
      nodePort: 32008
  type: NodePort
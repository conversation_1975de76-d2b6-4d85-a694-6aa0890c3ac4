package com.datalink.fdop.quality.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2023/6/13 16:06
 */
public enum InspectionType {

    // 入表触发
    INSERT_TABLE(0, "INSERT_TABLE"),
    // 同步检测
    SYNCHRONOUS(1, "SYNCHRONOUS"),
    // 规则监控
    RULE_MONITORING(2, "RULE_MONITORING"),
    ;

    InspectionType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final int code;
    @EnumValue
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}

package com.datalink.fdop.engine.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-02-13 15:27
 */
@Data
public class ConsumptionHead {

    private String verId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long yearMonth;
    private String factoryId;
    private String materialId;
    private String materialDesc;
    private BigDecimal consumeQty;
    private BigDecimal consumeAmount;
    private BigDecimal adjustQty;
    private BigDecimal adjustAmount;
    private BigDecimal costQty;
    private BigDecimal costAmount;
    private BigDecimal costValue;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
    private String actionFlag;

}

package com.datalink.fdop.data.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.domain.BaseCopy;
import com.datalink.fdop.common.core.web.page.TableDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.data.api.domain.DataServe;
import com.datalink.fdop.data.service.DataPreviewService;
import com.datalink.fdop.data.service.DataServeService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/5/16 10:40
 */
@RestController
@RequestMapping("/data/share")
public class DataServeController extends BaseController {

    @Autowired
    public DataServeService dataServeService;

    @ApiOperation(value = "创建修改服务API")
    @Log(title = "数据服务API模块", businessType = BusinessType.INSERT_OR_UPDATE)
    @PostMapping(value = "/saveOrUpdate")
    public R saveOrUpdate( @Validated @RequestBody List<DataServe> dataServes) {
        return dataServeService.saveOrUpdate(dataServes);
    }

    @ApiOperation(value = "创建服务API")
    @Log(title = "数据服务API模块", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataServe dataServe) {
        return R.ok(dataServeService.create(dataServe));
    }

    @ApiOperation(value = "修改服务API")
    @Log(title = "修改服务API模块", businessType = BusinessType.INSERT)
    @PostMapping(value = "/update")
    public R update(@Validated @RequestBody DataServe dataServe) {
        return R.ok(dataServeService.update(dataServe));
    }


    @ApiOperation(value = "删除服务API")
    @Log(title = "数据服务API模块", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/del")
    public R del(@RequestBody List<Long> ids) {
        return dataServeService.del(ids);
    }

    @ApiOperation(value = "查询服务API")
    @Log(title = "数据管理模块", businessType = BusinessType.OTHER)
    @PostMapping(value = "/query")
    public TableDataInfo queryDataShare(@Validated @RequestBody(required = false) DataServe dataServe) {
        startPage();
        List<DataServe> list= dataServeService.queryDataShare(dataServe);
        return getDataTable(list);
    }

    @ApiOperation("复制服务API")
    @Log(title = "数据管理模块", businessType = BusinessType.INSERT)
    @PostMapping(value = "/copy/{pid}")
    public R copy(@PathVariable("pid") Long pid, @Validated @RequestBody List<BaseCopy> dataServeList) {
        dataServeService.copy(pid, dataServeList);
        return R.ok();
    }

    @ApiOperation("根据id查服务API")
    @Log(title = "数据服务API")
    @GetMapping(value = "/selectById/{id}")
    public R<DataServe> selectById(@PathVariable(value = "id") Long id) {
        return R.ok(dataServeService.selectById(id));
    }



}

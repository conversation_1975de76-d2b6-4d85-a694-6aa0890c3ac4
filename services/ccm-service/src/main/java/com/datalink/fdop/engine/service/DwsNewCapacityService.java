package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewCapacity;

import java.util.List;

public interface DwsNewCapacityService extends IService<DwsNewCapacity> {

    PageDataInfo<DwsNewCapacity> overview(
            String verId, String plantId, String sort, SearchVo searchVo);

    List<DwsNewCapacity> listAll();

    List<DwsNewCapacity> selectNoPage(String verId, String plantId, String sort, SearchVo searchVo);
}

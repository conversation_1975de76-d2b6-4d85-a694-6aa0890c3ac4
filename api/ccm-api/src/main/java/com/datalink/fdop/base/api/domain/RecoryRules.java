package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022-11-07 14:59
 */
@Data
@TableName(schema = "zjdata", value = "ccm_recory_rules")
@ApiModel
public class RecoryRules {

    @Excel(name = "管理范围", sort = 1)
    @ApiModelProperty(value = "管理范围")
    @NotBlank(message = "管理范围不能为空")
    private String controlAreaId;

    @ApiModelProperty(value = "管理范围描述")
    @TableField(exist = false)
    private String controlAreaDesc;

    @Excel(name = "批次类型", sort = 2)
    @ApiModelProperty(value = "批次类型")
    private String lotType;

    @Excel(name = "批次类型描述", sort = 3)
    @ApiModelProperty(value = "批次类型描述")
    private String lotTypeDesc;

    @Excel(name = "批次类型文本", sort = 4)
    @ApiModelProperty(value = "批次类型文本")
    private String lotTypeText;

    @Excel(name = "批次类型组", sort = 5)
    @ApiModelProperty(value = "批次类型组")
    private String lotTypeGroup;

    @Excel(name = "批次类型组描述", sort = 6)
    @ApiModelProperty(value = "批次类型组描述")
    private String lotTypeGroupDesc;

    @Excel(name = "工单类型", sort = 7)
    @ApiModelProperty(value = "工单类型")
    private String workOrderType;

    @Excel(name = "工单类型描述", sort = 8)
    @ApiModelProperty(value = "工单类型描述")
    private String workOrderDesc;

    @Excel(name = "工单类型组", sort = 9)
    @ApiModelProperty(value = "工单类型组")
    private String workOrderGroup;

    @Excel(name = "工单类型组描述", sort = 10)
    @ApiModelProperty(value = "工单类型组描述")
    private String workOrderGroupDesc;

    @Excel(name = "计量：在制", sort = 11)
    @ApiModelProperty(value = "计量：在制")
    private Boolean forWip;

    @Excel(name = "回收方法", sort = 12)
    @ApiModelProperty(value = "回收方法")
    private String recoryMethod;

    @Excel(name = "结算类型", sort = 13)
    @ApiModelProperty(value = "结算类型")
    private String settlementType;

    @Excel(name = "是否启用", sort = 14, readConverterExp = "true=启用,false=不启用")
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "创建人")
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更改人")
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;

}

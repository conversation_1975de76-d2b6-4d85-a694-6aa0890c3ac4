package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalSetEnt;
import com.datalink.fdop.engine.mapper.DwhFinalSetEntMapper;
import com.datalink.fdop.engine.service.DwhFinalSetEntService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalSetEntServiceImpl extends ServiceImpl<DwhFinalSetEntMapper, DwhFinalSetEnt> implements DwhFinalSetEntService {

    @Autowired
    private DwhFinalSetEntMapper dwhFinalSetEntMapper;

    @Override
    public PageDataInfo<DwhFinalSetEnt> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwhFinalSetEnt> page = PageUtils.getPage(DwhFinalSetEnt.class);
        IPage<DwhFinalSetEnt> iPage = dwhFinalSetEntMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalSetEnt> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwhFinalSetEntMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}
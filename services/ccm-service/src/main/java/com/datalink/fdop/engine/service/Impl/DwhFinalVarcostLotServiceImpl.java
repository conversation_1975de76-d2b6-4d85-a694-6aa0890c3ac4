package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalVarcostLot;
import com.datalink.fdop.engine.api.domain.DwsNewVarcostLot;
import com.datalink.fdop.engine.mapper.DwhFinalVarcostLotMapper;
import com.datalink.fdop.engine.mapper.DwsNewVarcostLotMapper;
import com.datalink.fdop.engine.service.DwhFinalVarcostLotService;
import com.datalink.fdop.engine.service.DwsNewVarcostLotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalVarcostLotServiceImpl extends ServiceImpl<DwhFinalVarcostLotMapper, DwhFinalVarcostLot> implements DwhFinalVarcostLotService {

    @Autowired
    private DwhFinalVarcostLotMapper dwhFinalVarcostLotMapper;

    @Override
    public PageDataInfo<DwhFinalVarcostLot> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwhFinalVarcostLot> page = PageUtils.getPage(DwhFinalVarcostLot.class);
        IPage<DwhFinalVarcostLot> iPage = dwhFinalVarcostLotMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalVarcostLot> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwhFinalVarcostLotMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}
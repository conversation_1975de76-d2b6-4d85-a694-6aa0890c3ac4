<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.RatioMapper">

    <select id="selectByPid" resultType="com.datalink.fdop.base.api.domain.AllocationScale">
        SELECT ls.*, bk.code as kostl_code, bk.abbreviation as kostl_abbreviation
        FROM zjdata.p_d_rule_ratio ls left join zjdata.p_d_kostl bk on ls.kostl_id = bk.id
        where ls.pid = #{pid}
    </select>

    <delete id="deleteByPid">
        delete from zjdata.p_d_rule_ratio
        where pid = #{pid}
    </delete>

</mapper>


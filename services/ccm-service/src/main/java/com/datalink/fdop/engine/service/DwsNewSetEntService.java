package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewSetEnt;

import java.util.List;

public interface DwsNewSetEntService extends IService<DwsNewSetEnt> {

    PageDataInfo<DwsNewSetEnt> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo);

    List<DwsNewSetEnt> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo);
}
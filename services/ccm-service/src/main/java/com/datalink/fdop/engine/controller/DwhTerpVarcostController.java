package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.base.service.CostStructureHeadService;
import com.datalink.fdop.base.service.FactoryService;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhTerpVarcost;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhTerpVarcostService;
import com.datalink.fdop.engine.utils.DynamicExcelExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwhTerpVarcost")
@Api(tags = "交互视图-计算约当分配")
public class DwhTerpVarcostController extends BaseController {

    @Autowired
    private DwhTerpVarcostService dwhTerpVarcostService;

    @Autowired
    private CostStructureHeadService costStructureHeadService;

    @Autowired
    private FactoryService factoryService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "交互视图-计算约当分配")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params , "verId");
        String plantId = MapUtils.getAsString(params , "plantId");
        String yearMonth = MapUtils.getAsString(params , "yearMonth");
        PageDataInfo<DwhTerpVarcost> overview = dwhTerpVarcostService.overview(verId, plantId, yearMonth, sort, queryParam.getSearchVo());
        String controlAreaId = factoryService.getControlAreaIdByPlantId(plantId);
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicOsColumnList(controlAreaId);
        return R.ok(MetaUtils.getMetadata(DwhTerpVarcost.class, overview, dynamicColumns));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "定版视图-计算约当分配", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params , "verId");
        String plantId = MapUtils.getAsString(params , "plantId");
        String yearMonth = MapUtils.getAsString(params , "yearMonth");
        List<DwhTerpVarcost> list = dwhTerpVarcostService.selectNoPage(verId, plantId, yearMonth,
                sort, queryParam.getSearchVo());
        String controlAreaId = factoryService.getControlAreaIdByPlantId(plantId);
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicOsColumnList(controlAreaId);
        DynamicExcelExportUtil<DwhTerpVarcost> excelUtil = new DynamicExcelExportUtil<>(DwhTerpVarcost.class);
        excelUtil.exportExcelWithDynamicColumns(response, list, dynamicColumns, "约当分配管理-交互视图数据");

    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhTerpVarcost> util = new ExcelUtil<>(DwhTerpVarcost.class);
        util.importTemplateExcel(response, "约当分配管理-交互视图模板");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_terp_varcost", null));
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwh.dwh_terp_varcost", null));
    }

}
package com.datalink.fdop.quality.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.quality.api.enums.DataQualityLogStatus;
import com.datalink.fdop.quality.api.enums.DataQualityStorageType;
import com.datalink.fdop.quality.api.enums.InspectionType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/11 10:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "zjdata", value = "d_q_data_quality_log")
public class DataQualityLog {

    @ApiModelProperty(value = "规则日志id", required = true)
    @TableId(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "规则调用状态")
    private DataQualityLogStatus dataQualityLogStatus;

    @ApiModelProperty(value = "工作流实例id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processInstanceId;

    @ApiModelProperty(value = "工作流实例名称")
    @TableField(exist = false)
    private String processInstanceName;

    @ApiModelProperty(value = "任务实例id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskInstanceId;

    @ApiModelProperty(value = "任务实例名称")
    @TableField(exist = false)
    private String taskInstanceName;

    @ApiModelProperty(value = "调用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date callTime;

    @ApiModelProperty(value = "所属任务code")
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private Long taskDefinitionCode;

    @ApiModelProperty(value = "所属任务名称")
    @TableField(exist = false)
    private String taskDefinitionName;

    @ApiModelProperty(value = "所属表/实体")
    private DataQualityStorageType storageType;

    @ApiModelProperty(value = "绑定的实体id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long entityId;

    @ApiModelProperty(value = "所属表/实体的名称")
    private String storageName;

    @ApiModelProperty(value = "检验类型")
    private InspectionType inspectionType;

    @ApiModelProperty(value = "规则监控ID")
    private String monitorId;

    @ApiModelProperty(value = "错误日志")
    private String errorLog;

    @ApiModelProperty(value = "错误状态")
    private Boolean errorStatus;

    @TableField(exist = false)
    @ApiModelProperty(value = "高级搜索条件")
    private SearchVo searchCondition;

}

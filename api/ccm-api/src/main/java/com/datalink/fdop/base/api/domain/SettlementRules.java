package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022-11-07 14:59
 */
@Data
@TableName(schema = "zjdata", value = "ccm_settlement_rules")
@ApiModel
public class SettlementRules {

    @ApiModelProperty(value = "管理范围")
    @NotBlank(message = "管理范围不能为空")
    @Excel(name = "管理范围",sort = 1)
    private String controlAreaId;

    @TableField(exist = false)
    @ApiModelProperty(value = "管理范围描述")
    private String controlAreaDesc;

    @ApiModelProperty(value = "结算类型")
    @NotBlank(message = "结算类型不能为空")
    @Excel(name = "结算类型",sort = 2)
    private String settlementType;

    @ApiModelProperty(value = "借方科目")
    @Excel(name = "借方科目",sort = 3)
    private String drAccount;

    @ApiModelProperty(value = "借方成本单元")
    @Excel(name = "借方成本中心",sort = 4)
    private String drCostCenter;

    @ApiModelProperty(value = "借方项目号码")
    @Excel(name = "借方项目号码",sort = 5)
    private String drProject;


    @ApiModelProperty(value = "贷方科目")
    @Excel(name = "贷方科目",sort = 6)
    private String crAccount;

    @ApiModelProperty(value = "贷方成本单元")
    @Excel(name = "贷方成本单元",sort = 7)
    private String crCostCenter;

    @ApiModelProperty(value = "贷方项目号码")
    @Excel(name = "贷方项目号码",sort = 8)
    private String crProject;

    @ApiModelProperty(value = "是否启用")
    @Excel(name = "是否启用", sort = 9, readConverterExp = "true=启用,false=不启用")
    private Boolean enable;

    @ApiModelProperty(value = "创建人")
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更改人")
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;

}

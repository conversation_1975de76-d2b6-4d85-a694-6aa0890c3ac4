package com.datalink.fdop.graph.api;

import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.graph.api.dto.TaskInfo;
import com.datalink.fdop.graph.api.factory.RemoteFlinkGraphlFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "remoteFlinkGraphService", path = "/graph/etl",
        value = ServiceNameConstants.DATA_SERVICE, fallbackFactory = RemoteFlinkGraphlFallbackFactory.class)
public interface RemoteFlinkGraphService {

    @PostMapping(value = "/submitFlinkCluster")
    public R<String> submitFlinkCluster(@RequestBody TaskInfo taskInfo);

}

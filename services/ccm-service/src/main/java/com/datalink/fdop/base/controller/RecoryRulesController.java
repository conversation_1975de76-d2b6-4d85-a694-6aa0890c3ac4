package com.datalink.fdop.base.controller;

import com.datalink.fdop.base.api.domain.EvaluateArea;
import com.datalink.fdop.base.api.domain.RecoryRules;
import com.datalink.fdop.base.api.domain.SpecialMat;
import com.datalink.fdop.base.service.RecoryRulesService;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-03-13 11:36
 */
@RestController
@RequestMapping("/base/recoryRules")
@Api(tags = "fccm-回收规则API")
public class RecoryRulesController extends BaseController {

    @Autowired
    private RecoryRulesService recoryRulesService;

    @ApiOperation("查")
    @Log(title = "回收规则")
    @PostMapping("/query")
    public R<PageDataInfo> query(@RequestParam(required = false, defaultValue = "ASC") String sort,
                                 @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(recoryRulesService.query(sort, searchVo));
    }

    @ApiOperation("增")
    @Log(title = "回收规则")
    @PostMapping("/add")
    public R add(@Validated @RequestBody RecoryRules recoryRules) {
        return R.ok(recoryRulesService.add(recoryRules));
    }

    @ApiOperation("改")
    @Log(title = "回收规则")
    @PutMapping("/update")
    public R update(@Validated @RequestBody RecoryRules recoryRules) {
        return R.ok(recoryRulesService.update(recoryRules));
    }

    @ApiOperation("删")
    @Log(title = "回收规则")
    @DeleteMapping("/del")
    public R delete(@Validated @RequestBody List<RecoryRules> list) {
        return R.ok(recoryRulesService.delete(list));
    }

    @ApiOperation("全查")
    @Log(title = "回收规则")
    @GetMapping("/queryAll")
    public R queryAll() {
        return R.ok(recoryRulesService.queryAll());
    }

    @ApiOperation(value = "批量启用/禁用")
    @Log(title = "回收规则", businessType = BusinessType.UPDATE)
    @PostMapping("/batchStatus")
    public R batchStatus(@RequestBody List<RecoryRules> statusList) {
        return R.ok(recoryRulesService.updateBatchStatus(statusList));
    }

    @ApiOperation(value = "导入数据")
    @Log(title = "回收规则", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R importData(MultipartFile file, @RequestParam(value = "updateSupport", required = false) Boolean updateSupport) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        ExcelUtil<RecoryRules> util = new ExcelUtil<>(RecoryRules.class);
        List<RecoryRules> list = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        return R.ok(recoryRulesService.importData(list, updateSupport, operName));
    }

    @ApiOperation(value = "导出模板")
    @Log(title = "回收规则", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<RecoryRules> util = new ExcelUtil<>(RecoryRules.class);
        util.importTemplateExcel(response, "回收规则模板");
    }

    @ApiOperation("导出数据")
    @Log(title = "回收规则", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "DESC") String sort) {
        List<RecoryRules> list = recoryRulesService.list();
        ExcelUtil<RecoryRules> util = new ExcelUtil<>(RecoryRules.class);
        util.exportExcel(response, list, "回收规则数据");
    }

    @ApiOperation(value = "工单类型组和描述（下拉框）")
    @GetMapping("/listWorkOrderGroup")
    public R<List<SelectVo>> listWorkOrderGroup() {
        return R.ok(recoryRulesService.lambdaQuery()
                .select(RecoryRules::getWorkOrderGroup, RecoryRules::getWorkOrderGroupDesc)
                .groupBy(RecoryRules::getWorkOrderGroup, RecoryRules::getWorkOrderGroupDesc)
                .eq(RecoryRules::getEnable, true)
                .orderByAsc(RecoryRules::getWorkOrderGroup)
                .list().stream()
                .filter(specialMat -> specialMat != null && specialMat.getWorkOrderGroup() != null)
                .map(specialMat -> new SelectVo(specialMat.getWorkOrderGroup(), specialMat.getWorkOrderGroupDesc()))
                .collect(Collectors.toList()));
    }


}

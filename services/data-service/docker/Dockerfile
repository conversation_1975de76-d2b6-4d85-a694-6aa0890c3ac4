FROM harbor.semi-tech.com/fsp/data-platform/java8-python3-base:latest

# 安装字体库依赖，解决 POI Excel 导出字体问题
RUN apt-get update && apt-get install -y --no-install-recommends fontconfig libfreetype6 fonts-dejavu-core fonts-liberation && apt-get clean && rm -rf /var/lib/apt/lists/* && fc-cache -fv

VOLUME /tmp

COPY ./InterfaceDocumentation.doc /opt/

#RUN mkdir -p $SEATUNNEL_HOME/dwms/

RUN mkdir -p /plugins/drive-center/2.0.0
COPY ./target/plugins/fdop-datasource-*.jar /plugins/drive-center/2.0.0
COPY ./target/data-service-1.0.0-repackage.jar /

ENV TZ=Asia/Shanghai

ENV JAVA_OPTS="-Xms2048m -Xmx10240m -Djava.awt.headless=true"

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -Dfile.encoding=utf-8 -Djava.security.egd=file:/dev/./urandom -jar /data-service-1.0.0-repackage.jar \
--nacos.server.addr=${NACOS_SERVER} \
--nacos.config.group=${NACOS_GROUP} \
--nacos.server.group=${NACOS_GROUP} \
"]
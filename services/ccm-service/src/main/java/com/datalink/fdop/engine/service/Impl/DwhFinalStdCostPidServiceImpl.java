package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostPid;
import com.datalink.fdop.engine.mapper.DwhFinalStdCostPidMapper;
import com.datalink.fdop.engine.service.DwhFinalStdCostPidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalStdCostPidServiceImpl extends ServiceImpl<DwhFinalStdCostPidMapper, DwhFinalStdCostPid> implements DwhFinalStdCostPidService {

    @Autowired
    private DwhFinalStdCostPidMapper dwhFinalStdCostPidMapper;

    @Override
    public PageDataInfo<DwhFinalStdCostPid> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwhFinalStdCostPid> page = PageUtils.getPage(DwhFinalStdCostPid.class);
        IPage<DwhFinalStdCostPid> iPage = dwhFinalStdCostPidMapper.selectAll(controlAreaId, productId, verId, plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalStdCostPid> selectNoPage(String controlAreaId, String productId, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwhFinalStdCostPidMapper.selectNoPage(controlAreaId, productId, verId, plantId, date, sort, searchVo);
    }
}
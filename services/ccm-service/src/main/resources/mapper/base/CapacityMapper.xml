<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CapacityMapper">

    <sql id="selectSql">
        SELECT COALESCE(c.factory_id, co.factory_id)             AS factory_id,
               COALESCE(c.capacity_id, co.capacity_id)           AS capacity_id,
               COALESCE(c.equip_id, co.equip_id)                 AS equip_id,
               COALESCE(c.chamber_id, co.chamber_id)             AS chamber_id,
               COALESCE(c.equip_group_id, co.equip_group_id)     AS equip_group_id,
               COALESCE(c.rp_flag, co.rp_flag)                   AS rp_flag,
               COALESCE(c.date_from, co.date_from)               AS date_from,
               COALESCE(c.date_to, co.date_to)                   AS date_to,
               COALESCE(c.equip_group_desc, co.equip_group_desc) AS equip_group_desc,
               COALESCE(c.equip_type, co.equip_type)             AS equip_type,
               COALESCE(c.equip_group_flag, co.equip_group_flag) AS equip_group_flag,
               COALESCE(c.work_center_id, co.work_center_id)     AS work_center_id,
               COALESCE(c.work_cost_center, co.work_cost_center) AS work_cost_center,
               COALESCE(c.equip_desc, co.equip_desc)             AS equip_desc,
               COALESCE(c.work_area, co.work_area)               AS work_area,
               COALESCE(c.menu_id, co.menu_id)                   AS menu_id,
               COALESCE(c.enable, co.enable)                     AS enable,
               COALESCE(c.create_by, co.create_by)               AS create_by,
               COALESCE(c.create_time, co.create_time)           AS create_time,
               COALESCE(c.update_by, co.update_by)               AS update_by,
               COALESCE(c.update_time, co.update_time)           AS update_time,
               COALESCE(c.delete_flag, false)                    AS delete_flag
        FROM zjdata.ccm_capacity_orig co FULL OUTER JOIN zjdata.ccm_capacity c
        ON c.factory_id = co.factory_id
            and c.capacity_id = co.capacity_id and c.equip_group_id = co.equip_group_id
    </sql>

    <update id="deleteByKey">
        update zjdata.ccm_capacity
        set delete_flag = true
        where (factory_id, capacity_id, equip_group_id) in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            (#{item.factoryId}, #{item.capacityId}, #{item.equipGroupId})
        </foreach>
    </update>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        select (SELECT COUNT(1)
                FROM zjdata.ccm_capacity
                where factory_id = #{factoryId}
                  AND capacity_id = #{capacityId}
                  and equip_group_id = #{equipGroupId}
                  and delete_flag = false)
                   +
               (SELECT COUNT(1)
                FROM zjdata.ccm_capacity_orig
                where factory_id = #{factoryId}
                  AND capacity_id = #{capacityId}
                  and equip_group_id = #{equipGroupId})
    </select>

    <delete id="deleteById" parameterType="String">
        delete
        from zjdata.ccm_capacity
        where (factory_id, capacity_id, equip_group_id) = (#{factoryId}, #{capacityId}, #{equipGroupId})
    </delete>

    <select id="checkIdExist" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_capacity
        where factory_id = #{factoryId}
          AND capacity_id = #{capacityId}
          and equip_group_id = #{equipGroupId}
          and delete_flag = false
        limit 1
    </select>

    <!--    <update id="deleteById" parameterType="String">-->
    <!--        update zjdata.ccm_cost_center-->
    <!--        set delete_flag = true-->
    <!--        where cost_center_id = #{id}-->
    <!--          AND date_to = '${date}'-->
    <!--          and control_area_id = #{controlAreaId}-->
    <!--    </update>-->


    <!--    <select id="list" parameterType="com.datalink.fdop.base.api.domain.CostCenter" resultType="com.datalink.fdop.base.api.domain.CostCenter">-->
    <!--        SELECT a.*, company.company_desc as company_desc, evaluateArea.evaluate_area_desc as evaluate_area_desc, menu.menu_desc as menu_desc,area.control_area_desc as control_area_desc-->
    <!--        FROM zjdata.ccm_capacity a left join zjdata.ccm_company company on a.company_id = company.company_id-->
    <!--        left join zjdata.ccm_control_area area on a.control_area_id = area.control_area_id-->
    <!--        left join zjdata.ccm_cost_center_menu menu on a.menu_id = menu.menu_id-->
    <!--        <where>-->
    <!--            <if test="costCenter.costCenterId != null and costCenter.costCenterId != ''">-->
    <!--                AND a.cost_center_id = #{costCenter.costCenterId}-->
    <!--            </if>-->
    <!--            <if test="costCenter.menuId != null and costCenter.menuId != ''">-->
    <!--                AND a.menu_id = #{costCenter.menuId}-->
    <!--            </if>-->
    <!--            <if test="costCenter.dateFrom != null">-->
    <!--                AND a.date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(costCenter.dateFrom)}'-->
    <!--            </if>-->
    <!--        </where>-->
    <!--        ORDER BY cost_center_id, date_to,control_area_id desc-->
    <!--    </select>-->

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.Capacity">
        SELECT a.*, factory.factory_desc as factory_desc, menu.menu_desc as menu_desc
        FROM (<include refid="selectSql"/>) a left join zjdata.ccm_factory factory on a.factory_id = factory.factory_id
        left join zjdata.ccm_capacity_menu menu on a.menu_id = menu.menu_id
        where a.factory_id = #{factoryId} and a.capacity_id =#{capacityId} and a.equip_group_id =#{equipGroupId} and
        a.delete_flag = false
    </select>

    <select id="selectIdsByPid" resultType="com.datalink.fdop.base.api.domain.Capacity">
        select factory_id, capacity_id, equip_group_id from (<include refid="selectSql"/>) a where a.menu_id = #{pid}
        and a.delete_flag = false
    </select>

    <update id="bacthUpdatePidById">
        update zjdata.ccm_capacity
        set menu_id = (select p_menu_id from zjdata.ccm_cost_center_menu where menu_id = #{menuId} limit 1)
        where menu_id = #{menuId}
    </update>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.Capacity">
        update zjdata.ccm_capacity
        set
        <if test="dateTo != null">
            date_to = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateTo)}',
        </if>
        <if test="dateFrom != null">
            date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}',
        </if>
        <if test="chamberId != null">chamber_id = #{chamberId},</if>
        <if test="equipId != null">equip_id = #{equipId},</if>
        <if test="rpFlag != null">rp_flag = #{rpFlag},</if>
        <if test="equipGroupDesc != null">equip_group_desc = #{equipGroupDesc},</if>
        <if test="equipType != null">equip_type = #{equipType},</if>
        <if test="equipGroupFlag != null">equip_group_flag = #{equipGroupFlag},</if>
        <if test="workCenterId != null">work_center_id = #{workCenterId},</if>
        <if test="workCostCenter != null">work_cost_center = #{workCostCenter},</if>
        <if test="menuId != null">menu_id = #{menuId},</if>
        <if test="enable != null">enable = #{enable},</if>
        <if test="equipDesc != null">equip_desc = #{equipDesc},</if>
        <if test="workArea != null">work_area = #{workArea},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where factory_id = #{factoryId} and capacity_id =#{capacityId} and equip_group_id =#{equipGroupId}
    </update>

    <sql id="selectListSql">
        select
        node.*,
        factory.factory_desc as factory_desc,
        menu.name as menu_desc
        from
        (<include refid="selectSql"/>) node
        left join zjdata.ccm_factory factory on node.factory_id = factory.factory_id
        left join zjdata.ccm_capacity_menu menu on node.menu_id = menu.menu_id
        <where>
            and node.delete_flag = false
            <if test="menuId != null and menuId != '-1'">
                AND node.menu_id = #{menuId}
            </if>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY node.capacity_id ${sort}
    </sql>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.Capacity">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.Capacity">
        <include refid="selectListSql"/>
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_capacity
        SET enable = #{enable}
        where (factory_id, capacity_id, equip_group_id) in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            (#{item.factoryId}, #{item.capacityId}, #{item.equipGroupId})
        </foreach>
    </update>


    <select id="selectNodeTree" resultType="com.datalink.fdop.base.api.model.CapacityTree">
        select capacity_id as id, menu_id as pid, factory_id as factoryId, equip_group_id as equipGroupId, 'NODE' as
        menuType, equip_group_id as name, equip_group_desc as description from (<include refid="selectSql"/>) a
        <where>
            a.delete_flag = false
            <if test="code != null and code!= ''">
                and a.capacity_id like concat('%', #{code}, '%')
            </if>
        </where>
        ORDER BY id ${sort}
    </select>

    <select id="listAll" resultType="java.lang.String">
        select distinct capacity_id from (<include refid="selectSql"/>) a
        where a.enable = true and a.delete_flag = false
        <if test="capacityId !=null and capacityId !=''">
            and a.capacity_id =#{capacityId}
        </if>
    </select>

    <update id="batchUpdatePidByIds">
        update zjdata.ccm_capacity
        set menu_id = #{pid}
        where (factory_id, capacity_id, equip_group_id) in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            (#{item.factoryId}, #{item.capacityId}, #{item.equipGroupId})
        </foreach>
    </update>

    <select id="selectOrigProjectsByPid" resultType="com.datalink.fdop.base.api.domain.Capacity">
        select *
        from zjdata.ccm_capacity_orig
        where menu_id = #{menuId}
    </select>

    <select id="selectEquipId" resultType="java.lang.String">
        select distinct equip_id from (
        <include refid="selectSql"/>
        ) node
        where node.delete_flag = false and node.enable = true
    </select>

    <select id="selectEquipGroup" resultType="java.lang.String">
        select distinct equip_group_id from (
        <include refid="selectSql"/>
        ) node
        where node.delete_flag = false and node.enable = true
    </select>

    <select id="queryOrigList" resultType="com.datalink.fdop.base.api.domain.Capacity">
        select * from zjdata.ccm_capacity_orig node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY factory_id, capacity_id, equip_group_id ${sort}
    </select>

    <select id="queryList" resultType="com.datalink.fdop.base.api.domain.Capacity">
        select * from zjdata.ccm_capacity node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY factory_id, capacity_id, equip_group_id ${sort}
    </select>

    <delete id="batchDelete">
        delete from zjdata.ccm_capacity
        where (factory_id, capacity_id, equip_group_id) in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            (#{item.factoryId}, #{item.capacityId}, #{item.equipGroupId})
        </foreach>
    </delete>


</mapper>


package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostLayer;
import com.datalink.fdop.engine.mapper.DwhFinalStdCostLayerMapper;
import com.datalink.fdop.engine.service.DwhFinalStdCostLayerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalStdCostLayerServiceImpl extends ServiceImpl<DwhFinalStdCostLayerMapper, DwhFinalStdCostLayer> implements DwhFinalStdCostLayerService {

    @Autowired
    private DwhFinalStdCostLayerMapper dwhFinalStdCostLayerMapper;

    @Override
    public PageDataInfo<DwhFinalStdCostLayer> overview(
            String plantId, String verId, String controlAreaId, String productId,
            String date,String sort, SearchVo searchVo) {
        Page<DwhFinalStdCostLayer> page = PageUtils.getPage(DwhFinalStdCostLayer.class);
        IPage<DwhFinalStdCostLayer> iPage = dwhFinalStdCostLayerMapper.selectAll(plantId, verId, controlAreaId, productId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalStdCostLayer> selectNoPage(String plantId, String verId, String controlAreaId, String productId, String date, String sort, SearchVo searchVo) {
        return dwhFinalStdCostLayerMapper.selectNoPage(plantId, verId, controlAreaId, productId, date, sort, searchVo);
    }
}
package com.datalink.fdop.project.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.OrderRow;
import com.datalink.fdop.project.service.OrderRowService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/24 14:18
 */
@RestController
@RequestMapping("/row")
public class OrderRowController {


    @Autowired
    private OrderRowService orderRowService;



    @ApiOperation(value = "行查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/find")
    public R find(@RequestBody(required = false) OrderRow orderRow) {
        Page<OrderRow> page = PageUtils.getPage(OrderRow.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (orderRow.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<OrderRow> rowPage = orderRowService.page(page, new QueryWrapper<OrderRow>()
                .lambda()
                .eq(StringUtils.isNotEmpty(orderRow.getOrderNum()), OrderRow::getOrderNum, orderRow.getOrderNum())
                .eq(orderRow.getOrderRowNum() != null, OrderRow::getOrderRowNum, orderRow.getOrderRowNum())
                .orderByAsc(OrderRow::getOrderNum)
                .orderByAsc(OrderRow::getOrderRowNum)
        );
        List<OrderRow> list =rowPage.getRecords();
        list.stream().forEach(row -> {
            String materialCode = row.getMaterialCode();
            row.setMaterialDesc(orderRowService.findDesc(materialCode));
            String stockPCode = row.getStockPCode();
            row.setStockPDescription(orderRowService.findSPDesc(stockPCode));
        });
        if (orderRow.getSearchVo()!=null) {
            list = SearchUtils.getByEntityFilter(orderRow.getSearchVo(), list);
            rowPage.setTotal(list.size());
            list=list.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(list,(int)rowPage.getTotal()));
    }

}

package com.datalink.fdop.permissions.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.permissions.api.domain.Permissions;
import com.datalink.fdop.permissions.api.domain.PermissionsMenu;
import com.datalink.fdop.permissions.api.enums.PermissionsType;
import com.datalink.fdop.permissions.api.model.vo.PermissionsTree;
import com.datalink.fdop.permissions.service.IPermissionsMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/20 15:43
 */
@RequestMapping(value = "/permissions/menu/built")
@RestController
@Api(tags = "内置权限菜单api")
public class PermissionsMenuController extends BaseController {

    @Autowired
    private IPermissionsMenuService permissionsMenuService;

    @ApiOperation("添加权限对象菜单")
    @Log(title = "内置权限", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addPermissionsMenu")
    public R addPermissionsMenu(@Validated @RequestBody PermissionsMenu permissionsMenu) {
        return R.toResult(permissionsMenuService.addPermissionsMenu(permissionsMenu));
    }

    @ApiOperation("修改权限对象菜单")
    @Log(title = "内置权限", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updatePermissionsMenu")
    public R updatePermissionsMenu(@RequestBody PermissionsMenu permissionsMenu) {
        return R.toResult(permissionsMenuService.updatePermissionsMenu(permissionsMenu));
    }

    @ApiOperation("删除权限对象菜单")
    @Log(title = "内置权限", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/deletePermissionsMenu")
    public R deletePermissionsMenu(@RequestBody List<Long> ids) {
        return R.toResult(permissionsMenuService.deletePermissionsMenu(ids));
    }

    @ApiOperation("权限对象树结构")
    @Log(title = "内置权限")
    @GetMapping(value = "/tree")
    public R<List<PermissionsTree>> tree(
            @RequestParam(value = "permissionsType") PermissionsType permissionsType,
            @RequestParam(value = "sort", required = false, defaultValue = "ASC") String sort,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "isQueryNode", required = false, defaultValue = "true") Boolean isQueryNode) {
        return R.ok(permissionsMenuService.tree(permissionsType, sort, code, isQueryNode));
    }

    @ApiOperation("内置权限总览")
    @Log(title = "内置权限")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<Permissions>> overview(@RequestParam(value = "permissionsType") PermissionsType permissionsType,
                                                 @RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                                 @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                 @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(permissionsMenuService.overview(permissionsType, pid, sort, searchVo));
    }

}

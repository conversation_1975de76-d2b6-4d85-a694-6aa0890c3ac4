package com.datalink.fdop.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 网关启动程序
 *
 * <AUTHOR>
 */
@EnableFeignClients
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class GatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  网关启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "  _____ ______ __  __ _____ \n" +
                " / ____|  ____|  \\/  |_   _|\n" +
                "| (___ | |__  | \\  / | | |  \n" +
                " \\___ \\|  __| | |\\/| | | |  \n" +
                " ____) | |____| |  | |_| |_ \n" +
                "|_____/|______|_|  |_|_____|\n");
    }
    
}

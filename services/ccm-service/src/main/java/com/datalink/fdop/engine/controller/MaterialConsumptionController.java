package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.engine.api.domain.MaterialConsumption;
import com.datalink.fdop.engine.service.MaterialConsumptionService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:31
 */
@RestController
@RequestMapping("/fccm/MaterialConsumption")
public class MaterialConsumptionController {

    @Autowired
    private MaterialConsumptionService materialConsumptionService;

    @ApiOperation("查询原表")
    @Log(title = "FCCM功能报表")
    @PostMapping("/getOrg")
    public R<PageDataInfo> getOrg(@RequestParam(required = false, defaultValue = "ASC") String sort,
                                  @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(materialConsumptionService.getOrg(sort, searchVo));
    }

    @ApiOperation("查询编辑表")
    @Log(title = "FCCM功能报表")
    @PostMapping("/getEdit")
    public R<PageDataInfo> getEdit(@RequestParam(required = false, defaultValue = "ASC") String sort,
                                  @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(materialConsumptionService.getEdit(sort, searchVo));
    }

    @ApiOperation("查询最终表")
    @Log(title = "FCCM功能报表")
    @PostMapping("/getAll")
    public R<PageDataInfo> getAll(@RequestParam(required = false, defaultValue = "ASC") String sort,
                                   @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(materialConsumptionService.getAll(sort, searchVo));
    }

    @ApiOperation("保存")
    @Log(title = "FCCM功能报表")
    @PostMapping("/save")
    public R save(@Validated @RequestBody MaterialConsumption materialConsumption) {
        return R.ok(materialConsumptionService.saveData(materialConsumption));
    }

    @ApiOperation("删除")
    @Log(title = "FCCM功能报表")
    @DeleteMapping("del")
    public R del(@RequestBody List<MaterialConsumption> list) {
        return R.ok(materialConsumptionService.deleteByList(list));
    }

}

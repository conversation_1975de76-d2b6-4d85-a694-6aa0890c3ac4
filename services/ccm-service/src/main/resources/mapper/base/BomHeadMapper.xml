<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.BomHeadMapper">

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.BomHead" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_bom_head
        WHERE recipe_id = #{bomHead.recipeId} AND recipe_ver = #{bomHead.recipeVer} AND del_flag = false limit 1
    </select>

    <update id="deleteByIds" parameterType="Long">
        update zjdata.ccm_bom_head
        SET del_flag = true
        WHERE
        <foreach collection="bomHeadList" item="bomHead" separator=" or ">
            ( recipe_id = #{bomHead.recipeId} and recipe_ver = #{bomHead.recipeVer} )
        </foreach>
        and del_flag = false
    </update>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.BomHead" resultType="com.datalink.fdop.base.api.domain.BomHead">
        SELECT *
        FROM zjdata.ccm_bom_head
        WHERE del_flag = false
        <if test="bomHead.recipeId != null and bomHead.recipeId != ''">
            AND recipe_id = #{bomHead.recipeId}
        </if>
        <if test="bomHead.recipeVer != null">
            AND recipe_ver = #{bomHead.recipeVer}
        </if>
        <if test="bomHead.menuId != null and bomHead.menuId != ''">
            AND menu_id = #{bomHead.menuId}
        </if>
        ORDER BY recipe_id desc
    </select>

    <select id="selectIdsByPid" resultType="String">
        select recipe_id from zjdata.ccm_bom_head
        where menu_id = #{pid} and del_flag = false
    </select>

    <update id="bacthUpdatePidById">
        update zjdata.ccm_bom_head
        set menu_id = (select p_menu_id from zjdata.ccm_bom_menu where menu_id = #{menuId} limit 1)
        where menu_id = #{menuId} and del_flag = false
    </update>

    <update id="updateByKey">
        update zjdata.ccm_bom_head
        set
        <if test="recipeDesc != null">
            recipe_desc = #{recipeDesc},
        </if>
        <if test="recipeText != null">
            recipe_text = #{recipeText},
        </if>
        <if test="dateFrom != null">
            date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}',
        </if>
        <if test="dateTo != null">
            date_to = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateTo)}',
        </if>
        <if test="enable != null">
            enable = #{enable},
        </if>
        update_by = #{updateBy}, update_time = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where recipe_id = #{recipeId} and recipe_ver = #{recipeVer} and del_flag = false
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.BomHead">
        SELECT ls.*
        FROM zjdata.ccm_bom_head ls
        WHERE ls.del_flag = false
        <if test="a != null and a != ''">
            AND ${a}
        </if>
        <if test="menuId != null and menuId != '-1'.toString()">
            and ls.menu_id = #{menuId}
        </if>
        ORDER BY recipe_id, recipe_ver ${sort}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.BomHead">
        SELECT * FROM zjdata.ccm_bom_head
        WHERE del_flag = false AND recipe_id = #{recipeId} AND recipe_ver = #{recipeVer} limit 1
    </select>

</mapper>


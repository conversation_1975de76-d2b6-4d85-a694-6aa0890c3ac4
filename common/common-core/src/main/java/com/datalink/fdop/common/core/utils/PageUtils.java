package com.datalink.fdop.common.core.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.utils.sql.SqlUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.core.web.page.PageDomain;
import com.datalink.fdop.common.core.web.page.TableSupport;

import java.util.List;

/**
 * 分页工具类
 *
 * <AUTHOR>
 */
public class PageUtils extends PageHelper {
    /**
     * 设置请求分页数据
     */
    public static void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNo = pageDomain.getPageNo();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNo) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(pageNo, pageSize, orderBy).setReasonable(reasonable);
        }
    }

    public static <T> Page<T> getPage(Class<T> tClass) {
        Integer pageNo = ServletUtils.getParameterToInt(Constants.PAGE_NO) == null ? 1 : ServletUtils.getParameterToInt(Constants.PAGE_NO);
        Integer pageSize = ServletUtils.getParameterToInt(Constants.PAGE_SIZE) == null ? 10 : ServletUtils.getParameterToInt(Constants.PAGE_SIZE);
        return new Page<>(pageNo, pageSize);
    }

    public static <T> PageDataInfo<T> getPageInfo(List<T> list, int total) {
        Integer pageNo = ServletUtils.getParameterToInt(Constants.PAGE_NO) == null ? 1 : ServletUtils.getParameterToInt(Constants.PAGE_NO);
        Integer pageSize = ServletUtils.getParameterToInt(Constants.PAGE_SIZE) == null ? 10 : ServletUtils.getParameterToInt(Constants.PAGE_SIZE);
        PageDataInfo pageDataInfo = new PageDataInfo<>(pageNo, pageSize);
        pageDataInfo.setTotal(total);
        pageDataInfo.setTotalList(list);
        return pageDataInfo;
    }

}

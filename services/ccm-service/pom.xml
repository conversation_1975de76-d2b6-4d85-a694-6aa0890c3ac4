<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.datalink-info</groupId>
        <artifactId>services</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>ccm-service</artifactId>

    <description>
        CCM管理服务
    </description>

    <dependencies>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- Common Swagger -->
        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-swagger</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Common Security-->
        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>ccm-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>data-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dolphinscheduler-task-api</artifactId>
                    <groupId>org.apache.dolphinscheduler</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-datascope</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dolphinscheduler</groupId>
            <artifactId>dolphinscheduler-fdop-api</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
            </plugin>

        </plugins>
    </build>

</project>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.RecoryRulesMapper">

    <select id="query" resultType="com.datalink.fdop.base.api.domain.RecoryRules">
        select a.*, controlArea.control_area_desc as control_area_desc from zjdata.ccm_recory_rules a
            left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        <where>
            <if test="searchVo != null and searchVo != ''">
                and ${searchVo}
            </if>
        </where>
        order by control_area_id, lot_type ${sort}
    </select>



    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.RecoryRules">
        select a.*, controlArea.control_area_desc as control_area_desc from zjdata.ccm_recory_rules a
            left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        where a.control_area_id = #{controlAreaId} and a.lot_type = #{lotType}
        limit 1
    </select>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.RecoryRules">
        select a.*, controlArea.control_area_desc as control_area_desc from zjdata.ccm_recory_rules a
            left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        order by control_area_id, lot_type desc
    </select>
    <select id="checkUnique" resultType="java.lang.Integer">
        select count(1) from zjdata.ccm_recory_rules where control_area_id = #{controlAreaId} and lot_type = #{lotType}
    </select>

    <update id="update" parameterType="com.datalink.fdop.base.api.domain.RecoryRules">
        update zjdata.ccm_recory_rules
        set
        <if test="lotTypeDesc != null">lot_type_desc = #{lotTypeDesc},</if>
        <if test="lotTypeText != null">lot_type_text = #{lotTypeText},</if>
        <if test="lotTypeGroup != null">lot_type_group = #{lotTypeGroup},</if>
        <if test="workOrderType != null">work_order_type = #{workOrderType},</if>
        <if test="workOrderDesc != null">work_order_desc = #{workOrderDesc},</if>
        <if test="workOrderGroup != null">work_order_group = #{workOrderGroup},</if>
        <if test="recoryMethod != null">recory_method = #{recoryMethod},</if>
        <if test="settlementType != null">settlement_type = #{settlementType},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId} and lot_type = #{lotType}
    </update>

    <delete id="delete" parameterType="com.datalink.fdop.base.api.domain.RecoryRules">
        delete from zjdata.ccm_recory_rules
        where control_area_id = #{controlAreaId} and lot_type = #{lotType}
    </delete>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_recory_rules
            SET enable = #{item.enable}
            where control_area_id = #{item.controlAreaId} and lot_type = #{item.lotType}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_recory_rules (
        control_area_id, lot_type, lot_type_desc,lot_type_text,lot_type_group, work_order_type,work_order_desc,work_order_group,recory_method,settlement_type, enable,create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.controlAreaId},#{item.lotType},#{item.lotTypeDesc},
            #{item.lotTypeText},#{item.lotTypeGroup},#{item.workOrderType},
            #{item.workOrderDesc},#{item.workOrderGroup},#{item.recoryMethod},#{item.settlementType},
            #{item.enable},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>

</mapper>


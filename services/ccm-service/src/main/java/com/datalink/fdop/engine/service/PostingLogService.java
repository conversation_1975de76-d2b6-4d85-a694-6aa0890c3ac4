package com.datalink.fdop.engine.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.PostingLogHead2;
import com.datalink.fdop.engine.api.domain.PostingLogItem2;
import com.datalink.fdop.engine.model.vo.PostingLog;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-14 14:55
 */
public interface PostingLogService {

    PageDataInfo<PostingLog> query(String verId, String companyId, String actionFlag, String postingDate, String sort, SearchVo searchVo);

    int add(PostingLog postingLog);

    int addItem(PostingLogItem2 postingLogItem2);

    int updateHead(PostingLogHead2 postingLogHead2);

    int updateItem(PostingLogItem2 postingLogItem2);

    int delItem(List<PostingLogItem2> list);

    int updateStatus(Long id, String actionFlag);

    int undo(Long id, String actionFlag);
}

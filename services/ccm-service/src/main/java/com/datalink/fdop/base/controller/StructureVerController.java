package com.datalink.fdop.base.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.base.api.domain.Attribute;
import com.datalink.fdop.base.api.domain.StructureVer;
import com.datalink.fdop.base.service.StructureVerService;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/base/structureVer")
@Transactional(rollbackFor = Exception.class)
public class StructureVerController extends BaseController {

    @Autowired
    private StructureVerService structureVerService;

    @ApiOperation(value = "新增")
    @Log(title = "fccm组件版本",businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R create(@Validated @RequestBody StructureVer structureVer) {
        if (UserConstants.NOT_UNIQUE.equals(structureVerService.checkIdUnique(structureVer.getStructureVerId(),structureVer.getDateTo()))) {
            return R.fail("新增组件版本'" + structureVer.getStructureVerId() + "'失败，组件版本已存在");
        }
        return R.ok(structureVerService.create(structureVer));
    }

    @ApiOperation(value = "修改")
    @Log(title = "fccm组件版本",businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public R update(@Validated @RequestBody StructureVer structureVer) {
        if (UserConstants.UNIQUE.equals(structureVerService.checkIdUnique(structureVer.getStructureVerId(),structureVer.getDateTo()))) {
            return R.fail("修改组件版本'" + structureVer.getStructureVerId() + "'失败，组件版本不存在");
        }
        return R.ok(structureVerService.update(structureVer));
    }

    @ApiOperation("删除")
    @Log(title = "fccm组件版本",businessType = BusinessType.DELETE)
    @DeleteMapping("/delete")
    public R del(@RequestBody List<StructureVer> datas) {
        return R.toResult(structureVerService.deleteByIds(datas));
    }

    @ApiOperation(value = "分页查询")
    @Log(title = "fccm组件版本",businessType = BusinessType.OTHER)
    @PostMapping("/overview")
    public R query(@RequestBody(required = false) SearchVo searchVo,
                   @RequestParam(required = false, value = "sort", defaultValue = "asc") String sort) {
        Page<StructureVer> page = PageUtils.getPage(StructureVer.class);
        Page<StructureVer> dataPage = structureVerService.pageList(page,searchVo,sort);
        return R.ok(PageUtils.getPageInfo(dataPage.getRecords(),(int)dataPage.getTotal()));
    }

    @ApiOperation(value = "查询所有")
    @Log(title = "查询所有", businessType = BusinessType.UPDATE)
    @GetMapping("/listAll")
    public R<List<String>> listAll(@RequestParam(value = "structureVerId",required = false) String structureVerId) {
        return R.ok(structureVerService.listAll(structureVerId));
    }

    @ApiOperation(value = "批量启用/禁用")
    @Log(title = "fccm组件版本",businessType = BusinessType.UPDATE)
    @PostMapping("/batchStatus")
    public R batchStatus(@RequestParam(required = false) Boolean enable,@RequestBody List<StructureVer> statusList) {
        return R.ok(structureVerService.updateBatchStatus(enable,statusList));
    }

    @ApiOperation(value = "导入数据")
    @Log(title = "fccm组件版本",businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R importData(MultipartFile file, @RequestParam(value = "updateSupport", required = false) Boolean updateSupport) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        ExcelUtil<StructureVer> util = new ExcelUtil<>(StructureVer.class);
        List<StructureVer> list = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        return R.ok(structureVerService.importData(list,updateSupport,operName));
    }

    @ApiOperation(value = "导出模板")
    @Log(title = "fccm组件版本",businessType = BusinessType.EXPORT)
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<StructureVer> util = new ExcelUtil<>(StructureVer.class);
        util.importTemplateExcel(response, "属性管理模板");
    }

    @ApiOperation("导出数据")
    @Log(title = "fccm组件版本",businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "DESC") String sort){
        List<StructureVer> list = structureVerService.list();
        ExcelUtil<StructureVer> util = new ExcelUtil<>(StructureVer.class);
        util.exportExcel(response, list, "属性管理数据");
    }
}

server {
    listen       32198;
    listen  [::]:32198;
    server_name  localhost;
    client_max_body_size 2000m;
    proxy_send_timeout 600;
    proxy_read_timeout 600;
    proxy_connect_timeout 600;
    keepalive_timeout 600;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    location / {
        root   /usr/share/nginx/html/dist/;
        index  index.html index.htm;
        try_files $uri  $uri/  /index.html;
    }

    location /activity {
        alias  /usr/share/nginx/html/modeler/;
        index  modeler.html modeler.htm;
        try_files $uri $uri/  /modeler.html;
    }

    location /lowcode {
        alias /usr/share/nginx/html/lowcode/build/;
        index index.html index.htm;
        try_files $uri $uri/  /index.html;
    }

    location /datart {
        alias  /usr/share/nginx/html/datart/build/;
        index  index.html index.html;
        try_files $uri  $uri/  /index.html;
    }

    location  ~ /api/ {
        proxy_pass   http://localhost:8081;
        proxy_send_timeout 600;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        keepalive_timeout 600;
        client_max_body_size   2000m;
    }

    location  ~ /api/lowcode {
        proxy_pass   http://localhost:8081;
    }

    location  ~ /api/act/ {
        proxy_pass   http://localhost:8081;
    }

    location  /auth/ {
        proxy_pass   http://localhost:8081;
    }

    error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}                                                                                                                                                                                         

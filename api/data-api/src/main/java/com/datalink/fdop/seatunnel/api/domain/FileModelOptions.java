package com.datalink.fdop.seatunnel.api.domain;

import com.datalink.fdop.common.core.enums.DataModelType;
import lombok.Data;

/**
 * 文件类型选项（SFTP,FTP）
 */
@Data
public class FileModelOptions {

    /**
     * 类型 SFTP,FTP
     */
    private DataModelType modelType;

    /**
     * 生成的文件类型text json csv orc parquet excel
     */
    private String fileType;

    /**
     * 服务器地址
     */
    private String serverHost;

    /**
     * 服务器端口
     */
    private String serverPort;

    /**
     * 用户名
     */
    private String serverUser;

    /**
     * 密码
     */
    private String serverPassword;

    /**
     * 文件存放路径
     */
    private String filePath;

    /**
     * SeaTunnel 需要临时目录存放数据文件
     */
    private String tmpFilePath;

    /**
     * 文件名称 可以使用参数
     */
    private String fileName;

    /**
     * 文件名称时间格式，可以在 fileName中添加${now} 解析符seatuunel会自动替换为当前时间,默认格式为yyyy.MM.dd
     */
    private String fileNameTimeFormat;

    /**
     * 列分割符 csv text可以自定义列的分割字符
     */
    private String fieldDelimiter;

    /**
     * 行分隔符 csv text可以自定义行的分割字符
     */
    private String rowDelimiter;

    /**
     * 启用表头 csv text 可以支持去掉表头 2.3.4 新特性
     */
    private Boolean enableHeaderWrite;


}

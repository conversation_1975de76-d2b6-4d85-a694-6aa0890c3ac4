package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.FormulaManagement;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-13 11:40
 */
public interface FormulaManagementService extends IService<FormulaManagement> {

    PageDataInfo<FormulaManagement> query(String sort, SearchVo searchVo);

    int add(FormulaManagement formulaManagement);

    int update(FormulaManagement formulaManagement);

    int delete(List<FormulaManagement> list);

    List<FormulaManagement> queryAll();

    boolean updateBatchStatus(Boolean enable, List<FormulaManagement> statusList);

    String importData(List<FormulaManagement> list, Boolean isUpdateSupport, String operName);

    /**
     * 根据工厂代码查询公式信息
     *
     * @param plantId 工厂代码
     * @return
     */
    List<String> queryByPlantId(String plantId);

    List<String> queryByControlAreaId(String controlAreaId);

}

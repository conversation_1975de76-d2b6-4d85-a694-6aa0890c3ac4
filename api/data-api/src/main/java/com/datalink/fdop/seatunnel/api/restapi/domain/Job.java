package com.datalink.fdop.seatunnel.api.restapi.domain;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class Job {

    /**
     * 任务ID
     */
    private String jobId;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务状态 RUNNING
     */
    private String jobStatus;

    /**
     * 错误日志
     */
    private String errorMsg;

    /**
     * 环境信息
     */
    private Map<String, String> envOptions;

    /**
     * 任务创建时间
     */
    private String createTime;

    /**
     * 任务完成时间
     */
    private String finishTime;

    /**
     * Job图形化信息
     */
    private JobDag jobDag;

    /**
     * 依赖jar包路径
     */
    private List<PluginJar> pluginJarsUrls;

    /**
     * 是否开启SavePoint
     */
    private boolean isStartWithSavePoint;

    /**
     * metrics监控信息
     */
    private Metrics metrics;

    @Data
    public static class JobDag {
        private String jobId;
        private Map<String, List<Edge>> pipelineEdges;
        private Map<String, String> envOptions;
        private List<VertexInfo> vertexInfoMap;

        @Data
        public static class Edge {
            private String inputVertexId;
            private String targetVertexId;
        }

        @Data
        public static class VertexInfo {
            private int vertexId;
            private String type;
            private String vertexName;
            private List<String> tablePaths;
        }
    }

    @Data
    public static class PluginJar {
        private String jarPath;
    }

    @Data
    public static class Metrics {
        private Map<String, String> TableSourceReceivedCount;
        private Map<String, String> TableSourceReceivedQPS;
        private Map<String, String> TableSinkWriteQPS;
        private Map<String, String> TableSinkWriteCount;
        private String SinkWriteCount;
        private String SinkWriteBytesPerSeconds;
        private Map<String, String> TableSinkWriteBytesPerSeconds;
        private String SinkWriteQPS;
        private String SourceReceivedBytes;
        private String SourceReceivedBytesPerSeconds;
        private String SourceReceivedCount;
        private String SourceReceivedQPS;
        private Map<String, String> TableSourceReceivedBytesPerSeconds;
        private Map<String, String> TableSourceReceivedBytes;
        private String SinkWriteBytes;
        private Map<String, String> TableSinkWriteBytes;
    }
}

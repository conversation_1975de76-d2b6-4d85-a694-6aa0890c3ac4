<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.permissions.mapper.PermissionsElementMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties"
                javaType="com.datalink.fdop.permissions.api.domain.PermissionsElement"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insertPermissionsElement" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsElement"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',$$
        <foreach collection="permissionsElementList" item="permissionsElement">
            CREATE (:b_p_permissions_element
            ${@com.datalink.fdop.permissions.utils.DomainAgeUtils@getPermissionsElementAgeStr(permissionsElement)})
        </foreach>
        $$) as (m ag_catalog.agtype);
    </select>

    <select id="createPermissionsElementEdge"
            parameterType="com.datalink.fdop.permissions.api.domain.PermissionsElement" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (p_element:b_p_permissions_element), (element:d_e_data_element)
                                   WHERE p_element.id = ${permissionsElement.id} AND element.id = ${permissionsElement.id}
                                   CREATE (p_element)-[:b_p_permissions_element_edge {startTable:'b_p_permissions_element',
                               endTable:'d_e_data_element'}]->(element)-[:b_p_permissions_element_edge
            {startTable:'d_e_data_element',
            endTable:'b_p_permissions_element'}]->(p_element)
        return p_element
        $$) as (m ag_catalog.agtype);
    </select>

    <select id="deleteRelevancyEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (p_element:b_p_permissions_element)
        -[edge:b_p_permissions_relevancy_element_edge]->(relevancy)
        WHERE p_element.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE relevancy,p_element RETURN id(p_element),properties(p_element) $$) as (id
        ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="deletePermissionsElement" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (p_element:b_p_permissions_element)
        WHERE p_element.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE p_element RETURN id(p_element),properties(p_element) $$) as (id
        ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectElementByElementId" resultType="com.datalink.fdop.element.api.domain.DataElement">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (p_element:b_p_permissions_element) -[edge:b_p_permissions_element_edge]->(element:d_e_data_element)
            WHERE p_element.id = ${id}
            RETURN element.id, element.code, element.name, element.description, element.dataElementType,
                               element.fieldType,
                               element.length, element.decimalLength
                                   $$) as (id BIGINT,code TEXT
                                  ,name TEXT
                                  ,description TEXT
                                  ,dataElementType TEXT
                                  ,fieldType TEXT
                                  ,length BIGINT
                                  ,decimalLength BIGINT)
    </select>

    <select id="selectPermissionsElementList" resultType="com.datalink.fdop.element.api.domain.DataElement">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (p_element:b_p_permissions_element) -[edge:b_p_permissions_element_edge]->(element:d_e_data_element)
        <where>
            <if test="element.code != null and element.code != ''">AND element.code =~'.*${element.code}.*'
            </if>
            <if test="element.name != null and element.name != ''">AND element.name =~'.*${element.name}.*'
            </if>
            <if test="element.description != null and element.description != ''">AND element.description
                =~'.*${element.description}.*'
            </if>
            <if test="element.dataElementType != null">AND element.dataElementType
                =~'.*${element.dataElementType}.*'
            </if>
            <if test="element.fieldType != null">AND element.fieldType =~'.*${element.fieldType}.*'</if>
            <if test="element.length != null">AND element.length = ${element.length}</if>
            <if test="element.decimalLength != null">AND element.decimalLength = ${element.decimalLength}</if>
        </where>
        RETURN element.id, element.code, element.name, element.description, element.dataElementType,
        element.fieldType,
        element.length, element.decimalLength
        $$) as (id BIGINT,code TEXT
        ,name TEXT
        ,description TEXT
        ,dataElementType TEXT
        ,fieldType TEXT
        ,length BIGINT
        ,decimalLength BIGINT)
    </select>

</mapper> 
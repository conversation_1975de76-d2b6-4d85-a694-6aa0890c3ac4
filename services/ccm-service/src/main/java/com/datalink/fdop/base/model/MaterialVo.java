package com.datalink.fdop.base.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/10 15:13
 */
@Data
@ApiModel
public class MaterialVo {
    @ApiModelProperty(value = "内部物料编码")
    private List<String> materialCode;
    @ApiModelProperty(value = "物料类型")
    private List<String> materialType;
    @ApiModelProperty(value = "物料组")
    private List<String> materialGroup;
    @ApiModelProperty(value = "基本计量单位")
    private List<String> unit;
    @ApiModelProperty(value = "有效日期")
    private List<Date> effectiveDate;
    @ApiModelProperty(value = "物料状态")
    private List<String> materialStatu;

}
package com.datalink.fdop.element.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.datalink.fdop.element.api.enums.EntityInsertType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/12 11:45
 */
@Data
@ApiModel("数据实体表的映射关系")
@TableName(schema = "zjdata", value = "d_e_data_entity_Table_mapping")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataEntityTableMapping extends BaseField implements Serializable {

    private static final long serialVersionUID = -5508413529453322173L;

    @ApiModelProperty(value = "实体id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dataEntityId;

    @ApiModelProperty(value = "关联表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dataTableId;

    @ApiModelProperty(value = "实体字段id/数据元素字段id/数据元素录入类型字段id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long fieldId;

    @ApiModelProperty(value = "数据实体类型(CUSTOMIZE:自定义,PREDEFINED:预定义)")
    private EntityInsertType entityInsertType;

    @ApiModelProperty(value = "数据元素类型(MAIN:主数据,FIELD:字段,INPUT:录入)")
    private DataElementType dataElementType;

    @ApiModelProperty(value = "实体字段编码")
    @TableField(exist = false)
    private String entityFieldCode;

    @ApiModelProperty(value = "实体字段名称")
    @TableField(exist = false)
    private String entityFieldName;

    @ApiModelProperty(value = "实体字段描述")
    @TableField(exist = false)
    private String entityFieldDesc;

    @ApiModelProperty(value = "表字段名称")
    private String fieldName;

    public DataEntityTableMapping() {

    }

    public DataEntityTableMapping(Long dataEntityId, Long dataTableId, Long fieldId, EntityInsertType entityInsertType, DataElementType dataElementType, String fieldName) {
        this.dataEntityId = dataEntityId;
        this.dataTableId = dataTableId;
        this.fieldId = fieldId;
        this.entityInsertType = entityInsertType;
        this.dataElementType = dataElementType;
        this.fieldName = fieldName;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.MatnrPriceMapper">

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.MaterialPrice" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_material_price
        WHERE control_area_id = #{materialPrice.controlAreaId}
          AND ver_id = #{materialPrice.verId}
          AND company_id = #{materialPrice.companyId}
          AND factory_id = #{materialPrice.factoryId}
          AND material_id = #{materialPrice.materialId}
          AND del_flag = false limit 1
    </select>

    <update id="deleteById" parameterType="Long">
        update zjdata.ccm_material_price
        set del_flag = true
        where id = #{id} and del_flag = false
    </update>

    <update id="deleteByIds" parameterType="Long">
        update zjdata.ccm_material_price
        SET del_flag = true
        WHERE
        <foreach collection="materialPriceList" item="materialPrice" separator=" or ">
            ( control_area_id = #{materialPrice.controlAreaId}
            AND ver_id = #{materialPrice.verId}
            AND company_id = #{materialPrice.companyId}
            AND factory_id = #{materialPrice.factoryId}
            AND material_id = #{materialPrice.materialId} )
        </foreach>
        and del_flag = false
    </update>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.MaterialPrice" resultType="com.datalink.fdop.base.api.domain.MaterialPrice">
        SELECT *
        FROM zjdata.ccm_material_price
        WHERE del_flag = false
        ORDER BY control_area_id desc
    </select>

    <select id="selectIdsByPid" resultType="Long">
        select id from zjdata.ccm_material_price
        where material_id = #{pid}
    </select>

    <update id="bacthUpdatePidById">
        update zjdata.ccm_material_price
        set material_id = #{pid}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag = false
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.MaterialPrice">
        SELECT ls.*
        FROM zjdata.ccm_material_price ls
        WHERE ls.del_flag = false
        <if test="a != null and a != ''">
            AND ${a}
        </if>
        ORDER BY id ${sort}
    </select>

    <select id="selectTableIdByCode" resultType="java.lang.Long">
        SELECT id FROM zjdata.${tableName} where
        <choose>
            <when test="tableName.equals('p_d_material')">material_code</when>
            <when test="tableName.equals('ccm_factory')">factory_code</when>
            <otherwise>code</otherwise>
        </choose>
        = #{code} limit 1
    </select>

    <select id="selectInBom" parameterType="com.datalink.fdop.base.api.domain.MaterialPrice" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_material_price mp left join zjdata.ccm_bom_item bi on bi.material_id = mp.material_id
            left join zjdata.bom_mid bm on bm.id = bi.pid
            left join zjdata.bom_head bh on bh.id = bm.pid
            left join zjdata.p_d_werks plant on plant.id = bh.werks_id
        WHERE mp.del_flag = false AND #{materialPrice.verId} = bh.ver_id AND bi.material_id = #{materialPrice.materialId}
              AND plant.factory_id = #{materialPrice.factoryId}
    </select>

    <update id="update" parameterType="com.datalink.fdop.base.api.domain.MaterialPrice">
        update zjdata.ccm_material_price
        set price_lately = price_present, valid_date_lately = valid_date_present,
            price_present = price_will, valid_date_present = valid_date_will,
            price_will = null, valid_date_will = null
        where 1 = 1
        <if test="materialPrice.id != null">AND id = #{materialPrice.id}</if>
        <if test="materialPrice.materialId != null">AND material_id = #{materialPrice.materialId}</if>
    </update>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.MaterialPrice">
        update zjdata.ccm_material_price
        <set>
            <if test="evaluateSortId != null and evaluateSortId != ''">evaluate_sort_id = #{evaluateSortId},</if>
            <if test="evaluateAreaId != null and evaluateAreaId != ''">evaluate_area_id = #{evaluateAreaId},</if>
            <if test="inbom != null">inbom = #{inbom},</if>
            <if test="pricePresent != null">price_present = #{pricePresent},</if>
            <if test="dateFromPresent != null">date_from_present = #{dateFromPresent},</if>
            <if test="priceLately != null">price_lately = #{priceLately},</if>
            <if test="dateFromLately != null">date_from_lately = #{dateFromLately},</if>
            <if test="priceWill != null">price_will = #{priceWill},</if>
            <if test="dateFromWill != null">date_from_will = #{dateFromWill},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        where control_area_id = #{controlAreaId}
        AND ver_id = #{verId}
        AND company_id = #{companyId}
        AND factory_id = #{factoryId}
        AND material_id = #{materialId}
    </update>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.MaterialPrice">
        SELECT ls.*
        WHERE ls.del_flag = false
        <if test="a != null and a != ''">
            AND ${a}
        </if>
    </select>

    <select id="selectPrice" resultType="com.datalink.fdop.base.api.domain.MaterialPrice">
        SELECT ls.*
        WHERE ls.material_id = #{materialId} AND ls.ver_id = #{verId}
          AND ls.factory_id = #{factoryId} AND ls.del_flag = false limit 1
    </select>

</mapper>


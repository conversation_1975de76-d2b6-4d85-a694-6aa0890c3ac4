package com.datalink.fdop.fscm.api.model.vo;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/1/10 10:13
 */
@Data
public class RepertoryMoveQueryVo {


    @ApiModelProperty(value = "过账凭证号")
    private List<Integer>  voucherNum;
    @ApiModelProperty(value = "过账日期")
    private List<Date> postDate;
    @ApiModelProperty(value = "录入日期")
    private List<Date> enteringDate;
    @ApiModelProperty(value = "操作账户名")
    private List<String> username;
    @ApiModelProperty(value = "移动类型")
    private List<String> moveType;
    @ApiModelProperty(value = "内部物料编码")
    private List<String> materialCode;
    @ApiModelProperty(value = "发出工厂")
    private List<String> factoryCodeShip;
    @ApiModelProperty(value = "发出库存地点")
    private List<String> stockPCodeShip;
    @ApiModelProperty(value = "发出批次")
    private List<String> batchNumberShip;
    @ApiModelProperty(value = "发出片号")
    private List<String> pieceShip;
    @ApiModelProperty(value = "发出BIN号")
    private List<String> binNumShip;
    @ApiModelProperty(value = "采购合同/订单号")
    private List<String> orderNum;
    @ApiModelProperty(value = "工单号")
    private List<String> workOrderNum;
    @ApiModelProperty(value = "调拨单号")
    private List<String> transferOrderNum;
    @ApiModelProperty(value = "领退料单号")
    private List<String> pickReturnOrderNum;
    @ApiModelProperty(value = "报废单号")
    private List<String> scrapOrderNum;
    @ApiModelProperty(value = "出货需求单号")
    private List<String> shipRequestOrderNum;
    @ApiModelProperty("高级搜索传参")
    private SearchVo searchVo;



}

package com.datalink.fdop.fscm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.fscm.api.domain.AllotHead;
import com.datalink.fdop.fscm.api.model.vo.AllotQueryVo;
import com.datalink.fdop.fscm.api.model.vo.AllotShowVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/12 16:42
 */
@Transactional
public interface AllotHeadMapper extends BaseMapper<AllotHead> {
    List<AllotShowVo> queryData(@Param("allotQueryVo") AllotQueryVo allotQueryVo, @Param("delflag") String delflag, @Param("temporary")String temporary);
}

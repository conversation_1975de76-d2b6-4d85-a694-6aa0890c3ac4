package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022-11-07 16:57
 */
@Data
@TableName(schema = "zjdata", value = "ccm_flow_item")
@ApiModel
public class FlowItem {

    @Excel(name = "工艺编码", sort = 1)
    @ApiModelProperty(value = "工艺编码")
    @NotBlank(message = "工艺编码不能为空")
    private String technologyId;

    @Excel(name = "工艺版本", sort = 2)
    @NotNull(message = "工艺版本不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long technologyVer;

    @ApiModelProperty(value = "途程编码")
    @Excel(name = "途程编码", sort = 3)
    private String routerId;

    @ApiModelProperty(value = "途程版本")
    @Excel(name = "途程编码", sort = 4)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long routerVer;

    @Excel(name = "Layer_ID", sort = 14)
    @ApiModelProperty(value = "Layer_ID", required = true)
    private String layerId;
    @Excel(name = "Stage_ID", sort = 16)
    @ApiModelProperty(value = "Stage_ID", required = true)
    private String stageId;
    @Excel(name = "Step_ID", sort = 18)
    @ApiModelProperty(value = "Step_ID", required = true)
    private String stepId;
    @Excel(name = "工序序号", sort = 19)
    @NotNull(message = "工序序号不能为空")
    @ApiModelProperty(value = "工序序号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stepNo;
    @Excel(name = "工序类型", sort = 20)
    @ApiModelProperty(value = "工序类型")
    private String stepType;

    @Excel(name = "RECIPE", sort = 21)
    @ApiModelProperty(value = "RECIPE")
    private String recipeId;

    @Excel(name = "工作单元", sort = 22)
    @ApiModelProperty(value = "工作单元")
    private String workCenterId;

    @Excel(name = "基准单位", sort = 23)
    @ApiModelProperty(value = "基准单位")
    private String baseUnit;

    @Excel(name = "基准数量", sort = 24)
    @ApiModelProperty(value = "基准数量")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long baseQty;

    @Excel(name = "单位1", sort = 30)
    @ApiModelProperty(value = "单位1")
    private String unit1;
    @Excel(name = "数量1", sort = 31)
    @ApiModelProperty(value = "数量1")
    private Double qty1;
    @Excel(name = "单位2", sort = 34)
    @ApiModelProperty(value = "单位2")
    private String unit2;
    @Excel(name = "数量2", sort = 35)
    @ApiModelProperty(value = "数量2")
    private Double qty2;

    @Excel(name = "良率", sort = 36)
    @ApiModelProperty(value = "良率")
    private Double yieldRate;

    @Excel(name = "权数", sort = 37)
    @ApiModelProperty(value = "权数")
    private Double weight;

    @Excel(name = "文本", sort = 38)
    @ApiModelProperty(value = "文本")
    private String text;

    @Excel(name = "是否委外", sort = 39, readConverterExp = "true=是,false=否")
    @ApiModelProperty(value = "是否委外")
    private Boolean outsourcing;

    @ApiModelProperty(value = "创建人")
    private String createBy;
    @Excel(name = "创建时间", sort = 42, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更改人")
    private String updateBy;
    @Excel(name = "修改时间", sort = 44, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;
   @Excel(name = "是否启用", sort = 18, readConverterExp = "true=启用,false=不启用")
   @ApiModelProperty(value = "启用")
   private Boolean enable;
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlag;

}

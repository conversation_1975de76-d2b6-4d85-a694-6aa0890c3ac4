package com.datalink.fdop.stream.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.stream.api.RemoteStreamService;
import com.datalink.fdop.graph.api.dto.TaskInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


@Component
public class RemoteStreamFallbackFactory implements FallbackFactory<RemoteStreamService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteStreamFallbackFactory.class);

    @Override
    public RemoteStreamService create(Throwable throwable) {
        log.error("流元素服务调用失败:{}", throwable.getMessage());
        return new RemoteStreamService() {

            @Override
            public R<Boolean> checkTaskIsExists(String taskName) {
                return R.fail("检查任务状态失败:" + throwable.getMessage());
            }

            @Override
            public R<String> getLog(String taskName) {
                return R.fail("获取任务日志失败:" + throwable.getMessage());
            }

            @Override
            public R submitFlink2K8s(TaskInfo taskInfo) {
                return R.fail("提交flink任务到k8s失败:" + throwable.getMessage());
            }

            @Override
            public R stop(String taskName) {
                return R.fail("停止k8s中的flink任务失败:" + throwable.getMessage());
            }
        };
    }
}

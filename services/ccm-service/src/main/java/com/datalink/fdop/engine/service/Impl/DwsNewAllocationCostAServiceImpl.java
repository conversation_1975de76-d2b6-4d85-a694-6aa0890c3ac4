package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationCostA;
import com.datalink.fdop.engine.mapper.DwsNewAllocationCostAMapper;
import com.datalink.fdop.engine.service.DwsNewAllocationCostAService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewAllocationCostAServiceImpl extends ServiceImpl<DwsNewAllocationCostAMapper, DwsNewAllocationCostA> implements DwsNewAllocationCostAService {

    @Autowired
    private DwsNewAllocationCostAMapper dwsNewAllocationCostAMapper;

    @Override
    public PageDataInfo<DwsNewAllocationCostA> selectTargetedNewAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo) {
        Page<DwsNewAllocationCostA> page = PageUtils.getPage(DwsNewAllocationCostA.class);
        IPage<DwsNewAllocationCostA> iPage = dwsNewAllocationCostAMapper.selectTargetedNewAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public PageDataInfo<DwsNewAllocationCostA> selectCostNewAllocation(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo) {
        Page<DwsNewAllocationCostA> page = PageUtils.getPage(DwsNewAllocationCostA.class);
        IPage<DwsNewAllocationCostA> iPage = dwsNewAllocationCostAMapper.selectCostNewAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewAllocationCostA> selectNoPage(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo) {
        return dwsNewAllocationCostAMapper.selectNoPage(controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, sort, searchVo);
    }
}
package com.datalink.fdop.permissions.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.permissions.api.domain.Permissions;
import com.datalink.fdop.permissions.api.model.vo.PermissionsCopyVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/20 15:43
 */
public interface IPermissionsService {

    int addPermissions(Permissions permissions);

    int updatePermissions(Permissions permissions);

    void copy(Long pid, List<PermissionsCopyVo> permissionsCopyVoList);

    int deletePermissions(List<Long> ids);

    PageDataInfo<Permissions> selectPermissionsList(Permissions permissions);

    int bindPermissionsRole(Long id, List<Long> roleIds);

    int untiePermissionsRole(Long id, List<Long> roleIds);

    List<String> selectPermissionsRoleList(Long id);

    int bindPermissionsUser(Long id, List<Long> userIds);

    int untiePermissionsUser(Long id, List<Long> userIds);

    List<String> selectPermissionsUserList(Long id);

}

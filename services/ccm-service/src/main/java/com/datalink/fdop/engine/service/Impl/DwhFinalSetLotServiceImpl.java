package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalSetLot;
import com.datalink.fdop.engine.mapper.DwhFinalSetLotMapper;
import com.datalink.fdop.engine.service.DwhFinalSetLotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalSetLotServiceImpl extends ServiceImpl<DwhFinalSetLotMapper, DwhFinalSetLot> implements DwhFinalSetLotService {

    @Autowired
    private DwhFinalSetLotMapper dwhFinalSetLotMapper;

    @Override
    public PageDataInfo<DwhFinalSetLot> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwhFinalSetLot> page = PageUtils.getPage(DwhFinalSetLot.class);
        IPage<DwhFinalSetLot> iPage = dwhFinalSetLotMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalSetLot> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwhFinalSetLotMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}
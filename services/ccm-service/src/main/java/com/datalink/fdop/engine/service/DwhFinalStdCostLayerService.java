package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostLayer;

import java.util.List;

public interface DwhFinalStdCostLayerService extends IService<DwhFinalStdCostLayer> {

    PageDataInfo<DwhFinalStdCostLayer> overview(
            String plantId, String verId, String controlAreaId, String productId,
            String date, String sort, SearchVo searchVo);

    List<DwhFinalStdCostLayer> selectNoPage(String plantId,
                                            String verId,
                                            String controlAreaId,
                                            String productId,
                                            String date,
                                            String sort,
                                            SearchVo searchVo);
}
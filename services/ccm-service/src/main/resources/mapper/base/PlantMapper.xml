<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.PlantMapper">

    <select id="checkUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_factory
        WHERE factory_id = #{id} limit 1
    </select>

    <delete id="deleteById" parameterType="Long">
        delete from zjdata.ccm_factory
        where factory_id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="Long">
        delete from zjdata.ccm_factory
        WHERE ccm_factory.factory_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="pageList" resultType="com.datalink.fdop.base.api.domain.Plant">
        select a.*, plant.plant_desc as plant_desc
        from zjdata.ccm_factory a
        left join zjdata.ccm_plant plant on a.plant_id = plant.plant_id
        <where>
            <if test="searchVo!=null and searchVo!=''">
                AND ${searchVo}
            </if>
        </where>
        order by a.factory_id desc
    </select>

    <select id="selectFactoryId" resultType="java.lang.Long">
        SELECT factory_id FROM zjdata.ccm_factory
        WHERE factory_id = #{factoryId} limit 1
    </select>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.Plant">
        select a.*, plant.plant_desc as plant_desc
        from zjdata.ccm_factory a
            left join zjdata.ccm_plant plant on a.plant_id = plant.plant_id
        order by factory_id desc
    </select>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_factory
            SET enable = #{item.enable}
            WHERE factory_id = #{item.factoryId}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_factory (
        factory_id, factory_desc, factory_text,plant_id,enable, create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.factoryId},#{item.factoryDesc},#{item.factoryText},#{item.plantId},
            #{item.enable},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>

</mapper>


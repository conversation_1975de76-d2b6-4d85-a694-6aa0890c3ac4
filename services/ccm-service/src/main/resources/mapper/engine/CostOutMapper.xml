<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.CostOutMapper">

    <select id="getWipOrInv" resultType="com.datalink.fdop.engine.model.vo.WipCostReportVo">
        select a.year, a.month, a.year_month, a.ver_id, a.company_id, a.factory_id, a.control_area_id, a.evaluate_area_id,
               a.evaluate_class_id, a.product_id, a.unit1, a.qty1_${costType} as qty1, a.cost_${costType} as cost
        from fccm.${prefix}_cost_report_100 a
        where a.year_month = ${yearMonth} and a.company_id = '${companyId}'
        <if test="factoryId != null and factoryId != ''"> and a.factory_id = '${factoryId}' </if>
        and a.ver_id = '${verId}'
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="selectWipOrInvItem" resultType="com.datalink.fdop.engine.api.domain.WipCostReport200">
        select a.* from fccm.${prefix}_cost_report_200 a
        where a.year_month = ${yearMonth} and a.company_id = '${companyId}'
        <if test="factoryId != null and factoryId != ''"> and a.factory_id = '${factoryId}' </if>
          and a.ver_id = '${verId}'
          and a.product_id = '${productId}' and a.cost_type = '${costType}'
        order by cost_level
    </select>

    <select id="getRd" resultType="com.datalink.fdop.engine.api.domain.RdCostReport100">
        select a.* from fccm.rd_cost_report_100 a
        where a.year_month = ${yearMonth} and a.company_id = '${companyId}'
        <if test="factoryId != null and factoryId != ''"> and a.factory_id = '${factoryId}' </if>
          and a.ver_id = '${verId}'
        <if test="costCenterId != null and costCenterId != ''"> and a.cost_center_id = '${costCenterId}' </if>
        <if test="wbsId != null and wbsId != ''"> and a.wbs_id = '${wbsId}' </if>
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="getRdItem" resultType="com.datalink.fdop.engine.api.domain.RdCostReport200">
        select a.* from fccm.rd_cost_report_200 a
        where a.year_month = ${yearMonth} and a.company_id = '${companyId}'
        <if test="factoryId != null and factoryId != ''"> and a.factory_id = '${factoryId}' </if>
          and a.ver_id = '${verId}'
        <if test="costCenterId != null and costCenterId != ''"> and a.cost_center_id = '${costCenterId}' </if>
        <if test="wbsId != null and wbsId != ''"> and a.wbs_id = '${wbsId}' </if>
          and a.product_id = '${productId}' and a.lot_type = '${lotType}'
        order by cost_level
    </select>

    <select id="getEvent" resultType="com.datalink.fdop.engine.api.domain.EventCostReport100">
        select a.* from fccm.event_cost_report_100 a
        where a.year_month = ${yearMonth} and a.company_id = '${companyId}'
        <if test="factoryId != null and factoryId != ''"> and a.factory_id = '${factoryId}' </if>
          and a.ver_id = '${verId}'
        <if test="eventCode != null and eventCode != ''"> and a.event_code = '${eventCode}' </if>
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="getEventItem" resultType="com.datalink.fdop.engine.api.domain.EventCostReport200">
        select a.* from fccm.event_cost_report_200 a
        where a.year_month = ${yearMonth} and a.company_id = '${companyId}'
        <if test="factoryId != null and factoryId != ''"> and a.factory_id = '${factoryId}' </if>
          and a.ver_id = '${verId}'
        <if test="eventCode != null and eventCode != ''"> and a.event_code = '${eventCode}' </if>
          and a.product_id = '${productId}'
        order by cost_level
    </select>

    <select id="getPc" resultType="com.datalink.fdop.engine.api.domain.PcCostReport100">
        select a.* from fccm.pc_cost_report_100 a
        where a.year_month = ${yearMonth} and a.company_id = '${companyId}'
        <if test="factoryId != null and factoryId != ''"> and a.factory_id = '${factoryId}' </if>
          and a.ver_id = '${verId}'
        <if test="workOrder != null and workOrder != ''"> and a.work_order = '${workOrder}' </if>
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="getPcItem" resultType="com.datalink.fdop.engine.api.domain.PcCostReport200">
        select a.* from fccm.pc_cost_report_200 a
        where a.year_month = ${yearMonth} and a.company_id = '${companyId}'
        <if test="factoryId != null and factoryId != ''"> and a.factory_id = '${factoryId}' </if>
          and a.ver_id = '${verId}'
        <if test="workOrder != null and workOrder != ''"> and a.work_order = '${workOrder}' </if>
        order by cost_level
    </select>


</mapper>
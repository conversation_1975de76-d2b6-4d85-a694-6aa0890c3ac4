/*
 * Datart
 * <p>
 * Copyright 2021
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.datalink.fdop.datart.service.impl;

import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.datart.base.dto.ViewDetailDTO;
import com.datalink.fdop.datart.domain.Source;
import com.datalink.fdop.datart.domain.View;
import com.datalink.fdop.datart.domain.ViewMenu;
import com.datalink.fdop.datart.mapper.ViewMapper;
import com.datalink.fdop.datart.mapper.ViewMenuMapper;
import com.datalink.fdop.datart.service.SourceService;
import com.datalink.fdop.datart.service.ViewService;
import datart.core.base.consts.Const;
import datart.core.common.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class ViewServiceImpl implements ViewService {

    @Autowired
    private ViewMapper viewMapper;

    @Autowired
    private ViewMenuMapper viewMenuMapper;


    @Autowired
    private SourceService sourceService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public View createView(View view) {
        if (view.getSourceId() != null) {
            Source source = sourceService.createSourceByDataSourceId(view.getSourceId());
            view.setDataSourceId(source != null? source.getId() : null);
        }
        view.setId(UUIDGenerator.generate());
        view.setStatus(Const.DATA_STATUS_ACTIVE);
        if (view.getPid() != -1L && viewMenuMapper.selectById(view.getPid()) == null) {
            throw new ServiceException(Status.DATA_CHART_MENU_DOES_NOT_EXIST);
        }
        if (viewMapper.selectByCode(view.getCode()) != null) {
            throw new ServiceException(Status.THE_DATA_CHART_ALREADY_EXISTS);
        }
        int insert = viewMapper.createView(view);
        // TODO:添加实体和数据图表的关系（未来可能添加其他功能与图表的关系）
        // if (insert > 0) {
        //     dataChartMapper.createChartAndEntityEdge(dataChart.getId(), dataChart.getDataEntityId());
        // }
        // 创建元素边关系
        if (insert > 0 && view.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            viewMapper.createViewAndMenuEdge(view.getPid(), Arrays.asList(view.getId()));
        }
        
        //viewMapper.createView(view);

        return getViewDetail(view.getId());
    }

    @Override
    @Transactional
    public int deleteView(List<String> viewIds) {
        return viewMapper.deleteView(viewIds);
    }

    @Override
    public ViewDetailDTO getViewDetail(String viewId) {
        VlabelItem<View> viewVlabelItem = viewMapper.selectById(viewId);
        if (viewVlabelItem==null) {
            throw new ServiceException("视图不存在");
        }
        View view = viewMapper.selectById(viewId).getProperties();

        ViewDetailDTO viewDetailDTO = new ViewDetailDTO(view);
        // column permission
        viewDetailDTO.setRelSubjectColumns(new ArrayList<>());
        //view variables
        viewDetailDTO.setVariables(new ArrayList<>());
        // view variables rel
        viewDetailDTO.setRelVariableSubjects(new ArrayList<>());
        return viewDetailDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(View view) {
        if (view.getSourceId() != null) {
            Source source = sourceService.createSourceByDataSourceId(view.getSourceId());
            view.setDataSourceId(source != null ? source.getId() : null);
        }
        VlabelItem<View> viewVlabelItem = viewMapper.selectById(view.getId());
        if (viewVlabelItem == null) {
            throw new ServiceException("视图不存在");
        }
        View vlabelItem = viewMapper.selectById(view.getId()).getProperties();
        if (vlabelItem == null) {
            throw new ServiceException(Status.THE_DASHBOARD_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(view.getCode()) && viewMapper.checkCodeIsExists(view.getId(), view.getCode()) != null) {
            throw new ServiceException(Status.THE_DASHBOARD_ALREADY_EXISTS);
        }
        // 不能拖拽到节点里面
        if (view.getPid() != null && view.getPid() != -1L && viewMenuMapper.selectById(view.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_DASHBOARD_MENU);
        }
        String Script = "'" + view.getScript().replace( "'", "\\'")+ "'";
        view.setScript(Script);
        System.out.println("view.toString："+view.toString());
        int update = viewMapper.updateById(view);
        if (update > 0 && view.getPid() != null) {
            // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
            viewMapper.deleteViewAndMenuEdge(Arrays.asList(view.getId()), vlabelItem.getPid());
            if (view.getPid() != -1L) {
                // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                viewMapper.createViewAndMenuEdge(view.getPid(), Arrays.asList(view.getId()));
            }
        }
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(Long pid, List<String> viewIds) {
        VlabelItem<ViewMenu> vlabelItem = viewMenuMapper.selectById(pid);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_CHART_MENU_DOES_NOT_EXIST);
        }
        for (String viewId : viewIds) {
            VlabelItem<View> viewVlabelItem = viewMapper.selectById(viewId);
            if (viewVlabelItem==null) {
                throw new ServiceException("视图不存在");
            }
            View view = viewMapper.selectById(viewId).getProperties();
            view.setPid(pid);
            view.setName(view.getName()+"_copy");
            view.setCode(view.getCode()+"_copy");
            view.setDescription(view.getDescription()+"_copy");
            this.createView(view);
        }
    }


}

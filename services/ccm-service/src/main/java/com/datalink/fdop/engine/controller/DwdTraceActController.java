package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.engine.api.domain.DwdTraceAct;
import com.datalink.fdop.base.api.domain.ProductVo;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdMaterialMaraService;
import com.datalink.fdop.engine.service.DwdTraceActService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdTraceAct")
@Api(tags = "CCMS-dwd_trace_act")
public class DwdTraceActController extends BaseController {

    @Autowired
    private DwdTraceActService dwdTraceActService;

    @Autowired
    private DwdMaterialMaraService dwdMaterialMaraService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "报工作业履历")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        String productId = MapUtils.getAsString(params, "productId");
        String dateFrom = MapUtils.getAsString(params, "dateFrom");
        String dateTo = MapUtils.getAsString(params, "dateTo");
        PageDataInfo<DwdTraceAct> overview = dwdTraceActService.overview(verId, plantId, productId, dateFrom, dateTo, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdTraceAct.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "报工作业履历", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        String productId = MapUtils.getAsString(params, "productId");
        String dateFrom = MapUtils.getAsString(params, "dateFrom");
        String dateTo = MapUtils.getAsString(params, "dateTo");
        List<DwdTraceAct> list = dwdTraceActService.selectNoPage(verId, plantId, productId, dateFrom, dateTo, sort, queryParam.getSearchVo());
        ExcelUtil<DwdTraceAct> util = new ExcelUtil<>(DwdTraceAct.class);
        util.exportExcel(response, list, "报工整合管理-同步视图数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdTraceAct> util = new ExcelUtil<>(DwdTraceAct.class);
        util.importTemplateExcel(response, "报工整合管理-同步视图模板");
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwd.dwd_trace_act", null));
    }

    @ApiOperation("查询产品编码（选择列表）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "查询产品编码（选择列表）")
    @GetMapping(value = "/selectProductList")
    public Object selectProductList(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false, value = "productId") String productId,
            @RequestParam(required = false, value = "productDesc") String productDesc,
            @RequestParam(required = false, value = "productCimId") String productCimId,
            @RequestBody(required = false) SearchVo searchVo) {
        PageDataInfo<ProductVo> overview = dwdMaterialMaraService.selectProductListByTableName(productId, productDesc, productCimId, sort, searchVo, "dwd.dwd_trace_act");
        return R.ok(MetaUtils.getMetadata(ProductVo.class, overview));
    }

    @ApiOperation(value = "查询所有work_order")
    @GetMapping("/listWorkOrder")
    public R<List<String>> listWorkOrder() {
        return R.ok(dwdTraceActService
                .lambdaQuery()
                .select(DwdTraceAct::getWorkOrder)
                .groupBy(DwdTraceAct::getWorkOrder)
                .list()
                .stream()
                .filter(dwdTraceAct -> dwdTraceAct != null && dwdTraceAct.getWorkOrder() != null)
                .map(DwdTraceAct::getWorkOrder)
                .collect(Collectors.toList()));
    }


}

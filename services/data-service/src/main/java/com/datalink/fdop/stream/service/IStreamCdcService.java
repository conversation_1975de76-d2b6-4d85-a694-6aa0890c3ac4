package com.datalink.fdop.stream.service;

import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.stream.api.domain.DorisConfig;
import com.datalink.fdop.stream.api.domain.StreamCdc;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
public interface IStreamCdcService {

    int create(StreamCdc streamCdc);

    int update(StreamCdc streamCdc);

    int delete(List<Long> ids);

    StreamCdc selectById(Long id);

    int online(String code);

    int offline(String code);

    JSONObject showDorisStatus(StreamCdc streamCdc);

    String getDorisMonitorInfo();

    /**
     * Doris任务上线
     * @param streamCdc doris数据采集配置
     * @return  执行结果
     */
    R dorisOnline(StreamCdc streamCdc);

    /**
     * Doris任务下线
     * @param streamCdc doris数据采集配置
     * @return  执行结果
     */
    R dorisOffline(StreamCdc streamCdc);

    /**
     * Doris任务暂停
     * @param streamCdc doris数据采集配置
     * @return  执行结果
     */
    R dorisPause(StreamCdc streamCdc);

    /**
     * Doris任务恢复
     * @param streamCdc doris数据采集配置
     * @return  执行结果
     */
    R dorisResume(StreamCdc streamCdc);


    /**
     * Doris任务停止
     * @param streamCdc doris数据采集配置
     * @return  执行结果
     */
    R dorisStop(StreamCdc streamCdc);


    /**
     * DorisSQL语法校验
     * @param sql doris数据采集SQL
     * @return  执行结果
     */
    R checkDorisSql(String sql);
}


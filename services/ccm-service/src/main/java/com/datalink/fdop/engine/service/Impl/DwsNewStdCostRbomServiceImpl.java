package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostRbom;
import com.datalink.fdop.engine.mapper.DwsNewStdCostRbomMapper;
import com.datalink.fdop.engine.service.DwsNewStdCostRbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewStdCostRbomServiceImpl extends ServiceImpl<DwsNewStdCostRbomMapper, DwsNewStdCostRbom> implements DwsNewStdCostRbomService {

    @Autowired
    private DwsNewStdCostRbomMapper dwsNewStdCostRbomMapper;

    @Override
    public PageDataInfo<DwsNewStdCostRbom> overview(
            String controlAreaId, String productId, Integer batchQty,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwsNewStdCostRbom> page = PageUtils.getPage(DwsNewStdCostRbom.class);
        IPage<DwsNewStdCostRbom> iPage = dwsNewStdCostRbomMapper.selectAll(controlAreaId, productId, batchQty,verId, plantId, date,  page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewStdCostRbom> selectNoPage(String controlAreaId, String productId, Integer batchQty, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwsNewStdCostRbomMapper.selectNoPage(controlAreaId, productId, batchQty, verId, plantId, date, sort, searchVo);
    }


}

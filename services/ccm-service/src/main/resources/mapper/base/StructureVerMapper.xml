<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.StructureVerMapper">

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_structure_ver
        WHERE structure_ver_id = #{structureVerId} and date_to = '${dateTo}'  limit 1
    </select>

    <select id="listAll" resultType="java.lang.String">
        select distinct structure_ver_id from zjdata.ccm_structure_ver
        where enable = true
            <if test="structureVerId !=null and structureVerId !=''">
                 and structure_ver_id = #{structureVerId}
            </if>
    </select>

    <delete id="deleteById" parameterType="String">
        delete from zjdata.ccm_structure_ver
        where attribute_id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from zjdata.ccm_structure_ver
        WHERE (structure_ver_id,date_to) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.structureVerId},'${item.dateTo}')
        </foreach>
    </delete>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_structure_ver
        SET enable = #{enable}
        WHERE (structure_ver_id,date_to) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.structureVerId},'${item.dateTo}')
        </foreach>
    </update>

    <update id="update">
        UPDATE zjdata.ccm_structure_ver
        set
        <if test="structureVerDesc != null ">structure_ver_desc =#{structureVerDesc},</if>
        <if test="structureVerText != null ">structure_ver_text=#{structureVerText},</if>
        <if test="plantId != null ">plant_id =#{plantId},</if>
        <if test="companyId != null ">company_id =#{companyId},</if>
        <if test="controlAreaId != null ">control_area_id =#{controlAreaId},</if>
        <if test="dateFrom != null">date_from ='${dateFrom}',</if>
        <if test="enable != null ">enable =#{enable},</if>
        update_by =#{updateBy},update_time=#{updateTime}
        WHERE structure_ver_id = #{structureVerId} and date_to = '${dateTo}'
    </update>

    <select id="pageList" resultType="com.datalink.fdop.base.api.domain.StructureVer">
        select a.*,b.plant_desc as plantDesc,c.company_desc as companyDesc,d.control_area_desc as controlAreaDesc from zjdata.ccm_structure_ver  a
        left join zjdata.ccm_plant b on a.plant_id = b.plant_id
        left join zjdata.ccm_company c on a.company_id = c.company_id
        left join zjdata.ccm_control_area d on a.control_area_id = d.control_area_id
        <where>
            <if test="searchVo !=null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("a",searchVo)})
            </if>
        </where>
        order by a.structure_ver_id,a.date_to
        <if test="sort != null and sort != ''">
            ${sort}
        </if>
    </select>

</mapper>


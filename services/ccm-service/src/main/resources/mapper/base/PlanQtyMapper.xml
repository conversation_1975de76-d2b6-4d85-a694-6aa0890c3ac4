<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.PlanQtyMapper">

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.PlanQty" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_plan_qty
        WHERE control_area_id = #{planQty.controlAreaId}
          AND ver_id = #{planQty.verId}
          AND cost_center_id = #{planQty.costCenterId}
          AND cost_center_date_from = #{planQty.costCenterDateFrom}
          AND cost_structure_id = #{planQty.costStructureId}
          AND cost_structure_date_from = #{planQty.costStructureDateFrom}
          AND year_month = #{planQty.yearMonth}
          AND del_flag = false limit 1
    </select>

    <update id="deleteById" parameterType="Long">
        update zjdata.ccm_plan_qty
        set del_flag = true
        where id = #{id} and del_flag = false
    </update>

    <update id="deleteByIds" parameterType="Long">
        update zjdata.ccm_plan_qty
        SET del_flag = true
        WHERE
        <foreach collection="planQtyList" item="planQty" separator=" or ">
            ( control_area_id = #{planQty.controlAreaId}
            AND ver_id = #{planQty.verId}
            AND cost_center_id = #{planQty.costCenterId}
            AND cost_center_date_from = #{planQty.costCenterDateFrom}
            AND cost_structure_id = #{planQty.costStructureId}
            AND cost_structure_date_from = #{planQty.costStructureDateFrom}
            AND year_month = #{planQty.yearMonth} )
        </foreach>
        and del_flag = false
    </update>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.PlanQty">
        update zjdata.ccm_plan_qty
        <set>
            <if test="amount != null">amount = #{amount},</if>
            <if test="multiple != null">multiple = #{multiple},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        where control_area_id = #{controlAreaId}
        AND ver_id = #{verId}
        AND cost_center_id = #{costCenterId}
        AND cost_center_date_from = #{costCenterDateFrom}
        AND cost_structure_id = #{costStructureId}
        AND cost_structure_date_from = #{costStructureDateFrom}
        AND year_month = #{yearMonth} AND del_flag = false
    </update>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.PlanQty" resultType="com.datalink.fdop.base.api.domain.PlanQty">
        SELECT *
        FROM zjdata.ccm_plan_qty
        WHERE del_flag = false
        ORDER BY control_area_id desc
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.base.api.domain.PlanQty">
        SELECT ls.*, ver.code as ver_code, costCenter.code as kostl_code, costCenter.abbreviation as kostl_abbreviation,
               costStructureHead.code as lstar_code, costStructureHead.unit as qty_unit
        FROM zjdata.ccm_plan_qty ls left join zjdata.org_ver ver on ls.ver_id = ver.id
                                left join zjdata.p_d_kostl costCenter on costCenter.id = ls.kostl_id
                                left join zjdata.org_lstar costStructureHead on ls.lstar_id = costStructureHead.id
        where ls.code = #{code} and ls.del_flag = false limit 1
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.PlanQty">
        SELECT ls.*
        FROM zjdata.ccm_plan_qty ls
        WHERE ls.del_flag = false
        <if test="a != null and a != ''">
            AND ${a}
        </if>
        ORDER BY id ${sort}
    </select>

    <select id="selectTableIdByCode" resultType="java.lang.Long">
        SELECT id FROM zjdata.${tableName} where
        <choose>
            <when test="tableName.equals('org_company')">company_code</when>
            <otherwise>code</otherwise>
        </choose>
        = #{code} limit 1
    </select>

    <select id="selectId" resultType="com.datalink.fdop.base.api.domain.PlanQty">
        SELECT ls.*, ver.code as ver_code, costCenter.code as kostl_code, costCenter.abbreviation as kostl_abbreviation,
               costStructureHead.code as lstar_code, costStructureHead.unit as qty_unit
        FROM zjdata.ccm_plan_qty ls left join zjdata.org_ver ver on ls.ver_id = ver.id
                                left join zjdata.p_d_kostl costCenter on costCenter.id = ls.kostl_id
                                left join zjdata.org_lstar costStructureHead on ls.lstar_id = costStructureHead.id
        WHERE ls.del_flag = false AND ls.ver_id = #{verId} AND ls.kostl_id = #{kostlId}
            AND ls.period = #{time} AND ls.lstar_id = #{lstarId} limit 1
    </select>

    <select id="selectPlanQty" resultType="com.datalink.fdop.base.api.domain.PlanQty">
        SELECT ls.*, ver.code as ver_code, costCenter.code as kostl_code, costCenter.abbreviation as kostl_abbreviation,
               costStructureHead.code as lstar_code, costStructureHead.unit as qty_unit
        FROM zjdata.ccm_plan_qty ls left join zjdata.org_ver ver on ls.ver_id = ver.id
                                left join zjdata.p_d_kostl costCenter on costCenter.id = ls.kostl_id
                                left join zjdata.org_lstar costStructureHead on ls.lstar_id = costStructureHead.id
        WHERE ls.del_flag = false AND ls.ver_id = #{verId} AND ls.kostl_id = #{kostlId}
          AND ls.period = #{effectiveDate}
    </select>

</mapper>


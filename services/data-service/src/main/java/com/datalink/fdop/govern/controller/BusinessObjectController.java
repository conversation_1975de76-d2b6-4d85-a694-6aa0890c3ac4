package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.govern.api.domain.BusinessObject;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import com.datalink.fdop.govern.service.IBusinessObjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/govern/businessObject")
@Api(tags = "L3")
public class BusinessObjectController {

    @Autowired
    private IBusinessObjectService businessObjectService;

    @ApiOperation(value = "新增")
    @Log(title = "业务对象L3新增", businessType = BusinessType.INSERT)
    @PostMapping
    public R create(@RequestBody BusinessObject entity) {
        return R.ok(businessObjectService.create(entity));
    }

    @ApiOperation(value = "修改")
    @Log(title = "业务对象L3修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public R update(@RequestBody BusinessObject entity) {
        return R.ok(businessObjectService.update(entity));
    }

    @ApiOperation(value = "删除")
    @Log(title = "业务对象L3删除", businessType = BusinessType.DELETE)
    @DeleteMapping
    public R delete(@RequestBody List<Long> ids) {
        return R.toResult(businessObjectService.delete(ids));
    }

    @ApiOperation(value = "selectById")
    @Log(title = "业务对象L3根据id查询", businessType = BusinessType.DELETE)
    @GetMapping("/selectById/{id}")
    public R<BusinessObject> selectById(@PathVariable Long id) {
        return R.ok(businessObjectService.selectById(id));
    }

    @ApiOperation(value = "查询列表")
    @Log(title = "业务对象L3查询列表")
    @PostMapping("/list")
    public R<PageDataInfo> list(@RequestBody(required = false) BusinessObject entity,
                                @RequestParam(required = false, value = "sort", defaultValue = "asc") String sort) {
        return R.ok(businessObjectService.list(entity, sort));
    }

    @ApiOperation(value = "注册")
    @Log(title = "业务对象L3注册", businessType = BusinessType.UPDATE)
    @PostMapping("/register")
    public R register(@RequestBody List<Long> ids) {
        return R.ok(businessObjectService.register(ids));
    }

    @ApiOperation(value = "反注册")
    @Log(title = "业务对象L3反注册", businessType = BusinessType.UPDATE)
    @PostMapping("/unregister")
    public R unregister(@RequestBody List<Long> ids) {
        return R.ok(businessObjectService.unregister(ids));
    }

    @ApiOperation(value = "导入")
    @Log(title = "业务对象L3-导入")
    @PostMapping("/importData")
    public R importData(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        ExcelUtil<BusinessObject> util = new ExcelUtil<>(BusinessObject.class);
        List<BusinessObject> list = util.importExcel(file.getInputStream());
        String operatorName = SecurityUtils.getUsername();
        businessObjectService.importData(file.getOriginalFilename(), list, operatorName);
        return R.ok("导入中，请移至日志查看");
    }

    @ApiOperation(value = "导入模板")
    @Log(title = "业务对象L3-导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<BusinessObject> util = new ExcelUtil<>(BusinessObject.class);
        util.importTemplateExcel(response, "businessObject");
    }

    @ApiOperation("导出数据")
    @Log(title = "业务对象L3-导出数据")
    @PostMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestBody(required = false) BusinessObject entity,
                       @RequestParam(required = false, value = "sort", defaultValue = "asc") String sort) {
        List<BusinessObject> list = businessObjectService.getExportList(entity, sort);
        ExcelUtil<BusinessObject> util = new ExcelUtil<>(BusinessObject.class);
        util.exportExcel(response, list, "业务对象L3数据");
    }

    @ApiOperation(value = "导入日志")
    @Log(title = "业务对象L3-导入日志")
    @PostMapping(value = "/importLog")
    public R<PageDataInfo<ImportDataLog>> importLog(@RequestBody(required = false) ImportDataLog importDataLog,
                                                    @RequestParam(value = "sort", defaultValue = "DESC", required = false) String sort) {
        return R.ok(businessObjectService.importLog(importDataLog, sort));
    }

    @ApiOperation(value = "日志详情")
    @Log(title = "业务对象L3-日志详情")
    @GetMapping(value = "/importLog/selectById/{id}")
    public R<ImportDataLog> selectByIdLog(@PathVariable Long id) {
        return R.ok(businessObjectService.selectByIdLog(id));
    }

}

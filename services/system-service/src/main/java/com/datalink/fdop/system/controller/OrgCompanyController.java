package com.datalink.fdop.system.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.annotation.RequiresPermissions;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.system.api.domain.KokrsVo;
import com.datalink.fdop.system.api.domain.OrgCompany;
import com.datalink.fdop.system.domain.vo.CompanySearchVo;
import com.datalink.fdop.system.service.IOrgCompanyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-09-05 11:47
 */
@RestController
@RequestMapping("/system/company")
@Api(tags = "组织-公司API")
public class OrgCompanyController extends BaseController {

    @Autowired
    private IOrgCompanyService companyService;

    @ApiOperation(value = "新增公司")
    //@RequiresPermissions("system:company:add")
    @Log(title = "公司管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody OrgCompany company) {
        if (UserConstants.NOT_UNIQUE.equals(companyService.checkCompanyCodeUnique(company.getCompanyCode()))) {
            return R.fail("新增公司'" + company.getCompanyCode() + "'失败，公司已存在");
        }
        return R.toResult(companyService.insertCompany(company));
    }

    @ApiOperation(value = "修改公司")
    //@RequiresPermissions("system:company:edit")
    @Log(title = "公司管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@Validated @RequestBody OrgCompany company) {
        if (StringUtils.isNotEmpty(company.getCompanyCode())
                && UserConstants.NOT_UNIQUE.equals(companyService.checkCompanyCode(company.getId(), company.getCompanyCode()))) {
            return R.fail("修改公司'" + company.getCompanyCode() + "'失败，公司已存在");
        }
        return R.toResult(companyService.updateCompany(company));
    }

    @ApiOperation(value = "删除公司")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyIds", value = "公司ID集合", required = true, allowMultiple = true, dataType = "Long", paramType = "body", example = "[1,2]"),
    })
    //@RequiresPermissions("system:company:remove")
    @Log(title = "公司管理", businessType = BusinessType.DELETE)
    @DeleteMapping()
    public R remove(@RequestBody List<Long> companyIds) {
        return R.toResult(companyService.deleteCompanyByIds(companyIds));
    }

    @ApiOperation(value = "查询公司列表")
    @Log(title = "公司管理")
    @PostMapping("/query")
    public R<PageDataInfo> query(@RequestBody(required = false) CompanySearchVo searchVo) {
        return R.ok(companyService.query(searchVo));
    }

    @ApiOperation(value = "获取公司列表")
    @Log(title = "公司管理")
    @PostMapping("/list")
    public R<PageDataInfo> list(@RequestBody(required = false) OrgCompany company) {
        return R.ok(companyService.selectCompanyList(company));
    }

    @ApiOperation(value = "导出公司")
    @Log(title = "公司管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:company:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody(required = false) CompanySearchVo searchVo) {
        PageDataInfo<OrgCompany> query = companyService.query(searchVo);
        ExcelUtil<OrgCompany> util = new ExcelUtil<>(OrgCompany.class);
        util.exportExcel(response, query.getTotalList(), "公司数据");
    }

    @ApiOperation(value = "导入公司")
    @Log(title = "公司管理", businessType = BusinessType.IMPORT)
    //@RequiresPermissions("system:company:import")
    @PostMapping("/importData")
    public R importData(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        boolean updateSupport = false;
        ExcelUtil<OrgCompany> util = new ExcelUtil<>(OrgCompany.class);
        List<OrgCompany> companyList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = companyService.importCompany(companyList, updateSupport, operName);
        return R.ok(message);
    }

    @ApiOperation(value = "导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<OrgCompany> util = new ExcelUtil<>(OrgCompany.class);
        util.importTemplateExcel(response, "公司数据");
    }


    @ApiOperation(value = "分页查询kokrs")
    @Log(title = "fccm基础规则",businessType = BusinessType.OTHER)
    @PostMapping("/queryKokrs")
    public R query(@RequestBody(required = false) KokrsVo kokrs) {
        Page<KokrsVo> page = PageUtils.getPage(KokrsVo.class);
        SearchVo searchCondition = kokrs.getSearchCondition();
        String a =null;
        if (searchCondition!=null) {
            a= SearchUtils.parseSqlSearchCondition("a",searchCondition);
        }
        Page<KokrsVo> dataPage = companyService.pageList(page,a);
        return R.ok(PageUtils.getPageInfo(dataPage.getRecords(),(int)dataPage.getTotal()));
    }



}

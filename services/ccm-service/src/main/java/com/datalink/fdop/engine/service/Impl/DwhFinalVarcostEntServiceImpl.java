package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalVarcostEnt;
import com.datalink.fdop.engine.mapper.DwhFinalVarcostEntMapper;
import com.datalink.fdop.engine.service.DwhFinalVarcostEntService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalVarcostEntServiceImpl extends ServiceImpl<DwhFinalVarcostEntMapper, DwhFinalVarcostEnt> implements DwhFinalVarcostEntService {

    @Autowired
    private DwhFinalVarcostEntMapper dwhFinalVarcostEntMapper;

    @Override
    public PageDataInfo<DwhFinalVarcostEnt> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwhFinalVarcostEnt> page = PageUtils.getPage(DwhFinalVarcostEnt.class);
        IPage<DwhFinalVarcostEnt> iPage = dwhFinalVarcostEntMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalVarcostEnt> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwhFinalVarcostEntMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalPbom;

import java.util.List;

public interface DwhFinalPbomService extends IService<DwhFinalPbom> {

    PageDataInfo<DwhFinalPbom> overview(String verId,String  plantId,String productId,String sort, SearchVo searchVo);

    List<DwhFinalPbom> listAll();

    List<DwhFinalPbom> selectNoPage(String verId,
                                    String plantId,
                                    String productId,
                                    String sort,
                                    SearchVo searchVo);
}

package com.datalink.fdop.auth.mapper;

import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.auth.api.domain.AuthObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/7 10:32
 */

@Repository
@Transactional
@Mapper
public interface AuthGroupObjectRelationMapper {

    int saveRelation(@Param("groupId") Long groupId,@Param("objectIds") List<Long> objectIds);

    int delRelation(@Param("groupId") Long groupId,@Param("objectIds") List<Long> objectIds);

    List<VlabelItem<AuthObject>> showNotAdded(@Param("code") String code, @Param("name") String name, @Param("groupId") Long groupId);

    List<Long> showAdded( @Param("groupId") Long groupId);

    List<VlabelItem<AuthObject>> showNotAddedAuthObject(@Param("authObjectIds")List<Long> authObjectIds,@Param("code") String code, @Param("name") String name);

    List<VlabelItem<AuthObject>> showAddedAuthObject(@Param("authObjectIds")List<Long> authObjectIds,@Param("code") String code, @Param("name") String name);

    int showAddedCount(Long groupId);

    int showNotAddedAuthObjectCount(@Param("authObjectIds")List<Long> authObjectIds,@Param("code") String code, @Param("name") String name);

    int showAddedAuthObjectCount(@Param("authObjectIds")List<Long> authObjectIds,@Param("code") String code, @Param("name") String name);
}

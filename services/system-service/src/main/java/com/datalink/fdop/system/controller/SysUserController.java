package com.datalink.fdop.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.datasource.annotation.SwitchDataSource;
import com.datalink.fdop.common.datasource.dynamic.DataSourceHolder;
import com.datalink.fdop.common.datasource.dynamic.DynamicDataSource;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.annotation.RequiresPermissions;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.system.api.domain.SysOnlineUser;
import com.datalink.fdop.system.api.domain.SysRole;
import com.datalink.fdop.system.api.domain.SysUser;
import com.datalink.fdop.system.api.domain.TenantUser;
import com.datalink.fdop.system.api.model.LoginUser;
import com.datalink.fdop.system.service.ISysConfigService;
import com.datalink.fdop.system.service.ISysPermissionService;
import com.datalink.fdop.system.service.ISysRoleService;
import com.datalink.fdop.system.service.ISysUserService;
import com.datalink.fdop.tenant.api.domain.Tenant;
import com.datalink.fdop.tenant.api.domain.dto.DataSourceInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
@Api(tags = "用户API")
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    // @Autowired
    // private ISysPostService postService;

    @Autowired
    private ISysPermissionService permissionService;

    @Autowired
    private ISysConfigService configService;

    @ApiOperation(value = "获取用户列表")
    @PostMapping("/list")
    public R<PageDataInfo> list(@RequestBody(required = false) SysUser user) {
        return R.ok(userService.selectUserList(user));
    }

    @ApiOperation(value = "导出用户")
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    //@RequiresPermissions("system:user:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user).getTotalList();
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @ApiOperation(value = "导入用户")
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    //@RequiresPermissions("system:user:import")
    @PostMapping("/importData")
    public R importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return R.ok(message);
    }

    @ApiOperation(value = "导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    @ApiOperation(value = "根据租户id和用户名获取当前用户信息", hidden = true)
    // @InnerAuth
    @GetMapping("/info/{tenantId}/{username}")
    public R<LoginUser> info(@PathVariable("tenantId") Long tenantId, @PathVariable("username") String username) {
        LoginUser loginUser = userService.getLoginUserInfoByTenantIdAndUserName(tenantId, username);
        Long userId = loginUser.getSysUser().getUserId();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(userId);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(userId);
        loginUser.setRoles(roles);
        loginUser.setPermissions(permissions);
        return R.ok(loginUser);
    }

    @ApiOperation(value = "根据租户id和用户名锁定用户", hidden = true)
    // @InnerAuth
    @GetMapping("/info/{tenantId}/{username}/lockUser")
    public R lockUser(@PathVariable("tenantId") Long tenantId, @PathVariable("username") String username) {
        return R.ok(userService.lockUser(tenantId, username));
    }

    @ApiOperation(value = "获取当前用户信息", hidden = true)
    //@InnerAuth
    @GetMapping("/info/{username}")
    public R<LoginUser> info(@PathVariable("username") String username) {
        SysUser sysUser = userService.selectUserByUserName(username);
        if (StringUtils.isNull(sysUser)) {
            return R.fail("用户名或密码错误");
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser.getUserId());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser.getUserId());
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return R.ok(sysUserVo);
    }


    @ApiOperation(value = "获取当前用户信息", hidden = true)
    @GetMapping("/info/username")
    public R<LoginUser> info2(@RequestParam("username") String username) {
        SysUser sysUser = userService.selectUserByUserName(username);
        if (StringUtils.isNull(sysUser)) {
            return R.fail("用户名或密码错误");
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser.getUserId());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser.getUserId());
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return R.ok(sysUserVo);
    }

    @ApiOperation(value = "获取当前用户信息", hidden = true)
    @GetMapping("/info/infoById")
    public R<LoginUser> infoById(@RequestParam("userId") Long userId) {
        SysUser sysUser = userService.selectUserById(userId);
        if (StringUtils.isNull(sysUser)) {
            return R.fail("用户名或密码错误");
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser.getUserId());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser.getUserId());
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return R.ok(sysUserVo);
    }

    @ApiOperation(value = "注册用户信息", hidden = true)
    //  @InnerAuth
    @PostMapping("/register")
    public R<Boolean> register(@RequestBody SysUser sysUser) {
        String username = sysUser.getUserName();
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
            return R.fail("当前系统没有开启注册功能！");
        }
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(null, username))) {
            return R.fail("保存用户'" + username + "'失败，注册账号已存在");
        }
        return R.ok(userService.registerUser(sysUser));
    }

    @ApiOperation(value = "获取用户信息")
    @GetMapping("getInfo")
    public R getInfo() {
        Long userId = SecurityUtils.getUserId();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(userId);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(userId);
        Map<String, Object> map = new HashMap();
        map.put("user", userService.selectUserById(userId));
        map.put("roles", roles);
        map.put("permissions", permissions);
        return R.ok(map);
    }

    @ApiOperation(value = "根据用户编号获取详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "path"),
    })
    @RequiresPermissions("system:user:query")
    @GetMapping(value = {"/", "/{userId}"})
    public R getInfo(@PathVariable(value = "userId") Long userId) {
        userService.checkUserDataScope(userId);
        Map<String, Object> map = new HashMap();
        List<SysRole> roles = roleService.selectRoleAll();
        map.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        // ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            // map.put(AjaxResult.DATA_TAG, sysUser);
            map.put("user", sysUser);
            // ajax.put("postIds", postService.selectPostListByUserId(userId));
            map.put("roleIds", sysUser.getRoles().stream().map(sysRole -> sysRole.getRoleId().toString()).collect(Collectors.toList()));
        }
        return R.ok(map);
    }

    @ApiOperation(value = "新增用户")
    //@RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(null, user.getUserName()))) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(SecurityUtils.getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setDelFlag("0");
        return R.toResult(userService.insertUser(user));
    }

    @ApiOperation(value = "修改用户")
    //@RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (StringUtils.isNotEmpty(user.getUserName())
                && UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserId(), user.getUserName()))) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        return R.toResult(userService.updateUser(user));
    }


    @ApiOperation(value = "个人中心修改个人信息")
    //@RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateNotImportInfo")
    public R editNotImportInfo(@Validated @RequestBody SysUser user) {
        if (StringUtils.isNotEmpty(user.getUserName())
                && UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserId(), user.getUserName()))) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        return R.toResult(userService.updateUser(user));
    }


    @ApiOperation(value = "修改用户其他json")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/other")
    public R other(@RequestParam("username") String username, @RequestBody String otherJson) {
        return R.toResult(userService.updateUserOtherJson(username, otherJson));
    }

    @ApiOperation(value = "删除用户")
    //@RequiresPermissions("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping()
    public R remove(@RequestBody List<Long> userIds) {
        if (ArrayUtils.contains(userIds.toArray(), SecurityUtils.getUserId())) {
            return R.fail("当前用户不能删除");
        }
        return R.toResult(userService.deleteUserByIds(userIds));
    }

    @ApiOperation(value = "修改密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "oldPassword", value = "旧密码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "newPassword", value = "新密码", required = true, dataType = "String", paramType = "query"),
    })
    //@RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changePwd/{userId}")
    public R changePwd(@PathVariable("userId") Long userId, @RequestParam("oldPassword") String oldPassword, @RequestParam("newPassword") String newPassword) {
        SysUser user = new SysUser(userId);
        // userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        return R.toResult(userService.changePwd(userId, oldPassword, newPassword));
    }

    @ApiOperation(value = "解锁用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "query"),
    })
    //@RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/unlockUser/{userId}")
    public R unlockUser(@PathVariable("userId") Long userId) {
        SysUser user = new SysUser(userId);
        // userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        return R.toResult(userService.unlockUser(userId));
    }

    @ApiOperation(value = "第一次登录修改密码")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/firstLoginChangePwd/{tenantId}/{username}")
    public R changePwd(@PathVariable("tenantId") Long tenantId, @PathVariable("username") String username, @RequestParam("oldPassword") String oldPassword, @RequestParam("newPassword") String newPassword) {
        // 默认MAIN租户 预防异常导致报错
        if (tenantId == null || tenantId == 0L) {
            tenantId = Constants.DEFAULT_TENANT_ID;
        }
        // 获取租户信息
        Tenant tenant = userService.getTenant(tenantId);
        if (tenant == null) {
            throw new ServiceException("租户：" + (StringUtils.isNotEmpty(tenant.getCode()) ? tenant.getCode() : tenantId) + " 不存在");
        }
        if (DateUtils.isBefore(tenant.getExpirationTime(), new Date())) {
            throw new ServiceException("租户不可用，已过期");
        }
        if (!tenant.getStatus()) {
            throw new ServiceException("租户不可用，未初始化数据库");
        }
        // 切换到租户的数据源
        DynamicDataSource.getInstance().addDataSource(tenantId, JSONObject.parseObject(tenant.getDatasourceInfo(), DataSourceInfo.class));
        DataSourceHolder.setDataSourceKey(tenantId);
        // 支持手机号/用户名修改登录密码
        SysUser sysUser = userService.selectUserByUserName2(username);
        if (sysUser == null) {
            throw new ServiceException("用户不存在");
        }
        int i = userService.changePwd(sysUser.getUserId(), oldPassword, newPassword);
        return R.toResult(i);
    }

    @ApiOperation(value = "重置密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "password", value = "重置的密码", required = true, dataType = "String", paramType = "query"),
    })
    // @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R resetPwd(@RequestParam("userId") Long userId, @RequestParam("password") String password) {
        SysUser user = new SysUser(userId);
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setUpdateBy(SecurityUtils.getUsername());
        return R.toResult(userService.resetPwd(user));
    }

    @ApiOperation(value = "账号状态修改")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "帐号状态（0正常 1停用）", required = true, dataType = "String", paramType = "query"),
    })
    //@RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R changeStatus(@RequestParam("userId") Long userId, @RequestParam("status") String status) {
        SysUser user = new SysUser(userId);
        user.setStatus(status);
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(SecurityUtils.getUsername());
        return R.toResult(userService.updateUserStatus(user));
    }

    @ApiOperation(value = "修改多设备登录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "帐号状态（0正常 1停用）", required = true, dataType = "String", paramType = "query"),
    })
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeSingleLogin")
    public R changeStatus(@RequestParam("userId") Long userId, @RequestParam("singleLogin") Boolean singleLogin) {
        SysUser user = new SysUser(userId);
        user.setSingleLogin(singleLogin);
        userService.checkUserAllowed(user);
        user.setUpdateBy(SecurityUtils.getUsername());
        user.setUpdateTime(new Date());
        return R.toResult(userService.updateSingleLogin(user));
    }

    @ApiOperation(value = "获取用户选择框列表")
    @GetMapping("/optionselect")
    public R<List<SysUser>> optionselect() {
        return R.ok(userService.optionselect());
    }

    @ApiOperation(value = "根据用户编号获取授权角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "path"),
    })
    //@RequiresPermissions("system:user:query")
    @GetMapping("/authRole/{userId}")
    public R authRole(@PathVariable("userId") Long userId) {
        Map<String, Object> map = new HashMap();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        map.put("user", user);
        map.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return R.ok(map);
    }

    @GetMapping("/getRoles/{userId}")
    public R<List<SysRole>> getRoles(@PathVariable("userId") Long userId) {
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        return R.ok(roles);
    }

    @ApiOperation(value = "用户授权角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "roleIds", value = "角色id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body"),
    })
    //@RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole/selectAll")
    public R selectAuthRoleAll(@RequestParam("userId") Long userId, @RequestBody List<Long> roleIds) {
        userService.checkUserDataScope(userId);
        return R.toResult(userService.insertUserAuth(userId, roleIds));
    }

    @ApiOperation(value = "用户取消授权角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "roleIds", value = "角色id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body"),
    })
    //@RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole/cancelAll")
    public R cancelAuthRoleAll(@RequestParam("userId") Long userId, @RequestBody List<Long> roleIds) {
        return R.ok(userService.deleteAuthRoles(userId, roleIds));
    }


    @ApiOperation(value = "租户授权用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "租户ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "userIds", value = "用户id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body"),
    })
    //@RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authTenant/selectAll")
    public R selectAuthTenantAll(@RequestParam("tenantId") Long tenantId, @RequestBody List<Long> userIds) {
        return R.toResult(userService.insertTenantUserAuth(tenantId, userIds));
    }

    @ApiOperation(value = "租户取消授权用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "租户ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "userIds", value = "用户id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body"),
    })
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authTenant/cancelAll")
    public R cancelAuthTenantAll(@RequestParam("tenantId") Long tenantId, @RequestBody List<Long> userIds) {
        return R.ok(userService.deleteTenantUsers(tenantId, userIds));
    }

    @ApiOperation(value = "查询已分配租户的用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "租户ID", required = true, dataType = "Long", paramType = "query")
    })
    @PostMapping("/authTenant/allocatedList")
    public R<PageDataInfo> allocatedList(@RequestParam("tenantId") Long tenantId) {
        startPage();
        List<TenantUser> userList = userService.selectUserByTenantId(tenantId);
        return R.ok(getPageInfo(userList));
    }

    @ApiOperation(value = "查询用户可以切换的租户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "username", value = "用户登录名", required = true, dataType = "String", paramType = "query")
    })
    @SwitchDataSource
    @GetMapping("/authTenant/switchTenantList")
    public R<List<Tenant>> switchTenantListByUserName(@RequestParam("username") String username) {
        return R.ok(userService.switchTenantListByUserName(username));
    }

    @ApiOperation("获取在线用户数据")
    @GetMapping("getOnlineUser")
    public R<List<SysOnlineUser>> getOnlineUser(@RequestParam("getType") int getType) {
        return R.ok(userService.getOnlineUserList(getType));
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CockpitMapper">

    <select id="queryRule" resultType="com.datalink.fdop.base.api.domain.CockpitRules">
        select a.* from zjdata.ccm_cockpit_rules a
        where a.del_flag = false
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        order by todo_no ${sort}
    </select>

    <select id="selectById" resultType="java.lang.Long">
        select todo_no from zjdata.ccm_cockpit_rules where todo_no = ${todoNo} limit 1
    </select>

    <insert id="addRule" parameterType="com.datalink.fdop.base.api.domain.CockpitRules">
        insert into zjdata.ccm_cockpit_rules
        values (#{todoNo}, #{todoDesc}, #{link}, #{position}, #{owner}, #{todoAttachDesc},
                #{enable}, false, #{createBy}, #{createTime}, #{updateBy}, #{updateTime})
    </insert>

    <insert id="addStatus">
        insert into zjdata.ccm_cockpit_status values (${sqlString})
    </insert>

    <update id="update" parameterType="com.datalink.fdop.base.api.domain.CockpitRules">
        update zjdata.ccm_cockpit_rules
        set
        <if test="todoDesc != null">todo_desc = #{todoDesc},</if>
        <if test="link != null">link = #{link},</if>
        <if test="position != null">position = #{position},</if>
        <if test="owner != null">owner = #{owner},</if>
        <if test="todoAttachDesc != null">todo_attach_desc = #{todoAttachDesc},</if>
        <if test="enable != null">enable = #{enable},</if>
        <if test="delFlag != null">del_flag = #{delFlag},</if>
        update_by = #{updateBy}, update_time = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where todo_no = ${todoNo}
    </update>
    
    <update id="updateStatus" parameterType="com.datalink.fdop.base.api.domain.CockpitStatus">
        update zjdata.ccm_cockpit_status
        set 
        <if test="status != null and status != ''">status = #{status},</if>
        update_by = #{updateBy}, update_time = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where ver_id = #{verId} and company_id = #{companyId}
          and year_month = #{yearMonth} and factory_id = #{factoryId} and todo_no = #{todoNo}
    </update>

    <update id="delete">
        update zjdata.ccm_cockpit_rules
        set del_flag = true
        where todo_no in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteStatus">
        delete from zjdata.ccm_cockpit_status
        where todo_no in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryStatus" resultType="com.datalink.fdop.base.api.domain.CockpitStatus">
        select a.* from zjdata.ccm_cockpit_status a
        where a.ver_id = #{verId} and a.company_id = #{companyId} and a.year_month = #{yearMonth}
        <if test="factoryId != null and factoryId != ''">and a.factory_id = #{factoryId}</if>
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        order by todo_no ${sort}
    </select>

    <select id="queryRuleById" resultType="com.datalink.fdop.base.api.domain.CockpitRules">
        select * from zjdata.ccm_cockpit_rules where todo_no = #{todoNo} limit 1
    </select>

</mapper>
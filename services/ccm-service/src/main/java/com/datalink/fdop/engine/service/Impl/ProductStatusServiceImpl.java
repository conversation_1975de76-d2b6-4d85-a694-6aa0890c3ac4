package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.ProductStatus;
import com.datalink.fdop.engine.mapper.ProductStatusMapper;
import com.datalink.fdop.engine.service.ProductStatusService;
import com.datalink.fdop.engine.utils.TrinoSqlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:33
 */
@Service
public class ProductStatusServiceImpl extends ServiceImpl<ProductStatusMapper, ProductStatus> implements ProductStatusService {

    @Autowired
    private ProductStatusMapper productStatusMapper;

    @Override
    public PageDataInfo<ProductStatus> getOrg(String sort, SearchVo searchVo) {
        List<ProductStatus> iPage = productStatusMapper.getOrg(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<ProductStatus> getEdit(String sort, SearchVo searchVo) {
        List<ProductStatus> iPage = productStatusMapper.getEdit(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<ProductStatus> getAll(String sort, SearchVo searchVo) {
        List<ProductStatus> iPage = productStatusMapper.getAll(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public int saveData(ProductStatus productStatus) {
        if (productStatusMapper.selectByKey(productStatus) == null) {
            productStatusMapper.add(TrinoSqlUtils.productStatusVoSql(productStatus));
        } else {
            productStatusMapper.updateByKey(productStatus);
        }
        return 1;
    }

    @Override
    public int deleteByList(List<ProductStatus> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("请指定要删除的目标");
        }
        int i = 0;
        for (ProductStatus productStatus : list) {
            i = i + productStatusMapper.deleteByKey(productStatus);
        }
        return i;
    }

}

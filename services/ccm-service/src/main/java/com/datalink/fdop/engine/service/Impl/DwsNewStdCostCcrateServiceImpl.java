package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostCcrate;
import com.datalink.fdop.engine.mapper.DwsNewStdCostCcrateMapper;
import com.datalink.fdop.engine.service.DwsNewStdCostCcrateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewStdCostCcrateServiceImpl extends ServiceImpl<DwsNewStdCostCcrateMapper, DwsNewStdCostCcrate> implements DwsNewStdCostCcrateService {

    @Autowired
    private DwsNewStdCostCcrateMapper dwsNewStdCostCcrateMapper;

    @Override
    public PageDataInfo<DwsNewStdCostCcrate> overview(
            String controlAreaId, String costCenterId, String costStructureId, String activityId,
            String verId, String valueType, String date,
            String sort, SearchVo searchVo) {
        Page<DwsNewStdCostCcrate> page = PageUtils.getPage(DwsNewStdCostCcrate.class);
        IPage<DwsNewStdCostCcrate> iPage = dwsNewStdCostCcrateMapper.selectAll(controlAreaId, costCenterId, costStructureId, activityId, verId, valueType, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewStdCostCcrate> selectNoPage(String controlAreaId, String costCenterId, String costStructureId, String activityId, String verId, String valueType, String date, String sort, SearchVo searchVo) {
        return dwsNewStdCostCcrateMapper.selectNoPage(controlAreaId, costCenterId, costStructureId, activityId, verId, valueType, date, sort, searchVo);
    }
}
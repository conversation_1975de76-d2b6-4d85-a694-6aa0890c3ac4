package com.datalink.fdop.auth.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.MenuType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.auth.api.domain.AuthGroup;
import com.datalink.fdop.auth.api.domain.AuthGroupMenu;
import com.datalink.fdop.auth.mapper.AuthGroupMapper;
import com.datalink.fdop.auth.mapper.AuthGroupMenuMapper;
import com.datalink.fdop.auth.service.AuthGroupMenuService;
import com.datalink.fdop.auth.vo.AuthGroupTreeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/24 9:33
 */
@Service
public class AuthGroupMenuServiceImpl implements AuthGroupMenuService {

    @Autowired
    private AuthGroupMenuMapper authGroupMenuMapper;

    @Autowired
    private AuthGroupMapper authGroupMapper;

    @Override
    @Transactional
    public R saveOrUpdate(List<AuthGroupMenu> authGroupMenus) {
        for (AuthGroupMenu authGroupMenu : authGroupMenus) {
            //code是否存在
            VlabelItem<AuthGroupMenu> vlabelItem = authGroupMenuMapper.findByCode(authGroupMenu.getCode());
            if (vlabelItem != null && !vlabelItem.getProperties().getId().equals(authGroupMenu.getId())) {
                throw new ServiceException(Status.CODE_EXIST);
            }
            int i = 0;

            if (authGroupMenu.getId() != null) {
                VlabelItem<AuthGroupMenu> vlabelItem1 = authGroupMenuMapper.findById(authGroupMenu.getId());
                if (vlabelItem1==null) {
                    throw new ServiceException("权限组菜单不存在");
                }
                authGroupMenuMapper.delRelationByPid(authGroupMenu.getId(),vlabelItem1.getProperties().getPid());
                authGroupMenuMapper.updateById(authGroupMenu);
            } else {
                long id = IdWorker.getId();
                authGroupMenu.setId(id);
                i = authGroupMenuMapper.save(authGroupMenu.toString());
            }
            //存入菜单关系
            if (authGroupMenu.getPid()!=-1L) {
                i = authGroupMenuMapper.saveMenuRelation(authGroupMenu.getPid(), authGroupMenu.getId());
                if (i < 1) {
                    throw new ServiceException("保存失败");
                }
            }
        }
        return R.ok(null, Status.SAVE_SUCCEED.getMsg());
    }

    @Override
    @Transactional
    public int delAuthGroupMenu(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<AuthGroupMenu> vlabelItem = authGroupMenuMapper.findById(id);
            if (vlabelItem == null) {
                continue;
            }
            AuthGroupMenu authGroupMenu = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = authGroupMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(menuIdList)) {
                int update = authGroupMenuMapper.bacthUpdatePidById(menuIdList, authGroupMenu.getPid());
                if (update > 0 && authGroupMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    authGroupMenuMapper.createAuthGroupMenuEdge(authGroupMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = authGroupMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(elementIdList)) {
                int update = authGroupMapper.bacthUpdatePidById(elementIdList, authGroupMenu.getPid());
                if (update > 0 && authGroupMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    authGroupMapper.createAuthGroupAndMenuEdge(authGroupMenu.getPid(), elementIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return authGroupMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<AuthGroupTreeVo> queryAuthGroupMenuTree(String sort) {
        //查出所有菜单
        List<AuthGroupTreeVo> authGroupMenus = authGroupMenuMapper.findAll(sort);
        //查出所有权限组
        List<AuthGroupTreeVo>  authGroups = authGroupMapper.findAll(sort);
        //找出所有父级菜单
        List<AuthGroupTreeVo> parentMenus= Lists.newArrayList();
        for (AuthGroupTreeVo authGroupMenu : authGroupMenus) {
            if (authGroupMenu.getPid()==-1l){
                parentMenus.add(authGroupMenu);
            }
        }
        for (AuthGroupTreeVo authGroup: authGroups) {
            if (authGroup.getPid()==-1l){
                parentMenus.add(authGroup);
            }
        }
        authGroupMenus.addAll(authGroups);
        for (AuthGroupTreeVo parentMenu : parentMenus) {
            recursiveTree(parentMenu,authGroupMenus);
        }
        return parentMenus;
    }

    @Override
    public List<AuthGroup> queryGroupByMenuKey(String menuKey) {
        return null;
    }

    @Override
    public List<AuthGroupTreeVo> queryMenuTree(String sort) {
        //查出所有菜单
        List<AuthGroupTreeVo> authGroupMenus = authGroupMenuMapper.findAll(sort);

        //找出所有父级菜单
        List<AuthGroupTreeVo> parentMenus= Lists.newArrayList();
        for (AuthGroupTreeVo authGroupMenu : authGroupMenus) {
            if (authGroupMenu.getPid().equals("-1")){
                parentMenus.add(authGroupMenu);
            }
        }
        for (AuthGroupTreeVo parentMenu : parentMenus) {
            recursiveTree(parentMenu,authGroupMenus);
        }
        return parentMenus;
    }

    /**
     * 递归获取子节点
     *
     * @param parent
     * @param list
     * @return
     */
    private AuthGroupTreeVo recursiveTree(AuthGroupTreeVo parent, List<AuthGroupTreeVo> list) {
        for (AuthGroupTreeVo authGroupTreeVo : list) {
            if (parent.getId().equals(authGroupTreeVo.getPid())) {
                //如果是菜单就继续递归查询
                if (authGroupTreeVo.getMenuType() == MenuType.MENU) {
                    authGroupTreeVo = recursiveTree(authGroupTreeVo, list);
                }
                parent.getChildren().add(authGroupTreeVo);
            }
        }
        return parent;
    }
}

package com.datalink.fdop.quality.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.quality.api.RemoteQualityLogService;
import com.datalink.fdop.quality.api.domain.DataQualityLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 数据治理模块降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteQualityLogFallbackFactory implements FallbackFactory<RemoteQualityLogService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteQualityLogFallbackFactory.class);

    @Override
    public RemoteQualityLogService create(Throwable throwable) {
        log.error("数据质量模块调用失败:{}", throwable.getMessage());
        return new RemoteQualityLogService() {
            @Override
            public R<DataQualityLog> create(DataQualityLog dataQualityLog) {
                return R.fail("生成规则日志失败:" + throwable.getMessage());
            }
        };
    }
}

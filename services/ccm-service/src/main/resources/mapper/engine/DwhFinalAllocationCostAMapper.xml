<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwhFinalAllocationCostAMapper">

    <select id="selectTargetedFinalAllocation" resultType="com.datalink.fdop.engine.api.domain.DwhFinalAllocationCostA">
        SELECT
        *
        FROM dwh.dwh_final_allocation_cost_a node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and node.control_area_id = #{controlAreaId}
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != ''">
                and node.year_month >= #{yearMonthFrom}
            </if>
            <if test="yearMonthTo != null and yearMonthTo != ''">
                and node.year_month &lt;= #{yearMonthTo}
            </if>
            <if test="allocationType != null and allocationType != ''">
                and node.allocation_type in
                <foreach item="item" collection="allocationType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companyId != null and companyId != ''">
                and node.cost_object_fr_company_id in
                <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        node.ver_id,node.value_type,node.control_area_id,
        node.year_month,node.allocation_type,node.cost_object_fr_company_id,
        node.cost_object_fr_plant_id,node.cost_element_id ${sort}
    </select>

    <select id="selectCostFinalAllocation" resultType="com.datalink.fdop.engine.api.domain.DwhFinalAllocationCostA">
        SELECT *
        FROM dwh.dwh_final_allocation_cost_a node
        WHERE
        allocation_type = 'assign_non'
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
        </if>
        <if test="controlAreaId != null and controlAreaId != ''">
            and node.control_area_id = #{controlAreaId}
        </if>
        <if test="verId != null and verId != ''">
            and node.ver_id = #{verId}
        </if>
        <if test="yearMonthFrom != null and yearMonthFrom != ''">
            and node.year_month >= #{yearMonthFrom}
        </if>
        <if test="yearMonthTo != null and yearMonthTo != ''">
            and node.year_month &lt;= #{yearMonthTo}
        </if>
        <if test="companyId != null and companyId != ''">
            and node.cost_object_fr_company_id in
            <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY node.ver_id,node.value_type,node.control_area_id,
        node.year_month,node.allocation_type,node.cost_object_fr_company_id,
        node.cost_object_fr_plant_id,node.cost_element_id ${sort}
    </select>

    <select id="checkAllocationCost" resultType="String">
        select case
                   when a.num = 0 then 'unhave_num'
                   when b.num = 0 then 'unhave_method'
                   else ''
                   end as have_num
        from (select count(1) as num
              from dwh.dwh_final_allocation_cost_a
              where `year_month` >= #{yearMonthFrom}
                and `year_month` &lt;= #{yearMonthTo}
                and ver_id = #{verId}
                and control_area_id = #{controlAreaId}
                and allocation_type = 'assign_non') a
                 left join (select count(1) as num
                            from dwh.dwh_final_allocation_cost_a
                            where `year_month` >= #{yearMonthFrom}
                              and `year_month` &lt;= #{yearMonthTo}
                              and ver_id = #{verId}
                              and control_area_id = #{controlAreaId}
                              and allocation_method_collection like concat('%', #{allocationMethodId}, '%')
                              and allocation_type = 'assign_non') b on 1 = 1
    </select>

    <select id="checkTargetedAllocationCost" resultType="int">
        select
        count(1) as num
        from dwh.dwh_final_allocation_cost_a
        where `year_month` >= #{yearMonthFrom}
        and `year_month` &lt;= #{yearMonthTo}
        and allocation_type = #{allocationType}
        and cost_object_fr_company_id in
        <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwhFinalAllocationCostA">
        SELECT
        *
        FROM dwh.dwh_final_allocation_cost_a node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and node.control_area_id = #{controlAreaId}
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != ''">
                and node.year_month >= #{yearMonthFrom}
            </if>
            <if test="yearMonthTo != null and yearMonthTo != ''">
                and node.year_month &lt;= #{yearMonthTo}
            </if>
            <if test="allocationType != null and allocationType != ''">
                and node.allocation_type in
                <foreach item="item" collection="allocationType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companyId != null and companyId != ''">
                and node.cost_object_fr_company_id in
                <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        node.ver_id,node.value_type,node.control_area_id,
        node.year_month,node.allocation_type,node.cost_object_fr_company_id,
        node.cost_object_fr_plant_id,node.cost_element_id ${sort}
    </select>

</mapper>
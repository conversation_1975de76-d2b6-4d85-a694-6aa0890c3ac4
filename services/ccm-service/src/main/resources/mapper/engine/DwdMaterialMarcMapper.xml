<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwdMaterialMarcMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwdMaterialMarc">
        SELECT *
        FROM dwd.dwd_material_marc node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="materialId != null and materialId != ''">
                and node.material_id IN
                <foreach item="item" collection="materialId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id IN
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.material_id,node.plant_id ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwdMaterialMarc">
        SELECT *
        FROM dwd.dwd_material_marc node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="materialId != null and materialId != ''">
                AND node.material_id IN
                <foreach item="item" collection="materialId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="plantId != null and plantId != ''">
                AND node.plant_id IN
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.material_id,node.plant_id ${sort}
    </select>

</mapper>


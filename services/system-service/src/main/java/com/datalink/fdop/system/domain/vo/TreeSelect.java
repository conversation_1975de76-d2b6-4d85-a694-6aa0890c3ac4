package com.datalink.fdop.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.datalink.fdop.system.domain.SysMenu;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
@Data
public class TreeSelect implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "菜单ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "父菜单ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    @ApiModelProperty(value = "菜单编码", required = true)
    private String menuCode;

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    @ApiModelProperty(value = "类型（M目录 C菜单 F按钮）", required = true)
    private String menuType;

    /**
     * 页面类型
     */
    @ApiModelProperty(value = "页面类型（0普通页面 1永洪页面）")
    private String pageType;

    /**
     * 按钮权限选项
     */
    @ApiModelProperty(value = "按钮权限选项")
    private PermissionOption option;

    @Data
    public static class PermissionOption {
        @ApiModelProperty(value = "编辑权限")
        private Boolean edit = true;

        @ApiModelProperty(value = "工作流权限")
        private Boolean compute = true;
    }

    @ApiModelProperty(value = "报表ID", required = true)
    private String reportId;

    @ApiModelProperty(value = "路由地址")
    private String path;

    @ApiModelProperty(value = "菜单状态（0显示 1隐藏）", required = true)
    private String visible;

    @ApiModelProperty(value = "是否为外链（0是 1否）", required = true)
    private String isFrame;



    @ApiModelProperty(value = "默认打开此页面", required = true)
    private String defaultOpen;

    @ApiModelProperty(value = "子节点信息", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;


    @ApiModelProperty(value = "Icon编码", required = false)
    private String icon;

    public TreeSelect() {

    }

    public TreeSelect(SysMenu menu) {
        this.id = menu.getMenuId();
        this.parentId = menu.getParentId();
        this.menuCode = menu.getMenuCode();
        this.menuName = menu.getMenuName();
        this.menuType = menu.getMenuType();
        this.pageType = menu.getPageType();
        this.reportId = menu.getReportId();
        this.path = menu.getPath();
        this.visible = menu.getVisible();
        this.icon = menu.getIcon();
        this.defaultOpen = menu.getDefaultOpen();
        this.isFrame = menu.getIsFrame();
        this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalAllocationCostP;

import java.util.List;

public interface DwhFinalAllocationCostPService extends IService<DwhFinalAllocationCostP> {

    /**
     * 查询定向分摊-定版视图
     */
    PageDataInfo<DwhFinalAllocationCostP> selectTargetedFinalAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo);

    /**
     * 查询费用分摊-定版视图
     */
    PageDataInfo<DwhFinalAllocationCostP> selectCostFinalAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo);

    /**
     * 校验非定向分摊是否执行冲销操作
     */
    Boolean checkAllocationCost(String controlAreaId, String verId, String yearMonthFrom, String yearMonthTo, String allocationMethodId);

    /**
     * 校验定向分摊是否执行冲销操作
     */
    Boolean checkTargetedAllocationCost(String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType);

    List<DwhFinalAllocationCostP> selectNoPage(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo);
}
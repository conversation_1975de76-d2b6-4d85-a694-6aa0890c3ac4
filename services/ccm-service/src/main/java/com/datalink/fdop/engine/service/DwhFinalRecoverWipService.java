package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRecoverWip;

import java.util.List;

public interface DwhFinalRecoverWipService extends IService<DwhFinalRecoverWip> {

    PageDataInfo<DwhFinalRecoverWip> overview(String verId,String plantId, String date, String sort, SearchVo searchVo);

    List<DwhFinalRecoverWip> listAll();

    List<DwhFinalRecoverWip> selectNoPage(String verId, String plantId, String date, String sort, SearchVo searchVo);
}
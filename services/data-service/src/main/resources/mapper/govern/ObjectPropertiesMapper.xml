<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.ObjectPropertiesMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.ObjectProperties"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insert" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_object_properties ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getObjectPropertiesAgeStr(entity)})
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_object_properties)
                                   WHERE node.code = '${code}'
                                   and node.activeState = true
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype) limit 1
    </select>

    <select id="selectById" resultType="com.datalink.fdop.govern.api.domain.ObjectProperties">
        SELECT object_properties.*,
        business_object.name as l3Name, logical_object.name as l4Name,
        data_security_classification.code as securitySortCode, data_security_classification.name as securitySortName,
        data_security_level.code as securityLevelCode, data_security_level.name as securityLevelName,
        data_application_framework.code as sourceSystemCode, data_application_framework.name as sourceSystemName,
        datasource.code as dataSourceCode, datasource.name as dataSourceName,
        data_entity1.code as modelCode, data_entity1.name as modelName,
        data_entity2.code as odsModelCode, data_entity2.name as odsModelName
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_object_properties)
        where entity.id = ${id} AND entity.activeState = true
        RETURN
        entity.id,
        entity.pid,
        entity.name,
        entity.code,
        entity.scope,
        entity.englishName,
        entity.abbreviation,
        entity.abbrName,
        entity.synonym,
        entity.fillingMethod,
        entity.fillingMethodLabel,
        entity.usage,
        entity.required,
        entity.requiredLabel,
        entity.dataRule,
        entity.version,
        entity.ownerPost,
        entity.ownerJob,
        entity.owner,
        entity.consumer,
        entity.securitySortId,
        entity.securityLevelId,
        entity.defaultValue,
        entity.threshold,
        entity.range,
        entity.sourceSystemId,
        entity.sourceSystemCode,
        entity.sourceSystemName,
        entity.dataSourceId,
        entity.dataSourceCode,
        entity.dataSourceName,
        entity.schemaName,
        entity.tableName,
        entity.fieldName,
        entity.primaryKey,
        entity.foreignKey,
        entity.fieldType,
        entity.fieldDescription,
        entity.length,
        entity.decimalLength,
        entity.callSystem,
        entity.modelId,
        entity.modelCode,
        entity.modelName,
        entity.modelFieldCode,
        entity.modelFieldName,
        entity.odsModelId,
        entity.odsModelCode,
        entity.odsModelName,
        entity.odsModelFieldCode,
        entity.lockStatus,
        entity.activeState,
        entity.serialNumber,
        entity.createBy,
        entity.createTime,
        entity.updateBy,
        entity.updateTime
        $$) as object_properties(
        id text,
        pid text,
        name text,
        code text,
        scope text,
        englishName text,
        abbreviation text,
        abbrName text,
        synonym text,
        fillingMethod text,
        fillingMethodLabel text,
        usage text,
        required text,
        requiredLabel text,
        dataRule text,
        version text,
        ownerPost text,
        ownerJob text,
        owner text,
        consumer text,
        securitySortId text,
        securityLevelId text,
        defaultValue text,
        threshold text,
        range text,
        sourceSystemId text,
        sourceSystemCode text,
        sourceSystemName text,
        dataSourceId text,
        dataSourceCode text,
        dataSourceName text,
        schemaName text,
        tableName text,
        fieldName text,
        primaryKey text,
        foreignKey text,
        fieldType text,
        fieldDescription text,
        length text,
        decimalLength text,
        callSystem text,
        modelId text,
        modelCode text,
        modelName text,
        modelFieldCode text,
        modelFieldName text,
        odsModelId text,
        odsModelCode text,
        odsModelName text,
        odsModelFieldCode text,
        lockStatus text,
        activeState text,
        serialNumber text,
        createBy text,
        createTime text,
        updateBy text,
        updateTime text
        )
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_logical_object)
        RETURN entity.id, entity.pid, entity.code, entity.name $$)
        as logical_object(id text, pid text, code text, name text) on object_properties.pid = logical_object.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_business_object)
        RETURN entity.id, entity.code, entity.name $$)
        as business_object(id text, code text, name text) on logical_object.pid = business_object.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_classification)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_classification(id text, code text, name text) on object_properties.securitySortId = data_security_classification.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_level)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_level(id text, code text, name text) on object_properties.securityLevelId = data_security_level.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_application_framework)
        RETURN entity.id, entity.code, entity.name $$)
        as data_application_framework(id text, code text, name text) on object_properties.sourceSystemId = data_application_framework.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:datasource)
        RETURN entity.id, entity.code, entity.name $$)
        as datasource(id text, code text, name text) on object_properties.dataSourceId = datasource.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_e_data_entity)
        RETURN entity.id, entity.code, entity.name $$)
        as data_entity1(id text, code text, name text) on object_properties.modelId = data_entity1.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_e_data_entity)
        RETURN entity.id, entity.code, entity.name $$)
        as data_entity2(id text, code text, name text) on object_properties.odsModelId = data_entity2.id
        limit 1
    </select>

    <select id="codeIsOnly" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_object_properties)
                                   WHERE node.code = '${code}' and node.id &lt;&gt; ${id}
                                   and node.activeState = true
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype) limit 1
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.ObjectProperties" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_object_properties)
        WHERE node.id = ${id}
        <set>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="scope != null and scope != ''">node.scope = '${scope}',</if>
            <if test="englishName != null and englishName != ''">node.englishName = '${englishName}',</if>
            <if test="abbreviation != null and abbreviation != ''">node.abbreviation = '${abbreviation}',</if>
            <if test="abbrName != null and abbrName != ''">node.abbrName = '${abbrName}',</if>
            <if test="synonym != null and synonym != ''">node.synonym = '${synonym}',</if>
            <if test="fillingMethod != null">node.fillingMethod = '${fillingMethod}',</if>
            <if test="fillingMethodLabel != null and fillingMethodLabel != ''">node.fillingMethodLabel = '${fillingMethodLabel}',</if>
            <if test="usage != null and usage != ''">node.usage = '${usage}',</if>
            <if test="required != null">node.required = '${required}',</if>
            <if test="requiredLabel != null and requiredLabel != ''">node.requiredLabel =' ${requiredLabel}',</if>
            <if test="dataRule != null and dataRule != ''">node.dataRule = '${dataRule}',</if>
            <if test="version != null and version != ''">node.version = '${version}',</if>
            <if test="ownerPost != null and ownerPost != ''">node.ownerPost = '${ownerPost}',</if>
            <if test="ownerJob != null and ownerJob != ''">node.ownerJob = '${ownerJob}',</if>
            <if test="owner != null and owner != ''">node.owner = '${owner}',</if>
            <if test="consumer != null and consumer != ''">node.consumer = '${consumer}',</if>
            <if test="securitySortId != null">node.securitySortId = ${securitySortId},</if>
            <if test="securityLevelId != null">node.securityLevelId = ${securityLevelId},</if>
            <if test="defaultValue != null and defaultValue != ''">node.defaultValue = '${defaultValue}',</if>
            <if test="threshold != null and threshold != ''">node.threshold = '${threshold}',</if>
            <if test="range != null and range != ''">node.range = '${range}',</if>
            <if test="sourceSystemId != null">node.sourceSystemId = ${sourceSystemId},</if>
            <if test="dataSourceId != null">node.dataSourceId = ${dataSourceId},</if>
            <if test="schemaName != null and schemaName != ''">node.schemaName = '${schemaName}',</if>
            <if test="tableName != null and tableName != ''">node.tableName = '${tableName}',</if>
            <if test="fieldName != null and fieldName != ''">node.fieldName = '${fieldName}',</if>
            <if test="primaryKey != null">node.primaryKey = ${primaryKey},</if>
            <if test="foreignKey != null and foreignKey != ''">node.foreignKey = '${foreignKey}',</if>
            <if test="fieldType != null and fieldType != ''">node.fieldType = '${fieldType}',</if>
            <if test="fieldDescription != null and fieldDescription != ''">node.fieldDescription = '${fieldDescription}',</if>
            <if test="length != null">node.length = ${length},</if>
            <if test="decimalLength != null">node.decimalLength = ${decimalLength},</if>
            <if test="callSystem != null and callSystem != ''">node.callSystem = '${callSystem}',</if>
            <if test="modelId != null">node.modelId = ${modelId},</if>
            <if test="modelFieldCode != null and modelFieldCode != ''">node.modelFieldCode = '${modelFieldCode}',</if>
            <if test="modelFieldName != null and modelFieldName != ''">node.modelFieldName = '${modelFieldName}',</if>
            <if test="odsModelId != null">node.odsModelId = ${odsModelId},</if>
            <if test="odsModelFieldCode != null and odsModelFieldCode != ''">node.odsModelFieldCode = '${odsModelFieldCode}',</if>
            <if test="lockStatus != null">node.lockStatus = ${lockStatus},</if>
            <if test="activeState != null">node.activeState = ${activeState},</if>
            <if test="serialNumber != null">node.serialNumber = ${serialNumber},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="delete" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_object_properties)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.ObjectProperties" resultType="com.datalink.fdop.govern.api.domain.ObjectProperties">
        SELECT object_properties.*,
               business_object.name as l3Name, logical_object.name as l4Name,
               data_security_classification.code as securitySortCode, data_security_classification.name as securitySortName,
               data_security_level.code as securityLevelCode, data_security_level.name as securityLevelName,
               data_application_framework.code as sourceSystemCode, data_application_framework.name as sourceSystemName,
               datasource.code as dataSourceCode, datasource.name as dataSourceName,
               data_entity1.code as modelCode, data_entity1.name as modelName,
               data_entity2.code as odsModelCode, data_entity2.name as odsModelName
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_object_properties)
        <where>
            <if test="entity.id != null">AND entity.id = ${entity.id}</if>
            <if test="entity.pid != null">AND entity.pid = ${entity.pid}</if>
            <if test="entity.code != null and entity.code != ''">AND entity.code =~'.*${entity.code}.*'</if>
            <if test="entity.name != null and entity.name != ''">AND entity.name =~'.*${entity.name}.*'</if>
            <if test="entity.version != null and entity.version != ''">AND entity.version =~'.*${entity.version}.*'</if>
            <if test="entity.fieldName != null and entity.fieldName != ''">AND entity.fieldName =~'.*${entity.fieldName}.*'</if>
            <if test="entity.owner != null and entity.owner != ''">AND entity.owner =~'.*${entity.owner}.*'</if>
            <if test="entity.lockStatus != null">AND entity.lockStatus = ${lockStatus}</if>
            AND entity.activeState = true
        </where>
        RETURN
        entity.id,
        entity.pid,
        entity.name,
        entity.code,
        entity.scope,
        entity.englishName,
        entity.abbreviation,
        entity.abbrName,
        entity.synonym,
        entity.fillingMethod,
        entity.fillingMethodLabel,
        entity.usage,
        entity.required,
        entity.requiredLabel,
        entity.dataRule,
        entity.version,
        entity.ownerPost,
        entity.ownerJob,
        entity.owner,
        entity.consumer,
        entity.securitySortId,
        entity.securityLevelId,
        entity.defaultValue,
        entity.threshold,
        entity.range,
        entity.sourceSystemId,
        entity.sourceSystemCode,
        entity.sourceSystemName,
        entity.dataSourceId,
        entity.dataSourceCode,
        entity.dataSourceName,
        entity.schemaName,
        entity.tableName,
        entity.fieldName,
        entity.primaryKey,
        entity.foreignKey,
        entity.fieldType,
        entity.fieldDescription,
        entity.length,
        entity.decimalLength,
        entity.callSystem,
        entity.modelId,
        entity.modelCode,
        entity.modelName,
        entity.modelFieldCode,
        entity.modelFieldName,
        entity.odsModelId,
        entity.odsModelCode,
        entity.odsModelName,
        entity.odsModelFieldCode,
        entity.lockStatus,
        entity.activeState,
        entity.serialNumber,
        entity.createBy,
        entity.createTime,
        entity.updateBy,
        entity.updateTime
        $$) as object_properties(
        id text,
        pid text,
        name text,
        code text,
        scope text,
        englishName text,
        abbreviation text,
        abbrName text,
        synonym text,
        fillingMethod text,
        fillingMethodLabel text,
        usage text,
        required text,
        requiredLabel text,
        dataRule text,
        version text,
        ownerPost text,
        ownerJob text,
        owner text,
        consumer text,
        securitySortId text,
        securityLevelId text,
        defaultValue text,
        threshold text,
        range text,
        sourceSystemId text,
        sourceSystemCode text,
        sourceSystemName text,
        dataSourceId text,
        dataSourceCode text,
        dataSourceName text,
        schemaName text,
        tableName text,
        fieldName text,
        primaryKey text,
        foreignKey text,
        fieldType text,
        fieldDescription text,
        length text,
        decimalLength text,
        callSystem text,
        modelId text,
        modelCode text,
        modelName text,
        modelFieldCode text,
        modelFieldName text,
        odsModelId text,
        odsModelCode text,
        odsModelName text,
        odsModelFieldCode text,
        lockStatus text,
        activeState text,
        serialNumber text,
        createBy text,
        createTime text,
        updateBy text,
        updateTime text
        )
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_logical_object)
        RETURN entity.id, entity.pid, entity.code, entity.name $$)
        as logical_object(id text, pid text, code text, name text) on object_properties.pid = logical_object.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_business_object)
        RETURN entity.id, entity.code, entity.name $$)
        as business_object(id text, code text, name text) on logical_object.pid = business_object.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_classification)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_classification(id text, code text, name text) on object_properties.securitySortId = data_security_classification.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_level)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_level(id text, code text, name text) on object_properties.securityLevelId = data_security_level.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_application_framework)
        RETURN entity.id, entity.code, entity.name $$)
        as data_application_framework(id text, code text, name text) on object_properties.sourceSystemId = data_application_framework.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:datasource)
        RETURN entity.id, entity.code, entity.name $$)
        as datasource(id text, code text, name text) on object_properties.dataSourceId = datasource.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_e_data_entity)
        RETURN entity.id, entity.code, entity.name $$)
        as data_entity1(id text, code text, name text) on object_properties.modelId = data_entity1.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_e_data_entity)
        RETURN entity.id, entity.code, entity.name $$)
        as data_entity2(id text, code text, name text) on object_properties.odsModelId = data_entity2.id
        <where>
            <if test="entity.securitySortCode != null and entity.securitySortCode != ''">AND data_security_classification.code = '${entity.securitySortCode}'</if>
            <if test="entity.securitySortName != null and entity.securitySortName != ''">AND data_security_classification.name = '${entity.securitySortName}'</if>
            <if test="entity.securityLevelCode != null and entity.securityLevelCode != ''">AND data_security_level.code = '${entity.securityLevelCode}'</if>
            <if test="entity.securityLevelName != null and entity.securityLevelName != ''">AND data_security_level.name = '${entity.securityLevelName}'</if>
            <if test="entity.sourceSystemCode != null and entity.sourceSystemCode != ''">AND data_application_framework.code = '${entity.sourceSystemCode}'</if>
            <if test="entity.sourceSystemName != null and entity.sourceSystemName != ''">AND data_application_framework.name = '${entity.sourceSystemName}'</if>
            <if test="entity.dataSourceCode != null and entity.dataSourceCode != ''">AND datasource.code = '${entity.dataSourceCode}'</if>
            <if test="entity.dataSourceName != null and entity.dataSourceName != ''">AND datasource.name = '${entity.dataSourceName}'</if>
            <if test="entity.modelCode != null and entity.modelCode != ''">AND data_entity1.code = '${entity.modelCode}'</if>
            <if test="entity.modelName != null and entity.modelName != ''">AND data_entity1.name = '${entity.modelName}'</if>
            <if test="entity.odsModelCode != null and entity.odsModelCode != ''">AND data_entity2.code = '${entity.odsModelCode}'</if>
            <if test="entity.odsModelName != null and entity.odsModelName != ''">AND data_entity2.name = '${entity.odsModelName}'</if>
        </where>
        ORDER BY createTime ${sort}
    </select>

    <select id="getExportList" parameterType="com.datalink.fdop.govern.api.domain.ObjectProperties" resultType="com.datalink.fdop.govern.api.domain.ObjectProperties">
        SELECT object_properties.*,
        business_object.name as l3Name, logical_object.name as l4Name,
        data_security_classification.code as securitySortCode, data_security_classification.name as securitySortName,
        data_security_level.code as securityLevelCode, data_security_level.name as securityLevelName,
        data_application_framework.code as sourceSystemCode, data_application_framework.name as sourceSystemName,
        datasource.code as dataSourceCode, datasource.name as dataSourceName,
        data_entity1.code as modelCode, data_entity1.name as modelName,
        data_entity2.code as odsModelCode, data_entity2.name as odsModelName
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_object_properties)
        <where>
            <if test="entity.id != null">AND entity.id = ${entity.id}</if>
            <if test="entity.pid != null">AND entity.pid = ${entity.pid}</if>
            <if test="entity.code != null and entity.code != ''">AND entity.code =~'.*${entity.code}.*'</if>
            <if test="entity.name != null and entity.name != ''">AND entity.name =~'.*${entity.name}.*'</if>
            <if test="entity.version != null and entity.version != ''">AND entity.version =~'.*${entity.version}.*'</if>
            <if test="entity.fieldName != null and entity.fieldName != ''">AND entity.fieldName =~'.*${entity.fieldName}.*'</if>
            <if test="entity.owner != null and entity.owner != ''">AND entity.owner =~'.*${entity.owner}.*'</if>
            <if test="entity.lockStatus != null">AND entity.lockStatus = ${lockStatus}</if>
            AND entity.activeState = true
        </where>
        RETURN
        entity.id,
        entity.pid,
        entity.name,
        entity.code,
        entity.scope,
        entity.englishName,
        entity.abbreviation,
        entity.abbrName,
        entity.synonym,
        entity.fillingMethod,
        entity.fillingMethodLabel,
        entity.usage,
        entity.required,
        entity.requiredLabel,
        entity.dataRule,
        entity.version,
        entity.ownerPost,
        entity.ownerJob,
        entity.owner,
        entity.consumer,
        entity.securitySortId,
        entity.securityLevelId,
        entity.defaultValue,
        entity.threshold,
        entity.range,
        entity.sourceSystemId,
        entity.sourceSystemCode,
        entity.sourceSystemName,
        entity.dataSourceId,
        entity.dataSourceCode,
        entity.dataSourceName,
        entity.schemaName,
        entity.tableName,
        entity.fieldName,
        entity.primaryKey,
        entity.foreignKey,
        entity.fieldType,
        entity.fieldDescription,
        entity.length,
        entity.decimalLength,
        entity.callSystem,
        entity.modelId,
        entity.modelCode,
        entity.modelName,
        entity.modelFieldCode,
        entity.modelFieldName,
        entity.odsModelId,
        entity.odsModelCode,
        entity.odsModelName,
        entity.odsModelFieldCode,
        entity.lockStatus,
        entity.activeState,
        entity.serialNumber,
        entity.createBy,
        entity.createTime,
        entity.updateBy,
        entity.updateTime
        $$) as object_properties(
        id text,
        pid text,
        name text,
        code text,
        scope text,
        englishName text,
        abbreviation text,
        abbrName text,
        synonym text,
        fillingMethod text,
        fillingMethodLabel text,
        usage text,
        required text,
        requiredLabel text,
        dataRule text,
        version text,
        ownerPost text,
        ownerJob text,
        owner text,
        consumer text,
        securitySortId text,
        securityLevelId text,
        defaultValue text,
        threshold text,
        range text,
        sourceSystemId text,
        sourceSystemCode text,
        sourceSystemName text,
        dataSourceId text,
        dataSourceCode text,
        dataSourceName text,
        schemaName text,
        tableName text,
        fieldName text,
        primaryKey text,
        foreignKey text,
        fieldType text,
        fieldDescription text,
        length text,
        decimalLength text,
        callSystem text,
        modelId text,
        modelCode text,
        modelName text,
        modelFieldCode text,
        modelFieldName text,
        odsModelId text,
        odsModelCode text,
        odsModelName text,
        odsModelFieldCode text,
        lockStatus text,
        activeState text,
        serialNumber text,
        createBy text,
        createTime text,
        updateBy text,
        updateTime text
        )
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_logical_object)
        RETURN entity.id, entity.pid, entity.code, entity.name $$)
        as logical_object(id text, pid text, code text, name text) on object_properties.pid = logical_object.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_business_object)
        RETURN entity.id, entity.code, entity.name $$)
        as business_object(id text, code text, name text) on logical_object.pid = business_object.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_classification)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_classification(id text, code text, name text) on object_properties.securitySortId = data_security_classification.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_level)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_level(id text, code text, name text) on object_properties.securityLevelId = data_security_level.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_application_framework)
        RETURN entity.id, entity.code, entity.name $$)
        as data_application_framework(id text, code text, name text) on object_properties.sourceSystemId = data_application_framework.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:datasource)
        RETURN entity.id, entity.code, entity.name $$)
        as datasource(id text, code text, name text) on object_properties.dataSourceId = datasource.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_e_data_entity)
        RETURN entity.id, entity.code, entity.name $$)
        as data_entity1(id text, code text, name text) on object_properties.modelId = data_entity1.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_e_data_entity)
        RETURN entity.id, entity.code, entity.name $$)
        as data_entity2(id text, code text, name text) on object_properties.odsModelId = data_entity2.id
        <where>
            <if test="entity.securitySortCode != null and entity.securitySortCode != ''">AND data_security_classification.code = '${entity.securitySortCode}'</if>
            <if test="entity.securitySortName != null and entity.securitySortName != ''">AND data_security_classification.name = '${entity.securitySortName}'</if>
            <if test="entity.securityLevelCode != null and entity.securityLevelCode != ''">AND data_security_level.code = '${entity.securityLevelCode}'</if>
            <if test="entity.securityLevelName != null and entity.securityLevelName != ''">AND data_security_level.name = '${entity.securityLevelName}'</if>
            <if test="entity.sourceSystemCode != null and entity.sourceSystemCode != ''">AND data_application_framework.code = '${entity.sourceSystemCode}'</if>
            <if test="entity.sourceSystemName != null and entity.sourceSystemName != ''">AND data_application_framework.name = '${entity.sourceSystemName}'</if>
            <if test="entity.dataSourceCode != null and entity.dataSourceCode != ''">AND datasource.code = '${entity.dataSourceCode}'</if>
            <if test="entity.dataSourceName != null and entity.dataSourceName != ''">AND datasource.name = '${entity.dataSourceName}'</if>
            <if test="entity.modelCode != null and entity.modelCode != ''">AND data_entity1.code = '${entity.modelCode}'</if>
            <if test="entity.modelName != null and entity.modelName != ''">AND data_entity1.name = '${entity.modelName}'</if>
            <if test="entity.odsModelCode != null and entity.odsModelCode != ''">AND data_entity2.code = '${entity.odsModelCode}'</if>
            <if test="entity.odsModelName != null and entity.odsModelName != ''">AND data_entity2.name = '${entity.odsModelName}'</if>
        </where>
        ORDER BY createTime ${sort}
    </select>

    <select id="updateActiveState" resultType="java.lang.Integer">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_g_object_properties)
            WHERE entity.id = ${id}
            SET entity.activeState = false
            RETURN id(entity), properties(entity)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateActiveStateByIds" resultType="java.lang.Integer">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_g_object_properties)
            WHERE entity.id in
            <foreach collection="ids" item="id" open="[" separator="," close="]">
                ${id}
            </foreach>
            SET entity.activeState = false
            RETURN id(entity), properties(entity)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByIds" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
        $$ MATCH (node:d_g_object_properties)
        WHERE node.id in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        and node.activeState = true
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByPids" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
        $$ MATCH (node:d_g_object_properties)
        WHERE node.pid in
        <foreach collection="pids" item="pid" open="[" separator="," close="]">
            ${pid}
        </foreach>
        and node.activeState = true
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="lock" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_object_properties)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.lockStatus = true
        RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="unlock" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_object_properties)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.lockStatus = false
        RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper>
package com.datalink.fdop.quality.api.domain;

import com.datalink.fdop.common.core.domain.TreeVo;
import com.datalink.fdop.quality.api.enums.DataQualityType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/11 10:26
 */
@Data
public class DataQualityTree extends TreeVo<DataQualityTree> {

    @ApiModelProperty(value = "规则类型(ELEMENT:元素,ONE_ENTITY:单实体,MORE_ENTITY:多实体)", required = true)
    private DataQualityType dataQualityType;

}

package com.datalink.fdop.element.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.RemoteEntityService;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityTableVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class
RemoteEntityFallbackFactory implements FallbackFactory<RemoteEntityService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteEntityFallbackFactory.class);

    @Override
    public RemoteEntityService create(Throwable throwable) {
        log.error("数据元素服务调用失败:{}", throwable.getMessage());
        return new RemoteEntityService() {
            @Override
            public R<DataEntity> selectById(Long id) {
                return R.fail("查询数据实体失败:" + throwable.getMessage());
            }

            @Override
            public R<DataEntity> selectByCode(String code) {
                return R.fail("查询数据实体失败:" + throwable.getMessage());
            }

            @Override
            public R<List<DataEntityTableVo>> selectDataEntityTableVoByTenantIdAndIds(Long tenantId, List<Long> dataEntityIds) {
                return R.fail("数据实体关联表集合查询失败:" + throwable.getMessage());
            }
        };
    }
}

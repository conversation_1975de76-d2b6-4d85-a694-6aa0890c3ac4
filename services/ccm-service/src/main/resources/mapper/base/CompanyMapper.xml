<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CompanyMapper">

    <select id="checkCompanyIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_company
        WHERE company_id = #{id} limit 1
    </select>

    <delete id="deleteCompanyById" parameterType="String">
        delete from zjdata.ccm_company
        where company_id = #{id}
    </delete>

    <delete id="deleteCompanyByIds" parameterType="String">
        delete from zjdata.ccm_company
        WHERE company_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCompanyList" resultType="com.datalink.fdop.base.api.domain.Company">
        SELECT company.*, controlArea.control_area_id as control_area_id
        FROM zjdata.ccm_company company
        left join zjdata.ccm_control_area controlArea on company.control_area_id = controlArea.control_area_id
        <where>
            <if test="companyId != null and companyId != ''">
                AND company.company_id = #{companyId}
            </if>
            <if test="companyDesc != null and companyDesc != ''">
                AND company.company_desc like '%${companyDesc}%'
            </if>
            <if test="companyText != null and companyText != ''">
                AND company.company_text like '%${companyText}%'
            </if>
            <if test="enable != null">
                AND company.enable = #{enable}
            </if>
        </where>
        ORDER BY company.company_id ${sort}
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.Company">
        SELECT company.*, controlArea.control_area_id as control_area_id
        FROM zjdata.ccm_company company
                 left join zjdata.ccm_control_area controlArea on company.control_area_id = controlArea.control_area_id
        WHERE company.company_id = #{id}
        limit 1
    </select>

    <select id="query" resultType="com.datalink.fdop.base.api.domain.Company">
        select a.*, controlArea.control_area_id as control_area_id
        FROM zjdata.ccm_company a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        <where>
            <if test="searchVo != null and searchVo != ''">
                and ${searchVo}
            </if>
        </where>
        order by company_id ${sort}
    </select>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_company
            SET enable = #{item.enable}
            WHERE company_id = #{item.companyId}
        </foreach>
    </update>

    <select id="listCompanyId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct node.company_id as "key", dict.company_desc as "value"
        from ${orgTableName} node
                 left join zjdata.ccm_company dict
                           on node.company_id = dict.company_id
        where node.company_id is not null and node.company_id &lt;> ''
        order by node.company_id asc
    </select>

</mapper>
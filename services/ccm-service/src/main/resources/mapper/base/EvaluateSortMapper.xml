<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.EvaluateSortMapper">

    <select id="checkUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_evaluate_sort
        WHERE evaluate_sort_id = #{evaluateSortId}
        limit 1
    </select>

    <select id="checkEvaluateSortId" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_evaluate_sort
        WHERE evaluate_sort_id = #{evaluateSortId}
        limit 1
    </select>

    <delete id="deleteByKey" parameterType="String">
        delete
        from zjdata.ccm_evaluate_sort
        WHERE evaluate_sort_id = #{evaluateSortId}
    </delete>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.EvaluateSort">
        update zjdata.ccm_evaluate_sort
        set
        <if test="evaluateSortDesc != null">evaluate_sort_desc = #{evaluateSortDesc},</if>
        <if test="evaluateSortText != null">evaluate_sort_text = #{evaluateSortText},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE evaluate_sort_id = #{evaluateSortId}
    </update>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.EvaluateSort"
            resultType="com.datalink.fdop.base.api.domain.EvaluateSort">
        SELECT a.*
        FROM zjdata.ccm_evaluate_sort a
        <where>
            <if test="evaluateSort.evaluateSortId != null and evaluateSort.evaluateSortId != ''">
                AND a.evaluate_sort_id = #{evaluateSort.evaluateSortId}
            </if>
        </where>
        ORDER BY evaluate_sort_id desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.EvaluateSort">
        SELECT a.*
        FROM zjdata.ccm_evaluate_sort a
        <where>
            <if test="searchVo != null and searchVo !=''">
                AND ${searchVo}
            </if>
        </where>
        ORDER BY evaluate_sort_id ${sort}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.EvaluateSort">
        SELECT a.*
        FROM zjdata.ccm_evaluate_sort a
        WHERE a.evaluate_sort_id = #{evaluateSortId}
        limit 1
    </select>

    <select id="listAll" resultType="com.datalink.fdop.base.api.domain.EvaluateSort">
        select * from zjdata.ccm_evaluate_sort
        <where>
            <if test="evaluateSortId !=null and evaluateSortId !=''">
                evaluate_sort_id = #{evaluateSortId}
            </if>
        </where>
    </select>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_evaluate_sort
            SET enable = #{item.enable}
            WHERE evaluate_sort_id = #{item.evaluateSortId}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_evaluate_sort (
        evaluate_sort_id, evaluate_sort_desc,evaluate_sort_text, enable,create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.evaluateSortId},#{item.evaluateSortDesc},
            #{item.evaluateSortText},
            #{item.enable},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>

</mapper>


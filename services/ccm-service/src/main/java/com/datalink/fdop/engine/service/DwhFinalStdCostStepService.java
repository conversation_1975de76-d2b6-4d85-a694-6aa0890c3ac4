package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostStep;

import java.util.List;

public interface DwhFinalStdCostStepService extends IService<DwhFinalStdCostStep> {

    PageDataInfo<DwhFinalStdCostStep> overview(
            String plantId, String verId, String controlAreaId, String productId,
            String date, String sort, SearchVo searchVo);

    List<DwhFinalStdCostStep> listAll();

    List<DwhFinalStdCostStep> selectNoPage(String plantId, String verId, String controlAreaId, String productId, String date, String sort, SearchVo searchVo);
}
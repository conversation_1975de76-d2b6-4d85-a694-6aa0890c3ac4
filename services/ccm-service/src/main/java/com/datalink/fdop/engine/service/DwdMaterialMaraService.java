package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.ProductVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdMaterialMara;

import java.util.List;

public interface DwdMaterialMaraService extends IService<DwdMaterialMara> {

    PageDataInfo<DwdMaterialMara> overview(String materialType,
                                           String materialGroup,
                                           String materialId, String sort, SearchVo searchVo);

    List<DwdMaterialMara> selectNoPage(String materialType,
                                   String materialGroup,
                                   String materialId, String sort, SearchVo searchVo);

    List<String> selectProduct(String productId);

    List<String> selectMaterialGroup(String materialGroup);

    List<String> selectMaterialType(String materialType);

    List<String> selectMaterialId(String materialId);

    PageDataInfo<ProductVo> selectProductList(String productId,
                                              String productDesc,
                                              String productCimId, String sort, SearchVo searchVo);

    PageDataInfo<ProductVo> selectProductListByTableName(String productId,
                                              String productDesc,
                                              String productCimId, String sort, SearchVo searchVo, String tableName);

}

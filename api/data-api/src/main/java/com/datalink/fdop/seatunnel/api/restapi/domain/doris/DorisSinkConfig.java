package com.datalink.fdop.seatunnel.api.restapi.domain.doris;

import com.alibaba.fastjson.annotation.JSONField;
import com.datalink.fdop.seatunnel.api.restapi.domain.SinkConfig;
import lombok.Data;

import java.util.Map;

@Data
public class DorisSinkConfig extends CommonDorisConfig implements SinkConfig {

    /**
     * 在SeaTunnel中定义一个伪表供下游transform sink 引用
     */
    @JSONField(name = "result_table_name")
    private String resultTableName;

    /**
     * 是否开启事务提交
     */
    @JSONField(name = "sink.enable-2pc")
    private Boolean sinkEnable2pc;

    /**
     * 表名
     */
    @JSONField(name = "table.identifier")
    private String tableIdentifier;

    /**
     * 目标前缀
     */
    @JSONField(name = "sink.label-prefix")
    private String sinkLabelPrefix = "sink";

    /**
     * Doris 配置
     */
    @JSONField(name = "doris.config")
    private Map<String, String> dorisConfig;

}

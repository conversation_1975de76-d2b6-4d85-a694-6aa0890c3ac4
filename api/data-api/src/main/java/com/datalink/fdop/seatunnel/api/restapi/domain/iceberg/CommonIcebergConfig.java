package com.datalink.fdop.seatunnel.api.restapi.domain.iceberg;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

/**
 * 通用Iceberg配置类
 * <AUTHOR>
 **/
@Data
public class CommonIcebergConfig {


    /**
     * SeaTunnel插件类型
     */
    @JSONField(name = "plugin_name")
    private String pluginName = "Iceberg";

    /**
     * catalog名称
     */
    private String catalogName;

    /**
     * catalog配置
     */
    @JSONField(name = "iceberg.catalog.config")
    private Map<String, String> catalogConfig;

    /**
     * hadoop配置路径
     */
    @JSONField(name = "iceberg.hadoop-conf-path")
    private String hadoopConfPath;

    /**
     * kerberos凭证信息
     */
    @JSONField(name = "kerberos_principal")
    private String kerberosPrincipal;

    /**
     * kerberos凭证认证Keytab文件路径
     */
    @JSONField(name = "kerberos_keytab_path")
    private String kerberosKeytabPath;

    /**
     * kerberos凭证认证文件路径
     */
    @JSONField(name = "kerberos_krb5_conf_path")
    private String kerberosKrb5ConfPath;

    /**
     * 库
     */
    private String namespace;

    /**
     * 表
     */
    private String table;

}

package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 段的规则
 * <AUTHOR>
 * @create 2022-10-27 11:31
 */
@Data
@TableName(schema = "zjdata", value = "ccm_allocation_segments")
@ApiModel
public class AllocationSegments {

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    @NotBlank(message = "管理范围不能为空")
    private String controlAreaId;

    @ApiModelProperty(value = "管理范围描述")
    @TableField(exist = false)
    private String controlAreaDesc;

    @NotBlank(message = "分摊方法不能为空")
    @ApiModelProperty(value = "分摊方法")
    @Excel(name = "分摊方法")
    private String allocationMethodId;

    @ApiModelProperty(value = "KeyID")
    @TableField(value = "keyid")
    @Excel(name = "KeyID")
    private String keyId;

    @NotBlank(message = "段编码不能为空")
    @ApiModelProperty(value = "段编码")
    @Excel(name = "段编码")
    private String allocationSegmentId;

    @ApiModelProperty(value = "段描述")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "段描述")
    private String allocationSegmentDesc;

    @ApiModelProperty(value = "发送方规则")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "发送方规则")
    private String sendFactor;

    @ApiModelProperty(value = "发送者比例")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "发送方比例")
    private String sendScale;

    @ApiModelProperty(value = "接收方规则")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "接收方规则")
    private String acceptFactor;

    @ApiModelProperty(value = "接收方比例")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "接收方比例")
    private String acceptScale;

    @ApiModelProperty(value = "成本流要素")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "成本流要素")
    private String flowingElement;

    @ApiModelProperty(value = "从--成本中心自ID")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "从--成本中心自ID")
    private String sendCostCenterFrom;

    @ApiModelProperty(value = "从--成本中心至ID")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "从--成本中心至ID")
    private String sendCostCenterTo;

    @ApiModelProperty(value = "从--成本中心组ID")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "从--成本中心组ID")
    private String sendCostCenterGroup;

    @ApiModelProperty(value = "从--成本要素自ID")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "从--成本要素自ID")
    private String sendCostElementFrom;

    @ApiModelProperty(value = "从--成本要素至ID")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "从--成本要素至ID")
    private String sendCostElementTo;

    @ApiModelProperty(value = "从--成本要素组ID")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "从--成本要素组ID")
    private String sendCostElementGroup;

    @ApiModelProperty(value = "至--成本中心自ID")
   // @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "至--成本中心自ID")
    private String acceptCostCenterFrom;

    @ApiModelProperty(value = "至--成本中心至ID")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "至--成本中心至ID")
    private String acceptCostCenterTo;

    @ApiModelProperty(value = "至--成本中心组ID")
    //@TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "至--成本中心组ID")
    private String acceptCostCenterGroup;


    @ApiModelProperty(value = "创建人")
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更改人")
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;

}

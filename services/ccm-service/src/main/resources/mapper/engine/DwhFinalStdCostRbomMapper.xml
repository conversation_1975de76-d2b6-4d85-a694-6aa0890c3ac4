<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwhFinalStdCostRbomMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwhFinalStdCostRbom">
        SELECT * FROM (
        SELECT
        rank() over(partition by node.ver_id,node.control_area_id,node.plant_id,node.product_id order by
        node.effective_date desc) as num, node.*
        FROM dwh.dwh_final_std_cost_rbom node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and node.control_area_id = #{controlAreaId}
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="date!= null and date != '' ">
                and effective_date &lt;= #{date}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productId != null and productId != ''">
                and node.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) overview WHERE overview.num = 1
        ORDER BY overview.cost_code,overview.ver_id,overview.control_area_id,
        overview.plant_id,overview.product_id,overview.hierarchy,overview.hierarchy_inner_seq,
        overview.effective_date,overview.base_date,overview.step_cost_flag,overview.raw_material_id
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwhFinalStdCostRbom">
        SELECT * FROM (
        SELECT
        rank() over(partition by node.ver_id,node.control_area_id,node.plant_id,node.product_id order by
        node.effective_date desc) as num, node.*
        FROM dwh.dwh_final_std_cost_rbom node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and node.control_area_id = #{controlAreaId}
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="date!= null and date != '' ">
                and effective_date &lt;= #{date}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productId != null and productId != ''">
                and node.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) overview WHERE overview.num = 1
        ORDER BY overview.cost_code,overview.ver_id,overview.control_area_id,
        overview.plant_id,overview.product_id,overview.hierarchy,overview.hierarchy_inner_seq,
        overview.effective_date,overview.base_date,overview.step_cost_flag,overview.raw_material_id
    </select>


</mapper>


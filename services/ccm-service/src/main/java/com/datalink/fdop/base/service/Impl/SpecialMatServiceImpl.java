package com.datalink.fdop.base.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.base.api.domain.SpecialMat;
import com.datalink.fdop.base.mapper.SpecialMatMapper;
import com.datalink.fdop.base.service.SpecialMatService;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class SpecialMatServiceImpl extends ServiceImpl<SpecialMatMapper, SpecialMat> implements SpecialMatService {

    @Autowired
    private SpecialMatMapper specialMatMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(SpecialMat specialMat) {
        if (UserConstants.NOT_UNIQUE.equals(checkIdUnique(specialMat.getSpecialType(), specialMat.getRawMaterialId(), specialMat.getProductId()))) {
            throw new ServiceException("新增专用物料管理'" + specialMat.getRawMaterialId() + "'失败，记录已存在");
        }
        specialMat.setCreateTime(new Date());
        specialMat.setCreateBy(SecurityUtils.getUsername());
        if (specialMat.getEnable() == null) {
            specialMat.setEnable(true);
        }
        return save(specialMat);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByList(List<SpecialMat> keyList) {
        if (CollectionUtils.isEmpty(keyList)) {
            throw new ServiceException("请指定要删除的数据");
        }
        return specialMatMapper.deleteByKey(keyList);
    }

    @Override
    public int updateByKey(SpecialMat specialMat) {
        if (UserConstants.UNIQUE.equals(checkIdUnique(specialMat.getSpecialType(), specialMat.getRawMaterialId(), specialMat.getProductId()))) {
            throw new ServiceException("修改专用物料管理'" + specialMat.getRawMaterialId() + "'失败，记录不存在");
        }
        specialMat.setUpdateBy(SecurityUtils.getUsername());
        specialMat.setUpdateTime(new Date());
        return specialMatMapper.updateByKey(specialMat);
    }

    @Override
    public String importData(List<SpecialMat> list, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SpecialMat specialMat : list) {
            try {
                if (StringUtils.isEmpty(specialMat.getSpecialType())) {
                    failureNum++;
                    failureMsg.append("\n" + failureNum + "、工厂代码为空 ");
                } else if (StringUtils.isEmpty(specialMat.getRawMaterialId())) {
                    failureNum++;
                    failureMsg.append("\n" + failureNum + "、原料编码为空 ");
                } else if (StringUtils.isEmpty(specialMat.getProductId())) {
                    failureNum++;
                    failureMsg.append("\n" + failureNum + "、产品编码为空 ");
                } else if (specialMatMapper.checkIdUnique(specialMat.getSpecialType(), specialMat.getRawMaterialId(), specialMat.getProductId()) == 0) {
                    specialMat.setCreateBy(operName);
                    specialMat.setCreateTime(new Date());
                    this.insert(specialMat);
                    successNum++;
                    successMsg.append("\n" + successNum + "、专用物料管理 " + specialMat.getRawMaterialId() + " 导入成功");
                } else if (isUpdateSupport) {
                    specialMat.setUpdateBy(operName);
                    specialMat.setUpdateTime(new Date());
                    this.updateByKey(specialMat);
                    successNum++;
                    successMsg.append("\n" + successNum + "专用物料管理 " + specialMat.getRawMaterialId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("\n" + failureNum + "、专用物料管理 " + specialMat.getRawMaterialId() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "\n" + failureNum + "、专用物料管理 " + specialMat.getRawMaterialId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！成功导入 " + successNum + " 条数据， 数据如下：" + successMsg + "， \n共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public PageDataInfo<SpecialMat> overview(String specialType,String sort, SearchVo searchVo) {
        Page<SpecialMat> page = PageUtils.getPage(SpecialMat.class);
        IPage<SpecialMat> iPage = specialMatMapper.selectAll(specialType,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    public String checkIdUnique(String specialType, String rawMaterialId, String productId) {
        int count = specialMatMapper.checkIdUnique(specialType, rawMaterialId, productId);
        if (count == 1) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public boolean updateBatchStatus(Boolean enable, List<SpecialMat> keyList) {
        return specialMatMapper.updateBatchStatus(enable, keyList);
    }
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRbom;

import java.util.List;

public interface DwsNewRbomService extends IService<DwsNewRbom> {

    PageDataInfo<DwsNewRbom> overview(
            String verId,
            String plantId,
            String productId,
            String sort,
            SearchVo searchVo);

    List<DwsNewRbom> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo);
}

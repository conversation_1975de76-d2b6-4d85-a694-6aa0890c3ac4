package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.engine.service.DataBalanceService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023-03-28 10:23
 */
@RestController
@RequestMapping("/fccm/dataBalance")
public class DataBalanceController {

    @Autowired
    private DataBalanceService dataBalanceService;

    @ApiOperation("展示数据校验平衡")
    @Log(title = "事件规则")
    @PostMapping("/dataBalance")
    public R dataBalance(@RequestParam(value = "beginTime", required = false) String beginTime,
                         @RequestParam(value = "endTime", required = false) String endTime) {
        return R.ok(dataBalanceService.dataBalance(beginTime, endTime));
    }

}

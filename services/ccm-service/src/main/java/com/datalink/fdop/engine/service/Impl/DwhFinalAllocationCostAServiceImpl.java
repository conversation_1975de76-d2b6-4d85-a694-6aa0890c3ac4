package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalAllocationCostA;
import com.datalink.fdop.engine.mapper.DwhFinalAllocationCostAMapper;
import com.datalink.fdop.engine.service.DwhFinalAllocationCostAService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalAllocationCostAServiceImpl extends ServiceImpl<DwhFinalAllocationCostAMapper, DwhFinalAllocationCostA> implements DwhFinalAllocationCostAService {

    @Autowired
    private DwhFinalAllocationCostAMapper dwhFinalAllocationCostAMapper;

    @Override
    public PageDataInfo<DwhFinalAllocationCostA> selectTargetedFinalAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo) {
        Page<DwhFinalAllocationCostA> page = PageUtils.getPage(DwhFinalAllocationCostA.class);
        IPage<DwhFinalAllocationCostA> iPage = dwhFinalAllocationCostAMapper.selectTargetedFinalAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public PageDataInfo<DwhFinalAllocationCostA> selectCostFinalAllocation(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo) {
        Page<DwhFinalAllocationCostA> page = PageUtils.getPage(DwhFinalAllocationCostA.class);
        IPage<DwhFinalAllocationCostA> iPage = dwhFinalAllocationCostAMapper.selectCostFinalAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public Boolean checkAllocationCost(String controlAreaId, String verId, String yearMonthFrom, String yearMonthTo, String allocationMethodId) {
        String checkAllocationCost = dwhFinalAllocationCostAMapper.checkAllocationCost(controlAreaId, verId, yearMonthFrom, yearMonthTo, allocationMethodId);
        if (StringUtils.isEmpty(checkAllocationCost)) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean checkTargetedAllocationCost(String companyId, String yearMonthFrom, String yearMonthTo, String allocationType) {
        int checkTargetedAllocationCost = dwhFinalAllocationCostAMapper.checkTargetedAllocationCost(companyId, yearMonthFrom, yearMonthTo, allocationType);
        if (checkTargetedAllocationCost == 0) {
            return true;
        }
        return false;
    }

    @Override
    public List<DwhFinalAllocationCostA> selectNoPage(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo) {
        return dwhFinalAllocationCostAMapper.selectNoPage(controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, sort, searchVo);
    }

}
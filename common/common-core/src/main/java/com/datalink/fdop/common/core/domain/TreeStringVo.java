package com.datalink.fdop.common.core.domain;

import com.datalink.fdop.common.core.enums.MenuType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/17 13:39
 */
@Data
public class TreeStringVo<T> implements Serializable {

    private String id;

    private String pid;

    private MenuType menuType;

    private String name;

    private String code;

    private String description;

    private Integer serialNumber;

    private List<T> children = new ArrayList<T>();


    public TreeStringVo() {
    }

    public TreeStringVo(String id, String name, String code, String description) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.description = description;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.AllocationSegmentsMapper">

    <select id="checkUnique" resultType="int">
        SELECT COUNT(1) FROM zjdata.ccm_allocation_segments WHERE control_area_id = #{controlAreaId} AND keyid = #{keyId}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.AllocationSegments">
        select * from zjdata.ccm_allocation_segments  WHERE control_area_id = #{controlAreaId} AND keyid = #{keyId}
    </select>

    <update id="deleteByKey" parameterType="com.datalink.fdop.base.api.domain.AllocationDivision">
        delete from zjdata.ccm_allocation_segments
        where (control_area_id, keyid) in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.keyId})
        </foreach>
    </update>

    <sql id="selectListSql">
        SELECT node.*, controlArea.control_area_desc as control_area_desc
        FROM zjdata.ccm_allocation_segments node
        left join zjdata.ccm_control_area controlArea on node.control_area_id = controlArea.control_area_id
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                AND node.control_area_id = #{controlAreaId}
            </if>
            <if test="keyId != null and keyId != ''">
                AND node.keyid = #{keyId}
            </if>
        </where>
        ORDER BY node.allocation_method_id ${sort}
    </sql>

    <select id="selectList"  resultType="com.datalink.fdop.base.api.domain.AllocationSegments">
        <include refid="selectListSql"></include>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.AllocationSegments">
        <include refid="selectListSql"></include>
    </select>

</mapper>


package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostCapa;
import com.datalink.fdop.engine.mapper.DwsNewStdCostCapaMapper;
import com.datalink.fdop.engine.service.DwsNewStdCostCapaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewStdCostCapaServiceImpl extends ServiceImpl<DwsNewStdCostCapaMapper, DwsNewStdCostCapa> implements DwsNewStdCostCapaService {

    @Autowired
    private DwsNewStdCostCapaMapper dwsNewStdCostCapaMapper;

    @Override
    public PageDataInfo<DwsNewStdCostCapa> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwsNewStdCostCapa> page = PageUtils.getPage(DwsNewStdCostCapa.class);
        IPage<DwsNewStdCostCapa> iPage = dwsNewStdCostCapaMapper.selectAll(controlAreaId, productId, verId, plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewStdCostCapa> listAll() {
        return dwsNewStdCostCapaMapper.listAll();
    }

    @Override
    public List<DwsNewStdCostCapa> selectNoPage(String controlAreaId, String productId, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwsNewStdCostCapaMapper.selectNoPage(controlAreaId, productId, verId, plantId, date, sort, searchVo);
    }
}
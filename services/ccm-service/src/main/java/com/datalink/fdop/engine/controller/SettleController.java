package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.engine.service.SettleService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-28 14:11
 */
@RestController
@RequestMapping("/fccm/settle")
public class SettleController {

    @Autowired
    private SettleService settleService;

    @ApiOperation("期间成本结算 - 凭证 - temp")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/postingTemp")
    public R postingTemp(@RequestParam(value = "verId") String verId,
                    @RequestParam(value = "factoryId") String factoryId,
                    @RequestParam(value = "year") Long year,
                    @RequestParam(value = "month") Long month,
                    @RequestParam(value = "businessType") String businessType,
                    @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.posting(verId, factoryId, year, month, businessType, searchVo, "temp", null));
    }

    @ApiOperation("期间成本结算 - 凭证 - s1")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/postingS1")
    public R postingS1(@RequestParam(value = "verId") String verId,
                         @RequestParam(value = "factoryId") String factoryId,
                         @RequestParam(value = "year") Long year,
                         @RequestParam(value = "month") Long month,
                         @RequestParam(value = "businessType") String businessType,
                         @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.posting(verId, factoryId, year, month, businessType, searchVo, "s1", "S"));
    }

    @ApiOperation("期间成本结算 - 凭证 - s2")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/postingS2")
    public R postingS2(@RequestParam(value = "verId") String verId,
                         @RequestParam(value = "factoryId") String factoryId,
                         @RequestParam(value = "year") Long year,
                         @RequestParam(value = "month") Long month,
                         @RequestParam(value = "businessType") String businessType,
                         @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.posting(verId, factoryId, year, month, businessType, searchVo, "s1", "F"));
    }

    @ApiOperation("期间成本结算 - 结算完工品 - temp")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/fgTemp")
    public R fgTemp(@RequestParam(value = "verId") String verId,
                @RequestParam(value = "factoryId") String factoryId,
                @RequestParam(value = "year") Long year,
                @RequestParam(value = "month") Long month,
                @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.fg(verId, factoryId, year, month, searchVo, "temp", null));
    }

    @ApiOperation("期间成本结算 - 结算完工品 - s1")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/fgS1")
    public R fgS1(@RequestParam(value = "verId") String verId,
                @RequestParam(value = "factoryId") String factoryId,
                @RequestParam(value = "year") Long year,
                @RequestParam(value = "month") Long month,
                @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.fg(verId, factoryId, year, month, searchVo, "s1", "S"));
    }

    @ApiOperation("期间成本结算 - 结算完工品 - s2")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/fgS2")
    public R fgS2(@RequestParam(value = "verId") String verId,
                  @RequestParam(value = "factoryId") String factoryId,
                  @RequestParam(value = "year") Long year,
                  @RequestParam(value = "month") Long month,
                  @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.fg(verId, factoryId, year, month, searchVo, "s1", "F"));
    }

    @ApiOperation("期间成本结算 - 存档")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/settleS1Save")
    public R settleS1Save(@RequestParam(value = "verId") String verId,
                  @RequestParam(value = "factoryId") String factoryId,
                  @RequestParam(value = "year") Long year,
                  @RequestParam(value = "month") Long month,
                  @RequestParam(value = "businessType") String businessType) {
        return R.ok(settleService.settleSave(verId, factoryId, year, month, businessType, "S"));
    }

    @ApiOperation("期间成本结算 - 结算")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/settleS2Save")
    public R settleS2Save(@RequestParam(value = "verId") String verId,
                  @RequestParam(value = "factoryId") String factoryId,
                  @RequestParam(value = "year") Long year,
                  @RequestParam(value = "month") Long month,
                  @RequestParam(value = "businessType") String businessType) {
        return R.ok(settleService.settleSave(verId, factoryId, year, month, businessType, "F"));
    }

    @ApiOperation("期间成本结算 - 撤销存档")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/undoS1Save")
    public R undoS1Save(@RequestParam(value = "verId") String verId,
                        @RequestParam(value = "factoryId") String factoryId,
                        @RequestParam(value = "year") Long year,
                        @RequestParam(value = "month") Long month,
                        @RequestParam(value = "businessType") String businessType) {
        return R.ok(settleService.undoSave(verId, factoryId, year, month, businessType, "S"));
    }

    @ApiOperation("期间成本结算 - 撤销结算")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/undoS2Save")
    public R undoS2Save(@RequestParam(value = "verId") String verId,
                        @RequestParam(value = "factoryId") String factoryId,
                        @RequestParam(value = "year") Long year,
                        @RequestParam(value = "month") Long month,
                        @RequestParam(value = "businessType") String businessType) {
        return R.ok(settleService.undoSave(verId, factoryId, year, month, businessType, "F"));
    }

    @ApiOperation("期间成本结算 - 结算非完工 - temp")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/wipTemp")
    public R wipTemp(@RequestParam(value = "verId") String verId,
                  @RequestParam(value = "factoryId") String factoryId,
                  @RequestParam(value = "year") Long year,
                  @RequestParam(value = "month") Long month,
                  @RequestParam(value = "businessType") String businessType,
                  @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.wip(verId, factoryId, year, month, businessType, searchVo, "temp", null));
    }

    @ApiOperation("期间成本结算 - 结算非完工 - s1")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/wipS1")
    public R wipS1(@RequestParam(value = "verId") String verId,
                     @RequestParam(value = "factoryId") String factoryId,
                     @RequestParam(value = "year") Long year,
                     @RequestParam(value = "month") Long month,
                     @RequestParam(value = "businessType") String businessType,
                     @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.wip(verId, factoryId, year, month, businessType, searchVo, "s1", "S"));
    }

    @ApiOperation("期间成本结算 - 结算非完工 - s2")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/wipS2")
    public R wipS2(@RequestParam(value = "verId") String verId,
                     @RequestParam(value = "factoryId") String factoryId,
                     @RequestParam(value = "year") Long year,
                     @RequestParam(value = "month") Long month,
                     @RequestParam(value = "businessType") String businessType,
                     @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.wip(verId, factoryId, year, month, businessType, searchVo, "s1", "F"));
    }

    @ApiOperation("期间成本结算 - 结算事件（期间） - temp")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/eventTemp")
    public R eventTemp(@RequestParam(value = "verId") String verId,
                     @RequestParam(value = "factoryId") String factoryId,
                     @RequestParam(value = "year") Long year,
                     @RequestParam(value = "month") Long month,
                     @RequestParam(value = "businessType") String businessType,
                     @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.event(verId, factoryId, year, month, businessType, searchVo, "temp", null));
    }

    @ApiOperation("期间成本结算 - 结算事件（期间） - s1")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/eventS1")
    public R eventS1(@RequestParam(value = "verId") String verId,
                   @RequestParam(value = "factoryId") String factoryId,
                   @RequestParam(value = "year") Long year,
                   @RequestParam(value = "month") Long month,
                   @RequestParam(value = "businessType") String businessType,
                   @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.event(verId, factoryId, year, month, businessType, searchVo, "s1", "S"));
    }

    @ApiOperation("期间成本结算 - 结算事件（期间） - s2")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/eventS2")
    public R eventS2(@RequestParam(value = "verId") String verId,
                   @RequestParam(value = "factoryId") String factoryId,
                   @RequestParam(value = "year") Long year,
                   @RequestParam(value = "month") Long month,
                   @RequestParam(value = "businessType") String businessType,
                   @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(settleService.event(verId, factoryId, year, month, businessType, searchVo, "s1", "F"));
    }

}

package com.datalink.fdop.quality.api.domain;

import com.datalink.fdop.quality.api.enums.RegexRelationType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/17 17:30
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RegexRule {

    @ApiModelProperty(value = "规则关系")
    private RegexRelationType relation;

    @ApiModelProperty(value = "子规则")
    private List<RegexRule> children;

    @ApiModelProperty(value = "规则条件")
    private List<RegexValue> condition;

    @JsonIgnore
    public String getAgeStr() {
        return "{ relation: '" + this.getRelation() + "'" +
                (CollectionUtils.isNotEmpty(this.getChildren()) ? ", children: "
                        + this.getChildren().stream()
                        .map(regexRule -> regexRule.getAgeStr())
                        .collect(Collectors.joining(",", "[", "]")) : "") +
                (CollectionUtils.isNotEmpty(this.getCondition()) ? ", condition: "
                        + this.getCondition().stream()
                        .map(regexValue -> regexValue.getAgeStr())
                        .collect(Collectors.joining(",", "[", "]")) : "") +
                "}";
    }


}

package com.datalink.fdop.seatunnel.api;


import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelSubmitConfig;
import com.datalink.fdop.seatunnel.api.factory.RemoteSeaTunnelRestApiFallbackFactory;
import com.datalink.fdop.seatunnel.api.restapi.domain.Job;
import com.datalink.fdop.seatunnel.api.restapi.domain.JobInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(contextId = "remoteSeaTunnelRestApiService", path = "/seatunnel/seatunnel/rest/api",
        value = ServiceNameConstants.DATA_SERVICE, fallbackFactory = RemoteSeaTunnelRestApiFallbackFactory.class)
public interface RemoteSeaTunnelRestApiService {

    /**
     * 提交SeaTunnel任务
     * @param modelConfig 数据模型配置
     * @return Job信息
     */
    @PostMapping("/submit-job")
    R<JobInfo> submitJob(@RequestBody SeaTunnelSubmitConfig modelConfig);

    /**
     * 获取SeaTunnel任务
     * @param jobId JobId
     * @return Job执行信息
     */
    @PostMapping("/running-job/{jobId}")
    R<Job> getJob(@PathVariable(value = "jobId") String jobId);

    /**
     * 查询所有已完成的任务
     * @param state 根据任务状态查询
     * @return 任务集合信息
     */
    @PostMapping("/finished-jobs/{state}")
    R<List<Job>> finishedJobs(@PathVariable(value = "state") String state);

}

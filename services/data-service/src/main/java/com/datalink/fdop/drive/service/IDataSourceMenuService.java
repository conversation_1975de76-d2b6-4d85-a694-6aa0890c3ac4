package com.datalink.fdop.drive.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.DataSourceMenu;
import com.datalink.fdop.drive.api.model.DataSourceTree;

import java.util.List;

public interface IDataSourceMenuService {

    int create(DataSourceMenu dataSourceMenu);

    int delete(List<Long> ids);

    int update(DataSourceMenu dataSourceMenu);

    List<DataSourceTree> tree(String sort, String code, Boolean isQueryNode);

    PageDataInfo<DataSource> overview(Long pid, String sort, SearchVo searchVo);
}

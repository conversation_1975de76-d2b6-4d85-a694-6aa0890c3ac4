package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.engine.api.domain.DwdSptEg;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwdSptEgMapper extends BaseMapper<DwdSptEg> {

    IPage<DwdSptEg> selectAll(@Param("plantId") String plantId,
                              @Param("equipGroupId") String equipGroupId,
                              @Param("page") IPage<DwdSptEg> page,
                              @Param("sort") String sort,
                              @Param("searchVo") SearchVo searchVo);
    
    List<DwdSptEg> selectNoPage(@Param("plantId") String plantId,
                                @Param("equipGroupId") String equipGroupId,
                                @Param("sort") String sort,
                                @Param("searchVo") SearchVo searchVo);
}
package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpAllocationCostA;
import com.datalink.fdop.engine.mapper.DwhTerpAllocationCostAMapper;
import com.datalink.fdop.engine.service.DwhTerpAllocationCostAService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhTerpAllocationCostAServiceImpl extends ServiceImpl<DwhTerpAllocationCostAMapper, DwhTerpAllocationCostA> implements DwhTerpAllocationCostAService {

    @Autowired
    private DwhTerpAllocationCostAMapper dwhTerpAllocationCostAMapper;

    @Override
    public PageDataInfo<DwhTerpAllocationCostA> overview(String verId, String controlAreaId, String yearMonthFrom, String yearMonthTo,String allocationType, String sort, SearchVo searchVo) {
        Page<DwhTerpAllocationCostA> page = PageUtils.getPage(DwhTerpAllocationCostA.class);
        IPage<DwhTerpAllocationCostA> iPage = dwhTerpAllocationCostAMapper.selectAll(verId, controlAreaId, yearMonthFrom,yearMonthTo,allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhTerpAllocationCostA> selectNoPage(String verId, String controlAreaId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo) {
        return dwhTerpAllocationCostAMapper.selectNoPage(verId, controlAreaId, yearMonthFrom, yearMonthTo, allocationType, sort, searchVo);
    }
}
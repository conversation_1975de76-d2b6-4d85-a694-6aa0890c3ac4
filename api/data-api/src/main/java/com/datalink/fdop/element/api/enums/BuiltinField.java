package com.datalink.fdop.element.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2023/1/6 10:34
 */
public enum BuiltinField {

    // batchId
    _BATCH_ID(0, "fdop_batch_id"),
    // 导入时间
    _IMPORT_TIME(1, "fdop_import_time"),
    // 数据质量日志id
    GUALITY_LOG_ID(2, "guality_log_id"),
    // excel/csv sheet页名称 在采集任务时，会自动填入当前数据来源所属的SHEET页名称
    SHEET_NAME(3, "sheet_name"),
    // excel/csv 文件名称 在采集任务时，会自动填入当前数据来源所属的文件名称
    FILE_NAME(4, "FILE_NAME"),
    // 在采集任务时，会自动填入当前资源文件的更新时间
    FILE_UPDATE_TIME(5, "FILE_UPDATE_TIME"),
    // 在采集任务时，会自动填入当前资源文件的创建时间
    FILE_CREATE_TIME(5, "FILE_CREATE_TIME"),
    ;

    private final int code;
    @EnumValue
    private final String desc;

    BuiltinField(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}

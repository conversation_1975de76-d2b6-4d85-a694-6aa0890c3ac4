package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.ActualActivity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:34
 */
public interface ActualActivityMapper extends BaseMapper<ActualActivity> {

    List<ActualActivity> getOrg(@Param("sort") String sort, @Param("searchCondition") SearchVo searchCondition);

    List<ActualActivity> getEdit(@Param("sort") String sort, @Param("searchCondition") SearchVo searchVo);

    List<ActualActivity> getAll(@Param("sort") String sort, @Param("searchCondition") SearchVo searchVo);

    void add(@Param("sql") String sql);

    void updateByKey(ActualActivity actualActivity);

    ActualActivity selectByKey(ActualActivity actualActivity);

    int deleteByKey(ActualActivity actualActivity);
}

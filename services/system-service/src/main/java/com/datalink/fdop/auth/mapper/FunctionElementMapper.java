package com.datalink.fdop.auth.mapper;

import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.auth.api.domain.FunctionElement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/24 17:38
 */
@Repository
@Transactional
@Mapper
public interface FunctionElementMapper {


    List<VlabelItem<FunctionElement>> query();

    int saveFElemObjRelation(@Param("fElemIds") List<Long> fElemIds,@Param("objId") Long objId);

    int delFElemObjRelation(@Param("fElemIds") List<Long> fElemIds,@Param("objId") Long objId);

    VlabelItem<FunctionElement> findBycode(@Param("funcElemCode")String funcElemCode);

    String findEntityCode(Long entityId);

    String findElementCode(Long elementId);
}

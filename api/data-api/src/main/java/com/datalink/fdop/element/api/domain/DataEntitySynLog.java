package com.datalink.fdop.element.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseDomain;
import com.datalink.fdop.element.api.enums.DataEntitySynStatus;
import com.datalink.fdop.element.api.enums.EntityType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/12 11:45
 */
@Data
@TableName(schema = "zjdata", value = "d_e_data_entity_syn_log")
@ApiModel("数据实体同步日志")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataEntitySynLog extends BaseDomain {

    private static final long serialVersionUID = -5508413529453322173L;

    @ApiModelProperty(value = "日志id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "菜单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    @ApiModelProperty(value = "菜单编码")
    @TableField(exist = false)
    private String menuCode;

    @ApiModelProperty(value = "数据源id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dataSourceId;

    @ApiModelProperty(value = "数据源编码")
    @TableField(exist = false)
    private String dataSourceCode;

    @ApiModelProperty(value = "库")
    private String databaseName;

    @ApiModelProperty(value = "表")
    private String tableName;

    @ApiModelProperty(value = "视图")
    private String viewName;

    @ApiModelProperty(value = "实体编码")
    private String entityCode;

    @ApiModelProperty(value = "同步状态")
    private DataEntitySynStatus synStatus;

    @ApiModelProperty(value = "错误信息")
    private String error;

    @ApiModelProperty(value = "目标数据源")
    private String sinkDataSourceId;

    @ApiModelProperty(value = "目标数据库")
    private String sinkDataBaseName;

    @ApiModelProperty(value = "是否开启创表")
    private Boolean useCreateTable;

    @ApiModelProperty(value = "实体类型")
    private EntityType entityType;

    public DataEntitySynLog() {

    }

    public DataEntitySynLog(Long id, Long pid, Long dataSourceId, String databaseName, String tableName, String viewName, String entityCode, Boolean useCreateTable) {
        this.id = id;
        this.pid = pid;
        this.dataSourceId = dataSourceId;
        this.databaseName = databaseName;
        this.tableName = tableName;
        this.viewName = viewName;
        this.entityCode = entityCode;
        this.synStatus = DataEntitySynStatus.RUNNING;
        this.useCreateTable = useCreateTable;
    }

}

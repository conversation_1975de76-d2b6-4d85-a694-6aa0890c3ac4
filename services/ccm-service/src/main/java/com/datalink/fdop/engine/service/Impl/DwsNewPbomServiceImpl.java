package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewPbom;
import com.datalink.fdop.engine.mapper.DwsNewPbomMapper;
import com.datalink.fdop.engine.service.DwsNewPbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewPbomServiceImpl extends ServiceImpl<DwsNewPbomMapper, DwsNewPbom> implements DwsNewPbomService {

    @Autowired
    private DwsNewPbomMapper dwsNewPbomMapper;

    @Override
    public PageDataInfo<DwsNewPbom> overview(String verId,String  plantId,String productId,String sort, SearchVo searchVo) {
        Page<DwsNewPbom> page = PageUtils.getPage(DwsNewPbom.class);
        IPage<DwsNewPbom> iPage = dwsNewPbomMapper.selectAll(verId,  plantId, productId,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewPbom> listAll() {
        return dwsNewPbomMapper.listAll();
    }

    @Override
    public List<DwsNewPbom> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwsNewPbomMapper.selectNoPage(verId, plantId, productId, sort, searchVo);
    }


}

package com.datalink.fdop.quality.service;

import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.quality.api.model.dto.DataEntityQuality;
import com.datalink.fdop.quality.api.model.vo.CheckRegexVo;
import com.datalink.fdop.quality.api.model.vo.DataQualityCopyVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
public interface IDataQualityService {

    DataQuality create(DataQuality dataQuality);

    int update(DataQuality dataQuality);

    void bindQualityAndStandardEdge(Long qualityId, Long standardId);

    int copy(Long pid, List<DataQualityCopyVo> dataQualityCopyVoList);

    int delete(List<Long> ids);

    DataQuality selectById(Long id);

    String getRegex(Long id, String prefix);

    Boolean checkRegex(CheckRegexVo checkRegexVo);

    List<DataElementStructureVo> selectQualityElement(Long id, String code, String name);

    int bindQualityElement(Long id, List<DataElementStructureVo> dataElementStructureVoList);

    int unbindQualityElement(Long id, List<DataElementStructureVo> dataElementStructureVoList);

    List<DataEntityQuality> parseEntityQuality(Long dataEntityId);

    List<DataEntityQuality> parseElementQuality(Long elementId);

}

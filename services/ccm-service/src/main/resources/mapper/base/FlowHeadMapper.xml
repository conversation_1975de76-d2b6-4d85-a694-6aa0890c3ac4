<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.FlowHeadMapper">

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.FlowHead" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_flow_head
        WHERE technology_id = #{flowHead.technologyId}
          AND technology_ver = #{flowHead.technologyVer}
          AND del_flag = false limit 1
    </select>

    <update id="deleteByIds" parameterType="Long">
        update zjdata.ccm_flow_head
        SET del_flag = true
        WHERE
        <foreach collection="flowHeadList" item="flowHead" separator=" or ">
            ( technology_id = #{flowHead.technologyId} and technology_ver = #{flowHead.technologyVer} )
        </foreach>
        and del_flag = false
    </update>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.FlowHead" resultType="com.datalink.fdop.base.api.domain.FlowHead">
        SELECT *
        FROM zjdata.ccm_flow_head
        WHERE del_flag = false
        ORDER BY technology_id, technology_ver desc
    </select>

    <select id="selectIdsByPid" resultType="String">
        select technology_id from zjdata.ccm_flow_head
        where menu_id = #{pid} and del_flag = false
    </select>

    <update id="bacthUpdatePidById">
        update zjdata.ccm_flow_head
        set menu_id = (select p_menu_id from zjdata.ccm_flow_menu where menu_id = #{menuId} limit 1)
        where menu_id = #{menuId} and del_flag = false
    </update>

    <update id="updateByKey">
        update zjdata.ccm_flow_head
        set
        <if test="technologyDesc != null">technology_desc = #{technologyDesc},</if>
        <if test="technologyText != null">technology_text = #{technologyText},</if>
        <if test="dateFrom != null">date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}',</if>
        <if test="dateTo != null">date_to = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateTo)}',</if>
        <if test="menuId != null">menu_id = #{menuId},</if>
        <if test="enable != null">enable = #{enable},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        update_time = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE del_flag = false
            AND technology_id = #{technologyId}
            AND technology_ver = #{technologyVer}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.FlowHead">
        SELECT ls.*
        FROM zjdata.ccm_flow_head ls
        WHERE ls.del_flag = false
        <if test="a != null and a != ''">
            AND ${a}
        </if>
        ORDER BY technology_id, technology_ver ${sort}
    </select>

    <select id="selectById" parameterType="String"
            resultType="com.datalink.fdop.base.api.domain.FlowHead">
        SELECT *
        FROM zjdata.ccm_flow_head
        WHERE technology_id = #{technologyId} AND del_flag = false
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.FlowHead">
        SELECT *
        FROM zjdata.ccm_flow_head
        WHERE del_flag = false
          AND technology_id = #{technologyId}
          AND technology_ver = #{technologyVer} limit 1
    </select>

    <select id="getVerByCode" resultType="java.lang.Long">
        SELECT technology_ver
        FROM zjdata.ccm_flow_head
        WHERE technology_id = #{technologyId} AND del_flag = false
    </select>

</mapper>


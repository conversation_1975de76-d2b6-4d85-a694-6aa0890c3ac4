package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.base.api.domain.CcmRepcCapaegp;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CcmRepcCapaegpMapper extends BaseMapper<CcmRepcCapaegp> {

    int checkIdUnique(@Param("factoryId") String factoryId, @Param("oldEquipGroupId") String oldEquipGroupId, @Param("resDept") String resDept);

    int deleteBykey(@Param("factoryId") String factoryId, @Param("oldEquipGroupId") String oldEquipGroupId, @Param("resDept") String resDept);

    int updateByKey(CcmRepcCapaegp ccmRepcCapaegp);

    IPage<CcmRepcCapaegp> overview(IPage<CcmRepcCapaegp> page,@Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    boolean updateBatchStatus(@Param("enable") Boolean enable, @Param("list") List<CcmRepcCapaegp> list);
}
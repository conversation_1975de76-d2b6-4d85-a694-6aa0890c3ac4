package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-03-16 11:04
 */
@Data
public class DrillDowntReport200 {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long year;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long month;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long yearMonth;
    private String verId;
    private String companyId;
    private String factoryId;
    private String costCenterId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long costLevel;
    private String costStructureId;
    private String costStructureDesc;
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal costPlan;
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal costActual;
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal cost1;
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal cost2;
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal cost3;


    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private BigDecimal var;
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private BigDecimal varCost;

    public void setVar() {
        this.var = costPlan.subtract(costActual);
    }

}

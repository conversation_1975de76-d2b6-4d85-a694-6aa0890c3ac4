package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRevalueExp;
import com.datalink.fdop.engine.mapper.DwsNewRevalueExpMapper;
import com.datalink.fdop.engine.service.DwsNewRevalueExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRevalueExpServiceImpl extends ServiceImpl<DwsNewRevalueExpMapper, DwsNewRevalueExp> implements DwsNewRevalueExpService {

    @Autowired
    private DwsNewRevalueExpMapper dwsNewRevalueExpMapper;

    @Override
    public PageDataInfo<DwsNewRevalueExp> overview(String yearMonth, String verId, String controlAreaId, String companyId, String sort, SearchVo searchVo) {
        Page<DwsNewRevalueExp> page = PageUtils.getPage(DwsNewRevalueExp.class);
        IPage<DwsNewRevalueExp> iPage = dwsNewRevalueExpMapper.selectAll(yearMonth, verId, controlAreaId, companyId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRevalueExp> selectNoPage(String yearMonth, String verId, String controlAreaId, String companyId, String sort, SearchVo searchVo) {
        return dwsNewRevalueExpMapper.selectNoPage(yearMonth, verId, controlAreaId, companyId, sort, searchVo);
    }
}
package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import com.datalink.fdop.element.api.domain.DataEntityTableVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataEntityTableMapper extends BaseMapper<DataEntityTable> {

    int insertTable(@Param("dataEntityTable") DataEntityTable dataEntityTable);

    int updateById(DataEntityTable dataEntityTable);

    int deleteDataSourceTable(@Param("tableId") Long tableId);

    int updateDataSourceTable(@Param("tableId") Long tableId, @Param("dataSourceId") Long dataSourceId);

    int deleteEntityTableTableEdge(@Param("tableId") Long id);

    int createEntityTableTableEdge(@Param("tableId") Long id,
                                   @Param("dataSourceId") Long dataSourceId,
                                   @Param("databaseName") String databaseName,
                                   @Param("schemaName") String schemaName,
                                   @Param("tableName") String tableName);

    int deleteBatchIds(@Param("dataEntityId") Long dataEntityId, @Param("ids") List<Long> ids);

    DataEntityTable selectById(@Param("tableId") Long tableId);

    List<DataEntityTable> selectByIdList(@Param("tableIds") List<Long> tableIds);

    List<DataEntityTableVo> selectDataEntityTableVoByDataEntityIds(@Param("dataEntityIds") List<Long> dataEntityIds);

    DataEntityTable selectByCode(@Param("dataEntityId") Long dataEntityId, @Param("code") String code);

    DataEntityTable checkCodeIsExists(@Param("dataEntityId") Long dataEntityId, @Param("tableId") Long tableId, @Param("code") String code);

    List<DataEntityTable> selectList(DataEntityTable dataEntityTable);

    int selectTotal(@Param("dataEntityId") Long dataEntityId);

    List<DataEntityTableMapping> selectTableMapping(@Param("dataEntityId") Long dataEntityId, @Param("tableId") Long tableId);

}

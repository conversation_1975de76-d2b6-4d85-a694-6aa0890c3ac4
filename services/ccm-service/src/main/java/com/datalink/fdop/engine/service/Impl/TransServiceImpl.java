package com.datalink.fdop.engine.service.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.base.api.RemoteCostStructureHeadService;
import com.datalink.fdop.base.api.domain.CostStructureHead;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.drive.api.RemoteTaskService;
import com.datalink.fdop.engine.api.domain.*;
import com.datalink.fdop.engine.mapper.TransMapper;
import com.datalink.fdop.engine.service.TransService;
import com.datalink.fdop.param.api.RemoteParamService;
import com.datalink.fdop.param.api.domain.GlobalParam;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dolphinscheduler.fdop.api.RemoteProjectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-02-15 15:35
 */
@Service
public class TransServiceImpl implements TransService {

    private static final Logger log = LoggerFactory.getLogger(TransServiceImpl.class);

    @Autowired
    private RemoteProjectService remoteProjectService;

    @Autowired
    private RemoteTaskService remoteTaskService;

    @Autowired
    private RemoteParamService remoteParamService;

    @Autowired
    private TransMapper transMapper;

    @Autowired
    private RemoteCostStructureHeadService remoteCostStructureHeadService;

    @Override
    public Map<String, Object> stdCostRun(String verId, String controlAreaId,
                                          String factoryId, String effectiveDate,
                                          Long batchQty, String productId, Long productVer, String type, SearchVo searchVo) {
        R<List<CostStructureHead>> listR = remoteCostStructureHeadService.queryAll();
        if (listR.getCode() != 200) {
            throw new ServiceException("获取成本组件失败" + listR.getMsg());
        }
        List<CostStructureHead> costStructureHeadList = listR.getData();
        Map<String, String> costStructureHeadMap = costStructureHeadList.stream().collect(Collectors.toMap(CostStructureHead::getCostStructureId, CostStructureHead::getCostStructureDesc));
        List<StdCostItem> stdCostItemList = transMapper.selectStdCostItem(verId, controlAreaId, factoryId, effectiveDate, batchQty, productId, productVer, searchVo);
        Map<String, Object> result = Maps.newHashMap();
        List<Map<String, Object>> headColumn = this.getCommonColumn();
        if ("ProductID".equals(type)) {
            headColumn.add(getMap("technologyId", "工艺编码"));
            headColumn.add(getMap("technologyVer", "工艺版本"));
        } else if ("Layer".equals(type)) {
            headColumn.add(getMap("layerId", "LAYER"));
        } else if ("Capacity".equals(type)) {
            headColumn.add(getMap("costCenterId", "成本单元"));
        }
        headColumn.addAll(this.getHeadColumn());
        List<Map<String, Object>> itemColumn = this.getItemColumn();

        List<String> keys = new ArrayList<>();
        List<Map<String, Object>> costStructureColumn = new ArrayList<>();
        for (StdCostItem stdCostItem : stdCostItemList) {
            stdCostItem.setCostCode(stdCostItem.getControlAreaId()+stdCostItem.getVerId()
                    +stdCostItem.getFactoryId()+stdCostItem.getProductId()+stdCostItem.getProductVer()+ DateFormatUtils.format(stdCostItem.getEffectiveDate(), "yyyyMMdd"));
            if (stdCostItem.getCostStructure001() != null && !keys.contains("costStructure001")) {
                costStructureColumn.add(getMap("costStructure001", costStructureHeadMap.get("cost_structure_001")));
                keys.add("costStructure001");
            }
            if (stdCostItem.getCostStructure002() != null && !keys.contains("costStructure002")) {
                costStructureColumn.add(getMap("costStructure002", costStructureHeadMap.get("cost_structure_002")));
                keys.add("costStructure002");
            }
            if (stdCostItem.getCostStructure003() != null && !keys.contains("costStructure001")) {
                costStructureColumn.add(getMap("costStructure003", costStructureHeadMap.get("cost_structure_003")));
                keys.add("costStructure003");
            }
            if (stdCostItem.getCostStructure004() != null && !keys.contains("costStructure001")) {
                costStructureColumn.add(getMap("costStructure004", costStructureHeadMap.get("cost_structure_004")));
                keys.add("costStructure004");
            }
            if (stdCostItem.getCostStructure005() != null && !keys.contains("costStructure005")) {
                costStructureColumn.add(getMap("costStructure005", costStructureHeadMap.get("cost_structure_005")));
                keys.add("costStructure005");
            }
            if (stdCostItem.getCostStructure006() != null && !keys.contains("costStructure006")) {
                costStructureColumn.add(getMap("costStructure006", costStructureHeadMap.get("cost_structure_006")));
                keys.add("costStructure006");
            }
            if (stdCostItem.getCostStructure007() != null && !keys.contains("costStructure007")) {
                costStructureColumn.add(getMap("costStructure007", costStructureHeadMap.get("cost_structure_007")));
                keys.add("costStructure007");
            }
            if (stdCostItem.getCostStructure008() != null && !keys.contains("costStructure008")) {
                costStructureColumn.add(getMap("costStructure008", costStructureHeadMap.get("cost_structure_008")));
                keys.add("costStructure008");
            }
            if (stdCostItem.getCostStructure009() != null && !keys.contains("costStructure009")) {
                costStructureColumn.add(getMap("costStructure009", costStructureHeadMap.get("cost_structure_009")));
                keys.add("costStructure009");
            }
            if (stdCostItem.getCostStructure010() != null && !keys.contains("costStructure010")) {
                costStructureColumn.add(getMap("costStructure010", costStructureHeadMap.get("cost_structure_010")));
                keys.add("costStructure010");
            }
            if (stdCostItem.getCostStructure011() != null && !keys.contains("costStructure011")) {
                costStructureColumn.add(getMap("costStructure011", costStructureHeadMap.get("cost_structure_011")));
                keys.add("costStructure011");
            }
            if (stdCostItem.getCostStructure012() != null && !keys.contains("costStructure012")) {
                costStructureColumn.add(getMap("costStructure012", costStructureHeadMap.get("cost_structure_012")));
                keys.add("costStructure012");
            }
            if (stdCostItem.getCostStructure013() != null && !keys.contains("costStructure011")) {
                costStructureColumn.add(getMap("costStructure013", costStructureHeadMap.get("cost_structure_013")));
                keys.add("costStructure013");
            }
            if (stdCostItem.getCostStructure014() != null && !keys.contains("costStructure011")) {
                costStructureColumn.add(getMap("costStructure014", costStructureHeadMap.get("cost_structure_014")));
                keys.add("costStructure014");
            }
            if (stdCostItem.getCostStructure015() != null && !keys.contains("costStructure015")) {
                costStructureColumn.add(getMap("costStructure015", costStructureHeadMap.get("cost_structure_015")));
                keys.add("costStructure015");
            }
            if (stdCostItem.getCostStructure016() != null && !keys.contains("costStructure016")) {
                costStructureColumn.add(getMap("costStructure016", costStructureHeadMap.get("cost_structure_016")));
                keys.add("costStructure016");
            }
            if (stdCostItem.getCostStructure017() != null && !keys.contains("costStructure017")) {
                costStructureColumn.add(getMap("costStructure017", costStructureHeadMap.get("cost_structure_017")));
                keys.add("costStructure017");
            }
            if (stdCostItem.getCostStructure018() != null && !keys.contains("costStructure018")) {
                costStructureColumn.add(getMap("costStructure018", costStructureHeadMap.get("cost_structure_018")));
                keys.add("costStructure018");
            }
            if (stdCostItem.getCostStructure019() != null && !keys.contains("costStructure019")) {
                costStructureColumn.add(getMap("costStructure019", costStructureHeadMap.get("cost_structure_019")));
                keys.add("costStructure019");
            }
            if (stdCostItem.getCostStructure010() != null && !keys.contains("costStructure020")) {
                costStructureColumn.add(getMap("costStructure020", costStructureHeadMap.get("cost_structure_020")));
                keys.add("costStructure020");
            }
            if (stdCostItem.getCostStructure021() != null && !keys.contains("costStructure021")) {
                costStructureColumn.add(getMap("costStructure021", costStructureHeadMap.get("cost_structure_021")));
                keys.add("costStructure021");
            }
            if (stdCostItem.getCostStructure022() != null && !keys.contains("costStructure022")) {
                costStructureColumn.add(getMap("costStructure022", costStructureHeadMap.get("cost_structure_022")));
                keys.add("costStructure022");
            }
            if (stdCostItem.getCostStructure023() != null && !keys.contains("costStructure021")) {
                costStructureColumn.add(getMap("costStructure023", costStructureHeadMap.get("cost_structure_023")));
                keys.add("costStructure023");
            }
            if (stdCostItem.getCostStructure024() != null && !keys.contains("costStructure021")) {
                costStructureColumn.add(getMap("costStructure024", costStructureHeadMap.get("cost_structure_024")));
                keys.add("costStructure024");
            }
            if (stdCostItem.getCostStructure025() != null && !keys.contains("costStructure025")) {
                costStructureColumn.add(getMap("costStructure025", costStructureHeadMap.get("cost_structure_025")));
                keys.add("costStructure025");
            }
            if (stdCostItem.getCostStructure026() != null && !keys.contains("costStructure026")) {
                costStructureColumn.add(getMap("costStructure026", costStructureHeadMap.get("cost_structure_026")));
                keys.add("costStructure026");
            }
            if (stdCostItem.getCostStructure027() != null && !keys.contains("costStructure027")) {
                costStructureColumn.add(getMap("costStructure027", costStructureHeadMap.get("cost_structure_027")));
                keys.add("costStructure027");
            }
            if (stdCostItem.getCostStructure028() != null && !keys.contains("costStructure028")) {
                costStructureColumn.add(getMap("costStructure028", costStructureHeadMap.get("cost_structure_028")));
                keys.add("costStructure028");
            }
            if (stdCostItem.getCostStructure029() != null && !keys.contains("costStructure029")) {
                costStructureColumn.add(getMap("costStructure029", costStructureHeadMap.get("cost_structure_029")));
                keys.add("costStructure029");
            }
            if (stdCostItem.getCostStructure030() != null && !keys.contains("costStructure030")) {
                costStructureColumn.add(getMap("costStructure030", costStructureHeadMap.get("cost_structure_030")));
                keys.add("costStructure030");
            }
            if (stdCostItem.getCostStructure031() != null && !keys.contains("costStructure031")) {
                costStructureColumn.add(getMap("costStructure031", costStructureHeadMap.get("cost_structure_031")));
                keys.add("costStructure031");
            }
            if (stdCostItem.getCostStructure032() != null && !keys.contains("costStructure032")) {
                costStructureColumn.add(getMap("costStructure032", costStructureHeadMap.get("cost_structure_032")));
                keys.add("costStructure032");
            }
            if (stdCostItem.getCostStructure033() != null && !keys.contains("costStructure031")) {
                costStructureColumn.add(getMap("costStructure033", costStructureHeadMap.get("cost_structure_033")));
                keys.add("costStructure033");
            }
            if (stdCostItem.getCostStructure034() != null && !keys.contains("costStructure031")) {
                costStructureColumn.add(getMap("costStructure034", costStructureHeadMap.get("cost_structure_034")));
                keys.add("costStructure034");
            }
            if (stdCostItem.getCostStructure035() != null && !keys.contains("costStructure035")) {
                costStructureColumn.add(getMap("costStructure035", costStructureHeadMap.get("cost_structure_035")));
                keys.add("costStructure035");
            }
            if (stdCostItem.getCostStructure036() != null && !keys.contains("costStructure036")) {
                costStructureColumn.add(getMap("costStructure036", costStructureHeadMap.get("cost_structure_036")));
                keys.add("costStructure036");
            }
            if (stdCostItem.getCostStructure037() != null && !keys.contains("costStructure037")) {
                costStructureColumn.add(getMap("costStructure037", costStructureHeadMap.get("cost_structure_037")));
                keys.add("costStructure037");
            }
            if (stdCostItem.getCostStructure038() != null && !keys.contains("costStructure038")) {
                costStructureColumn.add(getMap("costStructure038", costStructureHeadMap.get("cost_structure_038")));
                keys.add("costStructure038");
            }
            if (stdCostItem.getCostStructure039() != null && !keys.contains("costStructure039")) {
                costStructureColumn.add(getMap("costStructure039", costStructureHeadMap.get("cost_structure_039")));
                keys.add("costStructure039");
            }
            if (stdCostItem.getCostStructure040() != null && !keys.contains("costStructure040")) {
                costStructureColumn.add(getMap("costStructure040", costStructureHeadMap.get("cost_structure_040")));
                keys.add("costStructure040");
            }
            if (stdCostItem.getCostStructure041() != null && !keys.contains("costStructure041")) {
                costStructureColumn.add(getMap("costStructure041", costStructureHeadMap.get("cost_structure_041")));
                keys.add("costStructure041");
            }
            if (stdCostItem.getCostStructure042() != null && !keys.contains("costStructure042")) {
                costStructureColumn.add(getMap("costStructure042", costStructureHeadMap.get("cost_structure_042")));
                keys.add("costStructure042");
            }
            if (stdCostItem.getCostStructure043() != null && !keys.contains("costStructure041")) {
                costStructureColumn.add(getMap("costStructure043", costStructureHeadMap.get("cost_structure_043")));
                keys.add("costStructure043");
            }
            if (stdCostItem.getCostStructure044() != null && !keys.contains("costStructure041")) {
                costStructureColumn.add(getMap("costStructure044", costStructureHeadMap.get("cost_structure_044")));
                keys.add("costStructure044");
            }
            if (stdCostItem.getCostStructure045() != null && !keys.contains("costStructure045")) {
                costStructureColumn.add(getMap("costStructure045", costStructureHeadMap.get("cost_structure_045")));
                keys.add("costStructure045");
            }
            if (stdCostItem.getCostStructure046() != null && !keys.contains("costStructure046")) {
                costStructureColumn.add(getMap("costStructure046", costStructureHeadMap.get("cost_structure_046")));
                keys.add("costStructure046");
            }
            if (stdCostItem.getCostStructure047() != null && !keys.contains("costStructure047")) {
                costStructureColumn.add(getMap("costStructure047", costStructureHeadMap.get("cost_structure_047")));
                keys.add("costStructure047");
            }
            if (stdCostItem.getCostStructure048() != null && !keys.contains("costStructure048")) {
                costStructureColumn.add(getMap("costStructure048", costStructureHeadMap.get("cost_structure_048")));
                keys.add("costStructure048");
            }
            if (stdCostItem.getCostStructure049() != null && !keys.contains("costStructure049")) {
                costStructureColumn.add(getMap("costStructure049", costStructureHeadMap.get("cost_structure_049")));
                keys.add("costStructure049");
            }
            if (stdCostItem.getCostStructure050() != null && !keys.contains("costStructure050")) {
                costStructureColumn.add(getMap("costStructure050", costStructureHeadMap.get("cost_structure_050")));
                keys.add("costStructure050");
            }
            if (stdCostItem.getCostStructure051() != null && !keys.contains("costStructure051")) {
                costStructureColumn.add(getMap("costStructure051", costStructureHeadMap.get("cost_structure_051")));
                keys.add("costStructure051");
            }
            if (stdCostItem.getCostStructure052() != null && !keys.contains("costStructure052")) {
                costStructureColumn.add(getMap("costStructure052", costStructureHeadMap.get("cost_structure_052")));
                keys.add("costStructure052");
            }
            if (stdCostItem.getCostStructure053() != null && !keys.contains("costStructure051")) {
                costStructureColumn.add(getMap("costStructure053", costStructureHeadMap.get("cost_structure_053")));
                keys.add("costStructure053");
            }
            if (stdCostItem.getCostStructure054() != null && !keys.contains("costStructure051")) {
                costStructureColumn.add(getMap("costStructure054", costStructureHeadMap.get("cost_structure_054")));
                keys.add("costStructure054");
            }
            if (stdCostItem.getCostStructure055() != null && !keys.contains("costStructure055")) {
                costStructureColumn.add(getMap("costStructure055", costStructureHeadMap.get("cost_structure_055")));
                keys.add("costStructure055");
            }
            if (stdCostItem.getCostStructure056() != null && !keys.contains("costStructure056")) {
                costStructureColumn.add(getMap("costStructure056", costStructureHeadMap.get("cost_structure_056")));
                keys.add("costStructure056");
            }
            if (stdCostItem.getCostStructure057() != null && !keys.contains("costStructure057")) {
                costStructureColumn.add(getMap("costStructure057", costStructureHeadMap.get("cost_structure_057")));
                keys.add("costStructure057");
            }
            if (stdCostItem.getCostStructure058() != null && !keys.contains("costStructure058")) {
                costStructureColumn.add(getMap("costStructure058", costStructureHeadMap.get("cost_structure_058")));
                keys.add("costStructure058");
            }
            if (stdCostItem.getCostStructure059() != null && !keys.contains("costStructure059")) {
                costStructureColumn.add(getMap("costStructure059", costStructureHeadMap.get("cost_structure_059")));
                keys.add("costStructure059");
            }
            if (stdCostItem.getCostStructure060() != null && !keys.contains("costStructure060")) {
                costStructureColumn.add(getMap("costStructure060", costStructureHeadMap.get("cost_structure_060")));
                keys.add("costStructure060");
            }
        }

        headColumn.addAll(costStructureColumn);
        itemColumn.addAll(costStructureColumn);
        result.put("headValues", PageUtils.getPageInfo(stdCostItemList, stdCostItemList.size()));
        result.put("itemValues", PageUtils.getPageInfo(stdCostItemList, stdCostItemList.size()));
        result.put("headColumn", headColumn);
        result.put("itemColumn", itemColumn);
        return result;
    }

    private Map<String, Object> getMap(String str, Object obj) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(str, obj);
        return map;
    }

    private List<Map<String, Object>> getItemColumn() {
        List<Map<String, Object>> list = getCommonColumn();
        list.add(getMap("effectiveDate", "生效日期"));
        list.add(getMap("technologyId", "工艺编码"));
        list.add(getMap("technologyVer", "工艺版本"));
        list.add(getMap("routeId", "途程编码"));
        list.add(getMap("routeVer", "途程版本"));
        list.add(getMap("layerId", "LAYER"));
        list.add(getMap("stageId", "STAGE"));
        list.add(getMap("stepId", "STEP"));
        list.add(getMap("stepNo", "STEP NO"));
        list.add(getMap("stepType", "STEP TYPE"));
        list.add(getMap("recipeId", "RECIPE"));
        list.add(getMap("batchQty", "运算批量"));
        list.add(getMap("baseUnit", "基准单位"));
        list.add(getMap("baseQty", "基本数量"));
        list.add(getMap("unit1", "单位1"));
        list.add(getMap("qty1", "数量1"));
        list.add(getMap("unit2", "单位2"));
        list.add(getMap("qty2", "数量2"));
        list.add(getMap("yieldRate", "良率"));
        list.add(getMap("weight", "权数"));
        list.add(getMap("stepMoveCost", "计划成本值"));
        list.add(getMap("cumulativeCost", "累计成本"));
        list.add(getMap("workCenterId", "工作单元"));
        list.add(getMap("workCenterDesc", "工作单元描述"));
        list.add(getMap("costCenterId", "成本单元"));
        list.add(getMap("costCenterDesc", "成本单元描述"));
        return list;
    }

    private List<Map<String, Object>> getHeadColumn() {
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(getMap("currency", "运算货币"));
        list.add(getMap("batchQty", "运算批量"));
        list.add(getMap("calculateDate", "运算日期"));
        list.add(getMap("effectiveDate", "生效日期"));
        list.add(getMap("stepMoveCost", "计划成本值"));
        return list;
    }

    private List<Map<String, Object>> getCommonColumn() {
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(getMap("costCode", "核算CODE"));
        list.add(getMap("controlAreaId", "管理范围"));
        list.add(getMap("verId", "数据集合"));
        list.add(getMap("factoryId", "工厂代码"));
        list.add(getMap("productId", "产品编码"));
        list.add(getMap("productVer", "产品版本"));
        list.add(getMap("productDesc", "产品描述"));
        return list;
    }

    @Override
    public Map<String, Object> consumptionRun(String verId, String factoryId, Long yearMonth, String sort, SearchVo searchVo) {
        List<ConsumptionHead> consumptionHeadList = transMapper.selectConsumptionHead(verId, factoryId, yearMonth, sort, searchVo);
        List<ConsumptionItem> consumptionItemList = transMapper.selectConsumptionItem(verId, factoryId, yearMonth, sort, searchVo);
        Map<String, Object> result = Maps.newHashMap();
        result.put("head", PageUtils.getPageInfo(consumptionHeadList, consumptionHeadList.size()));
        result.put("item", PageUtils.getPageInfo(consumptionItemList, consumptionItemList.size()));
        return result;
    }

    @Override
    public void stdCostSave(String verId, String controlAreaId, String factoryId, String effectiveDate,
                            Long batchQty, String productId, Long productVer) {

//        Long projectCode = getProjectCode(remoteProjectService.queryProjectByName(SecurityUtils.getTenantId().toString()));
//        String taskName = "";
//        Map<String, Object> map = Maps.newHashMap();
//        map.put("control_area_id", controlAreaId);
//        map.put("ver_id", verId);
//        map.put("factoryId", factoryId);
//        map.put("product_id", productId);
//        map.put("product_ver", productVer);
//        map.put("batch_qty", batchQty);
//        map.put("effective_date", effectiveDate);
//        if (StringUtils.isEmpty(productId) && productVer == null) {
//            taskName = "std_cost_1";
//        } else if (StringUtils.isNotEmpty(productId) && productVer == null) {
//            taskName = "std_cost_2";
//        } else if (StringUtils.isNotEmpty(productId) && productVer != null) {
//            taskName = "std_cost_3";
//        }
//        Long taskCode = transMapper.selectTaskCodeByName(taskName);
//        if (taskCode == null) {
//            log.info("{} 任务不存在", taskName);
//        } else {
//            String param = "std_cost_param";
//            R<GlobalParam> stdCostParamR = remoteParamService.selectByCode(param);
//            if (stdCostParamR.getCode() != 200) {
//                log.info("请检查全局参数服务是否启动");
//            } else {
//                GlobalParam stdCostParam = stdCostParamR.getData();
//                if (stdCostParam == null) {
//                    log.info("{} 参数不存在", param);
//                } else {
//                    R r = remoteParamService.updateParamValue(stdCostParam.getId(), map);
//                    if (r.getCode() != 200) {
//                        log.info("请检查全局参数服务是否启动");
//                    } else {
//                        R r1 = remoteTaskService.runTask(projectCode, taskCode);
//                        if (r1.getCode() != 200) {
//                            log.info("请检查调度服务是否启动");
//                        }
//                    }
//                }
//            }
//        }
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void consumptionSave(String verId, String factoryId, Long yearMonth) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("ver_id", verId);
        map.put("factory_id", factoryId);
        map.put("year_month", yearMonth);
        Long projectCode = getProjectCode(remoteProjectService.queryProjectByName(SecurityUtils.getTenantId().toString()));
        String taskName = "consumption";
        Long taskCode = transMapper.selectTaskCodeByName(taskName);
        if (taskCode == null) {
            log.info("{} 任务不存在", taskName);
        } else {
            String param = "consumption_param";
            R<GlobalParam> consumptionParamR = remoteParamService.selectByCode(param);
            if (consumptionParamR.getCode() != 200) {
                log.info("请检查全局参数服务是否启动");
            } else {
                GlobalParam consumptionParam = consumptionParamR.getData();
                if (consumptionParam == null) {
                    log.info("{} 参数不存在", param);
                } else {
                    R r = remoteParamService.updateParamValue(consumptionParam.getId(), map);
                    if (r.getCode() != 200) {
                        log.info("请检查全局参数服务是否启动");
                    } else {
                        R r1 = remoteTaskService.runTask(projectCode, taskCode);
                        if (r1.getCode() != 200) {
                            log.info("请检查调度服务是否启动");
                        }
                    }
                }
            }
        }
    }

    @Override
    public Map<String, Object> accountReport(String reportVer, String verId, String groupId, String companyId, String factoryId, Long year, String date1, String date2, BigDecimal thisVer, BigDecimal fcstVer) {
        List<AccountReport100> accountReport100List = transMapper.selectAccountReport100List(reportVer, verId, groupId, companyId, factoryId, year);
        List<AccountReport120> accountReport120List = transMapper.selectAccountReport120List(reportVer, verId, groupId, companyId, factoryId, year);
        List<AccountReport130> accountReport130List = transMapper.selectAccountReport130List(reportVer, verId, groupId, companyId, factoryId, year);
        List<AccountReport200> accountReport200List = transMapper.selectAccountReport200List(reportVer, verId, groupId, companyId, factoryId, thisVer, fcstVer);
        List<AccountReport220> accountReport220List = transMapper.selectAccountReport220List(reportVer, verId, groupId, companyId, factoryId, thisVer, fcstVer);
        List<AccountReport230> accountReport230List = transMapper.selectAccountReport230List(reportVer, verId, groupId, companyId, factoryId, thisVer, fcstVer);
        Map<String, Object> result = Maps.newHashMap();
        List<Map<String, Object>> dynamicColumn = new ArrayList<>();
        dynamicColumn.add(this.getMap("1月份", "value001"));
        dynamicColumn.add(this.getMap("2月份", "value002"));
        dynamicColumn.add(this.getMap("3月份", "value003"));
        dynamicColumn.add(this.getMap("4月份", "value004"));
        dynamicColumn.add(this.getMap("5月份", "value005"));
        dynamicColumn.add(this.getMap("6月份", "value006"));
        dynamicColumn.add(this.getMap("7月份", "value007"));
        dynamicColumn.add(this.getMap("8月份", "value008"));
        dynamicColumn.add(this.getMap("9月份", "value009"));
        dynamicColumn.add(this.getMap("10月份", "value010"));
        dynamicColumn.add(this.getMap("11月份", "value011"));
        dynamicColumn.add(this.getMap("12月份", "value012"));
        dynamicColumn.add(this.getMap("13月份", "value013"));
        dynamicColumn.add(this.getMap("14月份", "value014"));
        dynamicColumn.add(this.getMap("15月份", "value015"));
        dynamicColumn.add(this.getMap("16月份", "value016"));

        result.put("dynamicColumn", dynamicColumn);
        result.put("AccountReport100", PageUtils.getPageInfo(accountReport100List, accountReport100List.size()));
        result.put("AccountReport120", PageUtils.getPageInfo(accountReport120List, accountReport120List.size()));
        result.put("AccountReport130", PageUtils.getPageInfo(accountReport130List, accountReport130List.size()));
        result.put("AccountReport200", PageUtils.getPageInfo(accountReport200List, accountReport200List.size()));
        result.put("AccountReport220", PageUtils.getPageInfo(accountReport220List, accountReport220List.size()));
        result.put("AccountReport230", PageUtils.getPageInfo(accountReport230List, accountReport230List.size()));
        return result;
    }

    @Override
    public Map<String, Object> stdCostOut(String verId, String controlAreaId,
                                          String factoryId, String startTime,
                                          String endTime, String productId,
                                          Long productVer, String type, SearchVo searchVo) {
        R<List<CostStructureHead>> listR = remoteCostStructureHeadService.queryAll();
        if (listR.getCode() != 200) {
            throw new ServiceException("获取成本组件失败" + listR.getMsg());
        }
        List<CostStructureHead> costStructureHeadList = listR.getData();
        Map<String, String> costStructureHeadMap = costStructureHeadList.stream().collect(Collectors.toMap(CostStructureHead::getCostStructureId, CostStructureHead::getCostStructureDesc));
        List<StdCostItem> stdCostItemList = transMapper.selectStdCostItemOut(verId, controlAreaId, factoryId, startTime, endTime, productId, productVer, searchVo);
        Map<String, Object> result = Maps.newHashMap();
        List<Map<String, Object>> headColumn = this.getCommonColumn();
        if ("ProductID".equals(type)) {
            headColumn.add(getMap("technologyId", "工艺编码"));
            headColumn.add(getMap("technologyVer", "工艺版本"));
        } else if ("Layer".equals(type)) {
            headColumn.add(getMap("layerId", "LAYER"));
        } else if ("Capacity".equals(type)) {
            headColumn.add(getMap("costCenterId", "成本单元"));
        }
        headColumn.addAll(this.getHeadColumn());
        List<Map<String, Object>> itemColumn = this.getItemColumn();

        List<String> keys = new ArrayList<>();
        List<Map<String, Object>> costStructureColumn = new ArrayList<>();
        for (StdCostItem stdCostItem : stdCostItemList) {
            stdCostItem.setCostCode(stdCostItem.getControlAreaId()+stdCostItem.getVerId()
                    +stdCostItem.getFactoryId()+stdCostItem.getProductId()+stdCostItem.getProductVer()+ DateFormatUtils.format(stdCostItem.getEffectiveDate(), "yyyyMMdd"));
            if (stdCostItem.getCostStructure001() != null && !keys.contains("costStructure001")) {
                costStructureColumn.add(getMap("costStructure001", costStructureHeadMap.get("cost_structure_001")));
                keys.add("costStructure001");
            }
            if (stdCostItem.getCostStructure002() != null && !keys.contains("costStructure002")) {
                costStructureColumn.add(getMap("costStructure002", costStructureHeadMap.get("cost_structure_002")));
                keys.add("costStructure002");
            }
            if (stdCostItem.getCostStructure003() != null && !keys.contains("costStructure001")) {
                costStructureColumn.add(getMap("costStructure003", costStructureHeadMap.get("cost_structure_003")));
                keys.add("costStructure003");
            }
            if (stdCostItem.getCostStructure004() != null && !keys.contains("costStructure001")) {
                costStructureColumn.add(getMap("costStructure004", costStructureHeadMap.get("cost_structure_004")));
                keys.add("costStructure004");
            }
            if (stdCostItem.getCostStructure005() != null && !keys.contains("costStructure005")) {
                costStructureColumn.add(getMap("costStructure005", costStructureHeadMap.get("cost_structure_005")));
                keys.add("costStructure005");
            }
            if (stdCostItem.getCostStructure006() != null && !keys.contains("costStructure006")) {
                costStructureColumn.add(getMap("costStructure006", costStructureHeadMap.get("cost_structure_006")));
                keys.add("costStructure006");
            }
            if (stdCostItem.getCostStructure007() != null && !keys.contains("costStructure007")) {
                costStructureColumn.add(getMap("costStructure007", costStructureHeadMap.get("cost_structure_007")));
                keys.add("costStructure007");
            }
            if (stdCostItem.getCostStructure008() != null && !keys.contains("costStructure008")) {
                costStructureColumn.add(getMap("costStructure008", costStructureHeadMap.get("cost_structure_008")));
                keys.add("costStructure008");
            }
            if (stdCostItem.getCostStructure009() != null && !keys.contains("costStructure009")) {
                costStructureColumn.add(getMap("costStructure009", costStructureHeadMap.get("cost_structure_009")));
                keys.add("costStructure009");
            }
            if (stdCostItem.getCostStructure010() != null && !keys.contains("costStructure010")) {
                costStructureColumn.add(getMap("costStructure010", costStructureHeadMap.get("cost_structure_010")));
                keys.add("costStructure010");
            }
            if (stdCostItem.getCostStructure011() != null && !keys.contains("costStructure011")) {
                costStructureColumn.add(getMap("costStructure011", costStructureHeadMap.get("cost_structure_011")));
                keys.add("costStructure011");
            }
            if (stdCostItem.getCostStructure012() != null && !keys.contains("costStructure012")) {
                costStructureColumn.add(getMap("costStructure012", costStructureHeadMap.get("cost_structure_012")));
                keys.add("costStructure012");
            }
            if (stdCostItem.getCostStructure013() != null && !keys.contains("costStructure011")) {
                costStructureColumn.add(getMap("costStructure013", costStructureHeadMap.get("cost_structure_013")));
                keys.add("costStructure013");
            }
            if (stdCostItem.getCostStructure014() != null && !keys.contains("costStructure011")) {
                costStructureColumn.add(getMap("costStructure014", costStructureHeadMap.get("cost_structure_014")));
                keys.add("costStructure014");
            }
            if (stdCostItem.getCostStructure015() != null && !keys.contains("costStructure015")) {
                costStructureColumn.add(getMap("costStructure015", costStructureHeadMap.get("cost_structure_015")));
                keys.add("costStructure015");
            }
            if (stdCostItem.getCostStructure016() != null && !keys.contains("costStructure016")) {
                costStructureColumn.add(getMap("costStructure016", costStructureHeadMap.get("cost_structure_016")));
                keys.add("costStructure016");
            }
            if (stdCostItem.getCostStructure017() != null && !keys.contains("costStructure017")) {
                costStructureColumn.add(getMap("costStructure017", costStructureHeadMap.get("cost_structure_017")));
                keys.add("costStructure017");
            }
            if (stdCostItem.getCostStructure018() != null && !keys.contains("costStructure018")) {
                costStructureColumn.add(getMap("costStructure018", costStructureHeadMap.get("cost_structure_018")));
                keys.add("costStructure018");
            }
            if (stdCostItem.getCostStructure019() != null && !keys.contains("costStructure019")) {
                costStructureColumn.add(getMap("costStructure019", costStructureHeadMap.get("cost_structure_019")));
                keys.add("costStructure019");
            }
            if (stdCostItem.getCostStructure010() != null && !keys.contains("costStructure020")) {
                costStructureColumn.add(getMap("costStructure020", costStructureHeadMap.get("cost_structure_020")));
                keys.add("costStructure020");
            }
            if (stdCostItem.getCostStructure021() != null && !keys.contains("costStructure021")) {
                costStructureColumn.add(getMap("costStructure021", costStructureHeadMap.get("cost_structure_021")));
                keys.add("costStructure021");
            }
            if (stdCostItem.getCostStructure022() != null && !keys.contains("costStructure022")) {
                costStructureColumn.add(getMap("costStructure022", costStructureHeadMap.get("cost_structure_022")));
                keys.add("costStructure022");
            }
            if (stdCostItem.getCostStructure023() != null && !keys.contains("costStructure021")) {
                costStructureColumn.add(getMap("costStructure023", costStructureHeadMap.get("cost_structure_023")));
                keys.add("costStructure023");
            }
            if (stdCostItem.getCostStructure024() != null && !keys.contains("costStructure021")) {
                costStructureColumn.add(getMap("costStructure024", costStructureHeadMap.get("cost_structure_024")));
                keys.add("costStructure024");
            }
            if (stdCostItem.getCostStructure025() != null && !keys.contains("costStructure025")) {
                costStructureColumn.add(getMap("costStructure025", costStructureHeadMap.get("cost_structure_025")));
                keys.add("costStructure025");
            }
            if (stdCostItem.getCostStructure026() != null && !keys.contains("costStructure026")) {
                costStructureColumn.add(getMap("costStructure026", costStructureHeadMap.get("cost_structure_026")));
                keys.add("costStructure026");
            }
            if (stdCostItem.getCostStructure027() != null && !keys.contains("costStructure027")) {
                costStructureColumn.add(getMap("costStructure027", costStructureHeadMap.get("cost_structure_027")));
                keys.add("costStructure027");
            }
            if (stdCostItem.getCostStructure028() != null && !keys.contains("costStructure028")) {
                costStructureColumn.add(getMap("costStructure028", costStructureHeadMap.get("cost_structure_028")));
                keys.add("costStructure028");
            }
            if (stdCostItem.getCostStructure029() != null && !keys.contains("costStructure029")) {
                costStructureColumn.add(getMap("costStructure029", costStructureHeadMap.get("cost_structure_029")));
                keys.add("costStructure029");
            }
            if (stdCostItem.getCostStructure030() != null && !keys.contains("costStructure030")) {
                costStructureColumn.add(getMap("costStructure030", costStructureHeadMap.get("cost_structure_030")));
                keys.add("costStructure030");
            }
            if (stdCostItem.getCostStructure031() != null && !keys.contains("costStructure031")) {
                costStructureColumn.add(getMap("costStructure031", costStructureHeadMap.get("cost_structure_031")));
                keys.add("costStructure031");
            }
            if (stdCostItem.getCostStructure032() != null && !keys.contains("costStructure032")) {
                costStructureColumn.add(getMap("costStructure032", costStructureHeadMap.get("cost_structure_032")));
                keys.add("costStructure032");
            }
            if (stdCostItem.getCostStructure033() != null && !keys.contains("costStructure031")) {
                costStructureColumn.add(getMap("costStructure033", costStructureHeadMap.get("cost_structure_033")));
                keys.add("costStructure033");
            }
            if (stdCostItem.getCostStructure034() != null && !keys.contains("costStructure031")) {
                costStructureColumn.add(getMap("costStructure034", costStructureHeadMap.get("cost_structure_034")));
                keys.add("costStructure034");
            }
            if (stdCostItem.getCostStructure035() != null && !keys.contains("costStructure035")) {
                costStructureColumn.add(getMap("costStructure035", costStructureHeadMap.get("cost_structure_035")));
                keys.add("costStructure035");
            }
            if (stdCostItem.getCostStructure036() != null && !keys.contains("costStructure036")) {
                costStructureColumn.add(getMap("costStructure036", costStructureHeadMap.get("cost_structure_036")));
                keys.add("costStructure036");
            }
            if (stdCostItem.getCostStructure037() != null && !keys.contains("costStructure037")) {
                costStructureColumn.add(getMap("costStructure037", costStructureHeadMap.get("cost_structure_037")));
                keys.add("costStructure037");
            }
            if (stdCostItem.getCostStructure038() != null && !keys.contains("costStructure038")) {
                costStructureColumn.add(getMap("costStructure038", costStructureHeadMap.get("cost_structure_038")));
                keys.add("costStructure038");
            }
            if (stdCostItem.getCostStructure039() != null && !keys.contains("costStructure039")) {
                costStructureColumn.add(getMap("costStructure039", costStructureHeadMap.get("cost_structure_039")));
                keys.add("costStructure039");
            }
            if (stdCostItem.getCostStructure040() != null && !keys.contains("costStructure040")) {
                costStructureColumn.add(getMap("costStructure040", costStructureHeadMap.get("cost_structure_040")));
                keys.add("costStructure040");
            }
            if (stdCostItem.getCostStructure041() != null && !keys.contains("costStructure041")) {
                costStructureColumn.add(getMap("costStructure041", costStructureHeadMap.get("cost_structure_041")));
                keys.add("costStructure041");
            }
            if (stdCostItem.getCostStructure042() != null && !keys.contains("costStructure042")) {
                costStructureColumn.add(getMap("costStructure042", costStructureHeadMap.get("cost_structure_042")));
                keys.add("costStructure042");
            }
            if (stdCostItem.getCostStructure043() != null && !keys.contains("costStructure041")) {
                costStructureColumn.add(getMap("costStructure043", costStructureHeadMap.get("cost_structure_043")));
                keys.add("costStructure043");
            }
            if (stdCostItem.getCostStructure044() != null && !keys.contains("costStructure041")) {
                costStructureColumn.add(getMap("costStructure044", costStructureHeadMap.get("cost_structure_044")));
                keys.add("costStructure044");
            }
            if (stdCostItem.getCostStructure045() != null && !keys.contains("costStructure045")) {
                costStructureColumn.add(getMap("costStructure045", costStructureHeadMap.get("cost_structure_045")));
                keys.add("costStructure045");
            }
            if (stdCostItem.getCostStructure046() != null && !keys.contains("costStructure046")) {
                costStructureColumn.add(getMap("costStructure046", costStructureHeadMap.get("cost_structure_046")));
                keys.add("costStructure046");
            }
            if (stdCostItem.getCostStructure047() != null && !keys.contains("costStructure047")) {
                costStructureColumn.add(getMap("costStructure047", costStructureHeadMap.get("cost_structure_047")));
                keys.add("costStructure047");
            }
            if (stdCostItem.getCostStructure048() != null && !keys.contains("costStructure048")) {
                costStructureColumn.add(getMap("costStructure048", costStructureHeadMap.get("cost_structure_048")));
                keys.add("costStructure048");
            }
            if (stdCostItem.getCostStructure049() != null && !keys.contains("costStructure049")) {
                costStructureColumn.add(getMap("costStructure049", costStructureHeadMap.get("cost_structure_049")));
                keys.add("costStructure049");
            }
            if (stdCostItem.getCostStructure050() != null && !keys.contains("costStructure050")) {
                costStructureColumn.add(getMap("costStructure050", costStructureHeadMap.get("cost_structure_050")));
                keys.add("costStructure050");
            }
            if (stdCostItem.getCostStructure051() != null && !keys.contains("costStructure051")) {
                costStructureColumn.add(getMap("costStructure051", costStructureHeadMap.get("cost_structure_051")));
                keys.add("costStructure051");
            }
            if (stdCostItem.getCostStructure052() != null && !keys.contains("costStructure052")) {
                costStructureColumn.add(getMap("costStructure052", costStructureHeadMap.get("cost_structure_052")));
                keys.add("costStructure052");
            }
            if (stdCostItem.getCostStructure053() != null && !keys.contains("costStructure051")) {
                costStructureColumn.add(getMap("costStructure053", costStructureHeadMap.get("cost_structure_053")));
                keys.add("costStructure053");
            }
            if (stdCostItem.getCostStructure054() != null && !keys.contains("costStructure051")) {
                costStructureColumn.add(getMap("costStructure054", costStructureHeadMap.get("cost_structure_054")));
                keys.add("costStructure054");
            }
            if (stdCostItem.getCostStructure055() != null && !keys.contains("costStructure055")) {
                costStructureColumn.add(getMap("costStructure055", costStructureHeadMap.get("cost_structure_055")));
                keys.add("costStructure055");
            }
            if (stdCostItem.getCostStructure056() != null && !keys.contains("costStructure056")) {
                costStructureColumn.add(getMap("costStructure056", costStructureHeadMap.get("cost_structure_056")));
                keys.add("costStructure056");
            }
            if (stdCostItem.getCostStructure057() != null && !keys.contains("costStructure057")) {
                costStructureColumn.add(getMap("costStructure057", costStructureHeadMap.get("cost_structure_057")));
                keys.add("costStructure057");
            }
            if (stdCostItem.getCostStructure058() != null && !keys.contains("costStructure058")) {
                costStructureColumn.add(getMap("costStructure058", costStructureHeadMap.get("cost_structure_058")));
                keys.add("costStructure058");
            }
            if (stdCostItem.getCostStructure059() != null && !keys.contains("costStructure059")) {
                costStructureColumn.add(getMap("costStructure059", costStructureHeadMap.get("cost_structure_059")));
                keys.add("costStructure059");
            }
            if (stdCostItem.getCostStructure060() != null && !keys.contains("costStructure060")) {
                costStructureColumn.add(getMap("costStructure060", costStructureHeadMap.get("cost_structure_060")));
                keys.add("costStructure060");
            }
        }

        headColumn.addAll(costStructureColumn);
        itemColumn.addAll(costStructureColumn);
        result.put("headValues", PageUtils.getPageInfo(stdCostItemList, stdCostItemList.size()));
        result.put("itemValues", PageUtils.getPageInfo(stdCostItemList, stdCostItemList.size()));
        result.put("headColumn", headColumn);
        result.put("itemColumn", itemColumn);
        return result;
    }

    private Long getProjectCode(R data) {
        JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(data.getData()));
        return jsonObject.getLong("code");
    }

}

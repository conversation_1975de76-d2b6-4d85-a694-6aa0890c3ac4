package com.datalink.fdop.seatunnel.api.domain;

import lombok.Data;

@Data
public class SeaTunnelRunConfig {

    /**
     * ST env
     */
    private SeaTunnelEnv env;


    private CdcDataModelOptions cdcOptions;

    /**
     * ST source
     */
    private DataModelOptions sourceOptions;

    /**
     * ST sink
     */
    private DataModelOptions sinkOptions;

    /**
     * ST File Sink
     */
    private FileModelOptions sinkFileOptions;

    /**
     * SeaTunnel运行配置
     */
    private String submitConfig;

    public SeaTunnelRunConfig() {
    }

    public SeaTunnelRunConfig(SeaTunnelEnv env, DataModelOptions sourceOptions, DataModelOptions sinkOptions) {
        this.env = env;
        this.sourceOptions = sourceOptions;
        this.sinkOptions = sinkOptions;
    }

    public SeaTunnelRunConfig(SeaTunnelEnv env, DataModelOptions sourceOptions, FileModelOptions sinkFileOptions) {
        this.env = env;
        this.sourceOptions = sourceOptions;
        this.sinkFileOptions = sinkFileOptions;
    }

    public SeaTunnelRunConfig(SeaTunnelEnv env, CdcDataModelOptions cdcOptions, DataModelOptions sourceOptions, DataModelOptions sinkOptions, FileModelOptions sinkFileOptions, String submitConfig) {
        this.env = env;
        this.cdcOptions = cdcOptions;
        this.sourceOptions = sourceOptions;
        this.sinkOptions = sinkOptions;
        this.sinkFileOptions = sinkFileOptions;
        this.submitConfig = submitConfig;
    }

}

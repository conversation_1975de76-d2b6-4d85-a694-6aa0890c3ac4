/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.datalink.fdop.alert.plugin.email.template;

import com.datalink.fdop.alert.api.enmus.ShowType;

public interface AlertTemplate {

    /**
     * get a message from a specified alert template
     *
     * @param content alert message content
     * @param showType show type
     * @param showAll whether to show all
     * @return a message from a specified alert template
     */
    String getMessageFromTemplate(String content, ShowType showType, boolean showAll);

    /**
     * default showAll is true
     *
     * @param content alert message content
     * @param showType show type
     * @return a message from a specified alert template
     */
    default String getMessageFromTemplate(String content, ShowType showType) {
        return getMessageFromTemplate(content, showType, true);
    }
}

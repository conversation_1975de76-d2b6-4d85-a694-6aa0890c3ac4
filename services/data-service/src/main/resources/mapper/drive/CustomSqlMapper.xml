<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.drive.mapper.CustomSqlMapper">

    <select id="selectList1" resultType="java.util.LinkedHashMap" resultSetType="FORWARD_ONLY" fetchSize="-2147483648">
        ${sql}
    </select>

    <select id="selectList2" resultType="java.util.LinkedHashMap">
        ${sql}
    </select>

</mapper>


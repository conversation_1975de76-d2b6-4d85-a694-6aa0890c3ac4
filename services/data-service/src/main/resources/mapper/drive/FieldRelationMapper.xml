<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.drive.mapper.FieldRelationMapper">

    <select id="queryTableIsExists" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (table:d_c_${dbType}_field) return table
        $$) as (m ag_catalog.agtype);
    </select>

    <select id="toBase" resultType="com.datalink.fdop.drive.api.domain.FieldRelation">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (source:d_c_${dbType}_field) -[baseEdge: d_c_base_${dbType}_field_relation]->(base:d_c_base_field)
            RETURN source.code as sourceFieldCode, base.code as baseFieldCode, source.lengthMultiple,
                               source.decimalLengthMultiple
                                   $$) as (sourceFieldCode TEXT,baseFieldCode TEXT, lengthMultiple BIGINT, decimalLengthMultiple BIGINT)
    </select>

    <select id="toSource" resultType="com.datalink.fdop.drive.api.domain.FieldRelation">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (base:d_c_base_field) -[baseEdge: d_c_base_${dbType}_field_relation]->(source :d_c_${dbType}_field)-[flinkEdge: d_c_flink_${dbType}_field_relation]->(flink:d_c_flink_field)
            RETURN base.code as baseFieldCode, source.code as sourceFieldCode, flink.code as flinkFieldCode,
                               source.lengthMultiple,
                               source.decimalLengthMultiple,
                               baseEdge.defaultType
                                   $$) as (baseFieldCode TEXT,sourceFieldCode TEXT,flinkFieldCode TEXT, lengthMultiple BIGINT, decimalLengthMultiple BIGINT, defaultType BOOLEAN)
    </select>

    <select id="base2Flink" resultType="com.datalink.fdop.drive.api.domain.FieldRelation">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (base:d_c_base_field) -[]->(flink:d_c_flink_field)
            RETURN base.code as baseFieldCode, flink.code as flinkFieldCode
        $$) as (baseFieldCode TEXT,flinkFieldCode TEXT)
    </select>

    <select id="flink2Base" resultType="com.datalink.fdop.drive.api.domain.FieldRelation">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (flink:d_c_flink_field) -[]->(base:d_c_base_field)
            RETURN base.code as baseFieldCode, flink.code as flinkFieldCode
        $$) as (baseFieldCode TEXT,flinkFieldCode TEXT)
    </select>

</mapper>
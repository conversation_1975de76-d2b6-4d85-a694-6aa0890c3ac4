package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpAllocationCostA;

import java.util.List;

public interface DwhTerpAllocationCostAService extends IService<DwhTerpAllocationCostA> {

    PageDataInfo<DwhTerpAllocationCostA> overview(String verId, String controlAreaId,
                                                  String yearMonthFrom, String yearMonthTo, String allocationType,
                                                  String sort, SearchVo searchVo);

    List<DwhTerpAllocationCostA> selectNoPage(String verId, String controlAreaId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo);
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.project.mapper.OrderHeadMapper">

        <select id="queryData" resultType="com.datalink.fdop.project.api.model.vo.OrderQueryShowVo">
            SELECT
            t3.order_num,
            t3.statu,
            t3.order_type,
            t3.order_type_desc,
            t3.del_flag,
            t3.update_date,
            t3.update_time,
            t3.create_by,
            t3.supplier_code,
            t9.supplier_name,
            t3.purchase_code,
            t7.purchase_description,
            t3.company_code,
            t4.company_description,
            t3.order_row_num,
            t3.row_del_flag,
            t3.factory,
            t3.stock_p_code,
            t8.stock_p_description,
            t6.material_code,
            t6.material_desc,
            t3.order_quantity_receive,
            t3.order_unit,
            t3.basic_unit,
            t3.order_unit_num,
            t3.basic_unit_num,
            t3.delivery_control,
            t3.excessive_tolerance,
            t3.deficiency_tolerance,
            t3.project_type,
            t3.project_type_desc,
            t3.delivery_date,
            t3.batch_number,
            t3.return_flag,
            t3.comment,
            t3.bom_versions,
            t3.status
            FROM
            (
            SELECT
            t1.order_num,
            t2.material_code,
            t1.order_type_desc,
            t1.company_code,
            t2.factory,
            t1.purchase_code,
            t1.del_flag,
            t1.order_type,
            t1.update_date,
            t1.update_time,
            t1.create_by,
            t1.supplier_code,
            t2.order_row_num,
            t2.del_flag row_del_flag,
            t2.stock_p_code,
            t2.order_quantity_receive,
            t2.order_unit,
            t2.basic_unit,
            t2.order_unit_num,
            t2.basic_unit_num,
            t2.delivery_control,
            t2.excessive_tolerance,
            t2.deficiency_tolerance,
            t2.project_type,
            t2.project_type_desc,
            t2.delivery_date,
            t2.batch_number,
            t2.return_flag ,
            t1.statu,
            t1.comment,
            t2.bom_versions,
            t2.status
            FROM
            zjdata.p_d_order_row t2
            LEFT JOIN zjdata.p_d_order_head t1 ON t1.order_num = t2.order_num
            ) t3
            LEFT JOIN zjdata.org_company t4 ON t3.company_code = t4.company_code
            LEFT JOIN zjdata.org_factory t5 ON t3.factory = t5.factory_code
            LEFT JOIN zjdata.p_d_material t6 ON t3.material_code = t6.material_code
            LEFT JOIN zjdata.org_purchase t7 ON t3.purchase_code = t7.purchase_code
            LEFT JOIN zjdata.org_stock_place t8 ON t3.stock_p_code = t8.stock_p_code
            LEFT JOIN zjdata.p_d_supplier t9 ON t3.supplier_code = t9.supplier_code
            WHERE
            1 =1
                    <if test="object.orderNum !=null and object.orderNum.size() !=0" >
                        AND t3.order_num IN
                        <foreach collection="object.orderNum" item="orderNum" open="(" close=")" separator="," >
                            #{orderNum}
                        </foreach>
                    </if>
                    <if test="object.statu!=null and object.statu.size() !=0" >
                        AND t3.statu IN
                        <foreach collection="object.statu" item="statu" open="(" close=")" separator="," >
                            #{statu}
                        </foreach>
                    </if>
                    <if test="object.orderType!=null and object.orderType.size() !=0" >
                        AND t3.order_type IN
                        <foreach collection="object.orderType" item="orderType" open="(" close=")" separator="," >
                            #{orderType}
                        </foreach>
                    </if>
                    <if test="object.createBy!=null and object.createBy.size() !=0" >
                        AND t3.create_by IN
                        <foreach collection="object.createBy" item="createBy" open="(" close=")" separator="," >
                            #{createBy}
                        </foreach>
                    </if>
                    <if test="object.purchaseCode!=null and object.purchaseCode.size() !=0" >
                        AND t3.purchase_code IN
                        <foreach collection="object.purchaseCode" item="purchaseCode" open="(" close=")" separator="," >
                            #{purchaseCode}
                        </foreach>
                    </if>
                    <if test="object.companyCode!=null and object.companyCode.size() !=0" >
                        AND t4.company_code IN
                        <foreach collection="object.companyCode" item="companyCode" open="(" close=")" separator="," >
                            #{companyCode}
                        </foreach>
                    </if>
                    <if test="object.materialCode!=null and object.materialCode.size() !=0" >
                        AND t3.material_code IN
                        <foreach collection="object.materialCode" item="materialCode" open="(" close=")" separator="," >
                            #{materialCode}
                        </foreach>
                    </if>
                    <if test="object.supplierCode!=null and object.supplierCode.size() !=0" >
                        AND t9.supplier_code LIKE concat('%', #{object.supplierCode[0]}, '%')
                    </if>
                    <if test="object.supplierName!=null and object.supplierName.size() !=0" >
                        AND t9.supplier_name LIKE concat('%', #{object.supplierName[0]}, '%')
                    </if>
                    <if test="object.materialDesc!=null and object.materialDesc.size() !=0" >
                        AND t6.material_desc LIKE concat('%', #{object.materialDesc[0]}, '%')
                    </if>
                    <if test="delFlag!=null and delFlag!=''">
                        t3.del_flag=#{delFlag}
                    </if>
                order by t3.order_num
        </select>

</mapper>


package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwhFinalFlow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DwhFinalFlowMapper extends BaseMapper<DwhFinalFlow> {

    IPage<DwhFinalFlow> selectAll(
            @Param("verId") String verId, @Param("plantId") String  plantId, @Param("productId") String productId,
            @Param("page") IPage<DwhFinalFlow> page, @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwhFinalFlow> selectNoPage(@Param("verId") String verId,
                                    @Param("plantId") String plantId,
                                    @Param("productId") String productId,
                                    @Param("sort") String sort,
                                    @Param("searchVo") SearchVo searchVo);
}

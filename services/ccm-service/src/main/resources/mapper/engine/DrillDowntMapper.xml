<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DrillDowntMapper">

    <select id="queryHead1" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport100">
        select a.* from fccm.drill_downt_report_100 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}' and year_month = ${yearMonth}
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        order by year_month ${sort}
    </select>

    <select id="queryItem1" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport200">
        select a.* from fccm.drill_downt_report_200 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}'
          and year_month = ${yearMonth} and cost_level = ${level}
    </select>

    <select id="queryHead2" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport110">
        select a.* from fccm.drill_downt_report_110 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}' and year_month = ${yearMonth}
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        order by year_month ${sort}
    </select>

    <select id="queryItem2" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport210">
        select a.* from fccm.drill_downt_report_210 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}'
          and year_month = ${yearMonth} and cost_level = ${level}
    </select>

    <select id="queryHead3" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport120">
        select a.* from fccm.drill_downt_report_120 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}' and year_month = ${yearMonth}
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        order by year_month ${sort}
    </select>

    <select id="queryItem3" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport220">
        select a.* from fccm.drill_downt_report_220 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}'
          and year_month = ${yearMonth} and cost_level = ${level}
    </select>

    <select id="queryHead4" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport130">
        select a.* from fccm.drill_downt_report_130 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}' and year_month = ${yearMonth}
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        order by year_month ${sort}
    </select>

    <select id="queryItem4" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport230">
        select a.* from fccm.drill_downt_report_230 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}'
          and year_month = ${yearMonth} and cost_level = ${level}
    </select>

    <select id="queryHead5" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport140">
        select a.* from fccm.drill_downt_report_100 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}' and year_month = ${yearMonth}
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        order by year_month ${sort}
    </select>

    <select id="queryItem5" resultType="com.datalink.fdop.engine.api.domain.DrillDowntReport240">
        select a.* from fccm.drill_downt_report_200 a
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}'
          and year_month = ${yearMonth} and cost_level = ${level}
    </select>


</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.datalink-info</groupId>
        <artifactId>datasource-plugin</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>datasource-hana</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.sap.cloud.db.jdbc</groupId>
            <artifactId>ngdbc</artifactId>
            <version>2.16.14</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!--把当前项目所有的依赖打包到target目录下的lib文件夹下-->
            <plugin>
                <groupId>com.gitee.starblues</groupId>
                <artifactId>spring-brick-maven-packager</artifactId>
                <configuration>
                    <mode>${plugin.runMode}</mode>
                    <pluginInfo>
                        <id>fdop-datasource-hana</id>
                        <bootstrapClass>com.datalink.fdop.drive.plugin.datasource.hana.HanaApplication
                        </bootstrapClass>
                        <version>2.0.0</version>
                        <provider>ly</provider>
                        <description>hana插件</description>
                        <configFileName>application.yml</configFileName>
                    </pluginInfo>
                    <prodConfig>
                        <packageType>jar</packageType>
                        <outputDirectory>${project.basedir}/../../plugins/drive-center/2.0.0</outputDirectory>
                    </prodConfig>
                    <loadMainResourcePattern>
                        <includes>
                            <include>org.springframework.jdbc.core.JdbcTemplate</include>
                            <include>org.springframework.jdbc.core.**</include>
                        </includes>
                    </loadMainResourcePattern>
                    <loadToMain>
                        <dependencies>
                            <dependency>
                                <groupId>com.sap.cloud.db.jdbc</groupId>
                                <artifactId>ngdbc</artifactId>
                            </dependency>
                        </dependencies>
                    </loadToMain>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>
package com.datalink.fdop.seatunnel.api.restapi.domain.iceberg;

import com.alibaba.fastjson.annotation.JSONField;
import com.datalink.fdop.seatunnel.api.restapi.domain.SinkConfig;
import lombok.Data;

import java.util.Map;

/**
 * Iceberg目标配置信息
 */
@Data
public class IcebergSinkConfig extends CommonIcebergConfig implements SinkConfig {

    /**
     * 在SeaTunnel中定义一个伪表供下游transform sink 引用
     */
    @JSONField(name = "result_table_name")
    private String resultTableName;

    /**
     * Iceberg表写入配置
     */
    @JSONField(name = "iceberg.table.write-props")
    private Map<String, String> tableWriteProps;

    /**
     * Iceberg表开启Upsert模式
     */
    @JSONField(name = "iceberg.table.upsert-mode-enabled")
    private Boolean tableUpsertEnabled;

    /**
     * Iceberg表主键
     */
    @JSONField(name = "iceberg.table.primary-keys")
    private String tablePrimaryKeys;

    /**
     * 区分大小写
     */
    @JSONField(name = "case_sensitive")
    private Boolean caseSensitive = true;

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.EvaluateRevalueMapper">

    <delete id="deleteByKey">
        delete from zjdata.ccm_evaluate_revalue
        WHERE (orig_evaluate_sort_id, material_group, evaluate_split) IN
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.origEvaluateSortId}, #{item.materialGroup}, #{item.evaluateSplit})
        </foreach>
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_evaluate_revalue
        WHERE orig_evaluate_sort_id = #{origEvaluateSortId}
        AND material_group = #{materialGroup} AND evaluate_split = #{evaluateSplit}
        limit 1
    </select>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.EvaluateRevalue">
        UPDATE zjdata.ccm_evaluate_revalue
        SET
        <if test="evaluateSortId != null">evaluate_sort_id = #{evaluateSortId},</if>
        <if test="evaluateSortDesc != null">evaluate_sort_desc = #{evaluateSortDesc},</if>
        <if test="evaluateSortText != null">evaluate_sort_text = #{evaluateSortText},</if>
        <if test="enable != null">enable = #{enable},</if>
        <if test="evaluateSplit != null">evaluate_split = #{evaluateSplit},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE evaluate_area_id = #{evaluateAreaId} AND orig_evaluate_sort_id = #{origEvaluateSortId} AND evaluate_split = #{evaluateSplit}
        AND material_group = #{materialGroup}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.EvaluateRevalue">
        SELECT 
            *
        FROM zjdata.ccm_evaluate_revalue node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY evaluate_area_id, orig_evaluate_sort_id, material_group ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_evaluate_revalue
        SET enable = #{enable}
        WHERE (orig_evaluate_sort_id, material_group, evaluate_split) IN
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.origEvaluateSortId}, #{item.materialGroup}, #{item.evaluateSplit})
        </foreach>
    </update>
</mapper>
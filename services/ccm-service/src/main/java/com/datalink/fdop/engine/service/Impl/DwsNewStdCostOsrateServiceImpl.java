package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostOsrate;
import com.datalink.fdop.engine.mapper.DwsNewStdCostOsrateMapper;
import com.datalink.fdop.engine.service.DwsNewStdCostOsrateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewStdCostOsrateServiceImpl extends ServiceImpl<DwsNewStdCostOsrateMapper, DwsNewStdCostOsrate> implements DwsNewStdCostOsrateService {

    @Autowired
    private DwsNewStdCostOsrateMapper dwsNewStdCostOsrateMapper;

    @Override
    public PageDataInfo<DwsNewStdCostOsrate> overview(
            String controlAreaId, String productId, Integer batchQty,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwsNewStdCostOsrate> page = PageUtils.getPage(DwsNewStdCostOsrate.class);
        IPage<DwsNewStdCostOsrate> iPage = dwsNewStdCostOsrateMapper.selectAll(controlAreaId, productId, batchQty,verId, plantId, date,  page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewStdCostOsrate> selectNoPage(String controlAreaId, String productId, Integer batchQty, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwsNewStdCostOsrateMapper.selectNoPage(controlAreaId, productId, batchQty, verId, plantId, date, sort, searchVo);
    }
}
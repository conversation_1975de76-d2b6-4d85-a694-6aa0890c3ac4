package com.datalink.fdop.project.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.project.api.domain.CapsulationInfo;
import com.datalink.fdop.project.api.model.vo.CapsulationInfoShowVo;
import com.datalink.fdop.project.api.model.vo.CapsulationInfoVo;

/**
 * <AUTHOR>
 * @Date 2022/10/10 16:36
 */
public interface CapsulationInfoService  extends IService<CapsulationInfo> {
    void checkObject(CapsulationInfo capsulationInfo,String serialNum);

    Page<CapsulationInfoShowVo> pageData(Page<CapsulationInfoShowVo> page, CapsulationInfoVo infoVo);
}

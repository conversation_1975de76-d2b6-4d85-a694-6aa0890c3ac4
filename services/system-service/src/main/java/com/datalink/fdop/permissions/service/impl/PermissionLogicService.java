package com.datalink.fdop.permissions.service.impl;

import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.match.RegexUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.permissions.api.domain.ButtonPermissions;
import com.datalink.fdop.permissions.api.domain.Permissions;
import com.datalink.fdop.permissions.api.domain.RelevancyFront;
import com.datalink.fdop.permissions.api.enums.ContainType;
import com.datalink.fdop.permissions.api.enums.OperatorType;
import com.datalink.fdop.permissions.api.enums.RelevancyType;
import com.datalink.fdop.permissions.api.model.vo.RelevancyVo;
import com.datalink.fdop.permissions.api.model.vo.RowRelevancyVo;
import com.datalink.fdop.permissions.mapper.PermissionLogicMapper;
import com.datalink.fdop.permissions.mapper.RelevancyMapper;
import com.datalink.fdop.permissions.service.IPermissionLogicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/24 10:41
 */
@Service
@Slf4j
public class PermissionLogicService implements IPermissionLogicService {

    @Autowired
    private PermissionLogicMapper permissionLogicMapper;

    @Autowired
    private RelevancyMapper relevancyMapper;


    @Override
    public Boolean checkPermission() {
        Long userId = SecurityUtils.getUserId();
        return checkPermission(userId);
    }

    @Override
    public Boolean checkPermission(Long userId) {
        // 是否超级管理员用户(tadmin)
        Boolean isAdminUser = permissionLogicMapper.isAdminUser(userId);
        // 是否超级管理员角色
        Boolean isAdminRole = permissionLogicMapper.isAdminRole(userId);
        boolean checkPermission = isAdminUser || isAdminRole;
        log.info("用户ID:{},是否超级管理员用户:{},是否超级管理员角色:{},权限认证是否通过:{}", userId, isAdminUser, isAdminRole, checkPermission);
        return checkPermission;
    }

    @Override
    public Boolean checkMapperId(String mapperId) {
        // 判断查询ID是否存在
        return relevancyMapper.checkRelevancyBackByMapperId(mapperId);
    }

    @Override
    public List<RowRelevancyVo> selectRowPermissionsRelevancyList(String mapperId) {
        // 有行权限的关联字段
        List<RowRelevancyVo> rowPermissionRelevancyList = new ArrayList<>();
        // 校验权限
        if (this.checkPermission()) {
            return rowPermissionRelevancyList;
        }
        // 根据权限字段的mapperId获取行权限对象集合
        List<Permissions> permissionsList = permissionLogicMapper.selectRowPermissionsByMapperId(mapperId);
        if (CollectionUtils.isNotEmpty(permissionsList)) {
            // 过滤满足条件的权限对象
            permissionsList = getPermissionsList(permissionsList, true);
            if (CollectionUtils.isEmpty(permissionsList)) {
                return rowPermissionRelevancyList;
            }
            // 返回有权限的行
            List<RowRelevancyVo> rowRelevancyVoList = permissionLogicMapper.selectRowPermissionsRelevancyList(permissionsList);
            // 构建条件
            for (RowRelevancyVo rowRelevancyVo : rowRelevancyVoList) {
                // 是否转换成下划线
                Boolean isUnderline = rowRelevancyVo.getIsUnderline();
                if (isUnderline != null && isUnderline) {
                    // 给后台的字段转换成下划线
                    rowRelevancyVo.setField(RegexUtils.camelToUnderscore(rowRelevancyVo.getField()));
                }
                // 条件字段
                String field = rowRelevancyVo.getField();
                // 字段类型
                FieldType fieldType = rowRelevancyVo.getFieldType();
                // 包含关系
                ContainType containType = rowRelevancyVo.getContainType();
                // 运算符
                OperatorType operatorType = rowRelevancyVo.getOperatorType();
                // 起始值
                String startValue = rowRelevancyVo.getStartValue();
                // 结束值
                String endValue = rowRelevancyVo.getEndValue();
                // 条件
                String condition = getCondition(fieldType, containType, operatorType, field, startValue, endValue);
                // 赋值条件
                rowRelevancyVo.setCondition(condition);
            }
            rowPermissionRelevancyList.addAll(rowRelevancyVoList);
        }
        return rowPermissionRelevancyList;
    }

    @Override
    public List<RelevancyFront> selectNotColumnPermissionsList(String pageId) {
        // 无权限的关联字段
        List<RelevancyFront> notColumnPermissionRelevancyList = new ArrayList<>();
        // 校验权限
        if (this.checkPermission()) {
            return notColumnPermissionRelevancyList;
        }
        // 根据权限字段的pageId获取列权限对象集合
        List<Permissions> permissionsList = permissionLogicMapper.selectColumnPermissionsByPageId(pageId);

        if (CollectionUtils.isNotEmpty(permissionsList)) {
            // 过滤不满足条件的权限对象
            permissionsList = getPermissionsList(permissionsList, false);
            if (CollectionUtils.isEmpty(permissionsList)) {
                return notColumnPermissionRelevancyList;
            }
            // 返回无权限的列
            List<RelevancyFront> relevancyFronts = permissionLogicMapper.selectNotColumnPermissionRelevancyList(permissionsList);
            notColumnPermissionRelevancyList.addAll(relevancyFronts);
        }
        return notColumnPermissionRelevancyList;
    }

    @Override
    public List<ButtonPermissions> selectNotButtonPermissionsList(String pageId) {
        // 无权限的按钮
        List<ButtonPermissions> notButtonPermissionList = new ArrayList<>();
        // 校验权限
        if (this.checkPermission()) {
            return notButtonPermissionList;
        }
        // 根据按钮的pageId获取按钮权限对象集合
        List<Permissions> permissionsList = permissionLogicMapper.selectButtonPermissionsByPageId(pageId);

        if (CollectionUtils.isNotEmpty(permissionsList)) {
            // 过滤不满足条件的权限对象
            permissionsList = getPermissionsList(permissionsList, false);
            if (CollectionUtils.isEmpty(permissionsList)) {
                return notButtonPermissionList;
            }
            // 返回无权限的按钮
            notButtonPermissionList.addAll(permissionLogicMapper.selectNotButtonPermissionsList(permissionsList));
        }
        return notButtonPermissionList;
    }

    @Override
    public List<ButtonPermissions> selectAllNotButtonPermissionsList() {
        // 无权限的按钮
        List<ButtonPermissions> notButtonPermissionList = new ArrayList<>();
        // 校验权限
        if (this.checkPermission()) {
            return notButtonPermissionList;
        }
        // 获取所有按钮权限对象集合
        List<Permissions> permissionsList = permissionLogicMapper.selectButtonPermissionsByPageId(null);

        if (CollectionUtils.isNotEmpty(permissionsList)) {
            // 过滤不满足条件的权限对象
            permissionsList = getPermissionsList(permissionsList, false);
            if (CollectionUtils.isEmpty(permissionsList)) {
                return notButtonPermissionList;
            }
            // 返回无权限的按钮
            notButtonPermissionList.addAll(permissionLogicMapper.selectNotButtonPermissionsList(permissionsList));
        }
        return notButtonPermissionList;
    }

    /**
     * 获取权限对象，判断需要有权限的对象还是无权限的对象
     *
     * @param permissionsList
     * @param isPermissions
     * @return
     */
    private List<Permissions> getPermissionsList(List<Permissions> permissionsList, Boolean isPermissions) {
        // 过滤不满足条件的权限对象
        permissionsList = permissionsList.stream().filter(permissions -> {
            // 判断当前用户是否有权限
            Boolean flag = false;
            // 当前用户id
            Long userId = SecurityUtils.getUserId();
            // 根据权限对象获取用户权限列表
            List<Long> userIds = permissionLogicMapper.selectUserPermissionsList(permissions);
            if (CollectionUtils.isNotEmpty(userIds) && userIds.contains(userId)) {
                flag = true;
            }

            // 确定用户权限则跳过角色权限验证
            if (!flag) {
                // 根据权限对象获取角色权限列表
                List<Long> roleIds = permissionLogicMapper.selectRolePermissionsList(permissions);
                // 校验当前用户是否存在权限
                if (CollectionUtils.isNotEmpty(roleIds)) {
                    // 获取当前用户的所有角色
                    List<Long> userRoleIds = permissionLogicMapper.selectRoleByUserId(userId);
                    if (CollectionUtils.isNotEmpty(userRoleIds)) {
                        for (Long userRoleId : userRoleIds) {
                            if (roleIds.contains(userRoleId)) {
                                flag = true;
                                break;
                            }
                        }
                    }
                }
            }
            return isPermissions ? flag : !flag;
        }).collect(Collectors.toList());
        return permissionsList;
    }

    /**
     * 根据值类型转换值
     *
     * @param fieldType
     * @param value
     * @return
     */
    private String convertValue(FieldType fieldType, String value) {
        if (StringUtils.isEmpty(value)) {
            return value;
        }
        switch (fieldType) {
            case 布尔类型:
            case 时刻类型:
            case 字符类型:
            case 文本类型:
            case 时间类型:
            case 日期类型:
            case 二进制类型:
                return "'" + value + "'";
            case 短整数类型:
            case 长浮点类型:
            case 浮点类型:
            case 数值类型:
            case 长整数类型:
            case 时戳类型:
            case 整数类型:
                return value;
            default:
                return "''";
        }
    }

    /**
     * 获取条件
     *
     * @param fieldType
     * @param containType
     * @param operatorType
     * @param field
     * @param startValue
     * @param endValue
     * @return
     */
    private String getCondition(FieldType fieldType, ContainType containType, OperatorType operatorType, String field, String startValue, String endValue) {
        // 转换值
        switch (operatorType) {
            case EQ:
            case NEQ:
            case GT:
            case LT:
            case GE:
            case LE:
            case IN:
            case NOT_IN:
                startValue = convertValue(fieldType, startValue);
                break;
            case LIKE:
            case NOT_LIKE:
                break;
            case INTERVAL:
                startValue = convertValue(fieldType, startValue);
                endValue = convertValue(fieldType, endValue);
                break;
        }

        String condition = "";
        if (containType == ContainType.CONTAIN) {
            switch (operatorType) {
                case EQ:
                    condition = field + " = " + startValue;
                    break;
                case NEQ:
                    condition = field + " <> " + startValue;
                    break;
                case GT:
                    condition = field + " > " + startValue;
                    break;
                case LT:
                    condition = field + " < " + startValue;
                    break;
                case GE:
                    condition = field + " >= " + startValue;
                    break;
                case LE:
                    condition = field + " <= " + startValue;
                    break;
                case IN:
                    condition = field + " in (" + startValue.replaceAll(",", "','") + ")";
                    break;
                case NOT_IN:
                    condition = field + " not in (" + startValue.replaceAll(",", "','") + ")";
                    break;
                case LIKE:
                    condition = field + " like '%" + startValue + "%'";
                    break;
                case NOT_LIKE:
                    condition = field + " not like '%" + startValue + "%'";
                    break;
                case INTERVAL:
                    condition = field + " BETWEEN " + startValue + " AND " + endValue;
                    break;
            }
        } else {
            switch (operatorType) {
                case EQ:
                    condition = field + " <> " + startValue;
                    break;
                case NEQ:
                    condition = field + " = " + startValue;
                    break;
                case GT:
                    condition = field + " <= " + startValue;
                    break;
                case LT:
                    condition = field + " >= " + startValue;
                    break;
                case GE:
                    condition = field + " < " + startValue;
                    break;
                case LE:
                    condition = field + " > " + startValue;
                    break;
                case IN:
                    condition = field + " not in (" + startValue.replaceAll(",", "','") + ")";
                    break;
                case NOT_IN:
                    condition = field + " in (" + startValue.replaceAll(",", "','") + ")";
                    break;
                case LIKE:
                    condition = field + " not like '%" + startValue + "%'";
                    break;
                case NOT_LIKE:
                    condition = field + " like '%" + startValue + "%'";
                    break;
                case INTERVAL:
                    condition = field + " NOT BETWEEN " + startValue + " AND " + endValue;
                    break;
            }
        }
        return condition;
    }


    @Override
    public List<RelevancyVo> selectRelevancyList(RelevancyType relevancyType, RelevancyVo relevancyVo) {
        switch (relevancyType) {
            case FRONT:
                return permissionLogicMapper.selectFrontList(relevancyVo);
            case BACK:
                return permissionLogicMapper.selectBackList(relevancyVo);
            case BUTTON:
                return permissionLogicMapper.selectButtonList(relevancyVo);
            default:
                throw new ServiceException(Status.PARAM_ERROR);
        }
    }
}

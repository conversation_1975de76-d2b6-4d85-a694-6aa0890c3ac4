package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostCcrate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwsNewStdCostCcrateMapper extends BaseMapper<DwsNewStdCostCcrate> {

    IPage<DwsNewStdCostCcrate> selectAll(
            @Param("controlAreaId") String controlAreaId, @Param("costCenterId") String costCenterId,
            @Param("costStructureId") String costStructureId, @Param("activityId") String activityId,
            @Param("verId") String verId,
            @Param("valueType") String valueType, @Param("date") String date, @Param("page") IPage<DwsNewStdCostCcrate> page,
            @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    List<DwsNewStdCostCcrate> selectNoPage(@Param("controlAreaId") String controlAreaId,
                                           @Param("costCenterId") String costCenterId,
                                           @Param("costStructureId") String costStructureId,
                                           @Param("activityId") String activityId,
                                           @Param("verId") String verId,
                                           @Param("valueType") String valueType,
                                           @Param("date") String date,
                                           @Param("sort") String sort,
                                           @Param("searchVo") SearchVo searchVo);
}
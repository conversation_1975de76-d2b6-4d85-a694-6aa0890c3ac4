package com.datalink.fdop.quality.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @Date 2022/10/11 10:28
 */
public enum DataQualityLogStatus {

    // // 成功
    // SUCCESS(0, "SUCCESS"),
    // // 失败
    // FAIL(1, "FAIL"),
    // // 运行中
    // RUNNING(2, "RUNNING"),
    SUBMITTED_SUCCESS(0, "submit success"),
    RUNNING_EXECUTION(1, "running"),
    READY_PAUSE(2, "ready pause"),
    PAUSE(3, "pause"),
    READY_STOP(4, "ready stop"),
    STOP(5, "stop"),
    FAIL<PERSON><PERSON>(6, "failure"),
    SUCCESS(7, "success"),
    NEED_FAULT_TOLERANCE(8, "need fault tolerance"),
    <PERSON><PERSON><PERSON>(9, "kill"),
    WAITING_THREAD(10, "waiting thread"),
    WAITING_DEPEND(11, "waiting depend node complete"),
    DELAY_EXECUTION(12, "delay execution"),
    FORCED_SUCCESS(13, "forced success"),
    SERIAL_WAIT(14, "serial wait"),
    READY_BLOCK(15, "ready block"),
    BLOCK(16, "block"),
    DISPATCH(17, "dispatch"),
    ;

    DataQualityLogStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @EnumValue
    private final int code;
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

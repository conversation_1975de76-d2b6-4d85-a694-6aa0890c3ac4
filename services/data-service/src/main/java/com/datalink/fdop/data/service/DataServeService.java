package com.datalink.fdop.data.service;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.domain.BaseCopy;
import com.datalink.fdop.data.api.domain.DataServe;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/16 10:43
 */
public interface DataServeService {
    R saveOrUpdate(List<DataServe> dataServes);

    DataServe create(DataServe dataServes);

    int update(DataServe dataServe);

    R del(List<Long> ids);

    List<DataServe> queryDataShare(DataServe dataServe);

    void copy(Long pid, List<BaseCopy> dataServeList);

    DataServe selectById(Long id);

    DataServe selectByCode(String code);

    DataServe selectShareStatusById(Long id);
}

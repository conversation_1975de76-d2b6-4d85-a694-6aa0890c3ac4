package com.datalink.fdop.engine.service;

import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;

import java.util.List;

/**
 * 通用数据字典Service（关联ODS层描述字段）
 */
public interface DataDictionaryService {

    /**
     * 查询工厂代码，工厂代码描述
     *
     * @param orgTableName 原表名
     * @return SelectVo
     */
    List<SelectVo> listPlantId(String orgTableName, SearchVo searchVo);

    /**
     * 查询评估范围ID，评估范围描述
     *
     * @param orgTableName 原表名
     * @return SelectVo
     */
    List<SelectVo> selectEvaluateAreaId(String orgTableName, SearchVo searchVo);

    /**
     * 查询数据集，数据集描述
     *
     * @param orgTableName 原表名
     * @return SelectVo
     */
    List<SelectVo> listVerId(String orgTableName, SearchVo searchVo);

    /**
     * 查询公司代码，公司代码描述
     *
     * @param orgTableName 原表名
     * @return SelectVo
     */
    List<SelectVo> listCompanyId(String orgTableName, SearchVo searchVo);

    /**
     * 查询公司代码，公司代码描述
     *
     * @param orgTableName 原表名
     * @return SelectVo
     */
    List<SelectVo> listCompanyId(String orgTableName, String fieldName, SearchVo searchVo);

    /**
     * 查询管理范围，管理范围描述
     *
     * @param orgTableName 原表名
     * @return SelectVo
     */
    List<SelectVo> listManageScope(String orgTableName, SearchVo searchVo);

    /**
     * 获取成本中心代码，成本中心描述
     *
     * @param orgTableName 原表名
     * @return SelectVo
     */
    List<SelectVo> listCostCenterId(String orgTableName, SearchVo searchVo);

}

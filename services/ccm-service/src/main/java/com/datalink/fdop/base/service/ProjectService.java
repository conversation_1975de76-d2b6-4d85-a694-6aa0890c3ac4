package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.Project;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface ProjectService extends IService<Project> {

    boolean insert(Project project);

    String checkIdUnique(String id);

    int deleteByList(List<String> list);

    String importData(List<Project> list, Boolean isUpdateSupport, String operName);

    boolean copy(List<Project> list);

    List<Project> selectNoPage(String pid, String sort, SearchVo searchVo);

    PageDataInfo<Project> overview(String pid, String sort, SearchVo searchVo);

    PageDataInfo<Project> select(String startCode, String endCode, String sort);

    List<Project> getAll();

    int updateByKey(Project project);

    boolean updateBatchStatus(Boolean enable, List<Project> ids);

    PageDataInfo<Project> queryOrigList(String sort, SearchVo searchVo);

    PageDataInfo<Project> queryList(String sort,SearchVo searchVo);

    boolean batchDelete(List<Project> list);

}

/*
 * Datart
 * <p>
 * Copyright 2021
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.datalink.fdop.datart.service.impl;

import com.datalink.fdop.datart.api.domain.RelWidgetElement;
import com.datalink.fdop.datart.api.domain.RelWidgetWidget;
import com.datalink.fdop.datart.api.domain.Widget;
import com.datalink.fdop.datart.base.params.WidgetCreateParam;
import com.datalink.fdop.datart.base.params.WidgetRelParam;
import com.datalink.fdop.datart.base.params.WidgetUpdateParam;
import com.datalink.fdop.datart.mapper.RelWidgetElementMapper;
import com.datalink.fdop.datart.mapper.RelWidgetWidgetMapper;
import com.datalink.fdop.datart.mapper.WidgetMapper;
import com.datalink.fdop.datart.service.WidgetService;
import datart.core.common.UUIDGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WidgetServiceImpl implements WidgetService {

    private final WidgetMapper widgetMapper;

    private final RelWidgetElementMapper rweMapper;

    private final RelWidgetWidgetMapper rwwMapper;


    public WidgetServiceImpl(WidgetMapper widgetMapper,
                             RelWidgetElementMapper rweMapper,
                             RelWidgetWidgetMapper rwwMapper) {
        this.widgetMapper = widgetMapper;
        this.rweMapper = rweMapper;
        this.rwwMapper = rwwMapper;
    }

    @Override
    @Transactional
    public List<Widget> createWidgets(List<WidgetCreateParam> createParams) {
        if (CollectionUtils.isEmpty(createParams)) {
            return null;
        }
        ArrayList<Widget> widgets = new ArrayList<>();
        ArrayList<RelWidgetElement> elements = new ArrayList<>();
        for (WidgetCreateParam createParam : createParams) {
            Widget widget = new Widget();
            BeanUtils.copyProperties(createParam, widget);
            if (widget.getId() == null) {
                widget.setId(UUIDGenerator.generate());
            }
            widgets.add(widget);
            if (createParam.getDatachartId() != null) {
                elements.add(createRelWidgetElement(widget.getId(), "DATACHART", createParam.getDatachartId()));
            }
            if (!CollectionUtils.isEmpty(createParam.getViewIds())) {
                for (String viewId : createParam.getViewIds()) {
                    elements.add(createRelWidgetElement(widget.getId(), "VIEW", viewId));
                }
            }
            // insert widget relations
            updateWidgetRelations(createParam.getId(), createParam.getRelations());
        }
        //insert widgets
        if (!CollectionUtils.isEmpty(widgets)) {
            widgetMapper.batchInsert(widgets);
        }
        // insert widget elements
        if (!CollectionUtils.isEmpty(elements)) {
            rweMapper.batchInsert(elements);
        }

        return widgets;
    }

    @Override
    @Transactional
    public boolean updateWidgets(List<WidgetUpdateParam> updateParams) {
        if (CollectionUtils.isEmpty(updateParams)) {
            return false;
        }
        LinkedList<RelWidgetElement> elements = new LinkedList<>();
        LinkedList<String> elementToDelete = new LinkedList<>();
        List<Widget> widgetsToUpdate = updateParams.stream().map(updateParam -> {
            if (updateParam.getDatachartId() != null) {
                elements.add(createRelWidgetElement(updateParam.getId(), "DATACHART", updateParam.getDatachartId()));
            }
            if (!CollectionUtils.isEmpty(updateParam.getViewIds())) {
                for (String viewId : updateParam.getViewIds()) {
                    elements.add(createRelWidgetElement(updateParam.getId(), "VIEW", viewId));
                }
            }

            // update widget relations
            updateWidgetRelations(updateParam.getId(), updateParam.getRelations());

            elementToDelete.add(updateParam.getId());
            Widget widget = new Widget();
            BeanUtils.copyProperties(updateParam, widget);
            return widget;
        }).collect(Collectors.toList());
        // update widgets
        if (!CollectionUtils.isEmpty(widgetsToUpdate)) {
            for (Widget widget : widgetsToUpdate) {
                widgetMapper.update(widget);
            }
        }
        // update widget elements
        if (!CollectionUtils.isEmpty(elementToDelete)) {
            rweMapper.deleteByWidgets(elementToDelete);
        }
        if (!CollectionUtils.isEmpty(elements)) {
            rweMapper.batchInsert(elements);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean deleteWidgets(List<String> widgetIds) {

        if (CollectionUtils.isEmpty(widgetIds)) {
            return false;
        }
//        for (String id : widgetIds) {
//            fileService.deleteFiles(FileOwner.WIDGET, id);
//        }
        int delete = 0;
        delete += widgetMapper.deleteWidgets(widgetIds);
        delete += widgetMapper.deleteWidgetElements(widgetIds);
        delete += widgetMapper.deleteWidgetWidgets(widgetIds);
        return delete > 0;
    }

    private void updateWidgetRelations(String sourceId, List<WidgetRelParam> relations) {
        // delete targets
        rwwMapper.deleteBySourceId(sourceId);

        if (CollectionUtils.isEmpty(relations)) {
            return;
        }
        List<RelWidgetWidget> relWidgetWidgets = relations.stream().map(param -> {
            RelWidgetWidget rel = new RelWidgetWidget();
            BeanUtils.copyProperties(param, rel);
            rel.setId(UUIDGenerator.generate());
            return rel;
        }).collect(Collectors.toList());
        rwwMapper.batchInsert(relWidgetWidgets);
    }


    private RelWidgetElement createRelWidgetElement(String widgetId, String relType, String relId) {
        RelWidgetElement element = new RelWidgetElement();
        element.setId(UUIDGenerator.generate());
        element.setWidgetId(widgetId);
        element.setRelId(relId);
        element.setRelType(relType);
        return element;
    }

}

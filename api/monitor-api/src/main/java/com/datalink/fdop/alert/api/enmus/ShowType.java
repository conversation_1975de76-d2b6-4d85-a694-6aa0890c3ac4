package com.datalink.fdop.alert.api.enmus;

/**
 * <AUTHOR>
 * @Date 2022/5/23 16:22
 */
public enum ShowType {
    /**
     * 0 TABLE;
     * 1 TEXT;
     * 2 attachment;
     * 3 TABLE+attachment;
     * 4 MARKDOWN;
     */
    TABLE(0, "table"),
    TEXT(1, "text"),
    ATTACHMENT(2, "attachment"),
    TABLE_ATTACHMENT(3, "table attachment"),
    MARKDOWN(4, "markdown"),;

    private final int code;
    private final String descp;

    ShowType(int code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    public int getCode() {
        return code;
    }

    public String getDescp() {
        return descp;
    }
}


<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.SpecialMatMapper">

    <delete id="deleteByKey">
        delete from zjdata.ccm_special_mat
        WHERE (special_type, raw_material_id, product_id) IN
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.specialType},#{item.rawMaterialId}, #{item.productId})
        </foreach>
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_special_mat
        WHERE special_type = #{specialType} AND raw_material_id = #{rawMaterialId}
        AND product_id = #{productId}
        limit 1
    </select>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.SpecialMat">
        UPDATE zjdata.ccm_special_mat
        SET
        <if test="factorId != null">factor_id = #{factorId},</if>
        <if test="comments != null">comments = #{comments},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE special_type = #{specialType} AND raw_material_id = #{rawMaterialId}
        AND product_id = #{productId}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.SpecialMat">
        SELECT 
            special_type AS specialType,
            raw_material_id AS rawMaterialId,
            product_id AS productId,
            factor_id AS factorId,
            comments AS comments,
            enable AS enable,
            create_by AS createBy,
            create_time AS createTime,
            update_by AS updateBy,
            update_time AS updateTime
        FROM zjdata.ccm_special_mat node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="specialType != null and specialType !=''">
                and node.special_type in
                <foreach item="item" collection="specialType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY special_type, raw_material_id, product_id ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_special_mat
        SET enable = #{enable}
        WHERE (special_type, raw_material_id, product_id) IN
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.specialType},#{item.rawMaterialId}, #{item.productId})
        </foreach>
    </update>
</mapper>
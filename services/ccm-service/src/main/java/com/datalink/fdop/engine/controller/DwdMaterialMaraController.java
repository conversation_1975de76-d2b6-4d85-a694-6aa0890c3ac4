package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.domain.ProductVo;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwdMaterialMara;
import com.datalink.fdop.engine.service.DwdMaterialMaraService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdMaterialMara")
@Api(tags = "CCMS-物料评估信息")
public class DwdMaterialMaraController extends BaseController {

    @Autowired
    private DwdMaterialMaraService dwdMaterialMaraService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "物料主数据")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialType = MapUtils.getAsString(params, "materialType");
        String materialGroup = MapUtils.getAsString(params, "materialGroup");
        String materialId = MapUtils.getAsString(params, "materialId");
        PageDataInfo<DwdMaterialMara> overview = dwdMaterialMaraService.overview(materialType, materialGroup, materialId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdMaterialMara.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "物料主数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                   @RequestParam(required = false, defaultValue = "ASC") String sort,
                   @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialType = MapUtils.getAsString(params, "materialType");
        String materialGroup = MapUtils.getAsString(params, "materialGroup");
        String materialId = MapUtils.getAsString(params, "materialId");
        
        List<DwdMaterialMara> list = dwdMaterialMaraService.selectNoPage(materialType, materialGroup, materialId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdMaterialMara> util = new ExcelUtil<>(DwdMaterialMara.class);
        util.exportExcel(response, list, "基本信息");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdMaterialMara> util = new ExcelUtil<>(DwdMaterialMara.class);
        util.importTemplateExcel(response, "基本信息");
    }

    @ApiOperation("物料类型（SelectVo）")
    @GetMapping(value = "/selectMaterialType")
    public R<List<SelectVo>> selectMaterialType() {
        return R.ok(dwdMaterialMaraService.lambdaQuery()
                .select(DwdMaterialMara::getMaterialType, DwdMaterialMara::getMaterialTypeDesc)
                .groupBy(DwdMaterialMara::getMaterialType, DwdMaterialMara::getMaterialTypeDesc)
                .orderByAsc(DwdMaterialMara::getMaterialType).list().stream()
                .filter(dwdMaterialMara -> dwdMaterialMara != null && dwdMaterialMara.getMaterialType() != null)
                .map(dwdMaterialMara -> new SelectVo(dwdMaterialMara.getMaterialType(), dwdMaterialMara.getMaterialTypeDesc()))
                .collect(Collectors.toList()));
    }

    @ApiOperation("物料组（SelectVo）")
    @GetMapping(value = "/selectMaterialGroup")
    public R<List<SelectVo>> selectMaterialGroup(@RequestParam(value = "materialGroup", required = false) String materialGroup) {
        return R.ok(dwdMaterialMaraService.lambdaQuery()
                .select(DwdMaterialMara::getMaterialGroup, DwdMaterialMara::getMaterialGroupDesc)
                .groupBy(DwdMaterialMara::getMaterialGroup, DwdMaterialMara::getMaterialGroupDesc)
                .orderByAsc(DwdMaterialMara::getMaterialGroup).list().stream()
                .filter(dwdMaterialMara -> dwdMaterialMara != null && dwdMaterialMara.getMaterialGroup() != null)
                .map(dwdMaterialMara -> new SelectVo(dwdMaterialMara.getMaterialGroup(), dwdMaterialMara.getMaterialGroupDesc()))
                .collect(Collectors.toList()));
    }


    @ApiOperation("物料编码（SelectVo）")
    @GetMapping(value = "/listMaterialId")
    public R<List<SelectVo>> selectMaterialId(@RequestParam(value = "materialId", required = false) String materialId) {
        return R.ok(dwdMaterialMaraService.lambdaQuery()
                .select(DwdMaterialMara::getMaterialId, DwdMaterialMara::getMaterialDesc)
                .groupBy(DwdMaterialMara::getMaterialId, DwdMaterialMara::getMaterialDesc)
                .orderByAsc(DwdMaterialMara::getMaterialId).list().stream()
                .filter(dwdMaterialMara -> dwdMaterialMara != null && dwdMaterialMara.getMaterialId() != null)
                .map(dwdMaterialMara -> new SelectVo(dwdMaterialMara.getMaterialId(), dwdMaterialMara.getMaterialDesc()))
                .collect(Collectors.toList()));
    }

    @ApiOperation("查询产品CIM编码（下拉框）")
    @GetMapping(value = "/selectProductCimId")
    public R<List<String>> selectProductCimId() {
        return R.ok(dwdMaterialMaraService.lambdaQuery().select(DwdMaterialMara::getProductCimId)
                .groupBy(DwdMaterialMara::getProductCimId).list()
                .stream().filter(dwdMaterialMara -> dwdMaterialMara.getProductCimId() != null)
                .map(DwdMaterialMara::getProductCimId).collect(Collectors.toList()));
    }

    @ApiOperation("查询产品ERP编码（下拉框）")
    @GetMapping(value = "/selectProductErpId")
    public R<List<String>> selectProductErpId() {
        return R.ok(dwdMaterialMaraService.lambdaQuery().select(DwdMaterialMara::getProductErpId)
                .groupBy(DwdMaterialMara::getProductErpId).list()
                .stream().filter(dwdMaterialMara -> dwdMaterialMara.getProductErpId() != null)
                .map(DwdMaterialMara::getProductErpId).collect(Collectors.toList()));
    }


    @ApiOperation("查询产品编码（选择列表）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "查询产品编码（选择列表）")
    @GetMapping(value = "/selectProductList")
    public Object selectProductList(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false, value = "productId") String productId,
            @RequestParam(required = false, value = "productDesc") String productDesc,
            @RequestParam(required = false, value = "productCimId") String productCimId,
            @RequestBody(required = false) SearchVo searchVo) {
        PageDataInfo<ProductVo> overview = dwdMaterialMaraService.selectProductList(productId, productDesc, productCimId, sort, searchVo);
        return R.ok(MetaUtils.getMetadata(ProductVo.class, overview));
    }

}

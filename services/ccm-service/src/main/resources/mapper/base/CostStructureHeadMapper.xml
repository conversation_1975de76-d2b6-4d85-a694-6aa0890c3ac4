<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CostStructureHeadMapper">

    <update id="deleteBykey">
        update zjdata.ccm_cost_structure_head
        set del_flag = true
        where control_area_id = #{controlAreaId} AND cost_structure_id = #{costStructureId}
          AND date_from = '${date}' AND del_flag = false
    </update>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_cost_structure_head
        where control_area_id = #{controlAreaId} AND cost_structure_id = #{costStructureId}
          AND date_from = '${date}' AND del_flag = false
    </select>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.CostStructureHead" resultType="com.datalink.fdop.base.api.domain.CostStructureHead">
        SELECT a.*, activity.activity_desc as activity_desc, activity.activity_text as activity_text,
               controlArea.control_area_desc as control_area_desc,
               head.cost_structure_desc as parent_cost_structure_desc, menu.menu_desc as menu_desc
        FROM zjdata.ccm_cost_structure_head a
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
        left join zjdata.ccm_cost_structure_menu menu on a.menu_id = menu.menu_id and menu.del_flag = false
        left join zjdata.ccm_cost_structure_head head on head.control_area_id = a.parent_cost_structure_id and head.del_flag = false
        left join zjdata.ccm_activity activity on activity.activity_id = a.activity_id and activity.del_flag = false
        where a.del_flag = false
        <if test="costStructureHead.costStructureId != null and costStructureHead.costStructureId != ''">
            AND a.cost_structure_id = #{costStructureHead.costStructureId}
        </if>
        <if test="costStructureHead.controlAreaId != null and costStructureHead.controlAreaId != ''">
            AND a.control_area_id = #{costStructureHead.controlAreaId}
        </if>
        <if test="costStructureHead.menuId != null and costStructureHead.menuId != ''">
            AND a.menu_id = #{costStructureHead.menuId}
        </if>
        <if test="costStructureHead.dateFrom != null">
            AND a.date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(costStructureHead.dateFrom)}'
        </if>
        ORDER BY control_area_id, cost_structure_id, date_from desc
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.CostStructureHead">
        SELECT a.*, activity.activity_desc as activity_desc, activity.activity_text as activity_text, controlArea.control_area_desc as control_area_desc,
               head.cost_structure_desc as parent_cost_structure_desc, menu.menu_desc as menu_desc
        FROM zjdata.ccm_cost_structure_head a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
                 left join zjdata.ccm_cost_structure_menu menu on a.menu_id = menu.menu_id and menu.del_flag = false
                 left join zjdata.ccm_cost_structure_head head on head.control_area_id = a.parent_cost_structure_id and head.del_flag = false
                 left join zjdata.ccm_activity activity on activity.activity_id = a.activity_id and activity.del_flag = false
        where a.control_area_id = #{controlAreaId} AND a.cost_structure_id = #{costStructureId}
          AND a.date_from = '${date}' AND a.del_flag = false limit 1
    </select>

    <select id="selectIdsByPid" resultType="String">
        select cost_structure_id from zjdata.ccm_cost_structure_head
        where menu_id = #{pid} and del_flag = false
    </select>

    <update id="bacthUpdatePidById">
        update zjdata.ccm_cost_structure_head
        set menu_id = (select p_menu_id from zjdata.ccm_cost_structure_menu where menu_id = #{menuId} limit 1)
        where menu_id = #{menuId} and del_flag = false
    </update>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CostStructureHead">
        update zjdata.ccm_cost_structure_head
        set
        <if test="dateTo != null">
            date_to = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateTo)}',
        </if>
        <if test="costStructureDesc != null">cost_structure_desc = #{costStructureDesc},</if>
        <if test="costStructureText != null">cost_structure_text = #{costStructureText},</if>
        <if test="unit != null">unit = #{unit},</if>
        <if test="costLevel != null">cost_level = #{costLevel},</if>
        <if test="parentCostStructureId != null">parent_cost_structure_id = #{parentCostStructureId},</if>
        <if test="menuId != null">menu_id = #{menuId},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId} and cost_structure_id = #{costStructureId}
          and date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}'
          and del_flag = false
    </update>

    <select id="getAll" resultType="com.datalink.fdop.base.api.domain.CostStructureHead">
        SELECT a.*, activity.activity_desc as activity_desc, activity.activity_text as activity_text, controlArea.control_area_desc as control_area_desc,
               head.cost_structure_desc as parent_cost_structure_desc, menu.menu_desc as menu_desc
        FROM zjdata.ccm_cost_structure_head a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
                 left join zjdata.ccm_cost_structure_menu menu on a.menu_id = menu.menu_id and menu.del_flag = false
                 left join zjdata.ccm_cost_structure_head head on head.control_area_id = a.parent_cost_structure_id and head.del_flag = false
                 left join zjdata.ccm_activity activity on activity.activity_id = a.activity_id and activity.del_flag = false
        WHERE a.del_flag = false
        ORDER BY control_area_id, cost_structure_id, date_from desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.CostStructureHead">
        SELECT a.*, activity.activity_desc as activity_desc, activity.activity_text as activity_text, controlArea.control_area_desc as control_area_desc,
        head.cost_structure_desc as parent_cost_structure_desc, menu.menu_desc as menu_desc
        FROM zjdata.ccm_cost_structure_head a
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
        left join zjdata.ccm_cost_structure_menu menu on a.menu_id = menu.menu_id and menu.del_flag = false
        left join zjdata.ccm_cost_structure_head head on head.control_area_id = a.parent_cost_structure_id and head.del_flag = false
        left join zjdata.ccm_activity activity on activity.activity_id = a.activity_id and activity.del_flag = false
        WHERE a.del_flag = false
        <if test="searchVo != null and searchVo!= ''">
            and ${searchVo}
        </if>
        <if test="menuId != null and menuId != '-1'.toString()">
            and a.menu_id = #{menuId}
        </if>
        ORDER BY control_area_id ${sort}, cost_structure_id ${sort}, date_from ${sort}
    </select>

    <select id="getByLevel" resultType="com.datalink.fdop.base.api.domain.CostStructureHead">
        select * from zjdata.ccm_cost_structure_head where cost_level = #{level} AND menu_id = #{menuId}
    </select>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.CostStructureHead">
        select * from zjdata.ccm_cost_structure_head where del_flag = false
    </select>

    <select id="selectDynamicColumnList" resultType="com.datalink.fdop.common.core.domain.DynamicColumn">
        select
            cost_structure_id as columnName,cost_structure_desc columnDesc
        from zjdata.ccm_cost_structure_head
        where enable = true and structure_ver_id in
                                  (select structure_ver_id from zjdata.ccm_structure_ver where control_area_id = #{controlAreaId})
    </select>

    <select id="selectDynamicOsColumnList" resultType="com.datalink.fdop.common.core.domain.DynamicColumn">
        select
            os_cost_structure_id as columnName,os_cost_structure_desc columnDesc
        from zjdata.ccm_cost_structure_head
        where enable = true and structure_ver_id in
                                  (select structure_ver_id from zjdata.ccm_structure_ver where control_area_id = #{controlAreaId})
    </select>


</mapper>


package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRateActivityA;
import com.datalink.fdop.engine.mapper.DwhFinalRateActivityAMapper;
import com.datalink.fdop.engine.service.DwhFinalRateActivityAService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRateActivityAServiceImpl extends ServiceImpl<DwhFinalRateActivityAMapper, DwhFinalRateActivityA> implements DwhFinalRateActivityAService {

    @Autowired
    private DwhFinalRateActivityAMapper dwhFinalRateActivityAMapper;

    @Override
    public PageDataInfo<DwhFinalRateActivityA> overview(
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo)  {
        Page<DwhFinalRateActivityA> page = PageUtils.getPage(DwhFinalRateActivityA.class);
        IPage<DwhFinalRateActivityA> iPage = dwhFinalRateActivityAMapper.selectAll(
                controlAreaId, verId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRateActivityA> selectNoPage(String controlAreaId, String verId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwhFinalRateActivityAMapper.selectNoPage(controlAreaId, verId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
package com.datalink.fdop.auth.service.impl;

import com.datalink.fdop.auth.api.domain.AuthObject;
import com.datalink.fdop.auth.api.domain.DataDetailed;
import com.datalink.fdop.auth.api.vo.CalculateValueVo;
import com.datalink.fdop.auth.mapper.*;
import com.datalink.fdop.auth.service.TakeEffectAuthService;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.element.api.RemoteElementService;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.gather.api.utils.DataTypeChangeUtils;
import com.datalink.fdop.system.api.domain.SysRole;
import com.datalink.fdop.system.api.domain.SysUser;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/4/25 17:36
 */
@Service
@Slf4j
public class TakeEffectAuthServiceImpl implements TakeEffectAuthService {

    @Autowired
    public UserRelationMapper userRelationMapper;

    @Autowired
    public RoleRelationMapper roleRelationMapper;

    @Autowired
    public DataElementRelationMapper dataElementRelationMapper;

    @Autowired
    public DataDetailedMapper dataDetailedMapper;

    @Autowired
    public RemoteElementService remoteElementService;

    @Autowired
    public TakeEffectAuthMapper takeEffectAuthMapper;


    @Override
    public boolean takeEffect() {
        //
        return false;
    }

    @Override
    public List<AuthObject> findAuthObjectByUserId(Long userId) {
        //查出用户的权限
        List<AuthObject> authObjects = Lists.newArrayList();
        //找出用户权限对象下的所有权限对象
        List<VlabelItem<AuthObject>> vlabelItemsU = userRelationMapper.findObjectByUserId(userId);
        authObjects.addAll(vlabelItemsU.stream().map(VlabelItem::getProperties).collect(Collectors.toList()));
        //查出用户下的所有角色
        List<VlabelItem<SysRole>> sysRoleLists = roleRelationMapper.findRoleByUserId(userId);
        List<Long> roleIds = sysRoleLists.stream().map(sysRoleVlabelItem -> sysRoleVlabelItem.getProperties().getRoleId()).collect(Collectors.toList());
        //获取角色下的权限对象
        List<VlabelItem<AuthObject>> vlabelItemsR = roleRelationMapper.findObjectByRoleIds(roleIds);
        authObjects.addAll(vlabelItemsR.stream().map(VlabelItem::getProperties).collect(Collectors.toList()));
        return authObjects;
    }

    @Override
    public List<CalculateValueVo> calculateAuthDetailed(AuthObject authObject) {
        List<CalculateValueVo> result = Lists.newArrayList();
        //查元素 INLAY_ELEMENT_ROW_ALL 所有行权限
        List<VlabelItem<DataElement>> vlabelItems = dataElementRelationMapper.query(authObject.getId());
        List<DataElement> dataElements = vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
        List<String> rowAll = dataElements.stream().map(DataElement::getCode).filter(code -> code.equals("INLAY_ELEMENT_ROW_ALL")).collect(Collectors.toList());
        Boolean allAuth = false;
        if (CollectionUtils.isNotEmpty(rowAll)) {
            //获取所有的主数据元素
            allAuth = true;
            vlabelItems = dataElementRelationMapper.queryAll();
            dataElements = vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
        }
        for (DataElement dataElement : dataElements) {
            if (dataElement.getCode().equals("INLAY_ELEMENT_COLUMN") || dataElement.getCode().equals("INLAY_ELEMENT_COLUMN_ALL") || dataElement.getCode().equals("INLAY_ELEMENT_ROW_ALL")) {
                continue;
            }
            //查明细
            List<VlabelItem<DataDetailed>> items = dataDetailedMapper.query(dataElement.getId(), authObject.getId());
            List<DataDetailed> dataDetaileds = items.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
            //判断元素是否多主键
            List<DataElementStructure> dataElementStructures = remoteElementService
                    .list(dataElement.getId())
                    .getData();
            List<DataElementStructure> structures = Lists.newArrayList();
            for (DataElementStructure dataElementStructure : dataElementStructures) {
                if (dataElementStructure.getIsPk()) {
                    structures.add(dataElementStructure);
                }
            }
            if (CollectionUtils.isNotEmpty(structures)) {
                CalculateValueVo calculateValueVo = new CalculateValueVo();
                calculateValueVo.setElementCode(dataElement.getCode());
                calculateValueVo.setElementId(dataElement.getId());
                //拼接sql
                if (structures.size() == 1) {
                    //单主键
                    calculateValueVo.setMorePrimary(false);
                    String sql = this.jointSinglePrimarySql(dataDetaileds, dataElement);
                    if (allAuth) {
                        sql = " 1=1 ";
                    }
                    if (StringUtils.isNotEmpty(sql)) {
                        //执行sql
                        List<String> singleValue = takeEffectAuthMapper.querySinglePrimaryTable(sql, dataElement);
                        Map<String, Object> map = Maps.newHashMap();
                        map.put(dataElement.getCode(), singleValue);
                        calculateValueVo.setValue(map);
                        result.add(calculateValueVo);
                        //TODO:保存元素值和权限对象的关系
                    }
                } else if (structures.size() > 1) {
                    //多主键
                    List<Map<String, String>> primaryMapField = structures.stream().map(structure -> {
                        Map<String, String> res = Maps.newHashMap();
                        res.put(structure.getCode(), structure.getMapFieldName());
                        return res;
                    }).collect(Collectors.toList());
                    List<String> primaryKey = structures
                            .stream()
                            .map(structure -> structure.getCode())
                            .collect(Collectors.toList());
                    calculateValueVo.setMorePrimary(true);
                    calculateValueVo.setPrimaryKey(primaryKey);
                    String sql = jointMorePrimarySql(dataDetaileds, dataElement, structures);
                    if (allAuth) {
                        sql = " 1=1 ";
                    }
                    if (StringUtils.isNotEmpty(sql)) {
                        List<Map<String, Object>> mapList = takeEffectAuthMapper.queryMorePrimaryTable(sql, dataElement, primaryMapField);
                        Map<String, Object> map = Maps.newHashMap();
                        map.put(dataElement.getCode(), mapList);
                        calculateValueVo.setValue(map);
                        result.add(calculateValueVo);
                    }
                    //TODO:保存元素值和权限对象的关系

                }

            }

        }
        return result;
    }


    public List<String[]> getValue(String value, String code, List<DataElementStructure> structures) {
        List<String[]> result = Lists.newArrayList();
        if (StringUtils.isNotEmpty(value)) {
            String[] split = value.split(",");
            for (String s : split) {
                String[] sVal = s.split(":");
                String mapFieldName = "";
                for (DataElementStructure structure : structures) {
                    DataElement dataElement = remoteElementService.selectById(structure.getId()).getData();
                    if (dataElement.getCode().equals(sVal[0])) {
                        mapFieldName = structure.getMapFieldName();
                        break;
                    }
                }
                if (StringUtils.isNotEmpty(mapFieldName)) {
                    sVal[0] = mapFieldName;
                } else {
                    throw new ServiceException("元素" + code + "权限权限字段不匹配，需在权限页面重新配置");
                }
                result.add(sVal);
            }
        }
        return result;
    }

    private String jointMorePrimarySql(List<DataDetailed> dataDetaileds, DataElement dataElement, List<DataElementStructure> structures) {
        String resultSql = "";
        for (DataDetailed dataDetailed : dataDetaileds) {
            resultSql += "( ";
            String matchingPattern = dataDetailed.getMatchingPattern();
            //计算多主键
            String sValue = dataDetailed.getStartValue();
            String eValue = dataDetailed.getEndValue();
            List<String[]> sVals = null;
            if (StringUtils.isNotEmpty(sValue)) {
                sVals = getValue(sValue, dataElement.getCode(), structures);
            }

            //包含
            if (CollectionUtils.isNotEmpty(sVals) || dataDetailed.getMatchingPattern().equals("全部")) {
                if (matchingPattern.equals("等于")) {
                    if (dataDetailed.getContainIs()) {
                        for (String[] sVal : sVals) {
                            FieldType fieldType = structures.stream().filter(structure -> structure.getMapFieldName().equals(sVal[0])).map(DataElementStructure::getFieldType).collect(Collectors.toList()).get(0);
                            String value = DataTypeChangeUtils.changeField(fieldType, sVal[1]);
                            resultSql += " n." + sVal[0] + "=" + value + " and";
                        }
                    } else {
                        for (String[] sVal : sVals) {
                            FieldType fieldType = structures.stream().filter(structure -> structure.getMapFieldName().equals(sVal[0])).map(DataElementStructure::getFieldType).collect(Collectors.toList()).get(0);
                            String value = DataTypeChangeUtils.changeField(fieldType, sVal[1]);
                            resultSql += " n." + sVal[0] + "=" + value + " and";
                        }
                    }
                } else if (matchingPattern.equals("全部")) {
                    if (dataDetailed.getContainIs()) {
                        resultSql += " 1=1 and";
                    } else {
                        return "";
                    }
                }
            }
            if (resultSql.endsWith("and")) {
                resultSql = resultSql.substring(0, resultSql.length() - 3);
            }
            resultSql += " ) or";
        }
        if (resultSql.endsWith("or")) {
            resultSql = resultSql.substring(0, resultSql.length() - 2);
        }
        return resultSql;
    }

    private String jointSinglePrimarySql(List<DataDetailed> dataDetaileds, DataElement dataElement) {
        String resultSql = " ";
        try {
            String fieldName = dataElement.getMapFieldName();
            for (DataDetailed dataDetailed : dataDetaileds) {
                String startValue = dataDetailed.getStartValue();
                if (StringUtils.isNotEmpty(startValue) || dataDetailed.getMatchingPattern().equals("全部")) {
                    String[] eValue = null;
                    String[] sValue = null;
                    if (StringUtils.isNotEmpty(startValue)) {
                        sValue = startValue.split(":");
                        if (!sValue[0].equals(dataElement.getCode().toLowerCase())) {
                            log.error("元素" + dataElement.getCode() + "权限字段不匹配，需在权限页面重新配置");
                            continue;
                        }
                    }
                    String endValue = dataDetailed.getEndValue();
                    if (StringUtils.isNotEmpty(endValue)) {
                        eValue = endValue.split(":");
                        if (!eValue[0].equals(dataElement.getCode().toLowerCase())) {
                            log.error("元素" + dataElement.getCode() + "权限权限字段不匹配，需在权限页面重新配置");
                            continue;
                        }
                    }
                    String matchingPattern = dataDetailed.getMatchingPattern();
                    switch (matchingPattern) {
                        case "等于":
                            if (dataDetailed.getContainIs()) {
                                resultSql += " n." + fieldName + "= '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            } else {
                                resultSql += " not n." + fieldName + "= '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            }
                            break;
                        case "不等于":
                            if (dataDetailed.getContainIs()) {
                                resultSql += " n." + fieldName + "<> '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            } else {
                                resultSql += " not n." + fieldName + "<> '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            }
                            break;
                        case "大于":
                            if (dataDetailed.getContainIs()) {
                                resultSql += " n." + fieldName + "> '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            } else {
                                resultSql += " not n." + fieldName + "> '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            }
                            break;
                        case "小于":
                            if (dataDetailed.getContainIs()) {
                                resultSql += " n." + fieldName + "< '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            } else {
                                resultSql += " not n." + fieldName + "< '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            }
                            break;
                        case "小于等于":
                            if (dataDetailed.getContainIs()) {
                                resultSql += " n." + fieldName + "<= '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            } else {
                                resultSql += " not n." + fieldName + "<= '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            }
                            break;
                        case "大于等于":
                            if (dataDetailed.getContainIs()) {
                                resultSql += " n." + fieldName + ">= '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            } else {
                                resultSql += " not n." + fieldName + ">= '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and";
                            }
                            break;
                        case "like":
                            if (dataDetailed.getContainIs()) {
                                resultSql += " n." + fieldName + " Contains " + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + " and";
                            } else {
                                resultSql += " not n." + fieldName + " Contains " + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + " and";
                            }
                            break;
                        case "not like":
                            if (dataDetailed.getContainIs()) {
                                resultSql += " not n." + fieldName + " Contains " + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + " and";
                            } else {
                                resultSql += " n." + fieldName + " Contains " + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + " and";
                            }
                            break;
                        case "区间":
                            if (dataDetailed.getContainIs()) {
                                resultSql += "  (n." + fieldName + " > '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and n." + fieldName + " < " + DataTypeChangeUtils.changeField(dataElement.getFieldType(), eValue[1]) + ") and";
                            } else {
                                resultSql += " not (n." + fieldName + " > '" + DataTypeChangeUtils.changeField(dataElement.getFieldType(), sValue[1]) + "' and n." + fieldName + " < " + DataTypeChangeUtils.changeField(dataElement.getFieldType(), eValue[1]) + ") and";
                            }
                            break;
                        case "全部":
                            if (dataDetailed.getContainIs()) {
                                resultSql += "  1=1 and";
                            } else {
                                return "";
                            }
                            break;
                    }
                }
            }
            if (resultSql.endsWith("and")) {
                resultSql = resultSql.substring(0, resultSql.length() - 3);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultSql;
    }

    @Override
    public void insertionTable(CalculateValueVo calculateValue, SysUser sysUser, AuthObject authObject) {

    }

    @Override
    public int delTableData() {

        return 0;
    }

    @Override
    public List<Long> findColumnAuth(List<Long> authObjectIds) {
        return takeEffectAuthMapper.findColumnAuth(authObjectIds);
    }

    @Override
    public List<Long> findAllColumnAuth() {
        return takeEffectAuthMapper.findAllColumnAuth();
    }
}

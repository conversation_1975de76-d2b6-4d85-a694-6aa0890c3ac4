package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.model.DataElementTree;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataElementMapper extends BaseMapper<DataElement> {

    int createElementAndMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertElement(@Param("dataElement") DataElement dataElement);

    int insertElementOwnEdge(DataElement dataElement);

    int updateById(DataElement dataElement);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteElementAndMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    int deleteElementOwnEdge(Long id);

    DataElement selectById(Long id);

    DataElement selectByCode(String code);

    DataElement checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    IPage<DataElement> selectList(IPage<DataElement> page, @Param(value = "dataElement") DataElement dataElement);

    IPage<DataElement> overview(IPage<DataElement> page,
                                @Param("pid") Long pid,
                                @Param("sort") String sort,
                                @Param("searchVo") SearchVo searchVo);

    List<Long> selectIdsByPid(Long pid);

    List<DataElementStructureVo> selectElementByIds(@Param("dataElementIdss") List<List<Long>> dataElementIdss, @Param("name") String name);

    // 获取所有子级元素
    List<DataElement> selectSonElement(@Param("dataElementId") Long dataElementId);

    List<DataElement> selectSonElementField(@Param("dataElementId") Long dataElementId);

    List<DataElementTree> selectNodeTree(@Param("sort") String sort, @Param("code") String code);

    List<DataElementStructureVo> selectCiteElementTree(@Param("code") String code, @Param("name") String name);

}

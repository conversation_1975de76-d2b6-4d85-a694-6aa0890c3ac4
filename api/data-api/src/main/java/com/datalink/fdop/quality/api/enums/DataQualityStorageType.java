package com.datalink.fdop.quality.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2023/6/13 15:43
 */
public enum DataQualityStorageType {

    // 表
    TABLE(0, "TABLE"),
    // 视图
    VIEW(1, "VIEW"),
    // 实体
    ENTITY(2, "ENTITY"),
    ;

    DataQualityStorageType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final int code;
    @EnumValue
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

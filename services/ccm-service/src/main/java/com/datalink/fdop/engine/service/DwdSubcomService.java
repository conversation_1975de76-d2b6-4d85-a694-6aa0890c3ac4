package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.engine.api.domain.DwdSubcom;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdSubcomService extends IService<DwdSubcom> {

    PageDataInfo<DwdSubcom> overview(String productId, String sort, SearchVo searchVo);

    List<DwdSubcom> selectNoPage(String productId, String sort, SearchVo searchVo);
}
package com.datalink.fdop.auth.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.core.web.page.TableDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.auth.service.DataElementRelationService;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.model.DataElementTree;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/7 14:55
 */
@RestController
@RequestMapping("/management/data/element")
public class DataElementRelationController extends BaseController {

    @Autowired
    public DataElementRelationService dataElementRelationService;


    @ApiOperation(value = "保存权限对象与数据元素的关系")
    @Log(title = "数据管理模块", businessType = BusinessType.INSERT)
    @PostMapping(value = "/save")
    public R save(@RequestBody List<Long> dataElemIds, @RequestParam Long odjId) {
        return dataElementRelationService.save(dataElemIds,odjId);
    }

    @ApiOperation(value = "删除权限对象与数据元素的关系")
    @Log(title = "数据管理模块", businessType = BusinessType.DELETE)
    @PostMapping(value = "/del")
    public R del(@RequestBody List<Long> dataElemIds, @RequestParam Long odjId) {
        return dataElementRelationService.del(dataElemIds,odjId);
    }

    @ApiOperation(value = "查询权限对象绑定的数据元素")
    @Log(title = "数据管理模块", businessType = BusinessType.OTHER)
    @PostMapping(value = "/queryBindingElement" )
    public R<PageDataInfo<DataElement>> queryBindingElement( @RequestParam Long odjId,@RequestParam(required = false) String code ,@RequestParam(required = false) String name){
        PageDataInfo<DataElement> list =dataElementRelationService.queryBindingElement(odjId,code,name);
        return R.ok(list);
    }


    @ApiOperation(value = "查询权限对象未绑定的数据元素")
    @Log(title = "数据管理模块", businessType = BusinessType.OTHER)
    @PostMapping(value = "/queryNotBindingElement" )
    public TableDataInfo queryNotBindingElement( @RequestParam Long odjId,@RequestParam String code ,@RequestParam String name){
        startPage();
        List<DataElement> list =dataElementRelationService.queryNotBindingElement(odjId,code,name);
        return getDataTable(list);
    }

    @ApiOperation(value = "保存内置元素与元素关系")
    @Log(title = "数据管理模块", businessType = BusinessType.INSERT)
    @PostMapping(value = "/saveInlayElementRetation")
    public R saveInlayElementRetation(@RequestBody List<Long> dataElemIds, @RequestParam Long inlayElementId, @RequestParam Long odjId) {
        return dataElementRelationService.saveInlayElementRetation(dataElemIds,odjId,inlayElementId);
    }

    @ApiOperation(value = "获取内置元素的元素")
    @Log(title = "数据管理模块", businessType = BusinessType.INSERT)
    @PostMapping(value = "/getElement")
    public R getElement(@RequestParam Long odjId) {
        return R.ok(dataElementRelationService.getElement(odjId));
    }

    @ApiOperation(value = "删除内置元素与元素关系")
    @Log(title = "数据管理模块", businessType = BusinessType.DELETE)
    @PostMapping(value = "/delRatetion")
    public R delInlayElementRatetion(@RequestBody List<Long> dataElemIds, @RequestParam Long inlayElementId, @RequestParam Long odjId) {
        return dataElementRelationService.delInlayElementRetationl(dataElemIds,odjId,inlayElementId);
    }

    @ApiOperation(value = "展示主数据和内置元素树")
    @Log(title = "数据管理模块", businessType = BusinessType.DELETE)
    @PostMapping(value = "/showMainTree")
    public R showMainTree(@RequestParam(required = false, defaultValue = "DESC") String sort, @RequestParam(required = false) String code){
        List<DataElementTree> list=dataElementRelationService.showMainTree(sort,code);
        return R.ok(list);
    }

    @ApiOperation(value = "展示主数据和字段树")
    @Log(title = "数据管理模块", businessType = BusinessType.DELETE)
    @PostMapping(value = "/showMainFieldTree")
    public R showMainFieldTree(@RequestParam(required = false, defaultValue = "DESC") String sort, @RequestParam(required = false) String code){
        List<DataElementTree> list=dataElementRelationService.showMainFieldTree(sort,code);
        return R.ok(list);
    }


}

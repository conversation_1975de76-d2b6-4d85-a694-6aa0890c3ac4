<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CcmYieldMapper">

    <delete id="deleteBykey">
        delete from zjdata.ccm_yield
        WHERE plant_id = #{plantId} and product_id = #{productId} and date_from = '${dateFrom}'
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_yield
        WHERE plant_id = #{plantId} and product_id = #{productId} and date_from = '${dateFrom}'
        limit 1
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CcmYield">
        update zjdata.ccm_yield
        set
            <if test="productDesc != null">product_desc = #{productDesc},</if>
            <if test="productCimId != null">product_cim_id = #{productCimId},</if>
            <if test="productErpId != null">product_erp_id = #{productErpId},</if>
            <if test="yield != null ">yield = #{yield},</if>
            <if test="comments != null">comments = #{comments},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE plant_id = #{plantId} and product_id = #{productId} and date_from = '${dateFrom}'
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.CcmYield">
        SELECT a.*
        FROM zjdata.ccm_yield a
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productId != null and productId != ''">
                and a.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.plant_id, a.product_id,
                 a.date_from
                 ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_yield
        SET enable = #{enable}
        where (plant_id, product_id, date_from ) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.plantId},#{item.productId},'${item.dateFrom}')
        </foreach>
    </update>

    <select id="selectProductList" resultType="com.datalink.fdop.base.api.domain.ProductVo">
        SELECT
        node.product_id as productId,
        node.product_desc as  productDesc,
        node.product_cim_id as  productCimId
        FROM zjdata.ccm_yield node
        <where>
            AND node.product_id is not null and node.product_id &lt;> ''
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="productId != null and productId != ''">
                and node.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productDesc != null and productDesc != ''">
                AND node.product_desc LIKE CONCAT('%', #{productDesc}, '%')
            </if>
            <if test="productCimId != null and productCimId != ''">
                AND node.product_cim_id LIKE CONCAT('%', #{productCimId}, '%')
            </if>
        </where>
        ORDER BY node.product_id,node.product_desc,node.product_cim_id ${sort}
    </select>
    <select id="selectNoPage" resultType="com.datalink.fdop.base.api.domain.CcmYield">
        SELECT a.*
        FROM zjdata.ccm_yield a
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productId != null and productId != ''">
                and a.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.plant_id, a.product_id,
        a.date_from
        ${sort}
    </select>

</mapper>


<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.StructureItemMapper">

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_cost_structure_item
        WHERE chartofa =#{chartofa}
        and cost_element_fr =#{costElementFr}
        and cost_element_split =#{costElementSplit}
        and structure_ver_id =#{structureVerId}
        and cost_structure_id =#{costStructureId}
        limit 1
    </select>

    <select id="listAll" resultType="java.lang.String">
        select  attribute_id from zjdata.ccm_cost_structure_item
        <where>
            <if test="attributeId !=null and attributeId !=''">
                attribute_id =#{attributeId}
            </if>
        </where>
    </select>

    <delete id="deleteByIds" parameterType="String">
        delete from zjdata.ccm_cost_structure_item
        WHERE (chartofa,cost_element_fr,cost_element_split,structure_ver_id,cost_structure_id) IN
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            (#{item.chartofa},#{item.costElementFr},#{item.costElementSplit},#{item.structureVerId},#{item.costStructureId})
        </foreach>
    </delete>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_cost_structure_item
        SET enable = #{enable}
        WHERE (chartofa,cost_element_fr,cost_element_split,structure_ver_id,cost_structure_id) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.chartofa},#{item.costElementFr},#{item.costElementSplit},
            #{item.structureVerId},#{item.costStructureId}
            )
        </foreach>
    </update>

    <update id="update">
        UPDATE zjdata.ccm_cost_structure_item
        set
        <if test="costElementTo != null ">cost_element_to =#{costElementTo},</if>
        <if test="enable != null ">enable =#{enable},</if>
        update_by =#{updateBy},update_time=#{updateTime}
        WHERE chartofa =#{chartofa}
        and cost_element_fr =#{costElementFr}
        and cost_element_split =#{costElementSplit}
        and structure_ver_id =#{structureVerId}
        and cost_structure_id =#{costStructureId}
    </update>

    <select id="pageList" resultType="com.datalink.fdop.base.api.domain.StructureItem">
        select a.*,b.structure_ver_desc as structure_ver_desc,c.cost_structure_desc as cost_structure_desc from zjdata.ccm_cost_structure_item  a
        left join zjdata.ccm_structure_ver b on a.structure_ver_id = b.structure_ver_id
        left join zjdata.ccm_cost_structure_head c on a.cost_structure_id = c.cost_structure_id
        <where>
            <if test="searchVo !=null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("a",searchVo)})
            </if>
        </where>
        order by a.chartofa,a.cost_element_fr,
                 a.cost_element_split,a.structure_ver_id,
                 a.cost_structure_id
        <if test="sort != null and sort != ''">
            ${sort}
        </if>
    </select>

</mapper>


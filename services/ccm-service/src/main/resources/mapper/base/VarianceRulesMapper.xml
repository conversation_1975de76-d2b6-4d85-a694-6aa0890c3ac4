<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.VarianceRulesMapper">

    <select id="query" resultType="com.datalink.fdop.base.api.domain.VarianceRules">
        select a.*, controlArea.control_area_desc as control_area_desc
        from zjdata.ccm_variance_rules a
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
        where a.del_flag = false and controlArea.del_flag = false
        <if test="searchVo != null and searchVo != ''">
            and ${searchVo}
        </if>
        order by control_area_id, variance_source, variance_goes, evaluate_sort_group ${sort}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.VarianceRules">
        select a.*, controlArea.control_area_desc as control_area_desc
        from zjdata.ccm_variance_rules a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
        where a.del_flag = false and controlArea.del_flag = false
          and a.control_area_id = #{controlAreaId} and a.variance_source = #{varianceSource}
          and a.variance_goes = #{varianceGoes} and a.evaluate_sort_group = #{evaluateSortGroup}
        limit 1
    </select>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.VarianceRules">
        select a.*, controlArea.control_area_desc as control_area_desc
        from zjdata.ccm_variance_rules a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
        where a.del_flag = false and controlArea.del_flag = false
        order by control_area_id, variance_source, variance_goes, evaluate_sort_group desc
    </select>

    <update id="update" parameterType="com.datalink.fdop.base.api.domain.VarianceRules">
        update zjdata.ccm_variance_rules
        set
        <if test="drAccount != null">dr_account = #{drAccount},</if>
        <if test="drCostCenter != null">dr_cost_center = #{drCostCenter},</if>
        <if test="drWbs != null">dr_wbs = #{drWbs},</if>
        <if test="crAccount != null">cr_account = #{crAccount},</if>
        <if test="crCostCenter != null">cr_cost_center = #{crCostCenter},</if>
        <if test="crWbs != null">cr_wbs = #{crWbs},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId} and variance_source = #{varianceSource}
        and variance_goes = #{varianceGoes} and evaluate_sort_group = #{evaluateSortGroup}
        and del_flag = false
    </update>

    <update id="delete" parameterType="com.datalink.fdop.base.api.domain.VarianceRules">
        update zjdata.ccm_variance_rules
        set del_flag = true
        where del_flag = false
          and control_area_id = #{controlAreaId} and variance_source = #{varianceSource}
          and variance_goes = #{varianceGoes} and evaluate_sort_group = #{evaluateSortGroup}
    </update>

</mapper>

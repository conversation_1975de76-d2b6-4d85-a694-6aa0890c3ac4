package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewActualExp;

import java.util.List;

public interface DwsNewActualExpService extends IService<DwsNewActualExp> {

    PageDataInfo<DwsNewActualExp> overview(String verId, String companyId, String yearMonth, String sort, SearchVo searchVo);

    List<DwsNewActualExp> selectNoPage(String verId, String companyId, String yearMonth, String sort, SearchVo searchVo);
}
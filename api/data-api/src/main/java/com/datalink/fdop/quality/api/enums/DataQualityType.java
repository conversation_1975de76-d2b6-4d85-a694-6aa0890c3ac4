package com.datalink.fdop.quality.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @Date 2022/10/11 10:28
 */
public enum DataQualityType {

    // 元素
    ELEMENT(0, "ELEMENT"),
    // 单实体
    ONE_ENTITY(1, "ONE_ENTITY"),
    // 多实体
    MORE_ENTITY(2, "MORE_ENTITY"),
    ;

    DataQualityType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final int code;
    @EnumValue
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

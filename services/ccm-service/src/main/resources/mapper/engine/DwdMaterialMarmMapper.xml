<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwdMaterialMarmMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwdMaterialMarm">
        SELECT *
        FROM dwd.dwd_material_marm node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="materialId != null and materialId != ''">
                AND node.material_id IN
                <foreach item="item" collection="materialId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.material_id,node.usage_unit ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwdMaterialMarm">
        SELECT *
        FROM dwd.dwd_material_marm node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="materialId != null and materialId != ''">
                AND node.material_id IN
                <foreach item="item" collection="materialId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.material_id,node.usage_unit ${sort}
    </select>
</mapper>


package com.datalink.fdop.element.api.domain;

import com.datalink.fdop.element.api.enums.EntityType;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 实体血缘信息
 */
@Data
public class DataEntityBlood {

    @ApiModelProperty(value = "实体主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "实体编码")
    private String code;

    @ApiModelProperty(value = "实体名称")
    private String name;

    @ApiModelProperty(value = "实体类型")
    private EntityType entityType;

    @ApiModelProperty(value = "DWD/DIM大模型ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    @ApiModelProperty(value = "DWD/DIM编码")
    private String tableCode;

    @ApiModelProperty(value = "DWD/DIM名称")
    private String tableName;

    @ApiModelProperty(value = "实体结构")
    private List<DataEntityStructureVo> dataEntityStructures;

    @ApiModelProperty(value = "治理状态")
    private String governStatus;

}

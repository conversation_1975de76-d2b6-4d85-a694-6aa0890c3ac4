package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsChgFlow;

import java.util.List;

public interface DwsChgFlowService extends IService<DwsChgFlow> {

    PageDataInfo<DwsChgFlow> overview(
            String verId,String  plantId,String productId,
            String sort, SearchVo searchVo);

    List<DwsChgFlow> selectNoPage(String verId,
                                  String plantId,
                                  String productId,
                                  String sort,
                                  SearchVo searchVo);
}

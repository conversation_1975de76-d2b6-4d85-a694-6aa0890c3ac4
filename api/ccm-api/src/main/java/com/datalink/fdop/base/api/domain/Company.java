package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022-09-05 10:35
 */
@Data
@TableName(value = "ccm_company",schema = "zjdata")
@NoArgsConstructor
public class Company {

    @NotBlank(message = "公司代码不能为空")
    @Excel(name = "公司代码")
    @ApiModelProperty(value = "公司代码", required = true)
    @TableId
    private String companyId;

    @Excel(name = "公司代码描述")
    @ApiModelProperty(value = "公司代码描述", required = true)
    private String companyDesc;

    @Excel(name = "公司代码文本")
    @ApiModelProperty(value = "公司代码文本", required = true)
    private String companyText;

    @Excel(name = "注册地址")
    @ApiModelProperty(value = "注册地址", required = true)
    private String address;

    @Excel(name = "管理范围")
    @ApiModelProperty(value = "管理范围")
    private String controlAreaId;

    @TableField(exist = false)
    @ApiModelProperty(value = "管理范围描述")
    private String controlAreaDesc;

    @Excel(name = "货币")
    @ApiModelProperty(value = "货币", required = true)
    private String currency;

    @Excel(name = "是否启用", readConverterExp = "true=启用,false=不启用")
    @ApiModelProperty(value = "是否启用（true启用 false不启用）", required = true)
    private Boolean enable;

    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

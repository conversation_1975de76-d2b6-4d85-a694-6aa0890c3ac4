<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.project.mapper.ProcessingSingleMapper">

    <resultMap id="BaseResultMap" type="com.datalink.fdop.project.api.domain.ProcessingSingle">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="turnkeyProcess" column="turnkey_process" jdbcType="VARCHAR"/>
            <result property="serialNum" column="serial_num" jdbcType="VARCHAR"/>
            <result property="process" column="process" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,turnkey_process,serial_num,
        process
    </sql>
</mapper>

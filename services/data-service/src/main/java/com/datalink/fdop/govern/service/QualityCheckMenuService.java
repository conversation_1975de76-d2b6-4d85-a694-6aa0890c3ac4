package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.QualityCheck;
import com.datalink.fdop.govern.api.domain.QualityCheckMenu;
import com.datalink.fdop.govern.api.domain.QualityCheckMenuTree;
import com.datalink.fdop.govern.api.domain.QualityCheckPlan;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface QualityCheckMenuService {

    int create(QualityCheckMenu SeaTunnelCmdMenu);

    int createPlan(QualityCheckPlan qualityCheckPlan);

    int update(QualityCheckMenu SeaTunnelCmdMenu);

    List<QualityCheckMenuTree> tree(String sort, String code, Boolean isQueryNode);

    int delete(List<Long> ids);

    PageDataInfo<QualityCheckMenu> overview(Long pid, String sort, SearchVo searchVo);

    PageDataInfo<QualityCheck> overviewNode(Long pid, String sort, SearchVo searchVo);

}

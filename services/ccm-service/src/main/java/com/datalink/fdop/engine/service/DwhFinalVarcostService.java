package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalVarcost;

import java.util.List;

public interface DwhFinalVarcostService extends IService<DwhFinalVarcost> {

    PageDataInfo<DwhFinalVarcost> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwhFinalVarcost> listAll();

    List<DwhFinalVarcost> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);
}
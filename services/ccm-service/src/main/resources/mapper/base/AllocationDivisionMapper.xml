<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.AllocationDivisionMapper">

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.AllocationDivision">
        update zjdata.ccm_allocation_division
        set
            <if test="allocationSegmentId != null">allocation_segment_id = #{allocationSegmentId},</if>
            <if test="allocationSegmentDesc != null">allocation_segment_desc = #{allocationSegmentDesc},</if>
            <if test="allocationSegmentText != null">allocation_segment_text = #{allocationSegmentText},</if>
            <if test="allocationMethodId != null">allocation_method_id = #{allocationMethodId},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE control_area_id = #{controlAreaId} AND keyid = #{keyId}
    </update>

    <update id="deleteByKey" parameterType="com.datalink.fdop.base.api.domain.AllocationDivision">
        delete from zjdata.ccm_allocation_division
        where (control_area_id, keyid) in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.keyId})
        </foreach>
    </update>

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.AllocationDivision" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM zjdata.ccm_allocation_division WHERE control_area_id = #{controlAreaId} AND keyid = #{keyId}
    </select>

    <select id="list" resultType="com.datalink.fdop.base.api.domain.AllocationDivision">
        select * from zjdata.ccm_allocation_division
        where del_flag = false
        <if test="allocationDivision.controlAreaId != null and allocationDivision.controlAreaId != ''">and control_area_id = #{allocationDivision.controlAreaId}</if>
        <if test="allocationDivision.allocationMethodId != null and allocationDivision.allocationMethodId != ''">and allocation_method_id = #{allocationDivision.allocationMethodId}</if>
        <if test="allocationDivision.allocationSegmentId != null and allocationDivision.allocationSegmentId != ''">and allocation_segment_id = #{allocationDivision.allocationSegmentId}</if>
        <if test="allocationDivision.allocationSegmentDesc != null and allocationDivision.allocationSegmentDesc != ''">and allocation_segment_desc = #{allocationDivision.allocationSegmentDesc}</if>
        <if test="allocationDivision.allocationSegmentText != null and allocationDivision.allocationSegmentText != ''">and allocation_segment_text = #{allocationDivision.allocationSegmentText}</if>
        <if test="allocationDivision.enable != null">and enable = #{allocationDivision.enable},</if>
        order by control_area_id, allocation_method_id, allocation_segment_id desc
    </select>

    <sql id="selectListSql">
        SELECT
            node.*
        from
            zjdata.ccm_allocation_division node
        <where>
            <if test="allocationMethodId != null and allocationMethodId != ''">
                AND node.allocation_method_id = #{allocationMethodId}
            </if>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        order by keyid ${sort}
    </sql>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.AllocationDivision">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.AllocationDivision">
        <include refid="selectListSql"/>
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_allocation_division
        SET enable = #{enable}
        where (control_area_id, keyid) in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.keyId})
        </foreach>
    </update>

    <select id="selectNodeTree" resultType="com.datalink.fdop.base.api.model.AllocationMethodTree">
        select
            keyid as id,
            control_area_id as control_area_id,
            allocation_method_id as pid,
            'NODE' as menuType,
            allocation_segment_id as name,
            allocation_segment_desc as description
        from
            zjdata.ccm_allocation_division
        <where>
            <if test="controlAreaId != null and controlAreaId != ''">
                and control_area_id = #{controlAreaId}
            </if>
            <if test="code != null and code != ''">
                and allocation_segment_id = #{code}
            </if>
        </where>
        order by keyid ${sort}
    </select>


</mapper>
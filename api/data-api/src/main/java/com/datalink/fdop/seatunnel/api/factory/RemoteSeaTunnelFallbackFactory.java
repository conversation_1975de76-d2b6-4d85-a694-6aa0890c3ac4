package com.datalink.fdop.seatunnel.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.seatunnel.api.RemoteSeaTunnelService;
import com.datalink.fdop.seatunnel.api.domain.DataModelOptions;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelField;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelRunConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class RemoteSeaTunnelFallbackFactory implements FallbackFactory<RemoteSeaTunnelService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteSeaTunnelFallbackFactory.class);

    @Override
    public RemoteSeaTunnelService create(Throwable throwable) {
        log.error("SeaTunnel服务调用失败:{}", throwable.getMessage());
        return new RemoteSeaTunnelService() {

            @Override
            public R<DataSource> getDataModelOptionsDataSource(DataModelOptions options) {
                return R.fail("获取数据模型数据源信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SeaTunnelField>> getDataModelOptionsFields(DataModelOptions options) {
                return R.fail("获取数据模型的列信息失败:" + throwable.getMessage());
            }

            @Override
            public R<String> generateSeaTunnelRunConfig(SeaTunnelRunConfig runConfig) {
                return R.fail("获取SeaTunnel运行配置失败:" + throwable.getMessage());
            }

            @Override
            public R<String> generateSeaTunnelRunConfigSinkFile(SeaTunnelRunConfig runConfig) {
                return R.fail("获取SeaTunnel运行配置失败:" + throwable.getMessage());
            }

            @Override
            public R<String> transformPlaceholderSeaTunnelRunConfig(SeaTunnelRunConfig runConfig) {
                return R.fail("SeaTunnel运行配置通配符转换失败:" + throwable.getMessage());
            }
        };
    }
}

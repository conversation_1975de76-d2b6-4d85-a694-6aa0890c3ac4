package com.datalink.fdop.element.api;

import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.factory.RemoteEntityStrureFallbackFactory;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/17 15:13
 */
@FeignClient(contextId = "remoteEntityStrureService", path = "/element/entity/structure/",
        value = ServiceNameConstants.DATA_SERVICE, fallbackFactory = RemoteEntityStrureFallbackFactory.class)
public interface RemoteEntityStrureService {

    @GetMapping(value = "/{dataEntityId}/selectStructureById")
    public R<List<DataEntityStructureVo>> selectStructureById(@PathVariable(value = "dataEntityId") Long dataEntityId);

    @GetMapping(value = "/{dataEntityId}/selectStructureByIdAndTenantId/{tenantId}")
    public R<List<DataEntityStructureVo>> selectStructureByIdAndTenantId(@PathVariable(value = "tenantId") Long tenantId, @PathVariable(value = "dataEntityId") Long dataEntityId);

}

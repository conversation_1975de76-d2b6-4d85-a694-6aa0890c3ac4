package com.datalink.fdop.quality.api.domain;

import cn.hutool.core.text.UnicodeUtil;
import com.datalink.fdop.quality.api.enums.RegexRuleCiteType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2022/5/17 17:30
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RegexValue {

    @ApiModelProperty(value = "引用类型")
    private RegexRuleCiteType citeType;

    @ApiModelProperty(value = "正则表达式的值")
    private String regexValue;

    @JsonIgnore
    public String getAgeStr() {
        return "{ citeType: '" + this.getCiteType() + "'" +
                ", regexValue: '" + this.getRegexValue() + "'" +
                "}";
    }

    public RegexRuleCiteType getCiteType() {
        return citeType;
    }

    public void setCiteType(RegexRuleCiteType citeType) {
        this.citeType = citeType;
    }

    public String getRegexValue() {
        regexValue= UnicodeUtil.toUnicode(regexValue);
        return regexValue;
    }

    public void setRegexValue(String regexValue) {
        regexValue= UnicodeUtil.toUnicode(regexValue);
        this.regexValue = regexValue;
    }
}

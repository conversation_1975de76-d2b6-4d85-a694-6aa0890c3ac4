<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.StructureGroupMapper">

    <delete id="deleteByKey">
        delete from zjdata.ccm_cost_structure_group
        where cost_structure_group_id in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            #{item.costStructureGroupId}
        </foreach>
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_cost_structure_group
        where cost_structure_group_id =#{costStructureGroupId} limit 1
    </select>

    <select id="selectIdsByPid" resultType="com.datalink.fdop.base.api.domain.StructureGroup">
        select cost_structure_group_id from zjdata.ccm_cost_structure_group where menu_id = #{pid}
    </select>

    <update id="bacthUpdatePidById">
        update zjdata.ccm_cost_structure_group
        set menu_id = (select p_menu_id from zjdata.ccm_cost_structure_group_menu where menu_id = #{menuId} limit 1)
        where menu_id = #{menuId}
    </update>

    <sql id="selectListSql">
        select
        node.*,
        menu.cost_structure_group_desc as menu_desc
        from
        zjdata.ccm_cost_structure_group node
        left join zjdata.ccm_cost_structure_group menu on node.menu_id = menu.menu_id
        <where>
            <if test="menuId != null and menuId != '-1'">
                AND node.menu_id = #{menuId}
            </if>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY node.cost_structure_group_id ${sort}
    </sql>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.StructureGroup">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.StructureGroup">
        <include refid="selectListSql"/>
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_cost_structure_group
        SET enable = #{enable}
        where cost_structure_group_id in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            #{item.costStructureGroupId}
        </foreach>
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.StructureGroupTree">
        select cost_structure_group_id as id,
               menu_id as pid,
               'MENU' as menuType,
               cost_structure_group_text as name,
               cost_structure_group_desc as description
        from zjdata.ccm_cost_structure_group
        ORDER BY cost_structure_group_id ${sort}
    </select>

    <select id="listCostLevel" resultType="com.datalink.fdop.base.api.domain.StructureGroup">
        select * from zjdata.ccm_cost_structure_group
        where enable =true
        <if test="costStructureGroupId !=null">
            and cost_structure_group_id =#{costStructureGroupId}
        </if>
        <if test="costLevel !=null">
            and cost_level =#{costLevel}
        </if>
    </select>

    <update id="batchUpdatePidByIds">
        update zjdata.cost_structure_group_id
        set menu_id = #{pid}
        where cost_structure_group_id in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            #{item.costStructureGroupId}
        </foreach>
    </update>

</mapper>


package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwsNewRateStructureP;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwsNewRateStructurePService;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.utils.MapUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/base/dwsNewRateStructureP")
@Api(tags = "逻辑视图-组件费率")
public class DwsNewRateStructurePController extends BaseController {

    @Autowired
    private DwsNewRateStructurePService dwsNewRateStructurePService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "逻辑视图-组件费率")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params , "controlAreaId");
        String verId = MapUtils.getAsString(params , "verId");
        String companyId = MapUtils.getAsString(params , "companyId");
        String costCenterType = MapUtils.getAsString(params , "costCenterType");
        String yearMonthFrom = MapUtils.getAsString(params , "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params , "yearMonthTo");

        PageDataInfo<DwsNewRateStructureP> overview = dwsNewRateStructurePService.overview(
                controlAreaId, verId, companyId, costCenterType, yearMonthFrom, yearMonthTo, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwsNewRateStructureP.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "逻辑视图-组件费率", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params , "controlAreaId");
        String verId = MapUtils.getAsString(params , "verId");
        String companyId = MapUtils.getAsString(params , "companyId");
        String costCenterType = MapUtils.getAsString(params , "costCenterType");
        String yearMonthFrom = MapUtils.getAsString(params , "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params , "yearMonthTo");
        List<DwsNewRateStructureP> list = dwsNewRateStructurePService.selectNoPage(controlAreaId,verId,companyId,
                costCenterType,yearMonthFrom,yearMonthTo,sort,queryParam.getSearchVo());
        ExcelUtil<DwsNewRateStructureP> util = new ExcelUtil<>(DwsNewRateStructureP.class);
        util.exportExcel(response, list, "组件费率");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsNewRateStructureP> util = new ExcelUtil<>(DwsNewRateStructureP.class);
        util.importTemplateExcel(response, "组件费率");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dws.dws_new_rate_structure_p", null));
    }

    @ApiOperation(value = "管理范围（SelectVo）")
    @GetMapping("/manageScope")
    public R<List<SelectVo>> manageScope() {
        return R.ok(dataDictionaryService.listManageScope("dws.dws_new_rate_structure_p", null));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId() {
        return R.ok(dataDictionaryService.listCompanyId("dwh.dwh_final_allocation_factor_p", null));
    }

    @ApiOperation(value = "成本中心类型（下拉框）")
    @GetMapping("/costCenterType")
    public R<List<String>> costCenterType() {
        return R.ok(dwsNewRateStructurePService.lambdaQuery()
                .select(DwsNewRateStructureP::getCostCenterType)
                .groupBy(DwsNewRateStructureP::getCostCenterType)
                .orderByAsc(DwsNewRateStructureP::getCostCenterType).list().stream()
                .filter(dwsNewRateStructureP -> dwsNewRateStructureP != null && dwsNewRateStructureP.getCostCenterType() != null)
                .map(DwsNewRateStructureP::getCostCenterType).collect(Collectors.toList()));
    }

}
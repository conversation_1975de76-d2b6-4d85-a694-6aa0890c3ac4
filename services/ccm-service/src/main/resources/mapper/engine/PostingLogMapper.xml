<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.PostingLogMapper">

    <select id="queryHead" resultType="com.datalink.fdop.engine.api.domain.PostingLogHead2">
        select a.* from fccm.posting_log_head a
        where a.ver_id = '${verId}' and a.company_id = '${companyId}'
        and a.del_flag != true and a.posting_date = date('${postingDate}')
        <if test="actionFlag != null and actionFlag != ''">and a.action_flag = '${actionFlag}'</if>
        order by document_id, posting_date ${sort}
    </select>

    <select id="queryItem" resultType="com.datalink.fdop.engine.api.domain.PostingLogItem2">
        select a.* from fccm.posting_log_item a
        where a.del_flag != true
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        order by document_id, posting_item_code ${sort}
    </select>

    <select id="selectDocumentId" resultType="java.lang.Long">
        select document_id from fccm.posting_log_head
        where del_flag != true and document_id > ${prefix} and document_id &lt; ${suffix}
        order by document_id desc limit 1
    </select>

    <insert id="addHead">
        insert into fccm.posting_log_head
        values (${sqlString})
    </insert>

    <insert id="addItem">
        insert into fccm.posting_log_item
        values (${sqlString})
    </insert>

    <select id="selectPostingItemCode" resultType="java.lang.Long">
        select posting_item_code from fccm.posting_log_item
        where del_flag != true and document_id = ${documentId} order by posting_item_code desc limit 1
    </select>

    <update id="updateHead" parameterType="com.datalink.fdop.engine.api.domain.PostingLogHead2">
        update fccm.posting_log_head
        set
        <if test="verId != null">ver_id = '${verId}',</if>
        <if test="year != null">year = ${year},</if>
        <if test="month != null">month = ${month},</if>
        <if test="documentType != null">document_type = '${documentType}',</if>
        <if test="documentCode != null">document_code = '${documentCode}',</if>
        <if test="documentDate != null">document_date = date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(documentDate)}'),</if>
        <if test="postingDate != null">posting_date = date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(postingDate)}'),</if>
        <if test="controlAreaId != null">control_area_id = '${controlAreaId}',</if>
        <if test="controlAreaCurrency != null">control_area_currency = '${controlAreaCurrency}',</if>
        <if test="companyId != null">company_id = '${companyId}',</if>
        <if test="companyCurrency != null">company_currency = '${companyCurrency}',</if>
        <if test="documentCurrency != null">document_currency = '${documentCurrency}',</if>
        <if test="textHead != null">text_head = '${textHead}',</if>
        <if test="actionFlag != null">action_flag = '${actionFlag}',</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time = to_timestamp('${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}','yyyy-mm-dd hh24:mi:ss')
        where document_id = ${documentId} and del_flag != true
    </update>

    <update id="updateItem" parameterType="com.datalink.fdop.engine.api.domain.PostingLogItem2">
        update fccm.posting_log_item
        set
        <if test="dC != null">d_c = '${dC}',</if>
        <if test="postingItemCode != null">posting_item_code = ${postingItemCode},</if>
        <if test="generalAccountId != null">general_account_id = '${generalAccountId}',</if>
        <if test="generalAccountDesc != null">general_account_desc = '${generalAccountDesc}',</if>
        <if test="amountDocument != null">amount_document = ${amountDocument},</if>
        <if test="currencyDocument != null">currency_document = '${currencyDocument}',</if>
        <if test="amountCompany != null">amount_company = ${amountCompany},</if>
        <if test="currencyCompany != null">currency_company = '${currencyCompany}',</if>
        <if test="amountControlArea != null">amount_control_area = ${amountControlArea},</if>
        <if test="currencyControlArea != null">currency_control_area = '${currencyControlArea}',</if>
        <if test="factoryId != null">factory_id = '${factoryId}',</if>
        <if test="factoryDesc != null">factory_desc = '${factoryDesc}',</if>
        <if test="costCenterId != null">cost_center_id = '${costCenterId}',</if>
        <if test="costCenterDesc != null">cost_center_desc = '${costCenterDesc}',</if>
        <if test="wbsId != null">wbs_id = '${wbsId}',</if>
        <if test="wbsDesc != null">wbs_desc = '${wbsDesc}',</if>
        <if test="text != null">text = '${text}',</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time = to_timestamp('${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}','yyyy-mm-dd hh24:mi:ss')
        where document_id = ${documentId} and posting_item_code = ${postingItemCode} and del_flag != true
    </update>

    <update id="delItem" parameterType="com.datalink.fdop.engine.api.domain.PostingLogItem2">
        update fccm.posting_log_item
        set del_flag = true
        where document_id = ${documentId} and posting_item_code = ${postingItemCode} and del_flag != true
    </update>

    <update id="updateStatus">
        update fccm.posting_log_head
        set action_flag = '${actionFlag}'
        where document_id = ${id} and del_flag != true
    </update>

</mapper>
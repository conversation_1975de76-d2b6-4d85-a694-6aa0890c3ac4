package com.datalink.fdop.auth.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/5/10 14:40
 */
@TableName("d_m_menu_detailed")
@Data
@ApiModel("菜单明细")
public class MenuDetailed extends BaseField implements Serializable {


    private static final long serialVersionUID = 6194647884370401322L;

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "匹配符号")
    private String matchingPattern;

    @ApiModelProperty(value = "是否包含")
    private Boolean containIs;

    @ApiModelProperty(value = "匹配值")
    private String matchingValue;

    @Override
    public String toString() {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{");
        if (getId() != null) ageString.append("id: " + getId() + ",");
        if (StringUtils.isNotEmpty(getMatchingPattern())) ageString.append("matchingPattern: '" + getMatchingPattern() + "',");
        if (getContainIs()!=null) ageString.append("containIs: '" + getContainIs() + "',");
        if (StringUtils.isNotEmpty(getMatchingPattern())) ageString.append("matchingValue: '" + getMatchingValue() + "',");
        String string = ageString.toString();
        if (string.endsWith(",")){
            string=string.substring(0,string.length()-1);
        }
        string+="}";
        return string;
    }
}

package com.datalink.fdop.gather.api.model.dto;

import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.gather.api.domain.TemplateField;
import com.datalink.fdop.gather.api.enums.ProcessingMode;
import com.datalink.fdop.gather.api.enums.TemplateType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/3 14:56
 */
@Data
public class EntityTemplateDto extends TemplateDataDto {

    private Long dataSourceId;

    private String dataSourceType;

    private DataSource dataSource;

    private String databaseName;

    private String tmpDatabaseName;

    /**
     * 临时表
     */
    private String tmpTableName;

    /**
     * 临时日志表，存放校验异常的数据
     */
    private String tmpLogTableName;

    public EntityTemplateDto() {
    }

    public EntityTemplateDto(String batchId,
                             Long templateId,
                             Long logId,
                             ProcessingMode processingMode,
                             TemplateType templateType,
                             Long dataSourceId,
                             String dataSourceType,
                             DataSource dataSource,
                             String databaseName,
                             String tableName,
                             String tmpDatabaseName,
                             String tmpTableName,
                             List<TemplateField> templateFieldList) {
        super(batchId, templateId, logId, processingMode, templateType, tableName, templateFieldList);
        this.dataSourceId = dataSourceId;
        this.dataSourceType = dataSourceType;
        this.dataSource = dataSource;
        this.databaseName = databaseName;
        this.tmpDatabaseName = tmpDatabaseName;
        this.tmpTableName = tmpTableName;
    }

    public EntityTemplateDto(String batchId,
                             Long templateId,
                             Long logId,
                             ProcessingMode processingMode,
                             TemplateType templateType,
                             Long dataSourceId,
                             String dataSourceType,
                             DataSource dataSource,
                             String databaseName,
                             String tableName,
                             String tmpDatabaseName,
                             String tmpTableName,
                             String tmpLogTableName,
                             List<TemplateField> templateFieldList) {
        super(batchId, templateId, logId, processingMode, templateType, tableName, templateFieldList);
        this.dataSourceId = dataSourceId;
        this.dataSourceType = dataSourceType;
        this.dataSource = dataSource;
        this.databaseName = databaseName;
        this.tmpDatabaseName = tmpDatabaseName;
        this.tmpTableName = tmpTableName;
        this.tmpLogTableName = tmpLogTableName;
    }

}

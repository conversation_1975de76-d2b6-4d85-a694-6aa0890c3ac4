package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRecoverWip;
import com.datalink.fdop.engine.mapper.DwsNewRecoverWipMapper;
import com.datalink.fdop.engine.service.DwsNewRecoverWipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRecoverWipServiceImpl extends ServiceImpl<DwsNewRecoverWipMapper, DwsNewRecoverWip> implements DwsNewRecoverWipService {

    @Autowired
    private DwsNewRecoverWipMapper dwsNewRecoverWipMapper;

    @Override
    public PageDataInfo<DwsNewRecoverWip> overview(String verId,String plantId, String date, String sort, SearchVo searchVo) {
        Page<DwsNewRecoverWip> page = PageUtils.getPage(DwsNewRecoverWip.class);
        IPage<DwsNewRecoverWip> iPage = dwsNewRecoverWipMapper.selectAll(verId,plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRecoverWip> listAll() {
        return dwsNewRecoverWipMapper.listAll();
    }

    @Override
    public List<DwsNewRecoverWip> selectNoPage(String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwsNewRecoverWipMapper.selectNoPage(verId, plantId, date, sort, searchVo);
    }
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewCostOrd;

import java.util.List;

public interface DwsNewCostOrdService extends IService<DwsNewCostOrd> {

    PageDataInfo<DwsNewCostOrd> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwsNewCostOrd> listAll();

    List<DwsNewCostOrd> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);
}
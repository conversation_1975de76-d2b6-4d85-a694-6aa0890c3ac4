package com.datalink.fdop.govern.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "zjdata", value = "d_g_data_standard_temporary")
public class DataStandardTemporary extends DataStandard {


    @ApiModelProperty(value = " 用户id")
    private Long userId;

}

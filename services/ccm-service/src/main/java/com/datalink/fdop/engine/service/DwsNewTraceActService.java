package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.engine.api.domain.DwsNewTraceAct;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwsNewTraceActService extends IService<DwsNewTraceAct> {

    PageDataInfo<DwsNewTraceAct> overview(
            String verId, String plantId,String productId,
            String  dateFrom,String  dateTo,
            String sort, SearchVo searchVo);

    List<DwsNewTraceAct> selectNoPage(String verId,
                                      String plantId,
                                      String productId,
                                      String dateFrom,
                                      String dateTo,
                                      String sort,
                                      SearchVo searchVo);
}

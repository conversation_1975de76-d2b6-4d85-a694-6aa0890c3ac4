<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.AttributeMapper">

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_attribute
        WHERE attribute_id = #{id}
        limit 1
    </select>

    <select id="listAll" resultType="java.lang.String">
        select attribute_id from zjdata.ccm_attribute
        where enable = true
        <if test="attributeId !=null and attributeId !=''">
            and attribute_id =#{attributeId}
        </if>
    </select>

    <delete id="deleteById" parameterType="String">
        delete
        from zjdata.ccm_attribute
        where attribute_id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from zjdata.ccm_attribute
        WHERE attribute_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_attribute
        SET enable = #{enable}
        WHERE attribute_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.attributeId}
        </foreach>
    </update>


    <select id="pageList" resultType="com.datalink.fdop.base.api.domain.Attribute">
        select * from zjdata.ccm_attribute a
        <where>
            <if test="searchVo !=null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("a",searchVo)})
            </if>
        </where>
        order by attribute_id
        <if test="sort != null and sort != ''">
            ${sort}
        </if>
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.base.api.domain.Attribute">
        SELECT *
        FROM zjdata.ccm_attribute
        WHERE attribute_id = #{id}
        limit 1
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_attribute (
        attribute_id, attribute_desc, attribute_text,enable, create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.attributeId},#{item.attributeDesc},#{item.attributeText},
            #{item.enable},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>

    <select id="selectDynamicColumnList" resultType="com.datalink.fdop.common.core.domain.DynamicColumn">
        select attribute_id as columnName, attribute_desc as columnDesc
        from zjdata.ccm_attribute
        where enable = true and attribute_desc is not null
    </select>

</mapper>


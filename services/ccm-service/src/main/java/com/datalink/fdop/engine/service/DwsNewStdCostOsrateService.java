package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostOsrate;

import java.util.List;

public interface DwsNewStdCostOsrateService extends IService<DwsNewStdCostOsrate> {

    PageDataInfo<DwsNewStdCostOsrate> overview(
            String controlAreaId, String productId, Integer batchQty,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo);

    List<DwsNewStdCostOsrate> selectNoPage(String controlAreaId, String productId, Integer batchQty, String verId, String plantId, String date, String sort, SearchVo searchVo);
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostPbom;

import java.util.List;

public interface DwsNewStdCostPbomService extends IService<DwsNewStdCostPbom> {

    PageDataInfo<DwsNewStdCostPbom> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo);

    List<DwsNewStdCostPbom> listAll();

    List<DwsNewStdCostPbom> selectNoPage(String controlAreaId, String productId, String verId, String plantId, String date, String sort, SearchVo searchVo);
}

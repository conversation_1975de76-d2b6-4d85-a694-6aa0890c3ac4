package com.datalink.fdop.settlement.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.settlement.api.domain.PriceDefinition;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.settlement.api.model.vo.PriceSelectVo;
import com.github.pagehelper.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【f_s_price_definition】的数据库操作Mapper
 * @createDate 2023-03-30 15:49:27
 * @Entity com.datalink.fdop.settlement.PriceDefinition
 */
@Mapper
public interface PriceDefinitionMapper extends BaseMapper<PriceDefinition> {

    void execSql(@Param("sql") String sql);

    Page<Map<String, Object>> selectData(@Param("table") String table, @Param("priceSelectVo")  PriceSelectVo priceSelectVo, Page<Map> page);


}





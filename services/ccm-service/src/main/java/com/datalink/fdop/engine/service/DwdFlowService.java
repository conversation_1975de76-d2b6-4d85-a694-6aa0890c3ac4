package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.engine.api.domain.DwdFlow;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdFlowService extends IService<DwdFlow> {

    PageDataInfo<DwdFlow> overview(String plantId,String productId, String sort, SearchVo searchVo);

    List<DwdFlow> selectNoPage(String plantId, String productId, String sort, SearchVo searchVo);

}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.ActualPosted;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:32
 */
public interface ActualPostedService extends IService<ActualPosted> {

    PageDataInfo<ActualPosted> getOrg(String sort, SearchVo searchVo);

    int saveData(ActualPosted actualPosted);

    PageDataInfo<ActualPosted> getEdit(String sort, SearchVo searchVo);

    PageDataInfo<ActualPosted> getAll(String sort, SearchVo searchVo);

    int deleteByList(List<ActualPosted> list);
}

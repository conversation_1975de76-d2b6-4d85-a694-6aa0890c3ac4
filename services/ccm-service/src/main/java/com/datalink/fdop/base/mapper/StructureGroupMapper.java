package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.base.api.domain.StructureGroup;
import com.datalink.fdop.base.api.model.CapacityTree;
import com.datalink.fdop.base.api.model.StructureGroupTree;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StructureGroupMapper extends BaseMapper<StructureGroup> {

    int checkIdUnique(@Param("costStructureGroupId") String costStructureGroupId);

    int deleteByKey(@Param("keyList") List<StructureGroup> keyList);

    List<StructureGroup> selectIdsByPid(String pid);

    int bacthUpdatePidById(@Param("menuId") String menuId);

    IPage<StructureGroup> selectList(IPage<StructureGroup> page, @Param("menuId") String pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    List<StructureGroup> selectListNoPage(@Param("menuId") String pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    boolean updateBatchStatus(@Param("enable") Boolean enable,@Param("keyList") List<StructureGroup> list);

    List<StructureGroupTree> selectMenuTree(@Param("code") String code, @Param("sort") String sort);

    int batchUpdatePidByIds(@Param("keyList") List<StructureGroup> childrenList, @Param("pid") String pid);

    List<StructureGroup> listCostLevel(@Param("costStructureGroupId") String costStructureGroupId, @Param("costLevel") Long costLevel);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.EventRulesMapper">

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.EventRules">
        update zjdata.ccm_event_rules
        set
        <if test="balanceFactor !=null">balance_factor = #{balanceFactor},</if>
        <if test="balanceFactorDesc !=null">balance_factor_desc = #{balanceFactorDesc},</if>
        <if test="checkSign !=null">check_sign = #{checkSign},</if>
        <if test="dcFlag !=null">dc_flag = #{dcFlag},</if>
        <if test="enable !=null">enable = ${enable},</if>
        <if test="settlementType != null">settlement_type = #{settlementType},</if>
        <if test="eventType != null">event_type = #{eventType},</if>
        <if test="forVar != null">for_var = #{forVar},</if>
        <if test="recoryMethod != null">recory_method = #{recoryMethod},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId} and event_code = #{eventCode}
          and change_flag = #{changeFlag}
    </update>

    <select id="list" resultType="com.datalink.fdop.base.api.domain.EventRules">
        select a.* from zjdata.ccm_event_rules a
        <where>
            <if test="searchVo != null ">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
            </if>
        </where>
        order by control_area_id, event_code, change_flag ${sort}
    </select>

    <delete id="deleteByKey" parameterType="com.datalink.fdop.base.api.domain.EventRules">
        delete from zjdata.ccm_event_rules
        where control_area_id = #{controlAreaId} and event_code = #{eventCode}
          and change_flag = #{changeFlag}
    </delete>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.EventRules">
        select * from zjdata.ccm_event_rules
        where control_area_id = #{controlAreaId} and event_code = #{eventCode}
          and change_flag = #{changeFlag} limit 1
    </select>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.EventRules">
        select * from zjdata.ccm_event_rules
        order by control_area_id, event_code, change_flag desc
    </select>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_event_rules
            SET enable = #{item.enable}
            WHERE event_code = #{item.eventCode}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_event_rules (
        control_area_id, event_code, change_flag,balance_factor,balance_factor_desc, dc_flag,check_sign,settlement_type, enable,create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.controlAreaId},#{item.eventCode},#{item.changeFlag},
            #{item.balanceFactor},#{item.balanceFactorDesc},#{item.dcFlag},
            #{item.checkSign},#{item.settlementType},
            #{item.enable},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>

</mapper>


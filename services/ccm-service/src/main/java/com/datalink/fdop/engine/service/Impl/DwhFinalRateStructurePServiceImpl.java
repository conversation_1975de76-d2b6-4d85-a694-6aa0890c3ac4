package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRateStructureP;
import com.datalink.fdop.engine.mapper.DwhFinalRateStructurePMapper;
import com.datalink.fdop.engine.service.DwhFinalRateStructurePService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRateStructurePServiceImpl extends ServiceImpl<DwhFinalRateStructurePMapper, DwhFinalRateStructureP> implements DwhFinalRateStructurePService {

    @Autowired
    private DwhFinalRateStructurePMapper dwhFinalRateStructurePMapper;

    @Override
    public PageDataInfo<DwhFinalRateStructureP> overview(
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String sort,
            SearchVo searchVo) {
        Page<DwhFinalRateStructureP> page = PageUtils.getPage(DwhFinalRateStructureP.class);
        IPage<DwhFinalRateStructureP> iPage = dwhFinalRateStructurePMapper.selectAll(
                controlAreaId, verId, yearMonthFrom, yearMonthTo, page, costCenterType, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRateStructureP> selectNoPage(String controlAreaId, String verId, String yearMonthFrom, String yearMonthTo, String costCenterType, String sort, SearchVo searchVo) {
        return dwhFinalRateStructurePMapper.selectNoPage(controlAreaId, verId, yearMonthFrom, yearMonthTo,
                costCenterType, sort, searchVo);
    }
}
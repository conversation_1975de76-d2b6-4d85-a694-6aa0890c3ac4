package com.datalink.fdop.base.controller;

import com.datalink.fdop.base.api.domain.AllocationSegments;
import com.datalink.fdop.base.service.AllocationSegmentsService;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/allocationSegments")
@Api(tags = "fccm-段规则API")
@Transactional(rollbackFor = Exception.class)
public class AllocationSegmentsController extends BaseController {

    @Autowired
    private AllocationSegmentsService allocationSegmentsService;

    @ApiOperation(value = "保存")
    @Log(title = "段规则管理", businessType = BusinessType.INSERT_OR_UPDATE)
    @PostMapping("/save")
    public R save(@RequestBody AllocationSegments allocationSegments) {
        return R.toResult(allocationSegmentsService.saveAllocationSegments(allocationSegments));
    }


    @ApiOperation(value = "查询段规则详情")
    @GetMapping("/selectByKey")
    public R selectByKey(@RequestParam("controlAreaId") String controlAreaId, @RequestParam("keyId") String keyId) {
        return R.ok(allocationSegmentsService.selectByKey(controlAreaId, keyId));
    }

    @ApiOperation(value = "查询段规则详情接收比例")
    @GetMapping("/selectAllocationScaleList")
    public R<PageDataInfo> selectAllocationScaleList(@RequestParam(value = "controlAreaId") String controlAreaId,
                                                     @RequestParam(value = "keyId") String keyId,
                                                     @RequestParam(value = "sort", required = false, defaultValue = "ASC") String sort) {
        return R.ok(allocationSegmentsService.selectAllocationScaleList(controlAreaId, keyId, sort));
    }

    @ApiOperation(value = "总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "controlAreaId", value = "管理范围", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "keyId", value = "KeyID", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @PostMapping("/overview")
    public R<PageDataInfo> overview(@RequestParam(required = false, value = "controlAreaId") String controlAreaId,
                                    @RequestParam(required = false, value = "keyId") String keyId,
                                    @RequestParam(required = false, defaultValue = "ASC") String sort,
                                    @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(allocationSegmentsService.selectList(controlAreaId, keyId, sort, searchVo));
    }

    @ApiOperation(value = "导出段的规则")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "导出段的规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, value = "controlAreaId") String controlAreaId,
                       @RequestParam(required = false, value = "keyId") String keyId,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) SearchVo searchVo) {
        List<AllocationSegments> list = allocationSegmentsService.selectListNoPage(controlAreaId, keyId, sort, searchVo);
        ExcelUtil<AllocationSegments> util = new ExcelUtil<>(AllocationSegments.class);
        util.exportExcel(response, list, "段的规则数据");
    }

    @ApiOperation(value = "导入段的规则")
    @Log(title = "导入段的规则", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R importData(MultipartFile file, @RequestParam(value = "updateSupport", required = false) Boolean updateSupport) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        if (updateSupport == null) {
            updateSupport = false;
        }
        ExcelUtil<AllocationSegments> util = new ExcelUtil<>(AllocationSegments.class);
        List<AllocationSegments> allocationSegmentList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = allocationSegmentsService.importData(allocationSegmentList, updateSupport, operName);
        return R.ok(message);
    }

    @ApiOperation(value = "导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AllocationSegments> util = new ExcelUtil<>(AllocationSegments.class);
        util.importTemplateExcel(response, "分摊方法数据");
    }

}

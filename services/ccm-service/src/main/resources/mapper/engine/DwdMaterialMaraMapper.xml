<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwdMaterialMaraMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwdMaterialMara">
        SELECT *
        FROM dwd.dwd_material_mara node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="materialId != null and materialId != ''">
                AND node.material_id IN
                <foreach item="item" collection="materialId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialGroup != null and materialGroup != ''">
                AND node.material_group IN
                <foreach item="item" collection="materialGroup.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialType != null and materialType != ''">
                AND node.material_type IN
                <foreach item="item" collection="materialType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.material_id ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwdMaterialMara">
        SELECT *
        FROM dwd.dwd_material_mara node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="materialId != null and materialId != ''">
                AND node.material_id IN
                <foreach item="item" collection="materialId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialGroup != null and materialGroup != ''">
                AND node.material_group IN
                <foreach item="item" collection="materialGroup.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialType != null and materialType != ''">
                AND node.material_type IN
                <foreach item="item" collection="materialType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.material_id ${sort}
    </select>

    <select id="selectProduct" resultType="java.lang.String">
        SELECT DISTINCT product_id from
        dwd.dwd_material_mara
        <where>
            <if test="productId != null and productId != ''">
                AND product_id = #{productId}
            </if>
        </where>
    </select>

    <select id="selectMaterialGroup" resultType="java.lang.String">
        SELECT DISTINCT material_group from
        dwd.dwd_material_mara
        <where>
            <if test="materialGroup != null and materialGroup != ''">
                AND material_group = #{materialGroup}
            </if>
        </where>
    </select>

    <select id="selectMaterialType" resultType="java.lang.String">
        SELECT DISTINCT material_type from
        dwd.dwd_material_mara
        <where>
            <if test="materialType != null and materialType != ''">
                AND material_type = #{materialType}
            </if>
        </where>
    </select>

    <select id="selectMaterialId" resultType="java.lang.String">
        SELECT DISTINCT material_id from
        dwd.dwd_material_mara
        <where>
            <if test="materialId != null and materialId != ''">
                AND material_id = #{materialId}
            </if>
        </where>
    </select>

    <select id="selectProductListByTableName" resultType="com.datalink.fdop.base.api.domain.ProductVo">
        SELECT
            node.product_id as productId,
            mara.material_desc as productDesc,
            mara.product_cim_id as productCimId
        FROM (
            SELECT DISTINCT product_id FROM ${tableName}
        ) node
        LEFT JOIN dwd.dwd_material_mara mara ON node.product_id = mara.product_id
        <where>
            AND node.product_id is not null and node.product_id &lt;> ''
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="productId != null and productId != ''">
                and node.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productDesc != null and productDesc != ''">
                AND mara.material_desc LIKE CONCAT('%', #{productDesc}, '%')
            </if>
            <if test="productCimId != null and productCimId != ''">
                AND mara.product_cim_id LIKE CONCAT('%', #{productCimId}, '%')
            </if>
        </where>
        ORDER BY node.product_id,mara.material_desc,mara.product_cim_id ${sort}
    </select>

    <select id="selectProductList" resultType="com.datalink.fdop.base.api.domain.ProductVo">
        SELECT
            node.product_id as productId,
            node.material_desc as  productDesc,
            node.product_cim_id as  productCimId
        FROM dwd.dwd_material_mara node
        <where>
            AND node.product_id is not null and node.product_id &lt;> ''
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="productId != null and productId != ''">
                and node.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productDesc != null and productDesc != ''">
                AND node.material_desc LIKE CONCAT('%', #{productDesc}, '%')
            </if>
            <if test="productCimId != null and productCimId != ''">
                AND node.product_cim_id LIKE CONCAT('%', #{productCimId}, '%')
            </if>
        </where>
        ORDER BY node.product_id,node.material_desc,node.product_cim_id ${sort}
    </select>

</mapper>


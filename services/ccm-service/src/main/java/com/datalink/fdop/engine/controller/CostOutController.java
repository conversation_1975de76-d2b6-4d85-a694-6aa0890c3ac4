package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.engine.service.CostOutService;
import com.datalink.fdop.engine.utils.SearchUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-03-06 15:51
 */
@RestController
@RequestMapping("/fccm/costOut")
public class CostOutController {

    @Autowired
    private CostOutService costOutService;

    @ApiOperation("WIP成本输出")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/getWip")
    public R getWip(@RequestParam(value = "verId") String verId,
                    @RequestParam(value = "companyId") String companyId,
                    @RequestParam(value = "factoryId", required = false) String factoryId,
                    @RequestParam(value = "yearMonth") Long yearMonth,
                    @RequestParam(value = "costType") String costType,
                    @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(costOutService.getWipOrInv(verId, companyId, factoryId, yearMonth, costType, searchVo, "wip"));
    }

    @ApiOperation("库存成本输出")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/getInv")
    public R getInv(@RequestParam(value = "verId") String verId,
                    @RequestParam(value = "companyId") String companyId,
                    @RequestParam(value = "factoryId", required = false) String factoryId,
                    @RequestParam(value = "yearMonth") Long yearMonth,
                    @RequestParam(value = "costType") String costType,
                    @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(costOutService.getWipOrInv(verId, companyId, factoryId, yearMonth, costType, searchVo, "inv"));
    }

    @ApiOperation("工程费用输出")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/getRd")
    public R getRd(@RequestParam(value = "verId") String verId,
                    @RequestParam(value = "companyId") String companyId,
                    @RequestParam(value = "factoryId", required = false) String factoryId,
                    @RequestParam(value = "costCenterId", required = false) String costCenterId,
                    @RequestParam(value = "wbsId", required = false) String wbsId,
                    @RequestParam(value = "yearMonth") Long yearMonth,
                    @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(costOutService.getRd(verId, companyId, factoryId, costCenterId, wbsId, yearMonth, searchVo));
    }

    @ApiOperation("事件成本输出")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/getEvent")
    public R getEvent(@RequestParam(value = "verId") String verId,
                   @RequestParam(value = "companyId") String companyId,
                   @RequestParam(value = "factoryId", required = false) String factoryId,
                   @RequestParam(value = "eventCode", required = false) String eventCode,
                   @RequestParam(value = "yearMonth") Long yearMonth,
                   @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(costOutService.getEvent(verId, companyId, factoryId, eventCode, yearMonth, searchVo));
    }

    @ApiOperation("生产成本输出")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/getPc")
    public R getPc(@RequestParam(value = "verId") String verId,
                      @RequestParam(value = "companyId") String companyId,
                      @RequestParam(value = "factoryId", required = false) String factoryId,
                      @RequestParam(value = "workOrder", required = false) String workOrder,
                      @RequestParam(value = "yearMonth") Long yearMonth,
                      @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(costOutService.getPc(verId, companyId, factoryId, workOrder, yearMonth, searchVo));
    }


}

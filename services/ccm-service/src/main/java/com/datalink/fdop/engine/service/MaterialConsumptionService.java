package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.MaterialConsumption;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:32
 */
public interface MaterialConsumptionService extends IService<MaterialConsumption> {

    PageDataInfo<MaterialConsumption> getOrg(String sort, SearchVo searchVo);

    int saveData(MaterialConsumption materialConsumption);

    PageDataInfo<MaterialConsumption> getEdit(String sort, SearchVo searchVo);

    PageDataInfo<MaterialConsumption> getAll(String sort, SearchVo searchVo);

    int deleteByList(List<MaterialConsumption> list);
}

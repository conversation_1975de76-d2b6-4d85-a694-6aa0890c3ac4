<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.ActualActivityMapper">


    <select id="getOrg" resultType="com.datalink.fdop.engine.api.domain.ActualActivity">
        select a.* from fccm.actual_activity a
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchCondition)})
        </if>
    </select>

    <select id="getEdit" resultType="com.datalink.fdop.engine.api.domain.ActualActivity">
        select a.* from fccm.actual_activity_edit a
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchCondition)})
        </if>
    </select>

    <select id="getAll" resultType="com.datalink.fdop.engine.api.domain.ActualActivity">
        select a.* from fccm.actual_activity a
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchCondition)})
        </if>
        UNION ALL
        select b.* from fccm.actual_activity_edit b
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("b",searchCondition)})
        </if>
    </select>

    <select id="add" parameterType="String">
        insert into fccm.actual_activity_edit values (${sql})
    </select>

    <select id="updateByKey">
        update fccm.actual_activity_edit
        set
        <if test="factoryId !=null and factoryId != ''">factory_id = '${factoryId}',</if>
        <if test="productId !=null and productId != ''">product_id = '${productId}',</if>
        <if test="productVer !=null">product_ver = ${productVer},</if>
        <if test="technologyId !=null and technologyId != ''">technology_id = '${technologyId}',</if>
        <if test="technologyVer !=null">technology_ver = ${technologyVer},</if>
        <if test="routeId !=null and routeId != ''">route_id = '${routeId}',</if>
        <if test="routeVer !=null">route_ver = ${routeVer},</if>
        <if test="lotType !=null and lotType != ''">lot_type = '${lotType}',</if>
        <if test="lotId !=null and lotId != ''">lot_id = '${lotId}',</if>
        <if test="stepNo !=null">step_no = ${stepNo},</if>
        <if test="lotStatus !=null and lotStatus != ''">lot_status = '${lotStatus}',</if>
        <if test="workOrder !=null and workOrder != ''">work_order = '${workOrder}',</if>
        <if test="layerId !=null and layerId != ''">layer_id = '${layerId}',</if>
        <if test="stageId !=null and stageId != ''">stage_id = '${stageId}',</if>
        <if test="stepId !=null and stepId != ''">step_id = '${stepId}',</if>
        <if test="equipId !=null and equipId != ''">equip_id = '${equipId}',</if>
        <if test="equipGroupId !=null and equipGroupId != ''">equip_group_id = '${equipGroupId}',</if>
        <if test="unit1 !=null and unit1 != ''">unit1 = '${unit1}',</if>
        <if test="qty1 !=null">qty1 = ${qty1},</if>
        <if test="unit2 !=null and unit2 != ''">unit2 = '${unit2}',</if>
        <if test="qty2 !=null">qty2 = ${qty2},</if>
        <if test="reworkTag !=null and reworkTag != ''">rework_tag = '${reworkTag}',</if>
        <if test="costCenterId != null">cost_center_id = '${costCenterId}',</if>
        <if test="costStructureId != null">cost_structure_id = '${costStructureId}',</if>
        <if test="wbsId != null">wbs_id = '${wbsId}',</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time = to_timestamp('${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}','yyyy-mm-dd hh24:mi:ss')
        <if test="syncStatus != null and syncStatus != ''">, sync_status = '${syncStatus}'</if>
        <if test="text != null and text != ''">, text = '${text}'</if>
        where plant_id = '${plantId}'
        and date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(date)}','yyyy-mm-dd')
        and lot_id = '${lotId}' and history_step_no = ${historyStepNo}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.engine.api.domain.ActualActivity">
        select *
        from fccm.actual_activity_edit
        where plant_id = '${plantId}'
          and date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(date)}'
            , 'yyyy-mm-dd')
          and lot_id = '${lotId}'
          and history_step_no = ${historyStepNo}
            limit 1
    </select>

    <delete id="deleteByKey">
        delete from fccm.actual_activity_edit
        where plant_id = '${plantId}'
          and date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(date)}','yyyy-mm-dd')
          and lot_id = '${lotId}'
          and history_step_no = ${historyStepNo}
    </delete>

</mapper>


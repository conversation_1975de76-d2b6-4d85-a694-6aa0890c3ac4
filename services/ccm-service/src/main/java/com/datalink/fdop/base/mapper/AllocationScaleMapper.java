package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.base.api.domain.AllocationScale;
import com.datalink.fdop.base.api.domain.CostCenterGroupItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AllocationScaleMapper extends BaseMapper<AllocationScale> {

    int deleteScalesBySegmentKey(@Param("controlAreaId") String controlAreaId, @Param("keyId") String keyId);

    IPage<AllocationScale> selectList(Page<AllocationScale> page,
                                      @Param("controlAreaId") String controlAreaId,
                                      @Param("keyId") String keyId,
                                      @Param("allocationSegmentId") String allocationSegmentId,
                                      @Param("allocationMethodId") String allocationMethodId,
                                      @Param("costCenterFrom") String costCenterFrom,
                                      @Param("costCenterTo") String costCenterTo,
                                      @Param("scale") String scale,
                                      @Param("sort") String sort);

    IPage<AllocationScale> selectListByCostCenterGroupItems(Page<AllocationScale> page,
                                                            @Param("controlAreaId") String controlAreaId,
                                                            @Param("keyId") String keyId,
                                                            @Param("allocationSegmentId") String allocationSegmentId,
                                                            @Param("allocationMethodId") String allocationMethodId,
                                                            @Param("costCenterGroup") String costCenterGroup,
                                                            @Param("scale") String scale,
                                                            @Param("sort") String sort);

}

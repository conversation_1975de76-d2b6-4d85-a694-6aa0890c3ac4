<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.ProductMenuMapper">

    <update id="bacthUpdatePidById" parameterType="String">
        update zjdata.ccm_product_menu
        set p_menu_id = (select p_menu_id from zjdata.ccm_product_menu where menu_id = #{menuId} limit 1)
        where p_menu_id = #{menuId} and del_flag = false
    </update>

    <update id="deleteByIds" parameterType="String">
        update zjdata.ccm_product_menu
        set del_flag = true
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag = false
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.ProductTree">
        select null as product_id, null as product_ver, null as product_desc, null as product_text,
               null as technology_id, null as technology_ver, null as date_from, null as date_to,
               a.menu_id, null as enable, a.del_flag, null as create_by, null as create_time,
               null as update_by, null as update_time, a.p_menu_id, a.menu_desc,
               a.menu_text, 'MENU' as menu_type from zjdata.ccm_product_menu a
        where a.del_flag != true
        UNION
        select b.*,  b.menu_id as p_menu_id, null as menu_desc, null as menu_text,
               'NODE' as menu_type from zjdata.ccm_product b
        where b.del_flag != true
        order by menu_id ${sort}
    </select>

    <select id="selectByPids" resultType="String">
        select * from zjdata.ccm_product
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag != true
        order by product_id, product_ver desc
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.ProductMenu">
        select * from zjdata.ccm_product_menu
        where menu_id = #{id} and del_flag != true limit 1
    </select>

</mapper>

## GitLab CI/CD Config File

## https://ewiki.semi-tech.com/STC/CICD/index.md
## https://docs.gitlab.com/ee/ci/index.html
## https://docs.gitlab.com/ee/ci/yaml/index.html

---

variables:
  ## `BOT_ALIAS` and `TEMPLATE_ALIAS` can be set here, or defined in your Group/Project > Settings > CI/CD > Variables
  ## !!!: Project variables > Group variables > Instance(GitLab) variables > .yml job variables > .yml global variables
  ## CI/CD variable precedence: https://docs.gitlab.com/ee/ci/variables/index.html#cicd-variable-precedence
  BOT_ALIAS:        "TODO_replace_with_your_bot_alias"
  TEMPLATE_ALIAS:   "sonar"
  VERBOSE:          "false"
  JAVA_VERSION: "11"

default:
  tags:
    ## public runners
    - public

include:
  ## https://gitlab.semi-tech.com/stc/cicd/cicd-config
  - project:    stc/cicd/cicd-config
    ref:        master
    file:       java.yml

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalActualOrdExp;

import java.util.List;

public interface DwhFinalActualOrdExpService extends IService<DwhFinalActualOrdExp> {

    PageDataInfo<DwhFinalActualOrdExp> overview(String yearMonth, String verId, String controlAreaId, String sort, SearchVo searchVo);

    List<DwhFinalActualOrdExp> selectNoPage(String yearMonth, String verId, String controlAreaId, String sort, SearchVo searchVo);
}
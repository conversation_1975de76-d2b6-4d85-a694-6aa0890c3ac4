package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationCostA;

import java.util.List;

public interface DwsNewAllocationCostAService extends IService<DwsNewAllocationCostA> {

    /**
     * 分页查询定向分摊
     **/
    PageDataInfo<DwsNewAllocationCostA> selectTargetedNewAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo);

    /**
     * 分页查询费用分摊
     **/
    PageDataInfo<DwsNewAllocationCostA> selectCostNewAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo);

    List<DwsNewAllocationCostA> selectNoPage(String controlAreaId,
                                             String verId,
                                             String companyId,
                                             String yearMonthFrom,
                                             String yearMonthTo, String allocationType, String sort, SearchVo searchVo);
}
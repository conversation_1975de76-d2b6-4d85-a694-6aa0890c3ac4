package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewActualOrdExp;
import com.datalink.fdop.engine.mapper.DwsNewActualOrdExpMapper;
import com.datalink.fdop.engine.service.DwsNewActualOrdExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewActualOrdExpServiceImpl extends ServiceImpl<DwsNewActualOrdExpMapper, DwsNewActualOrdExp> implements DwsNewActualOrdExpService {

    @Autowired
    private DwsNewActualOrdExpMapper dwsNewActualOrdExpMapper;

    @Override
    public PageDataInfo<DwsNewActualOrdExp> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwsNewActualOrdExp> page = PageUtils.getPage(DwsNewActualOrdExp.class);
        IPage<DwsNewActualOrdExp> iPage = dwsNewActualOrdExpMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewActualOrdExp> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwsNewActualOrdExpMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}
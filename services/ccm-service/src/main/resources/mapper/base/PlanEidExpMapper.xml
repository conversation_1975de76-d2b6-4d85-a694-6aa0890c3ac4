<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.PlanEidExpMapper">

    <delete id="deleteByKey">
        delete from zjdata.ccm_plan_eid_exp
        WHERE (ver_id, control_area_id, value_type, year, month, cost_element_id, equip_id) IN
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.verId},#{item.controlAreaId}, #{item.valueType}, #{item.year}, #{item.month}, #{item.costElementId}, #{item.equipId})
        </foreach>
    </delete>

    <select id="checkIdUnique" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_plan_eid_exp
        WHERE ver_id = #{verId} AND control_area_id = #{controlAreaId} AND value_type = #{valueType}
        AND year = #{year} AND month = #{month} AND cost_element_id = #{costElementId} AND equip_id = #{equipId}
        limit 1
    </select>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.PlanEidExp">
        UPDATE zjdata.ccm_plan_eid_exp
        SET
        <if test="currency != null">currency = #{currency},</if>
        <if test="origCostElementId != null">orig_cost_element_id = #{origCostElementId},</if>
        <if test="revalueElementId != null">revalue_element_id = #{revalueElementId},</if>
        <if test="amount != null">amount = #{amount},</if>
        <if test="enable != null">enable = #{enable},</if>
        <if test="companyId != null">company_id = #{companyId},</if>
        <if test="plantId != null">plant_id = #{plantId},</if>
        <if test="costCenterId != null">cost_center_id = #{costCenterId},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE ver_id = #{verId} AND control_area_id = #{controlAreaId} AND value_type = #{valueType}
        AND year = #{year} AND month = #{month} AND cost_element_id = #{costElementId} AND equip_id = #{equipId}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.PlanEidExp">
        SELECT 
            *
        FROM zjdata.ccm_plan_eid_exp node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and CONCAT(year, month) between  #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
        ORDER BY ver_id, control_area_id, value_type, year, month, cost_element_id, equip_id ${sort}
    </select>
    <select id="selectNoPage" resultType="com.datalink.fdop.base.api.domain.PlanEidExp">
        SELECT
        *
        FROM zjdata.ccm_plan_eid_exp node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and CONCAT(year, month) between  #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
        ORDER BY ver_id, control_area_id, value_type, year, month, cost_element_id, equip_id ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_plan_eid_exp
        SET enable = #{enable}
        WHERE (ver_id, control_area_id, value_type, year, month, cost_element_id, equip_id) IN
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.verId},#{item.controlAreaId}, #{item.valueType}, #{item.year}, #{item.month}, #{item.costElementId}, #{item.equipId})
        </foreach>
    </update>
</mapper>
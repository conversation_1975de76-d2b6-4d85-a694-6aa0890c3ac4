package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRateStructureA;

import java.util.List;

public interface DwhFinalRateStructureAService extends IService<DwhFinalRateStructureA> {

    PageDataInfo<DwhFinalRateStructureA> overview(
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String sort,
            SearchVo searchVo);

    List<DwhFinalRateStructureA> selectNoPage(String controlAreaId,
                                              String verId,
                                              String yearMonthFrom,
                                              String yearMonthTo,
                                              String costCenterType,
                                              String sort,
                                              SearchVo searchVo);
}
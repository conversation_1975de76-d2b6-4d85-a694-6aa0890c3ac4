package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostRbom;

import java.util.List;

public interface DwhFinalStdCostRbomService extends IService<DwhFinalStdCostRbom> {

    PageDataInfo<DwhFinalStdCostRbom> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo);

    List<DwhFinalStdCostRbom> selectNoPage(String plantId,
                                           String verId,
                                           String controlAreaId,
                                           String productId,
                                           String date,
                                           String sort,
                                           SearchVo searchVo);
}

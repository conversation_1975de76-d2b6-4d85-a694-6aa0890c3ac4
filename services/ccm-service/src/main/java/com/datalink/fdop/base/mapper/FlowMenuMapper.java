package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.base.api.domain.FlowMenu;
import com.datalink.fdop.base.api.model.FlowTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-09-06 11:28
 */
public interface FlowMenuMapper extends BaseMapper<FlowMenu> {

    int bacthUpdatePidById(@Param("menuId") String menuId);

    int deleteByIds(@Param("ids") List<String> ids);

    List<FlowTree> selectMenuTree(@Param("sort")String sort, @Param("code")String code);

    FlowMenu selectById(@Param("id") String id);

}

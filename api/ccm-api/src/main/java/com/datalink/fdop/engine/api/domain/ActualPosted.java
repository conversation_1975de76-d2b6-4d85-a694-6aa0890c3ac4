package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-02-07 11:11
 */
@Data
@TableName(schema = "fccm", value = "actual_posted_edit")
public class ActualPosted {

    private String verId;
    private String companyId;
    private String costElementId;
    private String costElementDesc;
    private String costCenterId;
    private String costCenterDesc;
    private String sourceType;
    private String wbsId;
    private String wbsDesc;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long yearMonth;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date postingDate;
    private String currency;
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal amount;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
    private String syncStatus;
    private String text;

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.ProductMapper">

    <select id="checkUnique" parameterType="com.datalink.fdop.base.api.domain.Product" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_product
        WHERE product_id = #{productId} AND product_ver = #{productVer}
          AND technology_id = #{technologyId} AND technology_ver = #{technologyVer}
          AND del_flag = false limit 1
    </select>

    <update id="deleteByIds" parameterType="Long">
        update zjdata.ccm_product
        SET del_flag = true
        WHERE
        <foreach collection="products" item="product" open="(" separator=" or " close=")">
            product_id = #{product.productId} AND product_ver = #{product.productVer}
            AND technology_id = #{product.technologyId} AND technology_ver = #{product.technologyVer}
        </foreach>
        and del_flag = false
    </update>

    <update id="updateByKey">
        update zjdata.ccm_product
        set
        <if test="productDesc != null">product_desc = #{productDesc},</if>
        <if test="productText != null">product_text = #{productText},</if>
        <if test="dateFrom != null">date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}',</if>
        <if test="dateTo != null">date_to = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateTo)}',</if>
        <if test="menuId != null">menu_id = #{menuId},</if>
        <if test="enable != null">enable = #{enable},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        update_time = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE product_id = #{productId} AND product_ver = #{productVer}
        AND technology_id = #{technologyId} AND technology_ver = #{technologyVer}
        AND del_flag = false
    </update>

    <update id="bacthUpdatePidById">
        update zjdata.ccm_product
        set menu_id = (select p_menu_id from zjdata.ccm_product_menu where menu_id = #{menuId} limit 1)
        where menu_id = #{menuId} and del_flag = false
    </update>

    <select id="selectIdsByPid" resultType="String">
        select product_id from zjdata.ccm_product
        where menu_id = #{pid} and del_flag = false
    </select>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.Product" resultType="com.datalink.fdop.base.api.domain.Product">
        SELECT ls.*
        FROM zjdata.ccm_product ls
        WHERE ls.del_flag = false
        <if test="product.productId != null">
            AND ls.product_ver = #{product.productId}
        </if>
        <if test="product.productId != null">
            AND ls.product_id like '%${product.productId}%'
        </if>
        <if test="product.technologyId != null">
            AND ls.technology_id like '%${product.technologyId}%'
        </if>
        <if test="product.technologyVer != null">
            AND ls.technology_ver = #{product.technologyVer}
        </if>
        ORDER BY product_id, product_ver desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.Product">
        SELECT ls.*
        FROM zjdata.ccm_product ls
        WHERE ls.del_flag = false
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("ls",searchCondition)})
        </if>
        <if test="menuId != null and menuId != '-1'.toString()">
            and ls.menu_id = #{menuId}
        </if>
        ORDER BY product_id, product_ver ${sort}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.Product">
        SELECT *
        FROM zjdata.ccm_product
        where product_id = #{productId} AND product_ver = #{productVer}
          AND technology_id = #{technologyId} AND technology_ver = #{technologyVer}
          AND del_flag = false limit 1
    </select>

</mapper>


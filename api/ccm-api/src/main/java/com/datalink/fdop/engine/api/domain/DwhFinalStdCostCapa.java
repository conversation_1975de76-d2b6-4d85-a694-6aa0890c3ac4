package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_final_std_cost_capa")
@ApiModel("最终标准成本-能力成本管理")
@PermissionColumn(module = "ccms")
public class DwhFinalStdCostCapa {

    @ApiModelProperty(value = "STEP核算号")
    @Excel(name = "STEP核算号")
    private String costCode;

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    private String productId;

    @ApiModelProperty(value = "产品描述")
    @Excel(name = "产品描述")
    private String productDesc;

    @ApiModelProperty(value = "产品CIM编码")
    @Excel(name = "产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "产品ERP编码")
    @Excel(name = "产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "Equip_Group_Flag")
    @Excel(name = "Equip_Group_Flag")
    private String equipGroupFlag;

    @ApiModelProperty(value = "Equip_Group_ID")
    @Excel(name = "Equip_Group_ID")
    private String equipGroupId;

    @ApiModelProperty(value = "有效起始日期")
    @Excel(name = "有效起始日期")
    private String effectiveDate;

    @ApiModelProperty(value = "评估基于日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @Excel(name = "评估基于日期")
    private Date baseDate;

    @ApiModelProperty(value = "运算批量")
    @Excel(name = "运算批量")
    private Long batchQty;

    @ApiModelProperty(value = "运算时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "运算时间戳")
    private Date timestamp;

    @ApiModelProperty(value = "成本总值")
    @Excel(name = "成本总值")
    private BigDecimal stepMoveCost;

    @ApiModelProperty(value = "组件001成本")
    @Excel(name = "组件001成本")
    private BigDecimal costStructure001;

    @ApiModelProperty(value = "组件002成本")
    @Excel(name = "组件002成本")
    private BigDecimal costStructure002;

    @ApiModelProperty(value = "组件003成本")
    @Excel(name = "组件003成本")
    private BigDecimal costStructure003;

    @ApiModelProperty(value = "组件004成本")
    @Excel(name = "组件004成本")
    private BigDecimal costStructure004;

    @ApiModelProperty(value = "组件005成本")
    @Excel(name = "组件005成本")
    private BigDecimal costStructure005;

    @ApiModelProperty(value = "组件006成本")
    @Excel(name = "组件006成本")
    private BigDecimal costStructure006;

    @ApiModelProperty(value = "组件007成本")
    @Excel(name = "组件007成本")
    private BigDecimal costStructure007;

    @ApiModelProperty(value = "组件008成本")
    @Excel(name = "组件008成本")
    private BigDecimal costStructure008;

    @ApiModelProperty(value = "组件009成本")
    @Excel(name = "组件009成本")
    private BigDecimal costStructure009;

    @ApiModelProperty(value = "组件010成本")
    @Excel(name = "组件010成本")
    private BigDecimal costStructure010;

    @ApiModelProperty(value = "组件011成本")
    @Excel(name = "组件011成本")
    private BigDecimal costStructure011;

    @ApiModelProperty(value = "组件012成本")
    @Excel(name = "组件012成本")
    private BigDecimal costStructure012;

    @ApiModelProperty(value = "组件013成本")
    @Excel(name = "组件013成本")
    private BigDecimal costStructure013;

    @ApiModelProperty(value = "组件014成本")
    @Excel(name = "组件014成本")
    private BigDecimal costStructure014;

    @ApiModelProperty(value = "组件015成本")
    @Excel(name = "组件015成本")
    private BigDecimal costStructure015;

    @ApiModelProperty(value = "组件016成本")
    @Excel(name = "组件016成本")
    private BigDecimal costStructure016;

    @ApiModelProperty(value = "组件017成本")
    @Excel(name = "组件017成本")
    private BigDecimal costStructure017;

    @ApiModelProperty(value = "组件018成本")
    @Excel(name = "组件018成本")
    private BigDecimal costStructure018;

    @ApiModelProperty(value = "组件019成本")
    @Excel(name = "组件019成本")
    private BigDecimal costStructure019;

    @ApiModelProperty(value = "组件020成本")
    @Excel(name = "组件020成本")
    private BigDecimal costStructure020;

    @ApiModelProperty(value = "组件021成本")
    @Excel(name = "组件021成本")
    private BigDecimal costStructure021;

    @ApiModelProperty(value = "组件022成本")
    @Excel(name = "组件022成本")
    private BigDecimal costStructure022;

    @ApiModelProperty(value = "组件023成本")
    @Excel(name = "组件023成本")
    private BigDecimal costStructure023;

    @ApiModelProperty(value = "组件024成本")
    @Excel(name = "组件024成本")
    private BigDecimal costStructure024;

    @ApiModelProperty(value = "组件025成本")
    @Excel(name = "组件025成本")
    private BigDecimal costStructure025;

    @ApiModelProperty(value = "组件026成本")
    @Excel(name = "组件026成本")
    private BigDecimal costStructure026;

    @ApiModelProperty(value = "组件027成本")
    @Excel(name = "组件027成本")
    private BigDecimal costStructure027;

    @ApiModelProperty(value = "组件028成本")
    @Excel(name = "组件028成本")
    private BigDecimal costStructure028;

    @ApiModelProperty(value = "组件029成本")
    @Excel(name = "组件029成本")
    private BigDecimal costStructure029;

    @ApiModelProperty(value = "组件030成本")
    @Excel(name = "组件030成本")
    private BigDecimal costStructure030;

    @ApiModelProperty(value = "组件031成本")
    @Excel(name = "组件031成本")
    private BigDecimal costStructure031;

    @ApiModelProperty(value = "组件032成本")
    @Excel(name = "组件032成本")
    private BigDecimal costStructure032;

    @ApiModelProperty(value = "组件033成本")
    @Excel(name = "组件033成本")
    private BigDecimal costStructure033;

    @ApiModelProperty(value = "组件034成本")
    @Excel(name = "组件034成本")
    private BigDecimal costStructure034;

    @ApiModelProperty(value = "组件035成本")
    @Excel(name = "组件035成本")
    private BigDecimal costStructure035;

    @ApiModelProperty(value = "组件036成本")
    @Excel(name = "组件036成本")
    private BigDecimal costStructure036;

    @ApiModelProperty(value = "组件037成本")
    @Excel(name = "组件037成本")
    private BigDecimal costStructure037;

    @ApiModelProperty(value = "组件038成本")
    @Excel(name = "组件038成本")
    private BigDecimal costStructure038;

    @ApiModelProperty(value = "组件039成本")
    @Excel(name = "组件039成本")
    private BigDecimal costStructure039;

    @ApiModelProperty(value = "组件040成本")
    @Excel(name = "组件040成本")
    private BigDecimal costStructure040;

    @ApiModelProperty(value = "组件041成本")
    @Excel(name = "组件041成本")
    private BigDecimal costStructure041;

    @ApiModelProperty(value = "组件042成本")
    @Excel(name = "组件042成本")
    private BigDecimal costStructure042;

    @ApiModelProperty(value = "组件043成本")
    @Excel(name = "组件043成本")
    private BigDecimal costStructure043;

    @ApiModelProperty(value = "组件044成本")
    @Excel(name = "组件044成本")
    private BigDecimal costStructure044;

    @ApiModelProperty(value = "组件045成本")
    @Excel(name = "组件045成本")
    private BigDecimal costStructure045;

    @ApiModelProperty(value = "组件046成本")
    @Excel(name = "组件046成本")
    private BigDecimal costStructure046;

    @ApiModelProperty(value = "组件047成本")
    @Excel(name = "组件047成本")
    private BigDecimal costStructure047;

    @ApiModelProperty(value = "组件048成本")
    @Excel(name = "组件048成本")
    private BigDecimal costStructure048;

    @ApiModelProperty(value = "组件049成本")
    @Excel(name = "组件049成本")
    private BigDecimal costStructure049;

    @ApiModelProperty(value = "组件050成本")
    @Excel(name = "组件050成本")
    private BigDecimal costStructure050;

    @ApiModelProperty(value = "组件051成本")
    @Excel(name = "组件051成本")
    private BigDecimal costStructure051;

    @ApiModelProperty(value = "组件052成本")
    @Excel(name = "组件052成本")
    private BigDecimal costStructure052;

    @ApiModelProperty(value = "组件053成本")
    @Excel(name = "组件053成本")
    private BigDecimal costStructure053;

    @ApiModelProperty(value = "组件054成本")
    @Excel(name = "组件054成本")
    private BigDecimal costStructure054;

    @ApiModelProperty(value = "组件055成本")
    @Excel(name = "组件055成本")
    private BigDecimal costStructure055;

    @ApiModelProperty(value = "组件056成本")
    @Excel(name = "组件056成本")
    private BigDecimal costStructure056;

    @ApiModelProperty(value = "组件057成本")
    @Excel(name = "组件057成本")
    private BigDecimal costStructure057;

    @ApiModelProperty(value = "组件058成本")
    @Excel(name = "组件058成本")
    private BigDecimal costStructure058;

    @ApiModelProperty(value = "组件059成本")
    @Excel(name = "组件059成本")
    private BigDecimal costStructure059;

    @ApiModelProperty(value = "组件060成本")
    @Excel(name = "组件060成本")
    private BigDecimal costStructure060;

    @Excel(name = "Primary_Key")
    @ApiModelProperty(value = "Primary_Key")
    private String primaryKey;

    @Excel(name = "导入时间")
    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;
}
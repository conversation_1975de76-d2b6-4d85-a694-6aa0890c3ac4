<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.SettleMapper">

    <select id="selectTaskCodeByName" resultType="java.lang.Long">
        select code from dolphinscheduler.t_ds_task_definition where name = #{name} limit 1
    </select>

    <select id="selectSettleFgHead" resultType="com.datalink.fdop.engine.api.domain.SettleFgHead">
        select a.* from fccm.settle_fg_${tableName}_head a
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}' and a.year = ${year}
        and a.month = ${month} and a.del_flag != true
        <if test="actionFlag != null and actionFlag != ''"> and a.action_flag = '${actionFlag}' </if>
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        <if test="limitStatus != null and limitStatus != ''">limit 1</if>
    </select>

    <select id="selectSettleFgItem" resultType="com.datalink.fdop.engine.api.domain.SettleFgItem">
        select a.* from fccm.settle_fg_${tableName}_item a
        where a.posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(settleFgHead.postingDate)}','yyyy-mm-dd')
        and a.ver_id = '${settleFgHead.verId}' and a.factory_id = '${settleFgHead.factoryId}' and a.lot_id = '${settleFgHead.lotId}'
        and a.product_id = '${settleFgHead.productId}'
        <if test="actionFlag != null and actionFlag != ''"> and a.action_flag = '${actionFlag}' </if>
        and a.del_flag != true
    </select>

    <select id="selectSettleFgItemBatch" resultType="java.lang.String">
        select a.fdop_batch_id from fccm.settle_fg_${tableName}_item a
        where a.posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(settleFgHead.postingDate)}','yyyy-mm-dd')
          and a.ver_id = '${settleFgHead.verId}' and a.factory_id = '${settleFgHead.factoryId}' and a.lot_id = '${settleFgHead.lotId}'
          and a.product_id = '${settleFgHead.productId}' and a.del_flag != true
    </select>

    <select id="selectPostingLogHead" resultType="com.datalink.fdop.engine.api.domain.PostingLogHead">
        select a.* from fccm.posting_log_${tableName}_head a
        where a.business_type = '${businessType}' and a.ver_id = '${verId}' and a.factory_id = '${factoryId}'
        and a.year = ${year} and a.month = ${month} and a.del_flag != true
        <if test="actionFlag != null and actionFlag != ''"> and a.action_flag = '${actionFlag}' </if>
        limit 1
    </select>

    <select id="selectPostingLogItem" resultType="com.datalink.fdop.engine.api.domain.PostingLogItem">
        select a.* from fccm.posting_log_${tableName}_item a
        where a.business_type = '${businessType}' and a.ver_id = '${verId}' and a.factory_id = '${factoryId}'
        and a.year = ${year} and a.month = ${month} and a.company_id = '${companyId}'
        and a.document_code = ${documentCode} and a.del_flag != true
        <if test="actionFlag != null and actionFlag != ''"> and a.action_flag = '${actionFlag}' </if>
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="selectSettleWipHead" resultType="com.datalink.fdop.engine.api.domain.SettleWipHead">
        select a.* from fccm.settle_wip_${tableName}_head a
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}' and a.year = ${year}
        and a.month = ${month} and a.business_type = '${businessType}' and a.del_flag != true
        <if test="actionFlag != null and actionFlag != ''"> and a.action_flag = '${actionFlag}' </if>
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        <if test="limitStatus != null and limitStatus != ''">limit 1</if>
    </select>

    <select id="selectSettleWipItem" resultType="com.datalink.fdop.engine.api.domain.SettleWipItem">
        select a.* from fccm.settle_wip_${tableName}_item a
        where a.business_type = '${settleWipHead.businessType}' and a.data_type = '${settleWipHead.dataType}'
          and a.posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(settleWipHead.postingDate)}','yyyy-mm-dd')
          and a.ver_id = '${settleWipHead.verId}' and a.factory_id = '${settleWipHead.factoryId}' and a.lot_id = '${settleWipHead.lotId}'
          and a.product_id = '${settleWipHead.productId}' and a.history_step_no = ${settleWipHead.historyStepNo}
          and a.del_flag != true
          <if test="actionFlag != null and actionFlag != ''"> and a.action_flag = '${actionFlag}' </if>
    </select>

    <select id="selectSettleWipItemBatch" resultType="java.lang.String">
        select a.fdop_batch_id from fccm.settle_wip_${tableName}_item a
        where a.business_type = '${settleWipHead.businessType}' and a.data_type = '${settleWipHead.dataType}'
          and a.posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(settleWipHead.postingDate)}','yyyy-mm-dd')
          and a.ver_id = '${settleWipHead.verId}' and a.factory_id = '${settleWipHead.factoryId}' and a.lot_id = '${settleWipHead.lotId}'
          and a.product_id = '${settleWipHead.productId}' and a.history_step_no = ${settleWipHead.historyStepNo} and a.del_flag != true
    </select>

    <select id="selectSettleEventHead" resultType="com.datalink.fdop.engine.api.domain.SettleEventHead">
        select a.* from fccm.settle_event_${tableName}_head a
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}' and a.year = ${year}
        and a.month = ${month} and a.business_type = '${businessType}' and a.del_flag != true
        <if test="actionFlag != null and actionFlag != ''"> and a.action_flag = '${actionFlag}' </if>
        <if test="searchVo != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
        <if test="limitStatus != null and limitStatus != ''">limit 1</if>
    </select>

    <select id="selectSettleEventItem" resultType="com.datalink.fdop.engine.api.domain.SettleEventItem">
        select a.* from fccm.settle_event_${tableName}_item a
        where a.business_type = '${settleEventHead.businessType}' and a.data_type = '${settleEventHead.dataType}'
          and a.posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(settleEventHead.postingDate)}','yyyy-mm-dd')
          and a.ver_id = '${settleEventHead.verId}' and a.factory_id = '${settleEventHead.factoryId}' and a.lot_id = '${settleEventHead.lotId}' and a.event_code = '${settleEventHead.eventCode}'
          and a.product_id = '${settleEventHead.productId}' and a.history_step_no = ${settleEventHead.historyStepNo} and a.del_flag != true
          <if test="actionFlag != null and actionFlag != ''"> and a.action_flag = '${actionFlag}' </if>
    </select>

    <select id="selectSettleEventItemBatch" resultType="java.lang.String">
        select a.fdop_batch_id from fccm.settle_event_${tableName}_item a
        where a.business_type = '${settleEventHead.businessType}' and a.data_type = '${settleEventHead.dataType}'
          and a.posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(settleEventHead.postingDate)}','yyyy-mm-dd')
          and a.ver_id = '${settleEventHead.verId}' and a.factory_id = '${settleEventHead.factoryId}' and a.lot_id = '${settleEventHead.lotId}' and a.event_code = '${settleEventHead.eventCode}'
          and a.product_id = '${settleEventHead.productId}' and a.history_step_no = ${settleEventHead.historyStepNo} and a.del_flag != true
    </select>

    <insert id="settleSaveHeadToS1">
        insert into fccm.settle_${tableName}_s1_head
            (select a.* from fccm.settle_${tableName}_temp_head a
            where <if test="tableName != 'fg'">a.business_type = '${businessType}' and </if>
            a.ver_id = '${verId}' and a.factory_id = '${factoryId}'
            and a.year = ${year} and a.month = ${month} and a.del_flag != true)
    </insert>

    <update id="settleSaveHeadToS2">
        update fccm.settle_${tableName}_temp_head a
        set action_flag = 'F'
        where <if test="tableName != 'fg'">a.business_type = '${businessType}' and </if>
        a.ver_id = '${verId}' and a.factory_id = '${factoryId}'
        and a.year = ${year} and a.month = ${month} and a.del_flag != true and action_flag = 'S'
    </update>

    <insert id="settleSaveItemToS1">
        insert into fccm.settle_${tableName}_s1_item
        (select b.* from fccm.settle_${tableName}_temp_item b join fccm.settle_${tableName}_temp_head a
        on <if test="tableName != 'fg'">a.business_type = '${businessType}' and a.data_type = b.data_type and a.history_step_no = b.history_step_no and</if>
        a.posting_date = b.posting_date and a.ver_id = b.ver_id
        and a.factory_id = b.factory_id and a.lot_id = b.lot_id and a.product_id = b.product_id
        <if test="tableName == 'event'"> and a.event_code = b.event_code</if>
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}'
        and a.year = ${year} and a.month = ${month} and a.del_flag != true)
    </insert>

    <update id="settleSaveItemToS2">
        update fccm.settle_${tableName}_s1_item
        set action_flag = 'F'
        where ver_id = '${verId}' and factory_id = '${factoryId}'
        <if test="tableName != 'fg'">and business_type = '${businessType}'</if>
        and year = ${year} and month = ${month} and del_flag != true and action_flag = 'S'
    </update>

    <update id="updateHeadToS1">
        update fccm.settle_${tableName}_s1_head
        set action_flag = 'S'
        where action_flag is NULL
    </update>

    <update id="updateItemToS1">
        update fccm.settle_${tableName}_s1_item
        set action_flag = 'S'
        where action_flag is NULL
    </update>

    <update id="updatePostingHeadToS1">
        update fccm.posting_log_s1_head
        set action_flag = 'S'
        where action_flag is NULL
    </update>

    <update id="updatePostingItemToS1">
        update fccm.posting_log_s1_item
        set action_flag = 'S'
        where action_flag is NULL
    </update>

    <update id="postingSaveHeadToS2">
        update fccm.posting_log_s1_head
        set action_flag = 'F'
        where ver_id = '${verId}' and factory_id = '${factoryId}'
          and business_type = '${businessType}'
          and year = ${year} and month = ${month} and del_flag != true and action_flag = 'S'
    </update>

    <update id="postingSaveItemToS2">
        update fccm.posting_log_s1_item
        set action_flag = 'F'
        where ver_id = '${verId}' and factory_id = '${factoryId}'
        and business_type = '${businessType}'
        and year = ${year} and month = ${month} and del_flag != true and action_flag = 'S'
    </update>

    <select id="postingSaveHeadToS1">
        insert into fccm.posting_log_s1_head
        (select a.* from fccm.posting_log_temp_head a
         where a.business_type = '${businessType}' and a.ver_id = '${verId}' and a.factory_id = '${factoryId}'
           and a.year = ${year} and a.month = ${month} and a.del_flag != true)
    </select>

    <select id="postingSaveItemToS1">
        insert into fccm.posting_log_s1_item
        (select b.* from fccm.posting_log_temp_item b join fccm.posting_log_temp_head a
        on a.business_type = b.business_type and a.ver_id = b.ver_id
        and a.company_id = b.company_id and a.factory_id = b.factory_id
        and a.year = b.year and a.month = b.month and a.document_code = b.document_code
        where a.business_type = '${businessType}' and a.ver_id = '${verId}' and a.factory_id = '${factoryId}'
            and a.year = ${year} and a.month = ${month} and a.del_flag != true)
    </select>

    <select id="settleDeleteHead">
        update fccm.settle_${tableName}_s1_head
        set del_flag = true
        where <if test="tableName != 'fg'">business_type = '${businessType}' and </if>
        ver_id = '${verId}' and factory_id = '${factoryId}'
        and year = ${year} and month = ${month} and del_flag != true
        <if test="actionFlag != null and actionFlag != ''"> and action_flag = '${actionFlag}' </if>
    </select>

    <select id="settleDeleteItem">
        update fccm.settle_${tableName}_s1_item
        set del_flag = true
        where <if test="tableName != 'fg'">business_type = '${businessType}' and </if>
        ver_id = '${verId}' and factory_id = '${factoryId}' and del_flag != true
        <if test="actionFlag != null and actionFlag != ''"> and action_flag = '${actionFlag}' </if>
    </select>

    <select id="postingDeleteHead">
        update fccm.posting_log_s1_head
        set del_flag = true
            where business_type = '${businessType}' and ver_id = '${verId}' and factory_id = '${factoryId}'
            and year = ${year} and month = ${month} and del_flag != true
        <if test="actionFlag != null and actionFlag != ''"> and action_flag = '${actionFlag}' </if>
    </select>

    <select id="postingDeleteItem">
        update fccm.posting_log_s1_item
        set del_flag = true
        where business_type = '${businessType}' and ver_id = '${verId}' and factory_id = '${factoryId}'
          and year = ${year} and month = ${month} and del_flag != true
        <if test="actionFlag != null and actionFlag != ''"> and action_flag = '${actionFlag}' </if>
    </select>

</mapper>
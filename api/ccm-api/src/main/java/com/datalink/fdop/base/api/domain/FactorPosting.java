package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-08-01 15:30
 */
@Data
@TableName(schema = "zjdata", value = "ccm_factor_posting")
@ApiModel("因子统计")
public class FactorPosting {

    @NotBlank(message = "管理范围不能为空")
    private String controlAreaId;
    @NotBlank(message = "数据集合不能为空")
    private String verId;
    @NotBlank(message = "统计因子不能为空")
    private String factorId;
    @NotBlank(message = "成本单元不能为空")
    private String costCenterId;
    @NotNull(message = "年月不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long yearMonth;
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal qty;
    private Boolean enable;
    private Boolean delFlag;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;

}

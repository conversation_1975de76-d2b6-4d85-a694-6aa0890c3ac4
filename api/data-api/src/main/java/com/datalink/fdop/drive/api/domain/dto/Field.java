package com.datalink.fdop.drive.api.domain.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseDomain;
import com.datalink.fdop.common.core.enums.FieldType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/7 16:34
 */
@Data
@TableName(schema = "zjdata", value = "d_g_synchronization_field")
public class Field extends BaseDomain implements Serializable {

    private static final long serialVersionUID = -5911634157203671153L;
    @TableId(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @ApiModelProperty(value = "数据源id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dataSourceId;

    // 字段名称
    private String fieldName;

    // 字段类型
    private String fieldType;

    // 基础字段类型
    private FieldType baseFieldType;

    // flink字段类型
    private String flinkFieldType;

    // 字段长度
    private Long length;

    // 字段小数长度
    private Long decimalLength;

    // 是否主键
    private Boolean isPk;

    // 是否为空
    private Boolean isNull;

    // 字段注释
    private String fieldDesc;

    // 字段默认值
    private String fieldDefault;

    // 表名
    private String tableName;

    // 视图名
    private String viewName;

    private String hisField;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardId;

    private String standardCode;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long qualityId;

    private String qualityCode;

    private String metadata;

    private String code;
    private String lengthMultiple;
    private String decimalLengthMultiple;
    public Field() {
    }

    public Field(String fieldName, FieldType baseFieldType, Long length, Long decimalLength, Boolean isPk, Boolean isNull) {
        this.fieldName = fieldName;
        this.baseFieldType = baseFieldType;
        this.length = length;
        this.decimalLength = decimalLength;
        this.isPk = isPk;
        this.isNull = isNull;
    }




    public Field(String fieldName, String fieldType, FieldType baseFieldType, Long length, Long decimalLength, Boolean isPk, Boolean isNull, String fieldDesc) {
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.baseFieldType = baseFieldType;
        this.length = length;
        this.decimalLength = decimalLength;
        this.isPk = isPk;
        this.isNull = isNull;
        this.fieldDesc = fieldDesc;
    }

}

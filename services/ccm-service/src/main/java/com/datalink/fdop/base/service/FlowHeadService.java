package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.FlowHead;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface FlowHeadService extends IService<FlowHead> {

    boolean insert(FlowHead flowHead);

    String checkUnique(FlowHead flowHead);

    PageDataInfo<FlowHead> list(FlowHead flowHead);

    int deleteByIds(List<FlowHead> flowHeadList);

    String importData(List<FlowHead> list, Boolean isUpdateSupport, String operName);

    boolean copy(List<FlowHead> list);

    PageDataInfo<FlowHead> overview(String menuId, String sort, SearchVo searchVo);

    FlowHead selectById(Long id);

    int updateByKey(FlowHead flowHead);

    List<Long> getVerByCode(String code);

}

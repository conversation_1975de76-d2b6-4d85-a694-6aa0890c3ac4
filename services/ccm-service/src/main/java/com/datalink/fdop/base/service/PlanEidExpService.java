package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.PlanEidExp;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface PlanEidExpService extends IService<PlanEidExp> {

    boolean insert(PlanEidExp planEidExp);

    int deleteByList(List<PlanEidExp> list);

    String importData(List<PlanEidExp> list, Boolean isUpdateSupport, String operName);

    PageDataInfo<PlanEidExp> overview(String verId, String plantId, String yearMonthFrom, String yearMonthTo,String sort, SearchVo searchVo);

    int updateByKey(PlanEidExp planEidExp);

    boolean updateBatchStatus(Boolean enable, List<PlanEidExp> statusList);

    List<PlanEidExp> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo);
}
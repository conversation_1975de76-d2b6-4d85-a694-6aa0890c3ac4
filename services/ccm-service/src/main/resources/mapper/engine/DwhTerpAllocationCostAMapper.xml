<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwhTerpAllocationCostAMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwhTerpAllocationCostA">
        SELECT
        *
        FROM dwh.dwh_terp_allocation_cost_a node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and node.control_area_id = #{controlAreaId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != ''">
                and node.year_month &gt;= #{yearMonthFrom}
            </if>
            <if test="yearMonthTo != null and yearMonthTo != ''">
                and node.year_month &lt;= #{yearMonthTo}
            </if>
            <if test="allocationType != null and allocationType != ''">
                and node.allocation_type in
                <foreach item="item" collection="allocationType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        node.ver_id,node.value_type,node.control_area_id,node.year_month,node.allocation_type,node.cost_object_fr_company_id,node.cost_object_fr_plant_id,node.cost_element_id,node.cost_object_fr,node.factor_id
        ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwhTerpAllocationCostA">
        SELECT
        *
        FROM dwh.dwh_terp_allocation_cost_a node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and node.control_area_id = #{controlAreaId}
            </if>
            <if test="yearMonthFrom != null and yearMonthFrom != ''">
                and node.year_month &gt;= #{yearMonthFrom}
            </if>
            <if test="yearMonthTo != null and yearMonthTo != ''">
                and node.year_month &lt;= #{yearMonthTo}
            </if>
            <if test="allocationType != null and allocationType != ''">
                and node.allocation_type in
                <foreach item="item" collection="allocationType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        node.ver_id,node.value_type,node.control_area_id,node.year_month,node.allocation_type,node.cost_object_fr_company_id,node.cost_object_fr_plant_id,node.cost_element_id,node.cost_object_fr,node.factor_id
        ${sort}
    </select>

</mapper>
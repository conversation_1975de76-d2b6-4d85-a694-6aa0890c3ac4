package com.datalink.fdop.engine.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.*;

/**
 * <AUTHOR>
 * @date 2023-03-16 16:43
 */
public interface DrillDowntService {

    PageDataInfo<DrillDowntReport100> query1(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo);

    PageDataInfo<DrillDowntReport110> query2(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo);

    PageDataInfo<DrillDowntReport120> query3(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo);

    PageDataInfo<DrillDowntReport130> query4(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo);

    PageDataInfo<DrillDowntReport140> query5(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo);


}

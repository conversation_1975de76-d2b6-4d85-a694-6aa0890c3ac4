package com.datalink.fdop.engine.controller;

import com.datalink.fdop.engine.api.domain.DwhTerpTraceAct;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhTerpTraceActService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwhTerpTraceAct")
@Api(tags = "CCMS-dwh_terp_trace_act")
public class DwhTerpTraceActController extends BaseController {

    @Autowired
    private DwhTerpTraceActService dwhTerpTraceActService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "报工作业履历")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String dateFrom = MapUtils.getAsString(params, "dateFrom");
        String dateTo = MapUtils.getAsString(params, "dateTo");
        PageDataInfo<DwhTerpTraceAct> overview = dwhTerpTraceActService.overview(plantId, dateFrom, dateTo,sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhTerpTraceAct.class,overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "报工作业履历", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String dateFrom = MapUtils.getAsString(params, "dateFrom");
        String dateTo = MapUtils.getAsString(params, "dateTo");
        List<DwhTerpTraceAct> list = dwhTerpTraceActService.selectNoPage(plantId, dateFrom, dateTo, sort, queryParam.getSearchVo());
        ExcelUtil<DwhTerpTraceAct> util = new ExcelUtil<>(DwhTerpTraceAct.class);
        util.exportExcel(response, list, "报工整合管理-交互视图数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhTerpTraceAct> util = new ExcelUtil<>(DwhTerpTraceAct.class);
        util.importTemplateExcel(response, "报工整合管理-交互视图模板");
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwh.dwh_terp_trace_act", null));
    }

}

package com.datalink.fdop.base.controller;

import com.datalink.fdop.base.api.domain.CcmPlanExp;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.base.service.CcmPlanExpService;
import com.datalink.fdop.base.service.CompanyService;
import com.datalink.fdop.base.service.VerService;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/ccmPlanExp")
@Api(tags = "CCMS-费用计划管理")
public class CcmPlanExpController extends BaseController {

    @Autowired
    private CcmPlanExpService ccmPlanExpService;

    @Autowired
    private VerService verService;

    @Autowired
    private CompanyService companyService;

    @ApiOperation(value = "新增")
    @Log(title = "费用计划管理", businessType = BusinessType.INSERT)
    @PostMapping("/insert")
    public R add(@Validated @RequestBody CcmPlanExp ccmPlanExp) {
        return R.toResult(ccmPlanExpService.insert(ccmPlanExp));
    }

    @ApiOperation(value = "修改")
    @Log(title = "费用计划管理", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public R edit(@Validated @RequestBody CcmPlanExp ccmPlanExp) {
        if (UserConstants.UNIQUE.equals(ccmPlanExpService.checkIdUnique(ccmPlanExp.getControlAreaId(), ccmPlanExp.getVerId(),
                ccmPlanExp.getValueType(), ccmPlanExp.getYear(), ccmPlanExp.getMonth(),
                ccmPlanExp.getCostElementId(), ccmPlanExp.getCostCenterId()))) {
            return R.fail("修改mps'" + "'失败，mps不存在");
        }
        ccmPlanExp.setUpdateBy(SecurityUtils.getUsername());
        ccmPlanExp.setUpdateTime(new Date());
        return R.toResult(ccmPlanExpService.updateByKey(ccmPlanExp));
    }

    @ApiOperation(value = "删除费用计划管理")
    @Log(title = "费用计划管理", businessType = BusinessType.DELETE)
    @DeleteMapping("del")
    public R remove(@RequestBody List<CcmPlanExp> list) {
        return R.toResult(ccmPlanExpService.deleteByList(list));
    }

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "费用计划管理")
    @PostMapping(value = "/overview")
    public R<Object> overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false)QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        PageDataInfo<CcmPlanExp> overview = ccmPlanExpService.overview(sort, verId, companyId, yearMonthFrom, yearMonthTo, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(CcmPlanExp.class, overview, false, true));
    }

    @ApiOperation(value = "导出费用计划管理")
    @Log(title = "费用计划管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false)QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        List<CcmPlanExp> list = ccmPlanExpService.selectNoPage(verId, companyId, yearMonthFrom, yearMonthTo,
                sort,queryParam.getSearchVo());
        ExcelUtil<CcmPlanExp> util = new ExcelUtil<>(CcmPlanExp.class);
        util.exportExcel(response, list, "成本中心");
    }

    @ApiOperation(value = "导入费用计划管理")
    @Log(title = "费用计划管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R importData(MultipartFile file, @RequestParam(value = "updateSupport", required = false) Boolean updateSupport) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        if (updateSupport == null) {
            updateSupport = false;
        }
        ExcelUtil<CcmPlanExp> util = new ExcelUtil<>(CcmPlanExp.class);
        List<CcmPlanExp> CcmMpsList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = ccmPlanExpService.importData(CcmMpsList, updateSupport, operName);
        return R.ok(message);
    }

    @ApiOperation(value = "导入模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CcmPlanExp> util = new ExcelUtil<>(CcmPlanExp.class);
        util.importTemplateExcel(response, "成本中心");
    }

    @ApiOperation(value = "批量启用/禁用")
    @Log(title = "费用计划管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batchStatus")
    public R batchStatus(@RequestParam(value = "enable") Boolean enable, @RequestBody List<CcmPlanExp> statusList) {
        return R.ok(ccmPlanExpService.updateBatchStatus(enable, statusList));
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(verService.listVerId("zjdata.ccm_plan_exp"));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId() {
        return R.ok(companyService.listCompanyId("zjdata.ccm_plan_exp"));
    }

}

package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.govern.api.domain.DirectoryStructure;
import com.datalink.fdop.govern.api.domain.DirectoryStructureTree;
import com.datalink.fdop.govern.service.IDirectoryStructureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/govern/directoryStructure")
@Api(tags = "L1/L2")
public class DirectoryStructureController {

    @Autowired
    private IDirectoryStructureService directoryStructureService;

    @ApiOperation(value = "新增")
    @Log(title = "资产目录新增", businessType = BusinessType.INSERT)
    @PostMapping
    public R create(@RequestBody DirectoryStructure entity) {
        return R.ok(directoryStructureService.create(entity));
    }

    @ApiOperation(value = "修改")
    @Log(title = "资产目录修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public R update(@RequestBody DirectoryStructure entity) {
        return R.ok(directoryStructureService.update(entity));
    }

    @ApiOperation(value = "删除")
    @Log(title = "资产目录删除", businessType = BusinessType.DELETE)
    @DeleteMapping
    public R delete(@RequestBody List<Long> ids) {
        return R.toResult(directoryStructureService.delete(ids));
    }

    @ApiOperation(value = "selectById")
    @Log(title = "资产目录根据id查询", businessType = BusinessType.DELETE)
    @GetMapping("/selectById/{id}")
    public R<DirectoryStructure> selectById(@PathVariable Long id) {
        return R.ok(directoryStructureService.selectById(id));
    }

    @ApiOperation(value = "查询列表")
    @Log(title = "资产目录查询列表")
    @PostMapping("/list")
    public R<PageDataInfo> list(@RequestBody(required = false) DirectoryStructure entity,
                                @RequestParam(required = false, value = "sort", defaultValue = "desc") String sort) {
        return R.ok(directoryStructureService.list(entity, sort));
    }

    @ApiOperation("资产目录树结构")
    @Log(title = "资产目录")
    @GetMapping(value = "/tree")
    public R<List<DirectoryStructureTree>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String description) {
        return R.ok(directoryStructureService.tree(sort, code, name, description));
    }

    @ApiOperation("资产目录树结构-应用视角")
    @Log(title = "资产目录")
    @GetMapping(value = "/tree/application")
    public R<List<DirectoryStructureTree>> treeApplication(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String description) {
        return R.ok(directoryStructureService.treeApplication(sort, code, name, description));
    }

}

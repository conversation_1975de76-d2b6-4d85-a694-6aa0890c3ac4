package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpStdCost;

import java.util.List;

public interface DwhTerpStdCostService extends IService<DwhTerpStdCost> {

    PageDataInfo<DwhTerpStdCost> overview(
            String date,
            String controlAreaId, String verId, String plantId, String productId,
            String sort, SearchVo searchVo);

    List<DwhTerpStdCost> listAll();

    List<DwhTerpStdCost> selectNoPage(String date,
                                      String controlAreaId,
                                      String verId,
                                      String plantId,
                                      String productId,
                                      String sort,
                                      SearchVo searchVo);
}
package com.datalink.fdop.drive.provider;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidPooledConnection;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.security.UserGroupInformation;

import java.security.PrivilegedExceptionAction;
import java.sql.SQLException;

@Slf4j
public class KerberosDruidDataSourceWrapper extends DruidDataSource {

    private boolean useKerberos;

    private String principal;

    private String keytab;

    private String hdfsSitePath;

    private String kerberosConfPath;


    public KerberosDruidDataSourceWrapper() {
        super();
        this.useKerberos = false;
    }

    public KerberosDruidDataSourceWrapper(String principal, String keytab, String hdfsSitePath, String kerberosConfPath,Boolean useKerberos) {
        super();
        this.principal = principal;
        this.keytab = keytab;
        this.hdfsSitePath = hdfsSitePath;
        this.kerberosConfPath = kerberosConfPath;
        this.useKerberos = useKerberos;
    }


    // 创建一个函数，指向父类的getConnection(long)方法
    public DruidPooledConnection superGetConnection(long maxWaitMillis) throws SQLException {
        return super.getConnection(maxWaitMillis);
    }

    /**
     * 覆写父类的getConnection(long)方法，在父类的getConnection(long)方法外面包裹上kerberbos认证的代码块
     */
    @Override
    public DruidPooledConnection getConnection(final long maxWaitMillis) throws SQLException {
        if (!useKerberos) {
            return superGetConnection(maxWaitMillis);
        }
        // 在UserGroupInformation的doAs函数中实现Connection的创建
        // 覆写父类的getConnection(long)方法，在方法外面包裹上kerberbos认证的代码块
        KerberosDruidDataSourceWrapper _this = this;
        UserGroupInformation ugi = kerberosLogin();
        DruidPooledConnection conn = null;
        try {
            conn = ugi.doAs((PrivilegedExceptionAction<DruidPooledConnection>) () -> {
                DruidPooledConnection tcon = null;
                try {
                    // 父类的getConnection(long)方法
                    tcon = _this.superGetConnection(maxWaitMillis);
                } catch (SQLException e) {
                    log.error("getConnection error", e);
                }
                return tcon;
            });
        } catch (Exception e) {
            log.error("UserGroupInformation doAs getConnection error", e);
        }
        // 返回connection
        return conn;
    }

    /**
     * Hadoop kerberos 权限验证登录
     * @return
     */
    private UserGroupInformation kerberosLogin() {
        log.info("principal {}", principal);
        log.info("keytab {}", keytab);
        log.info("kerberosConfPath {}", kerberosConfPath);
        log.info("hdfsSitePath {}", hdfsSitePath);
        System.setProperty("java.security.krb5.conf", kerberosConfPath);
        // System.setProperty("sun.security.krb5.debug", "true");
        Configuration conf = new Configuration();
        conf.set("hadoop.security.authentication", "Kerberos");
        // 获取hdfs site path配置路径
        if (StringUtils.isNotEmpty(hdfsSitePath)) {
            String[] sitePathArray = hdfsSitePath.split(",");
            for (String sitePath : sitePathArray) {
                conf.addResource(new Path(sitePath));
            }
        }
        UserGroupInformation.setConfiguration(conf);
        UserGroupInformation ugi = null;
        try {
            ugi = UserGroupInformation.loginUserFromKeytabAndReturnUGI(principal, keytab);
        } catch (Exception e) {
           log.error("kerberosLogin error", e);
        }
        return ugi;
    }
}
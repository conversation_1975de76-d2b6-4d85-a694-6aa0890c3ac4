package com.datalink.fdop.fscm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.fscm.api.domain.ReceiptsState;
import com.datalink.fdop.fscm.mapper.ReceiptsStateMapper;
import com.datalink.fdop.fscm.service.ReceiptsStateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2022/11/21 14:19
 */
@Service
@Transactional
public class ReceiptsStateServiceImpl extends ServiceImpl<ReceiptsStateMapper, ReceiptsState> implements ReceiptsStateService {
}

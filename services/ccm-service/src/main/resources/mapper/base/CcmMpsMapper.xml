<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CcmMpsMapper">

    <delete id="deleteBykey">
        delete from zjdata.ccm_mps
        WHERE ver_id=#{verId} and plant_id = #{plantId} and product_id = #{productId} and mps_year = #{mpsYear}
          and mps_month = #{mpsMonth} and mps_type = #{mpsType}
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_mps
        WHERE ver_id=#{verId} and plant_id = #{plantId} and product_id = #{productId} and mps_year = #{mpsYear}
        and mps_month = #{mpsMonth} and mps_type = #{mpsType}
        limit 1
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CcmMps">
        update zjdata.ccm_mps
        set
            <if test="productMpsId != null and productMpsId != '' ">product_mps_id = #{productMpsId},</if>
            <if test="mpsIn != null and mpsIn!='' ">mps_in = #{mpsIn},</if>
            <if test="mpsOut != null and mpsOut != '' ">mps_out = #{mpsOut},</if>
            <if test="mpsRemark != null and mpsRemark != '' ">mps_remark = #{mpsRemark},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where ver_id=#{verId} and plant_id = #{plantId} and product_id = #{productId} and mps_year = #{mpsYear}
        and mps_month = #{mpsMonth} and mps_type = #{mpsType}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.CcmMps">
        SELECT a.*
        FROM zjdata.ccm_mps a
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
            <if test="verId != null and verId != ''">
                and a.ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and a.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and CONCAT(a.mps_year, a.mps_month) between #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
        ORDER BY a.ver_id, a.plant_id, a.product_id,
                 a.mps_year,a.mps_month,a.mps_type
                 ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.base.api.domain.CcmMps">
        SELECT a.*
        FROM zjdata.ccm_mps a
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
            <if test="verId != null and verId != ''">
                and a.ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and a.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and CONCAT(a.mps_year, a.mps_month) between #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
        ORDER BY a.ver_id, a.plant_id, a.product_id,
        a.mps_year,a.mps_month,a.mps_type
        ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_mps
        SET enable = #{enable}
        where (ver_id, plant_id, product_id, mps_year, mps_month, mps_type ) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.verId},#{item.plantId},#{item.productId},#{item.mpsYear},#{item.mpsMonth},#{item.mpsType})
        </foreach>
    </update>

</mapper>


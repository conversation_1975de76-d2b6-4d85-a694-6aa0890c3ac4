package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRateStructureP;

import java.util.List;

public interface DwhFinalRateStructurePService extends IService<DwhFinalRateStructureP> {

    PageDataInfo<DwhFinalRateStructureP> overview(
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String sort,
            SearchVo searchVo);

    List<DwhFinalRateStructureP> selectNoPage(String controlAreaId,
                                              String verId,
                                              String yearMonthFrom,
                                              String yearMonthTo,
                                              String costCenterType,
                                              String sort,
                                              SearchVo searchVo);
}
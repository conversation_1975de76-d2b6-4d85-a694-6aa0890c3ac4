package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalAllocationCostP;
import com.datalink.fdop.engine.mapper.DwhFinalAllocationCostPMapper;
import com.datalink.fdop.engine.service.DwhFinalAllocationCostPService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalAllocationCostPServiceImpl extends ServiceImpl<DwhFinalAllocationCostPMapper, DwhFinalAllocationCostP> implements DwhFinalAllocationCostPService {

    @Autowired
    private DwhFinalAllocationCostPMapper dwhFinalAllocationCostPMapper;

    @Override
    public PageDataInfo<DwhFinalAllocationCostP> selectTargetedFinalAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo) {
        Page<DwhFinalAllocationCostP> page = PageUtils.getPage(DwhFinalAllocationCostP.class);
        IPage<DwhFinalAllocationCostP> iPage = dwhFinalAllocationCostPMapper.selectTargetedFinalAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public PageDataInfo<DwhFinalAllocationCostP> selectCostFinalAllocation(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo) {
        Page<DwhFinalAllocationCostP> page = PageUtils.getPage(DwhFinalAllocationCostP.class);
        IPage<DwhFinalAllocationCostP> iPage = dwhFinalAllocationCostPMapper.selectCostFinalAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public Boolean checkAllocationCost(String controlAreaId, String verId, String yearMonthFrom, String yearMonthTo, String allocationMethodId) {
        String checkAllocationCost = dwhFinalAllocationCostPMapper.checkAllocationCost(controlAreaId, verId, yearMonthFrom, yearMonthTo, allocationMethodId);
        if (StringUtils.isEmpty(checkAllocationCost)) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean checkTargetedAllocationCost(String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType) {
        int checkTargetedAllocationCost = dwhFinalAllocationCostPMapper.checkTargetedAllocationCost(verId, companyId, yearMonthFrom, yearMonthTo, allocationType);
        if (checkTargetedAllocationCost == 0) {
            return true;
        }
        return false;
    }

    @Override
    public List<DwhFinalAllocationCostP> selectNoPage(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo) {
        return dwhFinalAllocationCostPMapper.selectNoPage(controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, sort, searchVo);
    }

}
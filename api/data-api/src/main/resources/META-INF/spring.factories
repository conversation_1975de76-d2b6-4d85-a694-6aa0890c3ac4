org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
  com.datalink.fdop.element.api.factory.RemoteEntityTableFallbackFactory,\
  com.datalink.fdop.element.api.factory.RemoteEntityStrureFallbackFactory,\
  com.datalink.fdop.element.api.factory.RemoteElementStrureFallbackFactory,\
  com.datalink.fdop.element.api.factory.RemoteElementFallbackFactory,\
  com.datalink.fdop.element.api.factory.RemoteEntityFallbackFactory,\
  com.datalink.fdop.drive.api.factory.RemoteFieldRelationFallbackFactory,\
  com.datalink.fdop.drive.api.factory.RemoteDriveFallbackFactory,\
  com.datalink.fdop.drive.api.factory.RemoteJdbcFallbackFactory,\
  com.datalink.fdop.drive.api.factory.RemoteTaskFallbackFactory,\
  com.datalink.fdop.drive.api.factory.RemoteTaskAndProcessFallbackFactory,\
  com.datalink.fdop.drive.api.factory.RemoteHive2FallbackFactory,\
  com.datalink.fdop.gather.api.factory.RemoteGatherFallbackFactory,\
  com.datalink.fdop.gather.api.factory.RemoteGatherLogFallbackFactory,\
  com.datalink.fdop.govern.api.factory.RemoteStandardFallbackFactory,\
  com.datalink.fdop.govern.api.factory.RemoteDolTaskFallbackFactory,\
  com.datalink.fdop.govern.api.factory.RemoteSyncFallbackFactory,\
  com.datalink.fdop.govern.api.factory.RemoteObjectPropertiesFallbackFactory,\
  com.datalink.fdop.quality.api.factory.RemoteQualityFallbackFactory,\
  com.datalink.fdop.quality.api.factory.RemoteQualityLogFallbackFactory,\
  com.datalink.fdop.graph.api.factory.RemoteFlinkSqlFallbackFactory,\
  com.datalink.fdop.graph.api.factory.RemoteEtlFallbackFactory,\
  com.datalink.fdop.graph.api.factory.RemoteFlinkGraphlFallbackFactory,\
  com.datalink.fdop.datart.api.factory.RemoteDatartUserFallbackFactory,\
  com.datalink.fdop.seatunnel.api.factory.RemoteSeaTunnelFallbackFactory,\
  com.datalink.fdop.seatunnel.api.factory.RemoteSeaTunnelRestApiFallbackFactory,\
  com.datalink.fdop.udf.api.factory.RemoteUdfFallbackFactory,\
  com.datalink.fdop.stream.api.factory.RemoteStreamFallbackFactory


package com.datalink.fdop.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.*;
import com.datalink.fdop.project.api.model.vo.BaseMaterialShowVo;
import com.datalink.fdop.project.api.model.vo.BaseMaterialVo;
import com.datalink.fdop.project.service.*;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/31 10:32
 */
@RestController
@RequestMapping("/base/material")
@Transactional
public class BaseMaterialController {

    @Autowired
    private BaseMaterialService baseMaterialService;
    @Autowired
    private ManufacturerService manufacturerService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private ManufacturerManageService manufacturerManageService;

    @Autowired
    private SupplierService supplierService;

    @ApiOperation(value = "新增")
    @Log(title = "工程资料",businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R create(@RequestBody List<BaseMaterial> baseMaterials) {
        baseMaterials.stream().forEach(baseMaterial -> {
            if (StringUtils.isEmpty(baseMaterial.getSupplierMaterialDesc())) {
                throw new ServiceException("供应商物料描述为空！");
            }
            if (StringUtils.isEmpty(baseMaterial.getSupplierMaterialCode())) {
                throw new ServiceException("供应商物料编码为空！");
            }
            if (StringUtils.isEmpty(baseMaterial.getMaterialCode())) {
                throw new ServiceException("内部物料编码为空！");
            }
            if (StringUtils.isEmpty(baseMaterial.getSupplierCode())) {
                throw new ServiceException("供应商编码为空！");
            }
            ManufacturerManage one = manufacturerManageService.getOne(new QueryWrapper<ManufacturerManage>().lambda().eq(ManufacturerManage::getSerialNum, "1"));
            if (one.getStatu().equals("0")) {
                List<Supplier> list = supplierService.list(new QueryWrapper<Supplier>()
                        .lambda()
                        .eq(Supplier::getSupplierCode, baseMaterial.getSupplierCode())
                        );
                if (CollectionUtils.isEmpty(list)) {
                    throw new ServiceException("供应商编码不存在，请先维护！");
                }
            }else {
                if (StringUtils.isEmpty(baseMaterial.getManufCode())){
                    throw new  ServiceException("制造商代码为空！");
                }
                if (StringUtils.isEmpty(baseMaterial.getManufType())){
                    throw new  ServiceException("制造商类别为空！");
                }
                List<Manufacturer> list = manufacturerService.list(new QueryWrapper<Manufacturer>()
                        .lambda()
                        .eq(Manufacturer::getSupplierCode, baseMaterial.getSupplierCode())
                        .eq(Manufacturer::getManufCode,StringUtils.isNotEmpty(baseMaterial.getManufCode())? baseMaterial.getManufCode(): Constants.DATA_DEFAULT_VALUE)
                        .eq(Manufacturer::getManufType,StringUtils.isNotEmpty(baseMaterial.getManufType())? baseMaterial.getManufType(): Constants.DATA_DEFAULT_VALUE));
                if (CollectionUtils.isEmpty(list)) {
                    throw new ServiceException("供应商编码&制造商代码&制造商类别组合不存在，请先维护！");
                }
            }

            List<Material> materials = materialService.list(new QueryWrapper<Material>()
                    .lambda()
                    .eq(Material::getMaterialCode, baseMaterial.getMaterialCode()));
            if (CollectionUtils.isEmpty(materials)) {
                throw new ServiceException("内部物料编码不存在，请先维护！");
            }
            BaseMaterial baseMaterialServiceOne = baseMaterialService.getOne(new QueryWrapper<BaseMaterial>()
                    .lambda()
                    .eq(BaseMaterial::getManufCode, baseMaterial.getManufCode())
                    .eq(BaseMaterial::getManufType, baseMaterial.getManufType())
                    .eq(BaseMaterial::getSupplierCode, baseMaterial.getSupplierCode())
                    .eq(BaseMaterial::getMaterialCode, baseMaterial.getMaterialCode()));
            if (baseMaterialServiceOne!=null) {
                throw new ServiceException("制造商编码&制造商类型&供应商编码&内部物料编码组合已存在！");
            }
        });
        if (baseMaterialService.saveBatch(baseMaterials)) {
            return R.ok("保存成功");
        }else {
            return R.fail("保存失败");
        }
    }


    @ApiOperation(value = "修改")
    @Log(title = "工程资料",businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody List<BaseMaterial> baseMaterials) {
        baseMaterials.stream().forEach(baseMaterial -> {
            if (StringUtils.isEmpty(baseMaterial.getSupplierMaterialDesc())) {
                throw new ServiceException("供应商物料描述为空！");
            }
            if (StringUtils.isEmpty(baseMaterial.getSupplierMaterialCode())) {
                throw new ServiceException("供应商物料编码为空！");
            }
            if (StringUtils.isEmpty(baseMaterial.getMaterialCode())) {
                throw new ServiceException("内部物料编码为空！");
            }
            if (StringUtils.isEmpty(baseMaterial.getSupplierCode())) {
                throw new ServiceException("供应商编码为空！");
            }
            ManufacturerManage one = manufacturerManageService.getOne(new QueryWrapper<ManufacturerManage>().lambda().eq(ManufacturerManage::getSerialNum, "1"));
            if (one.getStatu().equals("0")) {
                List<Supplier> list = supplierService.list(new QueryWrapper<Supplier>()
                        .lambda()
                        .eq(Supplier::getSupplierCode, baseMaterial.getSupplierCode())
                        );
                if (CollectionUtils.isEmpty(list)) {
                    throw new ServiceException("供应商编码不存在，请先维护！");
                }
            }else {
                if (StringUtils.isEmpty(baseMaterial.getManufCode())){
                    throw new  ServiceException("制造商代码为空！");
                }
                if (StringUtils.isEmpty(baseMaterial.getManufType())){
                    throw new  ServiceException("制造商类别为空！");
                }
                List<Manufacturer> list = manufacturerService.list(new QueryWrapper<Manufacturer>()
                        .lambda()
                        .eq(Manufacturer::getSupplierCode, baseMaterial.getSupplierCode())
                        .eq(StringUtils.isNotEmpty(baseMaterial.getManufCode()),Manufacturer::getManufCode, baseMaterial.getManufCode())
                        .eq(StringUtils.isNotEmpty(baseMaterial.getManufType()),Manufacturer::getManufType, baseMaterial.getManufType())
                );
                if (CollectionUtils.isEmpty(list)) {
                    throw new ServiceException("供应商编码&制造商代码&制造商类别组合不存在，请先维护！");
                }
            }
            List<Material> materials = materialService.list(new QueryWrapper<Material>()
                    .lambda()
                    .eq(Material::getMaterialCode, baseMaterial.getMaterialCode()));
            if (CollectionUtils.isEmpty(materials)) {
                throw new ServiceException("内部物料编码不存在，请先维护！");
            }
            BaseMaterial baseMaterialServiceOne = baseMaterialService.getOne(new QueryWrapper<BaseMaterial>()
                    .lambda()
                    .eq(BaseMaterial::getManufCode, baseMaterial.getManufCode())
                    .eq(BaseMaterial::getManufType, baseMaterial.getManufType())
                    .eq(BaseMaterial::getSupplierCode, baseMaterial.getSupplierCode())
                    .eq(BaseMaterial::getMaterialCode, baseMaterial.getMaterialCode())
                    .ne(BaseMaterial::getId,baseMaterial.getId())
            );
            if (baseMaterialServiceOne!=null) {
                throw new ServiceException("制造商编码&制造商类型&供应商编码&内部物料编码组合已存在！");
            }
        });
        if (baseMaterialService.saveOrUpdateBatch(baseMaterials)) {
            return R.ok("保存成功");
        }else {
            return R.fail("保存失败");
        }
    }



    @ApiOperation(value = "查询")
    @Log(title = "工程资料",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody BaseMaterialVo baseMaterialVo) {
        Page<BaseMaterialShowVo> page = PageUtils.getPage(BaseMaterialShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (baseMaterialVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<BaseMaterialShowVo> pageData=baseMaterialService.pageData(page,baseMaterialVo);
        List<BaseMaterialShowVo> records = pageData.getRecords();
        if (baseMaterialVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(baseMaterialVo.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }

    @ApiOperation(value = "导出MPN")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(@RequestBody BaseMaterialVo baseMaterialVo, HttpServletResponse response) throws IOException {
        Page<BaseMaterialShowVo> page = PageUtils.getPage(BaseMaterialShowVo.class);
        if (baseMaterialVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<BaseMaterialShowVo> pageData=baseMaterialService.pageData(page,baseMaterialVo);
        List<BaseMaterialShowVo> records = pageData.getRecords();
        if (baseMaterialVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(baseMaterialVo.getSearchVo(), records);
        }
        ExcelUtils.export3Excel(response,records, BaseMaterialShowVo.class,"MPN数据");

    }

    @ApiOperation(value = "删除")
    @Log(title = "工程资料",businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids) {
        if (baseMaterialService.removeBatchByIds(ids)) {
            return R.ok("删除成功");
        }else {
            return R.fail("删除失败");
        }
    }


}

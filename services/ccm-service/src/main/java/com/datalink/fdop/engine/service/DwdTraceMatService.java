package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdTraceMat;

import java.util.List;

public interface DwdTraceMatService extends IService<DwdTraceMat> {

    PageDataInfo<DwdTraceMat> overview(String yearMonth, String controlAreaId, String companyId, String sort, SearchVo searchVo);

    List<DwdTraceMat> selectNoPage(String yearMonth,
                                   String controlAreaId,
                                   String companyId,
                                   String sort,
                                   SearchVo searchVo);
}
package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationCostP;
import com.datalink.fdop.engine.mapper.DwsNewAllocationCostPMapper;
import com.datalink.fdop.engine.service.DwsNewAllocationCostPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewAllocationCostPServiceImpl extends ServiceImpl<DwsNewAllocationCostPMapper, DwsNewAllocationCostP> implements DwsNewAllocationCostPService {

    @Autowired
    private DwsNewAllocationCostPMapper dwsNewAllocationCostPMapper;

    @Override
    public PageDataInfo<DwsNewAllocationCostP> selectTargetedNewAllocation(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String allocationType,
            String sort,
            SearchVo searchVo) {
        Page<DwsNewAllocationCostP> page = PageUtils.getPage(DwsNewAllocationCostP.class);
        IPage<DwsNewAllocationCostP> iPage = dwsNewAllocationCostPMapper.selectTargetedNewAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public PageDataInfo<DwsNewAllocationCostP> selectCostNewAllocation(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo) {
        Page<DwsNewAllocationCostP> page = PageUtils.getPage(DwsNewAllocationCostP.class);
        IPage<DwsNewAllocationCostP> iPage = dwsNewAllocationCostPMapper.selectCostNewAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewAllocationCostP> selectNoPage(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String sort, SearchVo searchVo) {
        return dwsNewAllocationCostPMapper.selectNoPage(controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, sort, searchVo);
    }
}
package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.DataApplicationFramework;
import com.datalink.fdop.govern.api.domain.DataApplicationFrameworkTree;

import java.util.List;

public interface IDataApplicationFrameworkService {

    int create(DataApplicationFramework applicationFramework);

    int update(DataApplicationFramework applicationFramework);

    int delete(List<Long> ids);

    DataApplicationFramework selectById(Long id);

    DataApplicationFramework selectByCode(String code);

    List<DataApplicationFrameworkTree> treeList(String sort, String code, String name, String description);

    PageDataInfo<DataApplicationFramework> overview(Long pid, String sort, SearchVo searchVo);

    List<SelectVo> selectVoList(DataApplicationFramework applicationFramework);

    int copy(Long pid, List<DataApplicationFramework> applicationFrameworkList);

}

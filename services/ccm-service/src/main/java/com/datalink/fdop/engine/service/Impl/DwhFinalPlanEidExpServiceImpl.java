package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalPlanEidExp;
import com.datalink.fdop.engine.mapper.DwhFinalPlanEidExpMapper;
import com.datalink.fdop.engine.service.DwhFinalPlanEidExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalPlanEidExpServiceImpl extends ServiceImpl<DwhFinalPlanEidExpMapper, DwhFinalPlanEidExp> implements DwhFinalPlanEidExpService {

    @Autowired
    private DwhFinalPlanEidExpMapper dwhFinalPlanEidExpMapper;

    @Override
    public PageDataInfo<DwhFinalPlanEidExp> overview(
            String verId, String plantId, String yearMonthFrom,
            String yearMonthTo, String sort, SearchVo searchVo) {
        Page<DwhFinalPlanEidExp> page = PageUtils.getPage(DwhFinalPlanEidExp.class);
        IPage<DwhFinalPlanEidExp> iPage = dwhFinalPlanEidExpMapper.selectAll(verId, plantId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalPlanEidExp> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwhFinalPlanEidExpMapper.selectNoPage(verId, plantId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
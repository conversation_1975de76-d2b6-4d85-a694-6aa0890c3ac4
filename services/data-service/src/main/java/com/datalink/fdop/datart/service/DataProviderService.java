package com.datalink.fdop.datart.service;


import com.datalink.fdop.datart.base.params.TestExecuteParam;
import com.datalink.fdop.datart.base.params.ViewExecuteParam;
import com.datalink.fdop.datart.domain.Source;
import datart.core.data.provider.*;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Set;

public interface DataProviderService {

    DataProviderConfigTemplate getSourceConfigTemplate(String type) throws IOException;

    String init(Long dataEntityId) throws Exception;

    Dataframe testExecute(TestExecuteParam testExecuteParam) throws Exception;

    Dataframe execute(ViewExecuteParam viewExecuteParam) throws Exception;

    Set<StdSqlOperator> supportedStdFunctions(String sourceId);

    boolean validateFunction(String sourceId, String snippet);

    String decryptValue(String value);

    DataProviderSource parseDataProviderConfig(Source source);

    void updateSource(Source source);

    Set<String> readAllDatabases(String sourceId) throws SQLException;

    Set<String> readTables(String sourceId, String database) throws SQLException;

    Set<Column> readTableColumns(String sourceId, String database, String table) throws SQLException;
}

package com.datalink.fdop.fscm.api.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;

/**
 * <AUTHOR>
 * @Date 2022/11/16 13:56
 */
public class DedicatedConverter implements  Converter<String> {


   @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 这里读的时候会调用
     *
     * @param context
     * @return
     */
    @Override
    public String convertToJavaData(ReadConverterContext<?> context) {
        return context.getReadCellData().toString();
    }

    /**
     * 这里是写的时候会调用 不用管
     *1-字符串；2-整数；3-浮点数；4-日期；5-时间
     * @return
     */
    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) {
        String paperType="";
        switch (context.getValue()){
            case "0":
                paperType="专用";
                break;
            case "1":
                paperType="不专用";
                break;
            default:
                paperType="";
        }
        return new WriteCellData<>(paperType);
    }


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CostElementMapper">

    <sql id="selectSql">
        SELECT
            COALESCE(c.chartofa, co.chartofa) AS chartofa,
            COALESCE(c.cost_element_id, co.cost_element_id) AS cost_element_id,
            COALESCE(c.cost_element_desc, co.cost_element_desc) AS cost_element_desc,
            COALESCE(c.cost_element_text, co.cost_element_text) AS cost_element_text,
            COALESCE(c.cost_element_type, co.cost_element_type) AS cost_element_type,
            COALESCE(c.cost_element_group_id, co.cost_element_group_id) AS cost_element_group_id,
            COALESCE(c.enable, co.enable) AS enable,
            COALESCE(c.create_by, co.create_by) AS create_by,
            COALESCE(c.create_time, co.create_time) AS create_time,
            COALESCE(c.update_by, co.update_by) AS update_by,
            COALESCE(c.update_time, co.update_time) AS update_time,
            COALESCE(c.delete_flag, false) AS delete_flag
        FROM zjdata.ccm_cost_element_orig co
                 FULL OUTER JOIN zjdata.ccm_cost_element c
                                 ON c.chartofa = co.chartofa
                                     and c.cost_element_id = co.cost_element_id
    </sql>

    <update id="batchDeleteByKey">
        update zjdata.ccm_cost_element
        set delete_flag = true
        where (chartofa, cost_element_id) in
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.chartofa}, #{item.costElementId})
        </foreach>
    </update>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT (
        SELECT COUNT(1) FROM zjdata.ccm_cost_element where chartofa = #{chartofa}  and cost_element_id = #{costElementId} and delete_flag = false
                                                     ) +
               (SELECT COUNT(1) FROM zjdata.ccm_cost_element_orig where chartofa = #{chartofa}  and cost_element_id = #{costElementId})
    </select>

    <delete id="deleteByKey" parameterType="String">
        delete from zjdata.ccm_cost_element
        where chartofa = #{chartofa} and cost_element_id = #{costElementId}
    </delete>

    <select id="checkIdExist" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM zjdata.ccm_cost_element where chartofa = #{chartofa}  and cost_element_id = #{costElementId} and delete_flag = false
    </select>

    <sql id="selectListSql">
        SELECT
           *
        FROM (<include refid="selectSql"/>) node
        <where>
            and node.delete_flag = false
            <if test="costElementGroupId != null and costElementGroupId != '' and costElementGroupId != '-1'">
                AND node.cost_element_group_id = #{costElementGroupId}
            </if>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY cost_element_id ${sort}
    </sql>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.CostElement">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.CostElement">
        <include refid="selectListSql"/>
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.CostElement">
        SELECT * FROM (<include refid="selectSql"/>) node where node.cost_element_id = #{costElementId} AND node.chartofa =#{chartofa} and node.delete_flag = false
    </select>

    <select id="selectIdsByPid" resultType="String">
        select cost_element_id from (<include refid="selectSql"/>) a where a.menu_id = #{pid} and a.delete_flag = false
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CostElement">
        update zjdata.ccm_cost_element
        set
            <if test="costElementDesc != null">cost_element_desc = #{costElementDesc},</if>
            <if test="costElementText != null">cost_element_text = #{costElementText},</if>
            <if test="costElementType != null">cost_element_type = #{costElementType},</if>
            <if test="costElementGroupId != null">cost_element_group_id = #{costElementGroupId},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where chartofa =#{chartofa} and cost_element_id = #{costElementId}
    </update>

    <select id="selectCompanyId" resultType="String" parameterType="String">
        select company_id from zjdata.ccm_company where company_id = #{id} limit 1
    </select>

    <select id="selectEvaluateAreaId" resultType="String" parameterType="String">
        select evaluate_area_id from zjdata.ccm_evaluate_area where evaluate_area_id = #{id} limit 1
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_cost_element
        SET enable = #{enable}
        where (chartofa, cost_element_id) in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            (#{item.chartofa}, #{item.costElementId})
        </foreach>
    </update>


    <select id="selectListByChartofaAndEnable" resultType="com.datalink.fdop.base.api.domain.CostElement">
        SELECT
            *
        FROM (<include refid="selectSql"/>) node
        where node.enable = true and node.chartofa = #{chartofa} and node.delete_flag = false
    </select>

    <select id="queryOrigList" resultType="com.datalink.fdop.base.api.domain.CostElement">
        select * from zjdata.ccm_cost_element_orig node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY cost_element_id ${sort}
    </select>

    <select id="queryList" resultType="com.datalink.fdop.base.api.domain.CostElement">
        select * from zjdata.ccm_cost_element node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY cost_element_id ${sort}
    </select>

    <delete id="batchDelete">
        delete from zjdata.ccm_cost_element
        where (chartofa, cost_element_id) in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            (#{item.chartofa}, #{item.costElementId})
        </foreach>
    </delete>





</mapper>


<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CostElementGroupHeadMapper">

    <delete id="batchDeleteByKey">
        delete from zjdata.ccm_cost_element_group_head
        where (control_area_id, cost_element_group_id) in
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.costElementGroupId})
        </foreach>
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM zjdata.ccm_cost_element_group_head where control_area_id = #{controlAreaId}  and cost_element_group_id = #{costElementGroupId}
    </select>

    <select id="selectById" parameterType="String" resultType="com.datalink.fdop.base.api.domain.CostElementGroupHead">
        SELECT * FROM zjdata.ccm_cost_element_group_head where control_area_id = #{controlAreaId}  and cost_element_group_id = #{costElementGroupId}
    </select>

    <sql id="selectListSql">
        SELECT
            node.*, area.control_area_desc as control_area_desc
        FROM zjdata.ccm_cost_element_group_head node
            left join zjdata.ccm_control_area area on node.control_area_id = area.control_area_id
        <where>
            <if test="costElementGroupId != null and costElementGroupId != '' and costElementGroupId != '-1'">
                AND node.control_area_id = #{controlAreaId}  AND node.cost_element_group_id = #{costElementGroupId}
            </if>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY cost_element_group_id ${sort}
    </sql>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.CostElementGroupHead">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.CostElementGroupHead">
        <include refid="selectListSql"/>
    </select>

    <select id="selectIdsByPid" resultType="com.datalink.fdop.base.api.domain.CostElementGroupHead">
        select control_area_id, cost_element_group_id from zjdata.ccm_cost_element_group_head where control_area_id = #{controlAreaId} and pid = #{pid}
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CostElementGroupHead">
        update zjdata.ccm_cost_element_group_head
        set
            <!--<if test="controlAreaId != null and controlAreaId != ''">control_area_id = #{controlAreaId},</if>
            <if test="costElementGroupId != null and costElementGroupId != ''">cost_element_group_id = #{costElementGroupId},</if>-->
            <if test="costElementGroupDesc != null">cost_element_group_desc = #{costElementGroupDesc},</if>
            <if test="costElementGroupText != null">cost_element_group_text = #{costElementGroupText},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id =#{controlAreaId} and cost_element_group_id = #{costElementGroupId}
    </update>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_cost_element_group_head
        SET enable = #{enable}
        where (control_area_id, cost_element_group_id) in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.costElementGroupId})
        </foreach>
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.CostElementGroupHeadTree">
        select
            cost_element_group_id as id,
            control_area_id as control_area_id,
            pid as pid,
            'MENU' as menuType,
            cost_element_group_desc as name,
            cost_element_group_text as description
        from
            zjdata.ccm_cost_element_group_head
        <where>
            <if test="controlAreaId != null and controlAreaId != ''">
                and control_area_id = #{controlAreaId}
            </if>
            <if test="code != null and code != ''">
                and cost_element_group_id = #{code}
            </if>
        </where>
        order by cost_element_group_id ${sort}
    </select>

</mapper>


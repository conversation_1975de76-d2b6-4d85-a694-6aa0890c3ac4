package com.datalink.fdop.quality.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.quality.api.enums.DataQualityType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/10/11 10:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "zjdata", value = "d_q_data_quality")
public class DataQuality extends BaseField {

    @ApiModelProperty(value = "规则id", required = true)
    @TableId(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @NotNull(message = "请选择一个菜单")
    @ApiModelProperty(value = "父节点id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    @NotNull(message = "规则类型不能为空")
    @ApiModelProperty(value = "规则类型(ELEMENT:元素,ONE_ENTITY:单实体,MORE_ENTITY:多实体)", required = true)
    private DataQualityType dataQualityType;

    @ApiModelProperty(value = "是否引用数据标准", required = true)
    private Boolean isCiteStandard;

    @ApiModelProperty(value = "数据标准ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private Long standardId;

    @ApiModelProperty(value = "是否设置配置类型", required = true)
    private Boolean isConfigType;

    @ApiModelProperty(value = "字段类型")
    private FieldType fieldType;

    @ApiModelProperty(value = "长度")
    private Long length;

    @ApiModelProperty(value = "精度")
    private Long decimalLength;

    @ApiModelProperty(value = "正则表达式的规则", required = true)
    private RegexRule regexRule;

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.ControlAreaMapper">

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_control_area
        WHERE control_area_id = #{id} limit 1
    </select>

    <delete id="deleteById" parameterType="String">
        delete from zjdata.ccm_control_area
        where control_area_id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from zjdata.ccm_control_area
        WHERE control_area_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_control_area
            SET enable = #{item.enable}
            WHERE control_area_id = #{item.controlAreaId}
        </foreach>
    </update>


    <select id="pageList" resultType="com.datalink.fdop.base.api.domain.ControlArea">
        select * from zjdata.ccm_control_area  a
        <where>
            <if test="a!=null and a!=''">
                AND ${a}
            </if>
        </where>
        order by control_area_id
        <if test="sort != null and sort != ''">
            ${sort}
        </if>
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.base.api.domain.ControlArea">
        SELECT * FROM zjdata.ccm_control_area
        WHERE control_area_id = #{id} limit 1
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_control_area (
        control_area_id, control_area_desc, control_area_text,currency,chartofa, enable, create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.controlAreaId},#{item.controlAreaDesc},#{item.controlAreaText},
            #{item.currency},#{item.chartofa},#{item.enable},
            #{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>

</mapper>


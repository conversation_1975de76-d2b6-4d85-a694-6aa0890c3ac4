package com.datalink.fdop.param.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.param.api.domain.GlobalParam;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/9 21:28
 */
public class DomainAgeUtils {

    public static String getGlobalParamAgeStr(GlobalParam globalParam) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (globalParam.getId() != null ? globalParam.getId() : IdWorker.getId()))
                .append(StringUtils.isNotEmpty(globalParam.getCode()) ? ", code: '" + globalParam.getCode() + "'" : "")
                .append(StringUtils.isNotEmpty(globalParam.getName()) ? ", name: '" + globalParam.getName() + "'" : "")
                .append(StringUtils.isNotEmpty(globalParam.getDescription()) ? ", description: '" + globalParam.getDescription() + "'" : "")
                .append(globalParam.getParamUseType() != null ? ", paramUseType: '" + globalParam.getParamUseType() + "'" : "")
                .append(globalParam.getParamType() != null ? ", paramType: '" + globalParam.getParamType() + "'" : "")
                .append(globalParam.getValue() != null ? ", value: '" + globalParam.getValue() + "'" : "")
                .append(", createBy: '" + (StringUtils.isNotEmpty(globalParam.getCreateBy()) ? globalParam.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (globalParam.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(globalParam.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

}

package com.datalink.fdop.stream.service.impl;//package com.datalink.fdop.stream.service.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.datalink.fdop.stream.service.DebeziumService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Service;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.List;
//
//@Service
//public class DebeziumServiceImpl implements DebeziumService {
//
//    @Value("${k8s.url}:")
//    private String k8sIp;
//
//    @Autowired
//    private RestTemplate restTemplate;
//
//    @Override
//    public List getConnectorList(Integer port) {
//        return restTemplate.getForObject(k8sIp+port+"/connectors", List.class);
//    }
//
//    @Override
//    public JSONObject createConnector(Integer port, JSONObject jsonObject) {
//        return restTemplate.postForObject(k8sIp+port+"/connectors",jsonObject,JSONObject.class);
//    }
//
//    @Override
//    public void pauseConnector(Integer port, String name) {
//        restTemplate.put(k8sIp+port+"/connectors/"+name+"/pause", null);
//    }
//
//    @Override
//    public void resumeConnector(Integer port, String name) {
//        restTemplate.put(k8sIp+port+"/connectors/"+name+"/resume", null);
//    }
//
//    @Override
//    public void restartConnector(Integer port, String name) {
//        restTemplate.postForObject(k8sIp+port+"/connectors/"+name+"/restart", null, JSONObject.class);
//    }
//
//    @Override
//    public void deleteConnector(Integer port, String name) {
//        restTemplate.delete(k8sIp+port+"/connectors/"+name);
//    }
//
//    @Override
//    public JSONObject getConnector(Integer port, String name) {
//        return restTemplate.getForObject(k8sIp+port+"/connectors/"+name, JSONObject.class);
//    }
//
//    @Override
//    public JSONObject getConfig(Integer port, String name) {
//        return restTemplate.getForObject(k8sIp+port+"/connectors/"+name+"/config", JSONObject.class);
//    }
//
//    @Override
//    public JSONObject updateConfig(Integer port, String name) {
//        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(k8sIp+port+"/connectors/"+name+"/config", HttpMethod.PUT, null, JSONObject.class);
//        return responseEntity.getBody();
//    }
//
//    @Override
//    public JSONObject getConnectorStatus(Integer port, String name) {
//        return restTemplate.getForObject(k8sIp+port+"/connectors/"+name+"/status", JSONObject.class);
//    }
//
//    @Override
//    public JSONObject validateJson(Integer port, String type, JSONObject jsonObject) {
//        HttpEntity<JSONObject> entity = new HttpEntity<>(jsonObject);
//        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(k8sIp+port+"/connector-plugins/"+type+"/config/validate", HttpMethod.PUT, entity, JSONObject.class);
//        return responseEntity.getBody();
//    }
//}

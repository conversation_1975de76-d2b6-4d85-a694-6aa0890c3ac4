package com.datalink.fdop.gateway.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

@Slf4j
@Service
public class DynamicMacService {

    // MAC地址正则表达式
    private static final Pattern MAC_PATTERN =
            Pattern.compile("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$", Pattern.CASE_INSENSITIVE);

    @Value("${host.network.mount-path:/sys/class/net}")
    private String mountPath;

    // 优先网卡列表
    @Value("${host.network.interfaces:eth0,ens33,enp0s3,wlan0}")
    private String[] interfaces;

    // MAC地址缓存
    private String cachedMacAddress;
    private long lastUpdateTime;

    @PostConstruct
    public void init() {
        refreshMacAddress();
    }

    /**
     * 获取当前宿主机MAC地址（带缓存机制）
     */
    public String getCurrentMacAddress() {
        // 每5分钟刷新一次缓存
        if (System.currentTimeMillis() - lastUpdateTime > 300_000) {
            refreshMacAddress();
        }
        return cachedMacAddress;
    }

    public void refreshMacAddress() {
        for (String iface : interfaces) {
            try {
                String macPath = String.format("%s/%s/address", mountPath, iface);
                File macFile = new File(macPath);
                if (macFile.exists()) {
                    String mac = FileUtils.readFileToString(macFile, StandardCharsets.UTF_8).trim();
                    if (isValidMac(mac)) {
                        updateCachedMac(mac);
                        log.info("Refreshed host MAC from {}: {}", iface, cachedMacAddress);
                        return;
                    }
                }
            } catch (Exception e) {
                log.warn("Failed to get MAC for interface {}", iface, e);
            }
        }

        // 尝试系统命令获取MAC地址
        for (String iface : interfaces) {
            try {
                String mac = getMacBySystemCommand(iface);
                if (isValidMac(mac)) {
                    updateCachedMac(mac);
                    log.warn("Using fallback MAC from {}: {}", iface, cachedMacAddress);
                    return;
                }
            } catch (Exception e) {
                log.warn("Failed to get MAC via command for {}", iface, e);
            }
        }
        log.error("Failed to retrieve valid MAC address for any interface");
        cachedMacAddress = null;
    }

    private String getMacBySystemCommand(String interfaceName) {
        try {
            Process process = Runtime.getRuntime().exec(new String[]{
                    "sh", "-c", "ip link show " + interfaceName + " | awk '/ether/ {print $2}'"
            });
            try (InputStream inputStream = process.getInputStream()) {
                byte[] bytes = IOUtils.toByteArray(inputStream);
                return new String(bytes, StandardCharsets.UTF_8).trim();
            }
        } catch (Exception e) {
            log.error("Failed to get MAC via system command for {}", interfaceName, e);
            return null;
        }
    }

    private void updateCachedMac(String mac) {
        this.cachedMacAddress = mac.toLowerCase();
        this.lastUpdateTime = System.currentTimeMillis();
    }

    private boolean isValidMac(String mac) {
        return mac != null && MAC_PATTERN.matcher(mac).matches()
                && !mac.equalsIgnoreCase("00:00:00:00:00:00")
                && !mac.equalsIgnoreCase("00-00-00-00-00-00");
    }
}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRecoverWip;

import java.util.List;

public interface DwsNewRecoverWipService extends IService<DwsNewRecoverWip> {

    PageDataInfo<DwsNewRecoverWip> overview(String verId,String plantId, String date, String sort, SearchVo searchVo);

    List<DwsNewRecoverWip> listAll();

    List<DwsNewRecoverWip> selectNoPage(String verId, String plantId, String date, String sort, SearchVo searchVo);
}
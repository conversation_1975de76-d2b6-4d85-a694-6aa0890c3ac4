<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.WorkCenterMapper">

    <update id="deleteByKey">
        update zjdata.ccm_work_center
        set delete_flag = true
        where (plant_id, work_center_id, date_to) in
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.plantId}, #{item.workCenterId}, #{item.dateTo})
        </foreach>
    </update>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        select
        (SELECT COUNT(1) FROM zjdata.ccm_work_center WHERE plant_id = #{plantId} and work_center_id = #{workCenterId} AND date(date_to) = #{dateTo}::date and delete_flag = false)
        +
        (SELECT COUNT(1) FROM zjdata.ccm_work_center_orig WHERE plant_id = #{plantId} and work_center_id = #{workCenterId} AND date(date_to) = #{dateTo}::date)
    </select>

    <select id="checkIdExist" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM zjdata.ccm_work_center WHERE plant_id = #{plantId} and work_center_id = #{workCenterId} AND date(date_to) = #{dateTo}::date and delete_flag = false limit 1
    </select>

<!--    <update id="deleteById" parameterType="String">-->
<!--        update zjdata.ccm_work_center-->
<!--        set delete_flag = true-->
<!--        where work_center_id = #{id} AND date_to = '${date}' and plant_id = #{plantId}-->
<!--    </update>-->

    <delete id="deleteById" parameterType="String">
        delete from zjdata.ccm_work_center
        where work_center_id = #{workCenterId} AND date_to = '${dateTo}' and plant_id = #{plantId}
    </delete>

    <sql id="selectSql">
        SELECT
            COALESCE(c.work_center_id, co.work_center_id) AS work_center_id,
            COALESCE(c.date_from, co.date_from) AS date_from,
            COALESCE(c.date_to, co.date_to) AS date_to,
            COALESCE(c.work_center_desc, co.work_center_desc) AS work_center_desc,
            COALESCE(c.work_center_text, co.work_center_text) AS work_center_text,
            COALESCE(c.work_cost_center, co.work_cost_center) AS work_cost_center,
            COALESCE(c.cost_center_id, co.cost_center_id) AS cost_center_id,
            COALESCE(c.menu_id, co.menu_id) AS menu_id,
            COALESCE(c.enable, co.enable) AS enable,
            COALESCE(c.create_by, co.create_by) AS create_by,
            COALESCE(c.create_time, co.create_time) AS create_time,
            COALESCE(c.update_by, co.update_by) AS update_by,
            COALESCE(c.update_time, co.update_time) AS update_time,
            COALESCE(c.plant_id, co.plant_id) AS plant_id,
            COALESCE(c.control_area, co.control_area) AS control_area,
            COALESCE(c.delete_flag, false) AS delete_flag
        FROM zjdata.ccm_work_center_orig co
                 FULL OUTER JOIN zjdata.ccm_work_center c
                                 ON c.work_center_id = co.work_center_id
                                     and c.date_to = co.date_to and c.plant_id = co.plant_id
    </sql>

    <sql id="selectListSql">
        SELECT
            node.*, controlArea.control_area_desc as control_area_desc, menu.name as menu_desc,b.plant_desc
        FROM (
        <include refid="selectSql"/>
        ) node
        left join zjdata.ccm_control_area controlArea on node.control_area = controlArea.control_area_id
        left join zjdata.ccm_project_menu menu on node.menu_id = menu.menu_id
        left join zjdata.ccm_plant b on node.plant_id = b.plant_id
        <where>
            AND node.delete_flag = false
            <if test="menuId != null and menuId != '-1'">
                AND node.menu_id = #{menuId}
            </if>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY work_center_id ${sort}
    </sql>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        SELECT * FROM (
            <include refid="selectSql"/>
        ) AS node
        where (node.plant_id, node.work_center_id, node.date_to) in
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.plantId}, #{item.workCenterId}, #{item.dateTo})
        </foreach>
    </select>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        <include refid="selectListSql"/>
    </select>


    <select id="list" parameterType="com.datalink.fdop.base.api.domain.WorkCenter" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        SELECT a.*, costCenter.cost_center_desc as cost_center_desc, menu.menu_desc as menu_desc
        FROM (
        <include refid="selectSql"/>
        ) a
        left join zjdata.ccm_cost_center costCenter on a.cost_center_id = costCenter.cost_center_id
        left join zjdata.ccm_work_center_menu menu on a.menu_id = menu.menu_id
        <where>
            and a.delete_flag = false
            <if test="workCenter.workCenterId != null and workCenter.workCenterId != ''">
                AND a.work_center_id = #{workCenter.workCenterId}
            </if>
            <if test="workCenter.menuId != null and workCenter.menuId != ''">
                AND a.menu_id = #{workCenter.menuId}
            </if>
            <if test="workCenter.dateFrom != null">
                AND a.date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(workCenter.dateFrom)}'
            </if>
        </where>
        ORDER BY work_center_id, date_to desc
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        SELECT a.*, costCenter.cost_center_desc as cost_center_desc, menu.menu_desc as menu_desc
        FROM (
        <include refid="selectSql"/>
             ) a
                 left join zjdata.ccm_cost_center costCenter on a.cost_center_id = costCenter.cost_center_id
                 left join zjdata.ccm_work_center_menu menu on a.menu_id = menu.menu_id
        where a.work_center_id = #{id} AND a.date_to = '${date}' and a.plant_id =#{plantId} and a.delete_flag = false
        limit 1
    </select>

    <select id="selectIdsByPid" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        select date_to, work_center_id, date_to from (
            <include refid="selectSql"/>
        ) a where a.menu_id = #{pid} and a.delete_flag = false
    </select>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.WorkCenter">
        update zjdata.ccm_work_center
        set
        <if test="dateFrom != null">date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}',</if>
        <if test="workCenterDesc != null">work_center_desc = #{workCenterDesc},</if>
        <if test="workCenterText != null">work_center_text = #{workCenterText},</if>
        <if test="workCostCenter != null">work_cost_center = #{workCostCenter},</if>
        <if test="costCenterId != null">cost_center_id = #{costCenterId},</if>
        <if test="menuId != null">menu_id = #{menuId},</if>
        <if test="controlArea != null">control_area = #{controlArea},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where
            plant_id =#{plantId} and work_center_id = #{workCenterId} and date_to = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateTo)}'
    </update>

    <select id="selectCostCenterId" resultType="String" parameterType="String">
        select cost_center_id from (<include refid="selectSql"/> ) a
                              where a.cost_center_id = #{id} and a.delete_flag = false limit 1
    </select>

    <select id="select" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        SELECT a.*, costCenter.cost_center_desc as cost_center_desc, menu.menu_desc as menu_desc
        FROM (
        <include refid="selectSql"/>
        ) a
        left join zjdata.ccm_cost_center costCenter on a.cost_center_id = costCenter.cost_center_id
        left join zjdata.ccm_work_center_menu menu on a.menu_id = menu.menu_id
        <where>
            and a.delete_flag = false
            <if test="startCode != null and startCode != ''">
                AND
                a.work_center_id >= #{startCode}
            </if>
            <if test="endCode != null and endCode != ''">
                AND
                a.work_center_id &lt;= #{endCode}
            </if>
        </where>
        ORDER BY work_center_id, date_to,plant_id ${sort}
    </select>

    <select id="getAll" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        SELECT a.*, costCenter.cost_center_desc as cost_center_desc, menu.menu_desc as menu_desc
        FROM zjdata.ccm_work_center a
                 left join zjdata.ccm_cost_center costCenter on a.cost_center_id = costCenter.cost_center_id
                 left join zjdata.ccm_work_center_menu menu on a.menu_id = menu.menu_id
        ORDER BY work_center_id, date_to,plant_id desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        SELECT a.*, costCenter.cost_center_desc as cost_center_desc, menu.menu_desc as menu_desc
        FROM zjdata.ccm_work_center a
        left join zjdata.ccm_cost_center costCenter on a.cost_center_id = costCenter.cost_center_id
        left join zjdata.ccm_work_center_menu menu on a.menu_id = menu.menu_id
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
        </where>
        ORDER BY work_center_id, date_to,plant_id ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_work_center
        SET enable = #{enable}
        where (plant_id, work_center_id, date_to) in
        <foreach collection="keyList" item="item" index="index" separator=";">
            (#{item.plantId}, #{item.workCenterId}, '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(item.dateTo)}')
        </foreach>
    </update>

    <select id="selectNodeTree" resultType="com.datalink.fdop.base.api.model.WorkCenterTree">
        select work_center_id as id, menu_id as pid, plant_id as plantId, date_to as dateTo, 'NODE' as menuType,
        work_center_desc as name, work_center_text as description from(
            <include refid="selectSql"/>
        ) node
        <where>
            and node.delete_flag = false
            <if test="code != null and code!= ''">
                and node.work_center_id like concat('%', #{code}, '%')
            </if>
        </where>
        ORDER BY id ${sort}
    </select>

    <update id="batchUpdatePidByIds">
        update zjdata.ccm_work_center
        set menu_id = #{pid}
        where (plant_id, work_center_id, date_to) in
        <foreach collection="keyList" item="item" open="(" separator="," close=")">
            (#{item.plantId}, #{item.workCenterId}, #{item.dateTo})
        </foreach>
    </update>

    <select id="selectOrigProjectsByPid" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        select * from zjdata.ccm_work_center_orig where menu_id = #{menuId}
    </select>

    <select id="queryOrigList" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        select * from zjdata.ccm_work_center_orig node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY plant_id, work_center_id, date_to ${sort}
    </select>

    <select id="queryList" resultType="com.datalink.fdop.base.api.domain.WorkCenter">
        select * from zjdata.ccm_work_center node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY plant_id, work_center_id, date_to ${sort}
    </select>

    <delete id="batchDelete">
        delete from zjdata.ccm_work_center
        where (plant_id, work_center_id, date_to) in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            (#{item.plantId}, #{item.workCenterId}, #{item.dateTo})
        </foreach>
    </delete>




</mapper>


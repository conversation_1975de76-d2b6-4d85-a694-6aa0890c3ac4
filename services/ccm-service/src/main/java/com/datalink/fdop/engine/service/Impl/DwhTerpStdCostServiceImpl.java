package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpStdCost;
import com.datalink.fdop.engine.mapper.DwhTerpStdCostMapper;
import com.datalink.fdop.engine.service.DwhTerpStdCostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhTerpStdCostServiceImpl extends ServiceImpl<DwhTerpStdCostMapper, DwhTerpStdCost> implements DwhTerpStdCostService {

    @Autowired
    private DwhTerpStdCostMapper dwhTerpStdCostMapper;

    @Override
    public PageDataInfo<DwhTerpStdCost> overview(
            String date,
            String controlAreaId, String verId, String plantId, String productId,
            String sort, SearchVo searchVo) {
        Page<DwhTerpStdCost> page = PageUtils.getPage(DwhTerpStdCost.class);
        IPage<DwhTerpStdCost> iPage = dwhTerpStdCostMapper.selectAll(date,controlAreaId, verId, plantId, productId,  page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhTerpStdCost> listAll() {
        return dwhTerpStdCostMapper.listAll();
    }

    @Override
    public List<DwhTerpStdCost> selectNoPage(String date, String controlAreaId, String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwhTerpStdCostMapper.selectNoPage(date, controlAreaId, verId, plantId, productId, sort, searchVo);
    }
}
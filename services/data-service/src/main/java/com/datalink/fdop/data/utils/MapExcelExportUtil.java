package com.datalink.fdop.data.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

public class MapExcelExportUtil {

    private static final Logger log = LoggerFactory.getLogger(MapExcelExportUtil.class);

    /**
     * 导出Map数据（动态列）的Excel
     */
    public static void exportMapDataWithDynamicColumns(HttpServletResponse response,
                                                       List<Map<String, Object>> dataList,
                                                       List<DynamicColumn> dynamicColumns,
                                                       String title) {
        try {


            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 如果没有提供动态列，尝试从数据中自动推断列名
            if ((dynamicColumns == null || dynamicColumns.isEmpty()) && dataList != null && !dataList.isEmpty()) {
                dynamicColumns = new ArrayList<>();
                for (String key : dataList.get(0).keySet()) {
                    dynamicColumns.add(new DynamicColumn(key, key));
                }
            }

            // 如果 dynamicColumns 仍然为空，创建空的 Excel 文件
            if (dynamicColumns == null || dynamicColumns.isEmpty()) {
                List<List<String>> head = new ArrayList<>();
                head.add(Arrays.asList("暂无数据"));
                EasyExcel.write(response.getOutputStream())
                        .head(head)
                        .sheet(title)
                        .doWrite(new ArrayList<>());
                return;
            }

            // 构建表头
            List<List<String>> head = new ArrayList<>();
            for (int i = 0; i < dynamicColumns.size(); i++) {
                DynamicColumn column = dynamicColumns.get(i);
                if (column == null) {
                    continue;
                }

                String columnDesc = column.getColumnDesc();
                String columnName = column.getColumnName();

                // 确保表头不为空
                if (columnDesc == null || columnDesc.trim().isEmpty()) {
                    if (columnName != null && !columnName.trim().isEmpty()) {
                        columnDesc = columnName;
                    } else {
                        columnDesc = "列" + (i + 1);
                    }

                }

                // 再次确保不为空
                if (columnDesc == null || columnDesc.trim().isEmpty()) {
                    columnDesc = "列" + (i + 1);
                }


                head.add(Arrays.asList(columnDesc.trim()));
            }


            // 构建数据
            List<List<Object>> excelData = new ArrayList<>();
            if (dataList != null) {
                for (Map<String, Object> rowData : dataList) {
                    List<Object> row = new ArrayList<>();
                    for (DynamicColumn column : dynamicColumns) {
                        if (column != null) {
                            Object value = rowData.get(column.getColumnName());
                            row.add(value != null ? value : "");
                        } else {
                            row.add("");
                        }
                    }
                    excelData.add(row);
                }
            }


            // 创建样式策略
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = createCellStyleStrategy();

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream())
                    .head(head)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .sheet(title)
                    .doWrite(excelData);



        } catch (Exception e) {
            log.error("Excel导出异常：", e);
            throw new RuntimeException("Excel导出失败", e);
        }
    }

    /**
     * 创建EasyExcel样式策略
     */
    private static HorizontalCellStyleStrategy createCellStyleStrategy() {
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}
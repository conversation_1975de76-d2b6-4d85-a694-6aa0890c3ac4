package com.datalink.fdop.drive.utils;

import org.springframework.jdbc.core.JdbcTemplate;

import java.sql.Connection;
import java.sql.SQLException;

public class JdbcUtils {



    public static void closeConnect(JdbcTemplate jdbcTemplate){
        try {
            if (jdbcTemplate!=null) {
                Connection connection = jdbcTemplate.getDataSource().getConnection();
                if (connection!=null) {
                    connection.close();
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}

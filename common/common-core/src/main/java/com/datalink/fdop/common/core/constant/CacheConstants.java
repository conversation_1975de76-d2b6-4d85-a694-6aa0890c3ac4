package com.datalink.fdop.common.core.constant;

/**
 * 缓存的key 常量
 * 
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /*** 登录用户id redis key 用于实现灵活控制多设备登录*/
    public static final String LOGIN_USER_ID_KEY = "login_user_ids:";

}

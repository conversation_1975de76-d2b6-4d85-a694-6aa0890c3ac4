package com.datalink.fdop.data.utils;

import com.datalink.fdop.common.core.domain.DynamicColumn;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class MapExcelExportUtil {

    // 添加系统属性设置，避免字体管理器初始化
    static {
        try {
            // 设置无头模式，避免字体管理器依赖
            System.setProperty("java.awt.headless", "true");
            // 禁用字体管理器
            System.setProperty("sun.java2d.fontpath", "");
            // 禁用字体缓存
            System.setProperty("sun.java2d.noddraw", "true");
            // 禁用 DirectDraw
            System.setProperty("sun.java2d.d3d", "false");
            // 强制使用软件渲染
            System.setProperty("sun.java2d.opengl", "false");
        } catch (Exception e) {
            // 忽略系统属性设置异常
        }
    }

    private static final Logger log = LoggerFactory.getLogger(MapExcelExportUtil.class);

    /**
     * 导出Map数据（动态列）的Excel
     */
    public static void exportMapDataWithDynamicColumns(HttpServletResponse response,
                                                       List<Map<String, Object>> dataList,
                                                       List<DynamicColumn> dynamicColumns,
                                                       String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + title + ".xlsx");

        try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) {
            // 完全不使用样式，避免字体管理器初始化
            // Map<String, CellStyle> styles = createSafeStyles(workbook);

            // 如果没有提供动态列，尝试从数据中自动推断列名
            if ((dynamicColumns == null || dynamicColumns.isEmpty()) && dataList != null && !dataList.isEmpty()) {
                dynamicColumns = new ArrayList<>();
                for (String key : dataList.get(0).keySet()) {
                    dynamicColumns.add(new DynamicColumn(key, key));
                }
            }

            // 如果 dynamicColumns 仍然为空，生成一个空的 Excel 文件
            if (dynamicColumns == null || dynamicColumns.isEmpty()) {
                workbook.createSheet(title);
                workbook.write(response.getOutputStream());
                return;
            }


            // 创建Sheet页
            Sheet sheet = workbook.createSheet(title);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.setHeight((short) 280);
            for (int i = 0; i < dynamicColumns.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(dynamicColumns.get(i).getColumnDesc());
                // 完全不设置样式，避免字体管理器初始化
            }

            // 写入数据
            int rowIndex = 1;
            for (Map<String, Object> rowData : dataList) {
                Row row = sheet.createRow(rowIndex++);
                row.setHeight((short) 280);
                for (int i = 0; i < dynamicColumns.size(); i++) {
                    Cell cell = row.createCell(i);
                    String columnName = dynamicColumns.get(i).getColumnName();
                    Object value = rowData.get(columnName);

                    // 完全不设置样式，只设置值
                    if (value == null) {
                        cell.setCellValue("");
                    } else if (value instanceof Date) {
                        cell.setCellValue((Date) value);
                    } else if (value instanceof Number) {
                        cell.setCellValue(((Number) value).doubleValue());
                    } else {
                        cell.setCellValue(value.toString());
                    }
                }
            }

            // 设置列宽
            for (int i = 0; i < dynamicColumns.size(); i++) {
                sheet.setColumnWidth(i, (int) (15.89 * 256));
            }

            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("Exception when exporting Map data: ", e);
        }
    }




}
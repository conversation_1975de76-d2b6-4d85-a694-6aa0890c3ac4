package com.datalink.fdop.common.core.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 * <AUTHOR>
 * @Date 2021/8/25 13:57
 */
public class PatternUtil {

    // 获取数据对象中的占位符
    public static List<String> getDataObjectPlaceholder(String str) {
        List<String> list = new ArrayList<>();
        // 数据对象内的占位符: ##数据对象表编码##
        String regex = "\\#\\#(.*?)\\#\\#";
        Pattern pattern = Pattern.compile(regex);
        // 创建 matcher 对象
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            list.add(matcher.group(0));
        }
        return list;
    }

    // 获取参数中的占位符
    public static List<String> getParamPlaceholder(String str) {
        List<String> list = new ArrayList<>();
        // 参数内的占位符: $$参数编码.值的列$$
        String regex = "\\$\\$(.*?)\\$\\$";
        Pattern pattern = Pattern.compile(regex);
        // 创建 matcher 对象
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            list.add(matcher.group(0));
        }
        return list;
    }

    //截取某个字符到某个字符之间  特殊字符需要转义
    public static String cutString(String str, String start, String end) {
        if (isBlank(str)) {
            return str;
        }
        String reg = start + "(.*)" + end;
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            str = matcher.group(1);
        }
        return str;
    }

    // 获取字符串中的url
    public static List<String> getUrl(String str) {
        List<String> list = new ArrayList<>();
        Pattern p = Pattern.compile("(https?|http|ftp|file):\\/\\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]");
        Matcher m = p.matcher(str);
        while (m.find()) {
            list.add(m.group());
        }
        return list;
    }

}

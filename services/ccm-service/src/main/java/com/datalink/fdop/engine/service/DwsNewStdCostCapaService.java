package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostCapa;

import java.util.List;

public interface DwsNewStdCostCapaService extends IService<DwsNewStdCostCapa> {

    PageDataInfo<DwsNewStdCostCapa> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo);

    List<DwsNewStdCostCapa> listAll();

    List<DwsNewStdCostCapa> selectNoPage(String controlAreaId, String productId, String verId, String plantId, String date, String sort, SearchVo searchVo);
}
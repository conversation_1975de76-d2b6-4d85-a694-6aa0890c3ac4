package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalCostOrd;

import java.util.List;

public interface DwhFinalCostOrdService extends IService<DwhFinalCostOrd> {

    PageDataInfo<DwhFinalCostOrd> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwhFinalCostOrd> listAll();

    List<DwhFinalCostOrd> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);
}
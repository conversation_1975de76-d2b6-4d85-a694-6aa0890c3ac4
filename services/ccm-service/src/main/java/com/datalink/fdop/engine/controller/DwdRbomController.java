package com.datalink.fdop.engine.controller;

import com.datalink.fdop.engine.api.domain.DwdRbom;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdRbomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdRbom")
@Api(tags = "CCMS-dwd_rbom")
public class DwdRbomController extends BaseController {

    @Autowired
    private DwdRbomService dwdRbomService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "工艺管理")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String recipeId = MapUtils.getAsString(params, "recipeId");
        PageDataInfo<DwdRbom> overview = dwdRbomService.overview(plantId, recipeId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdRbom.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "工艺管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String recipeId = MapUtils.getAsString(params, "recipeId");

        List<DwdRbom> list = dwdRbomService.selectNoPage(plantId, recipeId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdRbom> util = new ExcelUtil<>(DwdRbom.class);
        util.exportExcel(response, list, "RBOM信息");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdRbom> util = new ExcelUtil<>(DwdRbom.class);
        util.importTemplateExcel(response, "RBOM信息");
    }

    @ApiOperation(value = "查询工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwd.dwd_rbom", null));
    }

    @ApiOperation(value = "查询Recipe ID（SelectVo）")
    @GetMapping("/listRecipeId")
    public R<List<SelectVo>> listRecipeId() {
        return R.ok(dwdRbomService
                .lambdaQuery()
                .select(DwdRbom::getRecipeId, DwdRbom::getRecipeDesc)
                .groupBy(DwdRbom::getRecipeId, DwdRbom::getRecipeDesc)
                .orderByAsc(DwdRbom::getRecipeId).list().stream()
                .filter(dwdRbom -> dwdRbom != null && dwdRbom.getRecipeId() != null)
                .map(dwdRbom -> new SelectVo(dwdRbom.getRecipeId(), dwdRbom.getRecipeDesc()))
                .collect(Collectors.toList()));
    }

}

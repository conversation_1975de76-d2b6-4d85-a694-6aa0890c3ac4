package com.datalink.fdop.quality.api.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/10/11 10:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "zjdata", value = "d_q_data_quality_menu")
public class DataQualityMenu extends BaseField {

    @ApiModelProperty(value = "规则菜单id", required = true)
    @TableId(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @NotNull(message = "请选择一个菜单")
    @ApiModelProperty(value = "父节点id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalAllocationFactorA;

import java.util.List;

public interface DwhFinalAllocationFactorAService extends IService<DwhFinalAllocationFactorA> {

    PageDataInfo<DwhFinalAllocationFactorA> overview(
            String controlAreaId, 
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String costCenterId,
            String factorId,
            String allocationType,
            String sort,
            SearchVo searchVo);

    List<DwhFinalAllocationFactorA> selectNoPage(String controlAreaId,
                                                 String verId,
                                                 String yearMonthFrom,
                                                 String yearMonthTo,
                                                 String costCenterType,
                                                 String costCenterId,
                                                 String factorId,
                                                 String allocationType, SearchVo searchVo);
}
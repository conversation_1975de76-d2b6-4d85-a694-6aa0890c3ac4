<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.DataEntityMenuMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.element.api.domain.DataEntityMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <sql id="entityMenu">
        RETURN ${alias}.id, ${alias}.pid,
            ${alias}.code,
            ${alias}.name,
            ${alias}.entityType,
            ${alias}.l3Id,
            ${alias}.description,
            ${alias}.createTime,
            ${alias}.updateTime
            $$) as (id BIGINT, pid BIGINT,
            code TEXT,
            name TEXT,
            entityType TEXT,
            l3Id BIGINT,
            description TEXT,
            createTime TEXT,
            updateTime TEXT)
    </sql>

    <select id="createEntityMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_e_data_entity_menu), (menuE:d_e_data_entity_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'d_e_data_entity_menu',endTable:'d_e_data_entity_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'d_e_data_entity_menu',endTable:'d_e_data_entity_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertEntityMenu" parameterType="String" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:d_e_data_entity_menu ${@com.datalink.fdop.element.utils.DomainAgeUtils@getDataEntityMenuAgeStr(dataEntityMenu)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.element.api.domain.DataEntityMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_entity_menu)
        WHERE menu.id = ${id}
        <set>
            <if test="pid != null">menu.pid = ${pid},</if>
            <if test="l3Id != null">menu.l3Id = ${l3Id},</if>
            <if test="code != null and code != ''">menu.code = '${code}',</if>
            <if test="entityType != null">menu.entityType = '${entityType}',</if>
            <if test="name != null">menu.name = '${name}',</if>
            <if test="description != null">menu.description = '${description}',</if>
            <if test="serialNumber != null">menu.serialNumber = '${serialNumber}',</if>
            menu.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            menu.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" parameterType="com.datalink.fdop.element.api.domain.DataEntityMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_entity_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteEntityMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[mme1:menu_menu_edge]->(menu)-[mme2:menu_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND menu.id = ${pid}
        DELETE mme1, mme2 RETURN id(mme1),
        properties(mme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_entity_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.element.api.domain.DataEntityMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_entity_menu) WHERE menu.id = ${id}
        <include refid="entityMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectEntityListByL3Id" resultType="com.datalink.fdop.element.api.domain.DataEntityMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_e_data_entity_menu) WHERE menu.l3Id = ${l3Id}
        <include refid="entityMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.element.api.domain.DataEntityMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_e_data_entity_menu) WHERE menu.code = '${code}'
        <include refid="entityMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectByPid" resultType="com.datalink.fdop.element.api.domain.DataEntityMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_e_data_entity_menu) WHERE menu.pid = ${pid}
        <include refid="entityMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.element.api.domain.DataEntityMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_e_data_entity_menu) WHERE menu.code = '${code}' AND menu.id &lt;&gt; ${id} RETURN id(menu),
                               properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_e_data_entity_menu) -[mme:menu_menu_edge]->(menuE:d_e_data_entity_menu)
            WHERE menuE.pid = ${pid}
            RETURN DISTINCT (menuE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="selectMenuTree" resultType="com.datalink.fdop.element.api.model.DataEntityTree">
        WITH overview as (
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_entity_menu)
        <where>
            <if test="code != null and code != ''">and toLower(menu.code )=~'.*${code}.*'</if>
            and (menu.entityType is null or (menu.entityType &lt;&gt; 'DWD' and menu.entityType &lt;&gt; 'DIM'))
        </where>
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description,menu.l3Id,'MENU' as menuType,
        menu.serialNumber, menu.entityType
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,l3Id BIGINT,menuType TEXT,serialNumber TEXT,
        entityType TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_entity_menu)
        <where>
            <if test="code != null and code != ''">and toLower(menu.code )=~'.*${code}.*'</if>
            and (menu.entityType = 'DWD' or menu.entityType = 'DIM')
        </where>
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description,menu.l3Id,'NODE' as menuType,
        menu.serialNumber, menu.entityType
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,l3Id BIGINT,menuType TEXT,serialNumber TEXT,
        entityType TEXT)
        )
        select * from overview ORDER BY serialNumber ${sort}
    </select>

    <select id="selectAllTaskNameByProjectCode" parameterType="Long"  resultType="String">
        select name from dolphinscheduler.t_ds_task_definition where project_code = #{projectCode}
    </select>

    <select id="selectProjectCodeByTenantId" parameterType="String"  resultType="Long">
        select code from dolphinscheduler.t_ds_project where name = #{tenantId}
    </select>

    <select id="selectByL3Id" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_e_data_entity_menu) WHERE menu.l3Id = ${l3Id} RETURN id(menu),
                               properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper> 
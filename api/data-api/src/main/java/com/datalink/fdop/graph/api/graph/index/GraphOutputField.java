package com.datalink.fdop.graph.api.graph.index;

import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.graph.api.graph.index.enums.AggFunction;
import com.datalink.fdop.graph.api.graph.index.enums.GraphFieldSourceType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/16 15:55
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GraphOutputField {

    // 非前端字段
    // 前置节点名称
    @JsonIgnore
    private String preNodeName;
    // 前置节点字段名称
    @JsonIgnore
    private String preFieldName;

    // 公共字段
    // 字段来源类型
    private GraphFieldSourceType fieldSource;
    // 引用字段来源节点id
    private String citeNodeId;
    // 引用字段来源节点的字段id
    private String citeFieldId;
    // 字段id
    private String fieldId;
    // 计算列自定义函数
    private String calcFunction;
    // 字段名称
    private String fieldName;
    // 字段描述
    private String fieldDesc;
    // 字段类型
    private FieldType fieldType;
    // 字段长度
    private Long length;
    // 字段小数长度
    private Long decimalLength;
    // 是否主键
    private Boolean isPk;

    // union field List
    private List<UnionField> unionFieldList;

    // aggregation
    // 聚合函数
    private AggFunction aggFunction;

    // rank
    private GraphRank rank;


}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpVarcost;

import java.util.List;

public interface DwhTerpVarcostService extends IService<DwhTerpVarcost> {

    PageDataInfo<DwhTerpVarcost> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwhTerpVarcost> listAll();

    List<DwhTerpVarcost> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);
}
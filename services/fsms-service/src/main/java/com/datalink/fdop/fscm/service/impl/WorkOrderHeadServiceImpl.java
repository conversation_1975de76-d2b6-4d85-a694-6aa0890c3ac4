package com.datalink.fdop.fscm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.redis.service.RedisService;
import com.datalink.fdop.fscm.api.domain.WorkOrderHead;
import com.datalink.fdop.fscm.api.domain.WorkOrderRow;
import com.datalink.fdop.fscm.api.domain.WorkOrderSegmentConfig;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderQueryShowVo;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderQueryVo;
import com.datalink.fdop.fscm.mapper.WorkOrderHeadMapper;
import com.datalink.fdop.fscm.service.WorkOrderHeadService;
import com.datalink.fdop.fscm.service.WorkOrderSegmentConfigService;
import com.datalink.fdop.project.api.model.vo.OrderQueryShowVo;
import com.datalink.fdop.project.api.model.vo.OrderQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:33
 */
@Service
@Transactional
public class WorkOrderHeadServiceImpl extends ServiceImpl<WorkOrderHeadMapper, WorkOrderHead> implements WorkOrderHeadService {
    private static final  String key="FCSM_WORK_ORDER_LOCK";
    @Autowired
    private WorkOrderHeadMapper workOrderHeadMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RedisService redisService;
    @Autowired
    private WorkOrderSegmentConfigService workOrderSegmentConfigService;

    @Override
    public Page<OrderQueryShowVo> queryData(Page<OrderQueryShowVo> page, OrderQueryVo orderQueryVo, String flag) {
        return workOrderHeadMapper.queryData(page,orderQueryVo,flag);
    }

    @Override
    public Page<WorkOrderQueryShowVo> queryWorkOrderData(Page<WorkOrderQueryShowVo> page, WorkOrderQueryVo workOrderQueryVo, String delFlag, String temporary) {
        return  workOrderHeadMapper.queryWorkOrderData(page,workOrderQueryVo,delFlag,temporary);
    }

    @Override
    public List<BigDecimal> findPostQuantity(String workOrderNum, Long workOrderRowNum) {
        return workOrderHeadMapper.findPostQuantity(workOrderNum,workOrderRowNum);
    }

    @Override
    @Transactional
    public String getWorkOrderNum(String workOrderType, String workOrderNum) {
        List<WorkOrderSegmentConfig> list = workOrderSegmentConfigService.list(new QueryWrapper<WorkOrderSegmentConfig>().lambda().eq(WorkOrderSegmentConfig::getWorkOrderType, workOrderType));
        List<String> encodes = list.stream().map(WorkOrderSegmentConfig::getEncode).collect(Collectors.toList());
        if (StringUtils.isEmpty(workOrderNum)) {
            if (encodes.contains("2")) {
                WorkOrderSegmentConfig one = workOrderSegmentConfigService.getOne(new QueryWrapper<WorkOrderSegmentConfig>()
                        .lambda()
                        .eq(WorkOrderSegmentConfig::getWorkOrderType, workOrderType)
                        .eq(WorkOrderSegmentConfig::getEncode, "2")
                );
                workOrderNum=String.valueOf(Long.parseLong(one.getNowNum())+1);
                one.setNowNum(workOrderNum);
                R r = workOrderSegmentConfigService.createOrUpdate(Arrays.asList(one));
                if (r.getCode()==200) {
                    return workOrderNum;
                }else {
                    workOrderNum=getWorkOrderNum(workOrderType,workOrderNum);
                }
            }else {
                throw new ServiceException("给号方式为外部给号，订单号不能为空");
            }
        }else {
            if (!encodes.contains("1")) {
                throw new ServiceException("给号方式不支持外部给号");
            }
        }

        return workOrderNum;
    }

    @Override
    public R setLock(String username, String workOrderNum) {
        Object o = redisService.get(key+"@#¥@#¥"+workOrderNum);
        if (o!=null) {
            String s = String.valueOf(o);
            if (s.contains("@#¥@#¥")) {
                String[] data = s.split("@#¥@#¥");
                String uName = data[0];
                String ip = data[1];
                if (uName.equals(username)&&ip.equals(workOrderNum)) {
                    redisService.set(key+"@#¥@#¥"+workOrderNum,username+"@#¥@#¥"+workOrderNum,300);
                }else {
                    return R.fail("用户"+uName+"已在登录修改界面");
                }
            }
        }else{
            redisService.set(key+"@#¥@#¥"+workOrderNum,username+"@#¥@#¥"+workOrderNum,300);
        }
        return R.ok();
    }

    @Override
    public R delLock(String username, String workOrderNum) {
        Object o = redisService.get(key+"@#¥@#¥"+workOrderNum);
        if (o!=null) {
            String s = String.valueOf(o);
            if (s.contains("@#¥@#¥")) {
                String[] data = s.split("@#¥@#¥");
                String uName = data[0];
                String wONum = data[1];
                if (uName.equals(uName)&&wONum.equals(workOrderNum)) {
                    redisService.delete(key+"@#¥@#¥"+workOrderNum);
                }else {
                    return R.fail("解锁失败");
                }
            }
        }
        return R.ok();
    }

    @Override
    public String getSupplierCode(String workOrderNum) {
        WorkOrderHead workOrderHead = workOrderHeadMapper.selectOne(new QueryWrapper<WorkOrderHead>().lambda().eq(WorkOrderHead::getWorkOrderNum, workOrderNum));
        if (workOrderHead!=null) {
            return workOrderHead.getSupplierCode();
        }
        return null;
    }

    @Override
    public void checkRowData(WorkOrderHead workOrderHead, List<WorkOrderRow> workOrderRows) {
        String isTurnkey=workOrderHeadMapper.findIsTurnkey(workOrderHead.getProcess());
        if (isTurnkey.equals("1")) {
            for (WorkOrderRow workOrderRow : workOrderRows) {
                if (StringUtils.isEmpty(workOrderRow.getIsFinal())) {
                    throw new ServiceException("因为本单为turnkey工单，所以需要在行项目确认所有行的是否最后产出品！");
                }
                if (workOrderRow.getIsFinal().equals("0")&&workOrderRow.getUpLayerNum()==null) {
                    throw new ServiceException("因为本单为turnkey工单，对不是最后产出品的行需要维护上层产出品的行号！");
                }
            }
        }
    }
}

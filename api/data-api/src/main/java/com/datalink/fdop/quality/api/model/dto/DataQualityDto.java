package com.datalink.fdop.quality.api.model.dto;

import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.quality.api.enums.DataQualityCiteType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/12 9:54
 */
@Data
public class DataQualityDto {

    @ApiModelProperty(value = "规则id/参数id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "编码", required = true)
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "引用类型(CUSTOM:手动,CITE:自动)")
    private DataQualityCiteType citeType;

    @JsonIgnore
    public String getAgeStr() {
        return "{id:" + this.getId() +
                ", code:'" + this.getCode() + "'" +
                (StringUtils.isNotEmpty(this.getName()) ? ", name: '" + this.getName() + "'" : "") +
                (StringUtils.isNotEmpty(this.getDescription()) ? ", description: '" + this.getDescription() + "'" : "") +
                ", status:" + this.getStatus() +
                ", citeType:'" + this.getCiteType() + "'" +
                "}";
    }

}

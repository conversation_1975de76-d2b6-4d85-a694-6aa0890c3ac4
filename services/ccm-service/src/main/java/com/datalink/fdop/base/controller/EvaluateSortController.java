package com.datalink.fdop.base.controller;

import com.datalink.fdop.base.api.domain.EvaluateSort;
import com.datalink.fdop.base.service.EvaluateSortService;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/evaluateSort")
@Api(tags = "fccm-评估分类API")
@Transactional(rollbackFor = Exception.class)
public class EvaluateSortController extends BaseController {

    @Autowired
    private EvaluateSortService evaluateSortService;

    @ApiOperation(value = "新增评估分类")
    @Log(title = "评估分类管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody EvaluateSort evaluateSort) {
        if (UserConstants.NOT_UNIQUE.equals(evaluateSortService.checkUnique(evaluateSort.getEvaluateSortId()))) {
            return R.fail("新增重评估类'" + evaluateSort.getEvaluateSortId() + "'失败，重评估类已存在");
        }
        return R.toResult(evaluateSortService.insert(evaluateSort));
    }

    @ApiOperation(value = "修改评估分类")
    @Log(title = "评估分类管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@Validated @RequestBody EvaluateSort evaluateSort) {
        if (UserConstants.UNIQUE.equals(evaluateSortService.checkUnique(evaluateSort.getEvaluateSortId()))) {
            return R.fail("修改重评估类'" + evaluateSort.getEvaluateSortId() + "'失败，重评估类不存在");
        }
        evaluateSort.setUpdateBy(SecurityUtils.getUsername());
        evaluateSort.setUpdateTime(new Date());
        return R.toResult(evaluateSortService.updateByKey(evaluateSort));
    }

    @ApiOperation(value = "删除评估分类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "BwatrIds", value = "评估分类ID集合", required = true, allowMultiple = true, dataType = "Long", paramType = "body", example = "[1,2]"),
    })
    @Log(title = "评估分类管理", businessType = BusinessType.DELETE)
    @DeleteMapping()
    public R remove(@RequestBody List<EvaluateSort> list) {
        return R.toResult(evaluateSortService.deleteByList(list));
    }

    @ApiOperation(value = "获取评估分类列表")
    @PostMapping("/list")
    public R<PageDataInfo> list(@RequestBody(required = false) EvaluateSort evaluateSort) {
        return R.ok(evaluateSortService.list(evaluateSort));
    }

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "评估分类管理")
    @PostMapping(value = "/overview")
    public R<PageDataInfo> overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) SearchVo searchVo
    ) {
        return R.ok(evaluateSortService.overview(sort, searchVo));
    }

    @ApiOperation(value = "查询所有")
    @PostMapping("/listAll")
    public R<List<EvaluateSort>> listAll(@RequestBody(required = false) EvaluateSort evaluateSort) {
        return R.ok(evaluateSortService.listAll(evaluateSort));
    }

    @ApiOperation(value = "批量启用/禁用")
    @Log(title = "数据集合管理",businessType = BusinessType.UPDATE)
    @PostMapping("/batchStatus")
    public R batchStatus(@RequestBody(required = false) List<EvaluateSort> statusList) {
        return R.ok(evaluateSortService.updateBatchStatus(statusList));
    }

    @ApiOperation(value = "导入数据")
    @Log(title = "数据集合管理",businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R importData(MultipartFile file, @RequestParam(value = "updateSupport", required = false) Boolean updateSupport) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        ExcelUtil<EvaluateSort> util = new ExcelUtil<>(EvaluateSort.class);
        List<EvaluateSort> list = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        return R.ok(evaluateSortService.importData(list,updateSupport,operName));
    }

    @ApiOperation(value = "导出模板")
    @Log(title = "数据集合管理",businessType = BusinessType.EXPORT)
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<EvaluateSort> util = new ExcelUtil<>(EvaluateSort.class);
        util.importTemplateExcel(response, "重评估类模板");
    }

    @ApiOperation("导出数据")
    @Log(title = "数据集合管理",businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "DESC") String sort){
        List<EvaluateSort> list = evaluateSortService.list();
        ExcelUtil<EvaluateSort> util = new ExcelUtil<>(EvaluateSort.class);
        util.exportExcel(response, list, "重评估类数据");
    }
}

package com.datalink.fdop.quality.api.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/6/14 13:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "zjdata", value = "d_q_data_quality_monitor")
public class DataQualityMonitor extends BaseField {

    @ApiModelProperty(value = "监控id", required = true)
    @TableId(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @NotNull(message = "请选择一个菜单")
    @ApiModelProperty(value = "父节点id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    @ApiModelProperty(value = "任务code", required = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskCode;

    @ApiModelProperty(value = "工作流code", required = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processCode;

    @ApiModelProperty(value = "定时id", required = true)
    private Integer scheduleId;

}

package com.datalink.fdop.drive.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.domain.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;


@Component
public class RemoteDriveFallbackFactory implements FallbackFactory<RemoteDriveService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteDriveFallbackFactory.class);

    @Override
    public RemoteDriveService create(Throwable throwable) {
        log.error("数据源服务调用失败:{}", throwable.getMessage());
        return new RemoteDriveService() {
            @Override
            public DataSource getDataSource(Long datasourceId) {
                return null;
            }

            @Override
            public R<DataSource> queryDataSource(Long id) {
                return R.fail("查询数据源信息失败:" + throwable.getMessage());
            }

            @Override
            public R<DataSource> queryDataSourceByTenantId(Long tenantId, Long id) {
                return R.fail("查询数据源信息失败:" + throwable.getMessage());
            }

            @Override
            public R<DataSource> selectByTenantIdAndCode(Long tenantId, String code) {
                return R.fail("查询数据源信息失败:" + throwable.getMessage());
            }

            @Override
            public R<DataSource> queryDataSource(String code) {
                return R.fail("查询数据源信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<DataSource>> selectAll(String sort) {
                return R.fail("查询数据源失败:" + throwable.getMessage());
            }

            @Override
            public R createDataSource(DataSource dataSource) {
                return R.fail("创建数据源失败: "+ throwable.getMessage());
            }

            @Override
            public R<Set<String>> getFieldBySql(Long id, Boolean flag, String sql) {
                return R.fail("查询数据源字段失败:" + throwable.getMessage());
            }
        };
    }
}

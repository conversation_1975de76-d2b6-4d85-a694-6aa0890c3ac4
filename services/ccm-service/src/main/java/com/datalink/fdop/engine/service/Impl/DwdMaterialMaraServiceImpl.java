package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.base.api.domain.ProductVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdMaterialMara;
import com.datalink.fdop.engine.mapper.DwdMaterialMaraMapper;
import com.datalink.fdop.engine.service.DwdMaterialMaraService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdMaterialMaraServiceImpl extends ServiceImpl<DwdMaterialMaraMapper, DwdMaterialMara> implements DwdMaterialMaraService {

    @Autowired
    private DwdMaterialMaraMapper dwdMaterialMaraMapper;

    @Override
    public PageDataInfo<DwdMaterialMara> overview(String materialType,
                                                  String materialGroup,
                                                  String materialId, String sort, SearchVo searchVo) {
        Page<DwdMaterialMara> page = PageUtils.getPage(DwdMaterialMara.class);
        IPage<DwdMaterialMara> iPage = dwdMaterialMaraMapper.selectAll(materialType, materialGroup, materialId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdMaterialMara> selectNoPage(String materialType,
                                          String materialGroup,
                                          String materialId, String sort, SearchVo searchVo) {
        return dwdMaterialMaraMapper.selectNoPage(materialType, materialGroup, materialId, sort, searchVo);
    }

    @Override
    public List<String> selectProduct(String productId) {
        return dwdMaterialMaraMapper.selectProduct(productId);
    }

    @Override
    public List<String> selectMaterialGroup(String materialGroup) {
        return dwdMaterialMaraMapper.selectMaterialGroup(materialGroup);
    }

    @Override
    public List<String> selectMaterialType(String materialType) {
        return dwdMaterialMaraMapper.selectMaterialType(materialType);
    }

    @Override
    public List<String> selectMaterialId(String materialId) {
        return dwdMaterialMaraMapper.selectMaterialId(materialId);
    }

    @Override
    public PageDataInfo<ProductVo> selectProductList(String productId, String productDesc, String productCimId, String sort, SearchVo searchVo) {
        Page<ProductVo> page = PageUtils.getPage(ProductVo.class);
        IPage<ProductVo> iPage = dwdMaterialMaraMapper.selectProductList(productId, productDesc, productCimId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public PageDataInfo<ProductVo> selectProductListByTableName(String productId, String productDesc, String productCimId, String sort, SearchVo searchVo, String tableName) {
        Page<ProductVo> page = PageUtils.getPage(ProductVo.class);
        IPage<ProductVo> iPage = dwdMaterialMaraMapper.selectProductListByTableName(productId, productDesc, productCimId, tableName, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

}

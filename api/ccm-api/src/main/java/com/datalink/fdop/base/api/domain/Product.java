package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-08-01 15:22
 */
@Data
@TableName(schema = "zjdata", value = "ccm_product")
@ApiModel("产品数据")
public class Product {

    @NotBlank(message = "产品编码不能为空")
    private String productId;
    @NotNull(message = "产品版本不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productVer;
    private String productDesc;
    private String productText;
    @NotBlank(message = "工艺编码不能为空")
    private String technologyId;
    @NotNull(message = "工艺版本不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long technologyVer;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date dateFrom;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date dateTo;
    private String menuId;
    private Boolean enable;
    private Boolean delFlag;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;

    @TableField(exist = false)
    private String menuDesc;

}

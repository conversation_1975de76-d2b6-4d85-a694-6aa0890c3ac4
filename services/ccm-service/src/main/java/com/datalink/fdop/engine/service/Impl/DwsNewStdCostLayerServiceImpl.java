package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostLayer;
import com.datalink.fdop.engine.mapper.DwsNewStdCostLayerMapper;
import com.datalink.fdop.engine.service.DwsNewStdCostLayerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewStdCostLayerServiceImpl extends ServiceImpl<DwsNewStdCostLayerMapper, DwsNewStdCostLayer> implements DwsNewStdCostLayerService {

    @Autowired
    private DwsNewStdCostLayerMapper dwsNewStdCostLayerMapper;

    @Override
    public PageDataInfo<DwsNewStdCostLayer> overview(
            String controlAreaId, String productId, Integer batchQty,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwsNewStdCostLayer> page = PageUtils.getPage(DwsNewStdCostLayer.class);
        IPage<DwsNewStdCostLayer> iPage = dwsNewStdCostLayerMapper.selectAll(controlAreaId, productId, batchQty, verId, plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewStdCostLayer> selectNoPage(String controlAreaId, String productId, Integer batchQty, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwsNewStdCostLayerMapper.selectNoPage(controlAreaId, productId, batchQty, verId, plantId, date, sort, searchVo);
    }
}
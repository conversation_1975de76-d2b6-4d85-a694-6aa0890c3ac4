package com.datalink.fdop.data.utils;

import com.datalink.fdop.common.core.domain.DynamicColumn;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class MapExcelExportUtil {

    // 添加系统属性设置，避免字体管理器初始化
    static {
        // 设置无头模式，避免字体管理器依赖
        System.setProperty("java.awt.headless", "true");
        // 禁用字体管理器
        System.setProperty("sun.java2d.fontpath", "");
    }

    private static final Logger log = LoggerFactory.getLogger(MapExcelExportUtil.class);

    /**
     * 导出Map数据（动态列）的Excel
     */
    public static void exportMapDataWithDynamicColumns(HttpServletResponse response,
                                                       List<Map<String, Object>> dataList,
                                                       List<DynamicColumn> dynamicColumns,
                                                       String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + title + ".xlsx");

        try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) {
            // 创建样式 - 使用安全模式
            Map<String, CellStyle> styles = createSafeStyles(workbook);

            // 如果没有提供动态列，尝试从数据中自动推断列名
            if ((dynamicColumns == null || dynamicColumns.isEmpty()) && dataList != null && !dataList.isEmpty()) {
                dynamicColumns = new ArrayList<>();
                for (String key : dataList.get(0).keySet()) {
                    dynamicColumns.add(new DynamicColumn(key, key));
                }
            }

            // 如果 dynamicColumns 仍然为空，生成一个空的 Excel 文件
            if (dynamicColumns == null || dynamicColumns.isEmpty()) {
                workbook.createSheet(title);
                workbook.write(response.getOutputStream());
                return;
            }


            // 创建Sheet页
            Sheet sheet = workbook.createSheet(title);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.setHeight((short) 280);
            for (int i = 0; i < dynamicColumns.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(dynamicColumns.get(i).getColumnDesc());
                // 安全设置样式
                CellStyle headerStyle = styles.get("header");
                if (headerStyle != null) {
                    cell.setCellStyle(headerStyle);
                }
            }

            // 写入数据
            int rowIndex = 1;
            for (Map<String, Object> rowData : dataList) {
                Row row = sheet.createRow(rowIndex++);
                row.setHeight((short) 280);
                for (int i = 0; i < dynamicColumns.size(); i++) {
                    Cell cell = row.createCell(i);
                    String columnName = dynamicColumns.get(i).getColumnName();
                    Object value = rowData.get(columnName);

                    if (value == null) {
                        cell.setCellValue("");
                        CellStyle dataStyle = styles.get("data");
                        if (dataStyle != null) {
                            cell.setCellStyle(dataStyle);
                        }
                    } else if (value instanceof Date) {
                        cell.setCellValue((Date) value);
                        CellStyle dateStyle = styles.get("date");
                        if (dateStyle != null) {
                            cell.setCellStyle(dateStyle);
                        }
                    } else if (value instanceof Number) {
                        cell.setCellValue(((Number) value).doubleValue());
                        CellStyle dataStyle = styles.get("data");
                        if (dataStyle != null) {
                            cell.setCellStyle(dataStyle);
                        }
                    } else {
                        cell.setCellValue(value.toString());
                        CellStyle dataStyle = styles.get("data");
                        if (dataStyle != null) {
                            cell.setCellStyle(dataStyle);
                        }
                    }
                }
            }

            // 设置列宽
            for (int i = 0; i < dynamicColumns.size(); i++) {
                sheet.setColumnWidth(i, (int) (15.89 * 256));
            }

            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("Exception when exporting Map data: ", e);
        }
    }

    /**
     * 创建样式
     */
    private static Map<String, CellStyle> createSafeStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();

        try {
            // 最简单的表头样式 - 完全避免字体操作
            CellStyle headerStyle = wb.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            styles.put("header", headerStyle);

            // 最简单的数据样式 - 完全避免字体操作
            CellStyle dataStyle = wb.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            styles.put("data", dataStyle);

            // 日期样式 - 只设置格式，不设置字体
            CellStyle dateStyle = wb.createCellStyle();
            dateStyle.setAlignment(HorizontalAlignment.CENTER);
            dateStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            try {
                CreationHelper createHelper = wb.getCreationHelper();
                dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd"));
            } catch (Exception dateEx) {
                log.debug("创建日期格式失败，使用默认格式: {}", dateEx.getMessage());
            }
            styles.put("date", dateStyle);

        } catch (Exception e) {
            log.error("创建Excel样式失败，使用空样式: {}", e.getMessage());
            // 如果连基本样式都创建失败，使用空的样式映射
            styles.put("header", null);
            styles.put("data", null);
            styles.put("date", null);
        }

        return styles;
    }


}
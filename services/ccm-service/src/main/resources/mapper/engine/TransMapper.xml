<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.TransMapper">

    <select id="selectTaskCodeByName" resultType="java.lang.Long">
        select code from dolphinscheduler.t_ds_task_definition where name = #{name} limit 1
    </select>

    <select id="selectConsumptionHead" resultType="com.datalink.fdop.engine.api.domain.ConsumptionHead">
        select a.* from fccm.consumption_temp_head a
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}' and a.year_month = ${yearMonth}
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="selectConsumptionItem" resultType="com.datalink.fdop.engine.api.domain.ConsumptionItem">
        select a.* from fccm.consumption_temp_item a
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}' and a.year_month = ${yearMonth}
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="selectStdCostHead" resultType="com.datalink.fdop.engine.api.domain.StdCostHead">
        select a.* from fccm.std_cost_head_temp a
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}' and a.control_area_id = '${controlAreaId}'
        <if test="productId != null and productId != ''">
            and a.product_id = '${productId}'
        </if>
        <if test="productVer != null">
            and a.product_ver = ${productVer}
        </if>
            and batch_qty = ${batchQty} and effective_date = date('${effectiveDate}')
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="selectStdCostItem" resultType="com.datalink.fdop.engine.api.domain.StdCostItem">
        select a.* from fccm.ccm_dwh_std_cost_item a
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}' and a.control_area_id = '${controlAreaId}'
        <if test="productId != null and productId != ''">
            and a.product_id = '${productId}'
        </if>
        <if test="productVer != null">
            and a.product_ver = ${productVer}
        </if>
            and batch_qty = ${batchQty} and effective_date = date('${effectiveDate}')
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="selectTaskParamByCode" resultType="java.lang.String">
        select task_params from dolphinscheduler.t_ds_task_definition where code = ${taskCode} limit 1
    </select>

    <select id="selectAccountReport100List" resultType="com.datalink.fdop.engine.api.domain.AccountReport100">
        select * from fccm.account_report100
        where 1=1
        <if test="reportVer != null">
            and report_ver = '${reportVer}'
        </if>
        <if test="verId != null">
            and ver_id = '${verId}'
        </if>
        <if test="controlAreaId != null">
            and control_area_id = '${controlAreaId}'
        </if>
        <if test="companyId != null">
            and company_id = '${companyId}'
        </if>
        <if test="factoryId != null">
            and factory_id = '${factoryId}'
        </if>
        <if test="year != null">
            and year = ${year}
        </if>
    </select>

    <select id="selectAccountReport130List" resultType="com.datalink.fdop.engine.api.domain.AccountReport130">
        select * from fccm.account_report130
        where 1=1
        <if test="reportVer != null">
            and report_ver = '${reportVer}'
        </if>
        <if test="verId != null">
            and ver_id = '${verId}'
        </if>
        <if test="controlAreaId != null">
            and control_area_id = '${controlAreaId}'
        </if>
        <if test="companyId != null">
            and company_id = '${companyId}'
        </if>
        <if test="factoryId != null">
            and factory_id = '${factoryId}'
        </if>
        <if test="year != null">
            and year = ${year}
        </if>
    </select>

    <select id="selectAccountReport120List" resultType="com.datalink.fdop.engine.api.domain.AccountReport120">
        select * from fccm.account_report120
        where 1=1
        <if test="reportVer != null">
            and report_ver = '${reportVer}'
        </if>
        <if test="verId != null">
            and ver_id = '${verId}'
        </if>
        <if test="controlAreaId != null">
            and control_area_id = '${controlAreaId}'
        </if>
        <if test="companyId != null">
            and company_id = '${companyId}'
        </if>
        <if test="factoryId != null">
            and factory_id = '${factoryId}'
        </if>
        <if test="year != null">
            and year = ${year}
        </if>
    </select>

    <select id="selectAccountReport200List" resultType="com.datalink.fdop.engine.api.domain.AccountReport200">
        select * from fccm.account_report200
        where 1=1
        <if test="reportVer != null">
            and report_ver = '${reportVer}'
        </if>
        <if test="verId != null">
            and ver_id = '${verId}'
        </if>
        <if test="groupId != null">
            and group_id = '${groupId}'
        </if>
        <if test="companyId != null">
            and company_id = '${companyId}'
        </if>
        <if test="factoryId != null">
            and factory_id = '${factoryId}'
        </if>
        <if test="thisVer != null">
            and this_ver = ${thisVer}
        </if>
        <if test="fcstVer != null">
            and fcst_ver = ${fcstVer}
        </if>
    </select>

    <select id="selectAccountReport230List" resultType="com.datalink.fdop.engine.api.domain.AccountReport230">
        select * from fccm.account_report230
        where 1=1
        <if test="reportVer != null">
            and report_ver = '${reportVer}'
        </if>
        <if test="verId != null">
            and ver_id = '${verId}'
        </if>
        <if test="controlAreaId != null">
            and control_area_id = '${controlAreaId}'
        </if>
        <if test="companyId != null">
            and company_id = '${companyId}'
        </if>
        <if test="factoryId != null">
            and factory_id = '${factoryId}'
        </if>
        <if test="thisVer != null">
            and this_ver = ${thisVer}
        </if>
        <if test="fcstVer != null">
            and fcst_ver = ${fcstVer}
        </if>
    </select>

    <select id="selectAccountReport220List" resultType="com.datalink.fdop.engine.api.domain.AccountReport220">
        select * from fccm.account_report220
        where 1=1
        <if test="reportVer != null">
            and report_ver = '${reportVer}'
        </if>
        <if test="verId != null">
            and ver_id = '${verId}'
        </if>
        <if test="controlAreaId != null">
            and control_area_id = '${controlAreaId}'
        </if>
        <if test="companyId != null">
            and company_id = '${companyId}'
        </if>
        <if test="factoryId != null">
            and factory_id = '${factoryId}'
        </if>
        <if test="thisVer != null">
            and this_ver = ${thisVer}
        </if>
        <if test="fcstVer != null">
            and fcst_ver = ${fcstVer}
        </if>
    </select>

    <select id="selectStdCostHeadOut" resultType="com.datalink.fdop.engine.api.domain.StdCostHead">
        select a.* from fccm.std_cost_head a
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}' and a.control_area_id = '${controlAreaId}'
        <if test="productId != null and productId != ''">
            and a.product_id = '${productId}'
        </if>
        <if test="productVer != null">
            and a.product_ver = ${productVer}
        </if>
        and effective_date between date('${startTime}') and date('${endTime}')
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

    <select id="selectStdCostItemOut" resultType="com.datalink.fdop.engine.api.domain.StdCostItem">
        select a.* from fccm.ccm_dwh_std_cost_item a
        where a.ver_id = '${verId}' and a.factory_id = '${factoryId}' and a.control_area_id = '${controlAreaId}'
        <if test="productId != null and productId != ''">
            and a.product_id = '${productId}'
        </if>
        <if test="productVer != null">
            and a.product_ver = ${productVer}
        </if>
        and effective_date between date('${startTime}') and date('${endTime}')
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchVo)})
        </if>
    </select>

</mapper>


package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalActualExp;
import com.datalink.fdop.engine.mapper.DwhFinalActualExpMapper;
import com.datalink.fdop.engine.service.DwhFinalActualExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalActualExpServiceImpl extends ServiceImpl<DwhFinalActualExpMapper, DwhFinalActualExp> implements DwhFinalActualExpService {

    @Autowired
    private DwhFinalActualExpMapper dwhFinalActualExpMapper;

    @Override
    public PageDataInfo<DwhFinalActualExp> overview(String verId, String companyId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwhFinalActualExp> page = PageUtils.getPage(DwhFinalActualExp.class);
        IPage<DwhFinalActualExp> iPage = dwhFinalActualExpMapper.selectAll(verId, companyId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalActualExp> selectNoPage(String verId, String companyId, String yearMonth, String sort, SearchVo searchVo) {
        return dwhFinalActualExpMapper.selectNoPage(verId, companyId, yearMonth, sort, searchVo);
    }
}
package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.FlowItem;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface FlowItemService extends IService<FlowItem> {

    boolean insert(FlowItem flowItem);

    String checkUnique(FlowItem flowItem);

    PageDataInfo<FlowItem> list(FlowItem flowItem);

    int deleteByIds(List<FlowItem> flowItemList);

    String importData(List<FlowItem> list, Boolean isUpdateSupport, String operName);

    // boolean copy(List<FlowItem> list);

    PageDataInfo<FlowItem> overview(Long pid, String sort, SearchVo searchVo);

    int updateByKey(FlowItem flowItem);

    // FlowItem selectByBomItem(BomItem bomItem);

}

package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostPid;
import com.datalink.fdop.engine.mapper.DwsNewStdCostPidMapper;
import com.datalink.fdop.engine.service.DwsNewStdCostPidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewStdCostPidServiceImpl extends ServiceImpl<DwsNewStdCostPidMapper, DwsNewStdCostPid> implements DwsNewStdCostPidService {

    @Autowired
    private DwsNewStdCostPidMapper dwsNewStdCostPidMapper;

    @Override
    public PageDataInfo<DwsNewStdCostPid> overview(
            String controlAreaId, String productId, Integer batchQty,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwsNewStdCostPid> page = PageUtils.getPage(DwsNewStdCostPid.class);
        IPage<DwsNewStdCostPid> iPage = dwsNewStdCostPidMapper.selectAll(controlAreaId, productId, batchQty, verId, plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewStdCostPid> selectNoPage(String controlAreaId, String productId, Integer batchQty, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwsNewStdCostPidMapper.selectNoPage(controlAreaId, productId, batchQty, verId, plantId, date, sort, searchVo);
    }
}
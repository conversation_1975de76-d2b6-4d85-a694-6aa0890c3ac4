package com.datalink.fdop.udf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.udf.api.domain.Udf;
import com.datalink.fdop.udf.api.model.vo.UdfTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface UdfMapper extends BaseMapper<Udf> {

    int createUdfAndMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertUdf(@Param("udf") Udf udf);

    int updateById(Udf udf);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteUdfAndMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<Udf> selectById(Long id);

    VlabelItem<Udf> selectByCode(String code);

    VlabelItem<Udf> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<UdfTree> selectNodeTree(@Param("sort") String sort, @Param("code") String code);

    IPage<Udf> overview(IPage<Udf> page,
                               @Param("pid") Long pid,
                               @Param("sort") String sort,
                               @Param("searchVo") SearchVo searchVo);

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwsNewCostOrdMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwsNewCostOrd">
        SELECT
        *
        FROM dws.dws_new_cost_ord node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node", searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="yearMonth != null and yearMonth != ''">
                and node.year_month = #{yearMonth}
            </if>
        </where>
        ORDER BY node.ver_id,node.value_type,node.control_area_id,
        node.year_month,node.plant_id,node.order_id,node.cost_center_id,
        node.orig_cost_element_id,node.cost_element_id,node.cost_structure_id
        ${sort}
    </select>

    <select id="listAll" resultType="com.datalink.fdop.engine.api.domain.DwsNewCostOrd">
        SELECT
            *
        FROM dws.dws_new_cost_ord
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwsNewCostOrd">
        SELECT
            *
        FROM dws.dws_new_cost_ord node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node", searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="yearMonth != null and yearMonth != ''">
                and node.year_month = #{yearMonth}
            </if>
        </where>
        ORDER BY node.ver_id,node.value_type,node.control_area_id,
        node.year_month,node.plant_id,node.order_id,node.cost_center_id,
        node.orig_cost_element_id,node.cost_element_id,node.cost_structure_id
        ${sort}
    </select>

</mapper>
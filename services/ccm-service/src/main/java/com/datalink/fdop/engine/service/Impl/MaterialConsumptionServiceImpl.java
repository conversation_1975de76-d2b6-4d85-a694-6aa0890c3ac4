package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.MaterialConsumption;
import com.datalink.fdop.engine.mapper.MaterialConsumptionMapper;
import com.datalink.fdop.engine.service.MaterialConsumptionService;
import com.datalink.fdop.engine.utils.TrinoSqlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:33
 */
@Service
public class MaterialConsumptionServiceImpl extends ServiceImpl<MaterialConsumptionMapper, MaterialConsumption> implements MaterialConsumptionService {

    @Autowired
    private MaterialConsumptionMapper materialConsumptionMapper;

    @Override
    public PageDataInfo<MaterialConsumption> getOrg(String sort, SearchVo searchVo) {
        List<MaterialConsumption> iPage = materialConsumptionMapper.getOrg(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<MaterialConsumption> getEdit(String sort, SearchVo searchVo) {
        List<MaterialConsumption> iPage = materialConsumptionMapper.getEdit(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<MaterialConsumption> getAll(String sort, SearchVo searchVo) {
        List<MaterialConsumption> iPage = materialConsumptionMapper.getAll(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public int saveData(MaterialConsumption materialConsumption) {
        if (materialConsumptionMapper.selectByKey(materialConsumption) == null) {
            materialConsumptionMapper.add(TrinoSqlUtils.materialConsumptionSql(materialConsumption));
        } else {
            materialConsumptionMapper.updateByKey(materialConsumption);
        }
        return 1;
    }

    @Override
    public int deleteByList(List<MaterialConsumption> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("请指定要删除的目标");
        }
        int i = 0;
        for (MaterialConsumption materialConsumption : list) {
            i = i + materialConsumptionMapper.deleteByKey(materialConsumption);
        }
        return i;
    }

}

package com.datalink.fdop.base.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.base.api.domain.StructureHead;
import com.datalink.fdop.base.service.StructureHeadService;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/base/structureHead")
@Transactional(rollbackFor = Exception.class)
public class StructureHeadController extends BaseController {

    @Autowired
    private StructureHeadService structureHeadService;

    @ApiOperation(value = "新增")
    @Log(title = "组件构成",businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R create(@Validated @RequestBody StructureHead structureHead) {
        if (UserConstants.NOT_UNIQUE.equals(structureHeadService.checkIdUnique(structureHead.getStructureVerId(),structureHead.getDateTo(),structureHead.getCostStructureId()))) {
            return R.fail("新增组件构成'" + structureHead.getStructureVerId() + "'失败，组件构成已存在");
        }
        return R.ok(structureHeadService.create(structureHead));
    }

    @ApiOperation(value = "修改")
    @Log(title = "组件构成",businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public R update(@Validated @RequestBody StructureHead structureHead) {
        if (UserConstants.UNIQUE.equals(structureHeadService.checkIdUnique(structureHead.getStructureVerId(),structureHead.getDateTo(),structureHead.getCostStructureId()))) {
            return R.fail("修改组件构成'" + structureHead.getStructureVerId() + "'失败，组件构成不存在");
        }
        return R.ok(structureHeadService.update(structureHead));
    }

    @ApiOperation("删除")
    @Log(title = "组件构成",businessType = BusinessType.DELETE)
    @DeleteMapping("/delete")
    public R del(@RequestBody List<StructureHead> datas) {
        return R.toResult(structureHeadService.deleteByIds(datas));
    }

    @ApiOperation(value = "分页查询")
    @Log(title = "组件构成",businessType = BusinessType.OTHER)
    @PostMapping("/overview")
    public R query(@RequestBody(required = false) SearchVo searchVo,
                   @RequestParam(required = false, value = "sort", defaultValue = "asc") String sort) {
        Page<StructureHead> page = PageUtils.getPage(StructureHead.class);
        Page<StructureHead> dataPage = structureHeadService.pageList(page,searchVo,sort);
        return R.ok(PageUtils.getPageInfo(dataPage.getRecords(),(int)dataPage.getTotal()));
    }

    @ApiOperation(value = "查询所有")
    @Log(title = "查询所有", businessType = BusinessType.UPDATE)
    @GetMapping("/listAll")
    public R<List<String>> listAll(@RequestParam(value = "costStructureId",required = false) String costStructureId) {
        return R.ok(structureHeadService.listAll(costStructureId));
    }

    @ApiOperation(value = "批量启用/禁用")
    @Log(title = "组件构成",businessType = BusinessType.UPDATE)
    @PostMapping("/batchStatus")
    public R batchStatus(@RequestParam(required = false) Boolean enable,@RequestBody List<StructureHead> statusList) {
        return R.ok(structureHeadService.updateBatchStatus(enable,statusList));
    }

    @ApiOperation(value = "导入数据")
    @Log(title = "组件构成",businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R importData(MultipartFile file, @RequestParam(value = "updateSupport", required = false) Boolean updateSupport) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        ExcelUtil<StructureHead> util = new ExcelUtil<>(StructureHead.class);
        List<StructureHead> list = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        return R.ok(structureHeadService.importData(list,updateSupport,operName));
    }

    @ApiOperation(value = "导出模板")
    @Log(title = "组件构成",businessType = BusinessType.EXPORT)
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<StructureHead> util = new ExcelUtil<>(StructureHead.class);
        util.importTemplateExcel(response, "组件构成模板");
    }

    @ApiOperation("导出数据")
    @Log(title = "组件构成",businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "DESC") String sort){
        List<StructureHead> list = structureHeadService.list();
        ExcelUtil<StructureHead> util = new ExcelUtil<>(StructureHead.class);
        util.exportExcel(response, list, "组件构成数据");
    }
}

package com.datalink.fdop.seatunnel.api.restapi.domain.jdbc;

import com.alibaba.fastjson.annotation.JSONField;
import com.datalink.fdop.seatunnel.api.restapi.domain.SourceConfig;
import lombok.Data;

/**
 * SeaTunnel Jdbc Source 配置
 */
@Data
public class JdbcSourceConfig extends CommonJdbcConfig implements SourceConfig {

    /**
     * 引用SeaTunnel 上下文配置 sink/transform 中定义的result_table_name
     */
    @JSONField(name = "source_table_name")
    private String sourceTableName;

}

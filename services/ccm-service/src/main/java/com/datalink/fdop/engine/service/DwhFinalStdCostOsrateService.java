package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostOsrate;

import java.util.List;

public interface DwhFinalStdCostOsrateService extends IService<DwhFinalStdCostOsrate> {

    PageDataInfo<DwhFinalStdCostOsrate> overview(
            String controlAreaId, String verId, String plantId, String productId,
            String date, String sort, SearchVo searchVo);

    List<DwhFinalStdCostOsrate> selectNoPage(String plantId,
                                             String verId,
                                             String controlAreaId,
                                             String productId,
                                             String date,
                                             String sort,
                                             SearchVo searchVo);
}
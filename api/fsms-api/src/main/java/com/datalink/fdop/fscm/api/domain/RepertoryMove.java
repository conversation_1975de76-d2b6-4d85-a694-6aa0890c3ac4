package com.datalink.fdop.fscm.api.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.datalink.fdop.fscm.api.converter.TimeConverter;
import com.datalink.fdop.fscm.api.converter.WriteOffConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Time;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/1/10 10:03
 */
@Data
@ApiModel
@ExcelIgnoreUnannotated
public class RepertoryMove {

    @ApiModelProperty(value = "过账凭证号")
    @ExcelProperty(value = "过账凭证号")
    private Integer  voucherNum;
    @ApiModelProperty(value = "过账凭证年份")
    @ExcelProperty(value = "过账凭证年份")
    private String voucherVintage;
    @ApiModelProperty(value = "过账日期")
    @ExcelProperty(value = "过账日期")
    private Date postDate;
    @ApiModelProperty(value = "录入日期")
    @ExcelProperty(value = "录入日期")
    private Date enteringDate;
    @ApiModelProperty(value = "录入时间")
    @ExcelProperty(value = "录入时间",converter = TimeConverter.class)
    private Time enteringTime;
    @ApiModelProperty(value = "操作账户名")
    @ExcelProperty(value = "操作账户名")
    private String username;
    @ApiModelProperty(value = "凭证抬头文本")
    @ExcelProperty(value = "凭证抬头文本")
    private String riseText;
    @ApiModelProperty(value = "过账凭证行号")
    @ExcelProperty(value = "过账凭证行号")
    private Long voucherRowNum;
    @ApiModelProperty(value = "移动类型")
    @ExcelProperty(value = "移动类型")
    private String moveType;
    @ApiModelProperty(value = "移动类型描述")
    @ExcelProperty(value = "移动类型描述")
    private String moveTypeDesc;
    @ApiModelProperty(value = "内部物料编码")
    @ExcelProperty(value = "内部物料编码")
    private String materialCode;
    @ApiModelProperty(value = "内部物料描述")
    @ExcelProperty(value = "内部物料描述")
    private String materialDesc;
    @ApiModelProperty(value = "发出工厂")
    @ExcelProperty(value = "发出工厂")
    private String factoryCodeShip;
    @ApiModelProperty(value = "收回工厂")
    @ExcelProperty(value = "收回工厂")
    private String factoryCodeReceive;
    @ApiModelProperty(value = "发出库存地点")
    @ExcelProperty(value = "发出库存地点")
    private String stockPCodeShip;
    @ApiModelProperty(value = "发出库存地点描述")
    @ExcelProperty(value = "发出库存地点描述")
    private String stockPDescriptionShip;
    @ApiModelProperty(value = "收回库存地点")
    @ExcelProperty(value = "收回库存地点")
    private String stockPCodeReceive;
    @ApiModelProperty(value = "收回库存地点描述")
    @ExcelProperty(value = "收回库存地点描述")
    private String stockPDescriptionReceive;
    @ApiModelProperty(value = "发出批次")
    @ExcelProperty(value = "发出批次")
    private String batchNumberShip;
    @ApiModelProperty(value = "收回批次")
    @ExcelProperty(value = "收回批次")
    private String batchNumberReceive;
    @ApiModelProperty(value = "发出片号")
    @ExcelProperty(value = "发出片号")
    private String pieceShip;
    @ApiModelProperty(value = "收回片号")
    @ExcelProperty(value = "收回片号")
    private String pieceReceive;
    @ApiModelProperty(value = "发出BIN号")
    @ExcelProperty(value = "发出BIN号")
    private String binNumShip;
    @ApiModelProperty(value = "收回BIN号")
    @ExcelProperty(value = "收回BIN号")
    private String binNumReceive;
    @ApiModelProperty(value = "过账数量")
    @ExcelProperty(value = "过账数量")
    private double postQuantity;
    @ApiModelProperty(value = "过账单位")
    @ExcelProperty(value = "过账单位")
    private String postUnit;
    @ApiModelProperty(value = "采购订单号")
    @ExcelProperty(value = "采购订单号")
    private String orderNum;
    @ApiModelProperty(value = "采购订单行号")
    @ExcelProperty(value = "采购订单行号")
    private Long orderRowNum;
    @ApiModelProperty(value = "采购订单子件项目号")
    @ExcelProperty(value = "采购订单子件项目号")
    private String subprojectNum;
    @ApiModelProperty(value = "工单号")
    @ExcelProperty(value = "工单号")
    private String workOrderNum;
    @ApiModelProperty(value = "工单行号")
    @ExcelProperty(value = "工单行号")
    private Long workOrderRowNum;
    @ApiModelProperty(value = "工单子件项目号")
    @ExcelProperty(value = "工单子件项目号")
    private Long workOrderChildrenNum;
    @ApiModelProperty(value = "调拨单号")
    @ExcelProperty(value = "调拨单号")
    private String transferOrderNum;
    @ApiModelProperty(value = "调拨单行号")
    @ExcelProperty(value = "调拨单行号")
    private Long transferOrderRowNum;
    @ApiModelProperty(value = "领退料单号")
    @ExcelProperty(value = "领退料单号")
    private String pickReturnOrderNum;
    @ApiModelProperty(value = "领退料单行号")
    @ExcelProperty(value = "领退料单行号")
    private Long pickReturnOrderRowNum;
    @ApiModelProperty(value = "报废单号")
    @ExcelProperty(value = "报废单号")
    private String scrapOrderNum;
    @ApiModelProperty(value = "报废单行号")
    @ExcelProperty(value = "报废单行号")
    private String scrapOrderRowNum;
    @ApiModelProperty(value = "出货需求单号")
    @ExcelProperty(value = "出货需求单号")
    private String shipRequestOrderNum;
    @ApiModelProperty(value = "出货需求单行号")
    @ExcelProperty(value = "出货需求单行号")
    private Long shipRequestOrderRowNum;
    @ApiModelProperty(value = "参考过账凭证号")
    @ExcelProperty(value = "参考过账凭证号")
    private String voucherNumReference;
    @ApiModelProperty(value = "参考过账凭证年份")
    @ExcelProperty(value = "参考过账凭证年份")
    private String voucherVintageReference;
    @ApiModelProperty(value = "参考过账凭证行号")
    @ExcelProperty(value = "参考过账凭证行号")
    private String voucherRowNumReference;
    @ApiModelProperty(value = "是否被冲销")
    @ExcelProperty(value = "是否被冲销",converter = WriteOffConverter.class)
    private String writeOff;
    @ApiModelProperty(value = "凭证行项目文本")
    @ExcelProperty(value = "凭证行项目文本")
    private String rowText;

    @ApiModelProperty(value = "turnkey非最后产出")
    @ExcelProperty(value = "turnkey非最后产出")
    private String turnkeyNotFinal;



}

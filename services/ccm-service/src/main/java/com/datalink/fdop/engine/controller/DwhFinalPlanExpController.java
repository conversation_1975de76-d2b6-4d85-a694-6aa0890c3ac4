package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhFinalPlanExp;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhFinalPlanExpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;



@RestController
@RequestMapping("/base/dwhFinalPlanExp")
@Api(tags = "CCMS-费用计划管理-定版视图：成本中心")
public class DwhFinalPlanExpController extends BaseController {

    @Autowired
    private DwhFinalPlanExpService dwhFinalPlanExpService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "queryParam", value = "高级搜索及过滤条件", required = false, dataTypeClass = QueryParam.class, paramType = "body"),
    })
    @Log(title = "维护视图")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        PageDataInfo<DwhFinalPlanExp> overview = dwhFinalPlanExpService.overview(verId, companyId, yearMonthFrom, yearMonthTo, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhFinalPlanExp.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "维护视图", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        List<DwhFinalPlanExp> list = dwhFinalPlanExpService.selectNoPage(verId, companyId, yearMonthFrom, yearMonthTo, sort, queryParam.getSearchVo());
        ExcelUtil<DwhFinalPlanExp> util = new ExcelUtil<>(DwhFinalPlanExp.class);
        util.exportExcel(response, list, "成本中心");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhFinalPlanExp> util = new ExcelUtil<>(DwhFinalPlanExp.class);
        util.importTemplateExcel(response, "成本中心");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_final_plan_exp", null));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId() {
        return R.ok(dataDictionaryService.listCompanyId("dwh.dwh_final_plan_exp", null));
    }

}
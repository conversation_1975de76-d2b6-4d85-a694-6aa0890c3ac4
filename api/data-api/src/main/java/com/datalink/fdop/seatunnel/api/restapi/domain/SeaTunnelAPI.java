package com.datalink.fdop.seatunnel.api.restapi.domain;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.datalink.fdop.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Slf4j
public class SeaTunnelAPI {

    /**
     * http://
     */
    public static final String HTTP = "http://";

    /**
     * 斜杠/
     */
    public static final String SLASH = "/";

    /**
     * 连接运行服务器超时时间 10秒
     */
    public static final Integer SERVER_TIME_OUT_ACTIVE = 10000;

    private final String address;

    public SeaTunnelAPI(String address) {
        this.address = address;
    }

    public static SeaTunnelAPI build(String address) {
        return new SeaTunnelAPI(address);
    }

    /**
     * 提交SeaTunnel任务
     *
     * @param job job
     * @return Job信息
     */
    public JobInfo submitJob(JobConfiguration job) {
        String jobConf = JSON.toJSONString(job);
        try {
            // jobId 必填（没什么用，但是ST接口就是要必填）
            // jobName job名称
            // isStartWithSavePoint 是否开启保存点
            log.info("submitJob result body {}", jobConf);
            String url = String.format(HTTP + address + SLASH + "submit-job?jobName=%s&isStartWithSavePoint=%s", job.getEnv().getJobName(), "false");
            log.info("submitJob url {}", url);
            String res = HttpUtil.post(url, jobConf, SERVER_TIME_OUT_ACTIVE);
            log.info("submitJob result {}", res);
            JobInfo jobInfo = JSON.parseObject(res, JobInfo.class);
            return jobInfo;
        } catch (Exception e) {
            log.error("submitJob 提交失败：{}", e.getLocalizedMessage());
        }
        return null;
    }

    /**
     * 生成指定长度的随机数字字符串
     *
     * @param length 需要生成的字符串长度
     * @return 生成的随机数字字符串
     */
    public static String generateJobId(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    /**
     * 获取运行中的Job
     *
     * @param jobId jobId
     * @return Job执行情况
     */
    public Job getJob(String jobId) {
        String url = String.format(HTTP + address + SLASH + "job-info/%s", jobId);
        log.info("getJob url {}", url);
        try {
            String res = HttpUtil.get(url, SERVER_TIME_OUT_ACTIVE);
            log.info("getJob result {}", res);
            Job job = JSON.parseObject(res, Job.class);
            return job;
        } catch (Exception e) {
            log.error("getJob 获取失败：{}", e.getLocalizedMessage());
        }
        return null;
    }

    /**
     * 获取已完成的Job列表信息
     *
     * @param state 查询状态
     * @return Job执行情况
     */
    public List<Job> finishedJobs(String state) {
        String url = String.format(HTTP + address + SLASH + "finished-jobs");
        if (StringUtils.isNotEmpty(state)) {
            url += "?state=" + state;
        }
        log.info("finishedJobs url {}", url);
        try {
            String res = HttpUtil.get(url, SERVER_TIME_OUT_ACTIVE);
            log.info("finishedJobs result {}", res);
            List<Job> jobs = JSON.parseArray(res, Job.class);
            return jobs;
        } catch (Exception e) {
            log.error("finishedJobs 获取失败：{}", e.getLocalizedMessage());
        }
        return new ArrayList<>();
    }


//    public static void main(String[] args) {
//        SeaTunnelAPI seaTunnelAPI = SeaTunnelAPI.build("************:5801");
//        List<Job> jobs = seaTunnelAPI.finishedJobs("FINISHED");
//        System.out.println(jobs);
//    }

}

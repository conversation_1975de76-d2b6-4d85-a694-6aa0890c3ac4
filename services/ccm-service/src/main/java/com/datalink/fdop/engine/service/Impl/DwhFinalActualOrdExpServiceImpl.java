package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalActualOrdExp;
import com.datalink.fdop.engine.mapper.DwhFinalActualOrdExpMapper;
import com.datalink.fdop.engine.service.DwhFinalActualOrdExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalActualOrdExpServiceImpl extends ServiceImpl<DwhFinalActualOrdExpMapper, DwhFinalActualOrdExp> implements DwhFinalActualOrdExpService {

    @Autowired
    private DwhFinalActualOrdExpMapper dwhFinalActualOrdExpMapper;

    @Override
    public PageDataInfo<DwhFinalActualOrdExp> overview(String yearMonth, String verId, String controlAreaId, String sort, SearchVo searchVo) {
        Page<DwhFinalActualOrdExp> page = PageUtils.getPage(DwhFinalActualOrdExp.class);
        IPage<DwhFinalActualOrdExp> iPage = dwhFinalActualOrdExpMapper.selectAll(yearMonth, verId, controlAreaId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalActualOrdExp> selectNoPage(String yearMonth, String verId, String controlAreaId, String sort, SearchVo searchVo) {
        return dwhFinalActualOrdExpMapper.selectNoPage(yearMonth, verId, controlAreaId, sort, searchVo);
    }
}
package com.datalink.fdop.element.api.domain;

import com.datalink.fdop.common.core.web.domain.BaseDomain;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2022/4/12 11:45
 */
@Data
@AllArgsConstructor
@ApiModel("数据元素结构对象")
public class DataElementStructure extends BaseDomain {

    private static final long serialVersionUID = 247558744502238297L;

    @ApiModelProperty(value = "数据元素列id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "编码", required = true)
    @NotNull(message = "编码不能为空")
    @Size(min = 0, max = 60, message = "编码不能超过60个字符")
    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]{0,59})", message = "实体编码只能以字母开头，数字或字母或下划线组成")
    private String code;

    @ApiModelProperty(value = "名称")
    @Size(min = 0, max = 60, message = "名称不能超过60个字符")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "数据元素类型(MAIN:主数据,FIELD:字段,INPUT:录入)")
    private DataElementType dataElementType;

    @ApiModelProperty(value = "字段类型")
    private FieldType fieldType;

    @ApiModelProperty(value = "长度")
    private Long length;

    @ApiModelProperty(value = "精度")
    private Long decimalLength;

    @ApiModelProperty(value = "是否主键")
    private Boolean isPk;

    @ApiModelProperty(value = "映射字段名")
    private String mapFieldName;

    @ApiModelProperty(value = "排序")
    private Integer seq;

    @ApiModelProperty(value = "父")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long father;

    @ApiModelProperty(value = "子")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long son;

    @ApiModelProperty(value = "批次")
    private Integer batch;

    public DataElementStructure() {
    }

    public DataElementStructure(String code, String name, String description, DataElementType dataElementType, FieldType fieldType, Long length, Long decimalLength, Boolean isPk) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.dataElementType = dataElementType;
        this.fieldType = fieldType;
        this.length = length;
        this.decimalLength = decimalLength;
        this.isPk = isPk;
    }

}

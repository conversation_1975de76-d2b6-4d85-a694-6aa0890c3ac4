package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostCcrate;
import com.datalink.fdop.engine.mapper.DwhFinalStdCostCcrateMapper;
import com.datalink.fdop.engine.service.DwhFinalStdCostCcrateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalStdCostCcrateServiceImpl extends ServiceImpl<DwhFinalStdCostCcrateMapper, DwhFinalStdCostCcrate> implements DwhFinalStdCostCcrateService {

    @Autowired
    private DwhFinalStdCostCcrateMapper dwhFinalStdCostCcrateMapper;

    @Override
    public PageDataInfo<DwhFinalStdCostCcrate> overview(String controlAreaId, String productId,
                                                        String verId, String plantId, String date,
                                                        String sort, SearchVo searchVo) {
        Page<DwhFinalStdCostCcrate> page = PageUtils.getPage(DwhFinalStdCostCcrate.class);
        IPage<DwhFinalStdCostCcrate> iPage = dwhFinalStdCostCcrateMapper.selectAll(controlAreaId, productId, verId, plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalStdCostCcrate> selectNoPage(String controlAreaId, String productId, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwhFinalStdCostCcrateMapper.selectNoPage(controlAreaId, productId, verId, plantId, date, sort, searchVo);
    }
}
package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.engine.api.domain.DwdCbom;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwdCbomMapper extends BaseMapper<DwdCbom> {

    IPage<DwdCbom> selectAll(
            @Param("plantId") String plantId, @Param("productId") String productId,
            @Param("page") IPage<DwdCbom> page, @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwdCbom> selectNoPage(@Param("plantId") String plantId,
                               @Param("productId") String productId,
                               @Param("sort") String sort,
                               @Param("searchVo") SearchVo searchVo);
}
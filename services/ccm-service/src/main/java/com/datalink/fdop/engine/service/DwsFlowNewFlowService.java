package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.engine.api.domain.DwsFlowNewFlow;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwsFlowNewFlowService extends IService<DwsFlowNewFlow> {

    PageDataInfo<DwsFlowNewFlow> overview(String verId,String plantId,String productId,String sort, SearchVo searchVo);

    List<DwsFlowNewFlow> selectNoPage(String verId,
                                      String plantId,
                                      String productId,
                                      String sort,
                                      SearchVo searchVo);
}

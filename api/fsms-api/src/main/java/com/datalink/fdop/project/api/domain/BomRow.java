package com.datalink.fdop.project.api.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/10/11 13:46
 */
@Data
@TableName(schema = "zjdata", value = "p_d_bom_row")
@ExcelIgnoreUnannotated
public class BomRow {

    @ApiModelProperty(value = "头表id")
    private String headId;
    @ApiModelProperty(value = "行号")
    private String rowNum;
    @ApiModelProperty(value = "子件物料编码")
    @ExcelProperty(value = "子件物料")
    private String childrenMaterialCode;
    @ApiModelProperty(value = "子件物料描述")
    @ExcelProperty(value = "物料描述")
    private String childrenMaterialDescribe;
    @ApiModelProperty(value = "组件用量")
    @ExcelProperty(value = "组件用量")
    private String count;
    @ApiModelProperty(value = "组件单位")
    @ExcelProperty(value = "组件单位")
    private String unit;
    @ApiModelProperty(value = "损耗率")
    @ExcelProperty(value = "损耗率")
    private String attritionRate;
    @ApiModelProperty(value = "是否主芯片")
    @ExcelProperty(value = "是否主芯片")
    private String isMasterChip;
    @ApiModelProperty(value = "子件物料BOM版本")
    private String childrenMaterialVersions;
    @TableField(exist = false)
    @ApiModelProperty(value = "下层标志" ,hidden = false)
    private boolean substratumFlag;

    @ApiModelProperty(value = "是否替代料件")
    @ExcelProperty(value = "是否替代料件")
    private String isSubstitute;

    @ApiModelProperty(value = "被替代料件行号")
    @ExcelProperty(value = "被替代料件行号")
    private String beSubstitutedRowNum;

}

package com.datalink.fdop.stream.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.stream.api.domain.Stream;
import com.datalink.fdop.stream.api.domain.StreamTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface StreamMapper extends BaseMapper<Stream> {

    int createStreamAndMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertStream(@Param("stream") Stream stream);

    int updateById(Stream stream);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteStreamAndMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<Stream> selectById(Long id);

    VlabelItem<Stream> selectByCode(String code);

    VlabelItem<Stream> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<StreamTree> selectTree(@Param("sort") String sort, @Param("code") String code);

    Integer querySerialNumber();

    int createStreamAndParamEdge(@Param("streamId") Long streamId, @Param("ids") List<Long> ids);


    int deleteStreamAndParamEdge( @Param("streamId") Long streamId);

}

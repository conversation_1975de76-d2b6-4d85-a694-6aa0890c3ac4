apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: data-platform
  labels:
    app: system-service
  name: system-service
  annotations:
    kubesphere.io/description: 系统管理
spec:
  replicas: 1
  selector:
    matchLabels:
      app: system-service
  template:
    metadata:
      labels:
        app: system-service
      annotations:
        kubesphere.io/imagepullsecrets: '{"container-s13352":"harbor-data-platform"}'
    spec:
      containers:
        - name: container-s13352
          imagePullPolicy: Always
          image: 'harbor.semi-tech.com/fsp/data-platform/system-service:v1.0.0'
          ports:
            - name: http-9300
              protocol: TCP
              containerPort: 9300
          env:
            - name: NACOS_GROUP
              valueFrom:
                configMapKeyRef:
                  name: data-platform-env
                  key: NACOS_GROUP
            - name: NACOS_SERVER
              valueFrom:
                configMapKeyRef:
                  name: data-platform-env
                  key: NACOS_SERVER
          livenessProbe:
            httpGet:
              scheme: HTTP
              path: 'http://127.0.0.1:9300/data-platform/health'
              port: 9300
            initialDelaySeconds: 0
            timeoutSeconds: 3
            periodSeconds: 30
            successThreshold: 1
            failureThreshold: 3
          volumeMounts:
            - name: host-time
              mountPath: /etc/localtime
              readOnly: true
      serviceAccount: default
      terminationGracePeriodSeconds: 30
      initContainers: []
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ''
          name: host-time
      imagePullSecrets:
        - name: harbor-data-platform
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
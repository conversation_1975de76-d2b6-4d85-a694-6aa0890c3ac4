<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CostCenterMapper">

    <sql id="selectSql">
        SELECT
            COALESCE(ccc.control_area_id, ccco.control_area_id) AS control_area_id,
            COALESCE(ccc.cost_center_id, ccco.cost_center_id) AS cost_center_id,
            COALESCE(ccc.date_from, ccco.date_from) AS date_from,
            COALESCE(ccc.date_to, ccco.date_to) AS date_to,
            COALESCE(ccc.cost_center_desc, ccco.cost_center_desc) AS cost_center_desc,
            COALESCE(ccc.cost_center_text, ccco.cost_center_text) AS cost_center_text,
            COALESCE(ccc.company_id, ccco.company_id) AS company_id,
            COALESCE(ccc.cost_center_group_id, ccco.cost_center_group_id) AS cost_center_group_id,
            COALESCE(ccc.enable, ccco.enable) AS enable,
            COALESCE(ccc.create_by, ccco.create_by) AS create_by,
            COALESCE(ccc.create_time, ccco.create_time) AS create_time,
            COALESCE(ccc.update_by, ccco.update_by) AS update_by,
            COALESCE(ccc.update_time, ccco.update_time) AS update_time,
            COALESCE(ccc.expense_type, ccco.expense_type) AS expense_type,
            COALESCE(ccc.plant_id, ccco.plant_id) AS plant_id,
            COALESCE(ccc.currency, ccco.currency) AS currency,
            COALESCE(ccc.cost_center_type, ccco.cost_center_type) AS cost_center_type,
            COALESCE(ccc.delete_flag, false) AS delete_flag
        FROM zjdata.ccm_cost_center_orig ccco
                 FULL OUTER JOIN zjdata.ccm_cost_center ccc
                                 ON ccco.control_area_id = ccc.control_area_id
                                     AND ccco.cost_center_id = ccc.cost_center_id
                                     AND ccco.date_to = ccc.date_to
    </sql>

    <update id="batchDeleteByKey">
        update zjdata.ccm_cost_center
        set delete_flag = true
        where (control_area_id, cost_center_id, date_to) in
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.costCenterId}, #{item.dateTo, jdbcType=DATE})
        </foreach>
    </update>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        select
            (SELECT COUNT(1)
                       FROM zjdata.ccm_cost_center
                       where control_area_id = #{controlAreaId}
                         and cost_center_id = #{costCenterId}
                         AND date(date_to) = #{dateTo}::date and delete_flag = false)
        +
        (SELECT COUNT(1)
        FROM zjdata.ccm_cost_center_orig
        where control_area_id = #{controlAreaId}
          and cost_center_id = #{costCenterId}
          AND date(date_to) = #{dateTo}::date)
    </select>

    <delete id="deleteByKey" parameterType="String">
        delete from zjdata.ccm_cost_center
        where (control_area_id, cost_center_id, date(date_to)) = (#{controlAreaId}, #{costCenterId}, #{dateTo}::date)
    </delete>

    <select id="checkIdExist" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM zjdata.ccm_cost_center node where node.control_area_id = #{controlAreaId}  and node.cost_center_id = #{costCenterId} AND node.date_to = #{item.dateTo, jdbcType=DATE} AND node.delete_flag = false AND node.enable = true
    </select>

    <select id="checkCostCenterIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM (
            <include refid="selectSql"/>
        ) node
        where node.cost_center_id = #{costCenterId} and node.delete_flag = false and node.enable = true
    </select>

    <sql id="selectListSql">
        SELECT
        node.*, company.company_desc as company_desc, area.control_area_desc as control_area_desc
        FROM ( <include refid="selectSql"/>
        ) node
        left join zjdata.ccm_company company on node.company_id = company.company_id
        left join zjdata.ccm_control_area area on node.control_area_id = area.control_area_id
        <where>
            node.delete_flag = false
            <if test="costCenterGroupId != null and costCenterGroupId != '' and costCenterGroupId != '-1'">
                AND node.cost_center_group_id = #{costCenterGroupId}
            </if>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY cost_center_id, date_to,control_area_id ${sort}
    </sql>

    <select id="selectListPage" resultType="com.datalink.fdop.base.api.domain.CostCenter">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.CostCenter">
        <include refid="selectListSql"/>
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.CostCenter">
        SELECT a.*, company.company_desc as company_desc, menu.menu_desc as menu_desc,area.control_area_desc as control_area_desc
        FROM (<include refid="selectSql"/>) a left join zjdata.ccm_company company on a.company_id = company.company_id
                                      left join zjdata.ccm_control_area area on a.control_area_id = area.control_area_id
                                      left join zjdata.ccm_cost_center_menu menu on a.menu_id = menu.menu_id
        where a.cost_center_id = #{id} AND a.date_to = '${date}' and a.control_area_id =#{controlAreaId} and delete_flag = false
        limit 1
    </select>

    <select id="selectIdsByPid" resultType="String">
        select cost_center_id from (<include refid="selectSql"/>) a
        where a.menu_id = #{pid} and a.delete_flag = false
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CostCenter">
        update zjdata.ccm_cost_center
        set
            <!--<if test="controlAreaId != null and controlAreaId != ''">control_area_id = #{controlAreaId},</if>
            <if test="costCenterId != null and costCenterId != ''">cost_center_id = #{costCenterId},</if>
            <if test="dateTo != null">date_to = #{dateTo},</if>-->
            <if test="dateFrom != null">date_from = #{dateFrom},</if>
            <if test="costCenterDesc != null">cost_center_desc = #{costCenterDesc},</if>
            <if test="costCenterText != null">cost_center_text = #{costCenterText},</if>
            <if test="costCenterType != null">cost_center_type = #{costCenterType},</if>
            <if test="expenseType != null">expense_type = #{expenseType},</if>
            <if test="plantId != null">plant_id = #{plantId},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="costCenterGroupId != null">cost_center_group_id = #{costCenterGroupId},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id =#{controlAreaId} and cost_center_id = #{costCenterId} and date_to = #{dateTo}
    </update>

    <select id="selectCompanyId" resultType="String" parameterType="String">
        select company_id from zjdata.ccm_company where company_id = #{id} limit 1
    </select>

    <select id="selectEvaluateAreaId" resultType="String" parameterType="String">
        select evaluate_area_id from zjdata.ccm_evaluate_area where evaluate_area_id = #{id} limit 1
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_cost_center
        SET enable = #{enable}
        where (control_area_id, cost_center_id, date_to) in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.costCenterId}, #{item.dateTo})
        </foreach>
    </update>

    <select id="queryTypeAll" resultType="String" parameterType="String">
        SELECT DISTINCT node.cost_center_type AS cost_center_type FROM
        (
            SELECT
                COALESCE(ccc.control_area_id, ccco.control_area_id) AS control_area_id,
                COALESCE(ccc.cost_center_type, ccco.cost_center_type) AS cost_center_type,
                COALESCE(ccc.enable, ccco.enable) AS enable,
                COALESCE(ccc.date_to, ccco.date_to) AS date_to,
                COALESCE(ccc.delete_flag, false)  AS delete_flag
            FROM zjdata.ccm_cost_center_orig ccco
                     FULL OUTER JOIN zjdata.ccm_cost_center ccc
                                     ON ccco.control_area_id = ccc.control_area_id
                                         AND ccco.cost_center_id = ccc.cost_center_id
                                         AND ccco.date_to = ccc.date_to
        ) as node
        WHERE node.enable = true
          AND node.control_area_id = #{controlAreaId}
          AND date(node.date_to) >= #{dateTo}::date
          AND node.delete_flag = false
          AND node.cost_center_type IS NOT NULL AND cost_center_type &lt;&gt; ''
    </select>

    <select id="queryOrigList" resultType="com.datalink.fdop.base.api.domain.CostCenter">
        select * from zjdata.ccm_cost_center_orig node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY cost_center_id, date_to,control_area_id ${sort}
    </select>

    <select id="queryList" resultType="com.datalink.fdop.base.api.domain.CostCenter">
        select * from zjdata.ccm_cost_center node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY cost_center_id, date_to,control_area_id ${sort}
    </select>

    <delete id="batchDelete">
        delete from zjdata.ccm_cost_center
        where (control_area_id, cost_center_id, date_to) in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.costCenterId}, #{item.dateTo})
        </foreach>
    </delete>


</mapper>


package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdMaterialMbew;

import java.util.List;

public interface DwdMaterialMbewService extends IService<DwdMaterialMbew> {

    PageDataInfo<DwdMaterialMbew> overview(String materialType, String materialGroup, String materialId, String evaluateAreaId, String sort, SearchVo searchVo);

    List<DwdMaterialMbew> selectNoPage(String materialType, String materialGroup, String materialId, String evaluateAreaId, String sort, SearchVo searchVo);
}
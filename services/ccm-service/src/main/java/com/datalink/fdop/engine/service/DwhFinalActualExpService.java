package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalActualExp;

import java.util.List;

public interface DwhFinalActualExpService extends IService<DwhFinalActualExp> {

    PageDataInfo<DwhFinalActualExp> overview(String verId, String companyId, String yearMonth, String sort, SearchVo searchVo);

    List<DwhFinalActualExp> selectNoPage(String verId,
                                         String companyId,
                                         String yearMonth,
                                         String sort,
                                         SearchVo searchVo);
}
package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.base.api.domain.StructureHead;
import com.datalink.fdop.base.api.domain.StructureVer;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface StructureHeadMapper extends BaseMapper<StructureHead> {

    int checkIdUnique(@Param("structureVerId") String structureVerId, @Param("dateTo") Date dateTo, @Param("costStructureId") String costStructureId);

    int deleteByIds(@Param(value = "list") List<StructureHead> list);

    Page<StructureHead> pageList(IPage<StructureHead> page, @Param("searchVo") SearchVo searchVo, @Param("sort") String sort);

    boolean updateBatchStatus(@Param("enable") Boolean enable , @Param("list") List<StructureHead> list);

    List<String> listAll(@Param("costStructureId") String costStructureId);

    int update(StructureHead structureVer);
}

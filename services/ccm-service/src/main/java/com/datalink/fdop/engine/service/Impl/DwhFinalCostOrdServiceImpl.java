package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalCostOrd;
import com.datalink.fdop.engine.mapper.DwhFinalCostOrdMapper;
import com.datalink.fdop.engine.service.DwhFinalCostOrdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalCostOrdServiceImpl extends ServiceImpl<DwhFinalCostOrdMapper, DwhFinalCostOrd> implements DwhFinalCostOrdService {

    @Autowired
    private DwhFinalCostOrdMapper dwhFinalCostOrdMapper;

    @Override
    public PageDataInfo<DwhFinalCostOrd> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwhFinalCostOrd> page = PageUtils.getPage(DwhFinalCostOrd.class);
        IPage<DwhFinalCostOrd> iPage = dwhFinalCostOrdMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalCostOrd> listAll() {
        return dwhFinalCostOrdMapper.listAll();
    }

    @Override
    public List<DwhFinalCostOrd> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwhFinalCostOrdMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
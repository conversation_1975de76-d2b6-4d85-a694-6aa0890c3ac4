package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdMps;

import java.util.List;

public interface DwdMpsService extends IService<DwdMps> {

    PageDataInfo<DwdMps> overview(
            String verId, String plantId, String yearMonthFrom, String yearMonthTo,
            String sort, SearchVo searchVo);

    List<DwdMps> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo);

}

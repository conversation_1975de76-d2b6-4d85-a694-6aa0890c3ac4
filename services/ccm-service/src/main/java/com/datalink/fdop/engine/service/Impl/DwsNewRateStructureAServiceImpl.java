package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRateStructureA;
import com.datalink.fdop.engine.mapper.DwsNewRateStructureAMapper;
import com.datalink.fdop.engine.service.DwsNewRateStructureAService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRateStructureAServiceImpl extends ServiceImpl<DwsNewRateStructureAMapper, DwsNewRateStructureA> implements DwsNewRateStructureAService {

    @Autowired
    private DwsNewRateStructureAMapper dwsNewRateStructureAMapper;

    @Override
    public PageDataInfo<DwsNewRateStructureA> overview(
            String controlAreaId,
            String verId,
            String companyId,
            String costCenterType,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo) {
        Page<DwsNewRateStructureA> page = PageUtils.getPage(DwsNewRateStructureA.class);
        IPage<DwsNewRateStructureA> iPage = dwsNewRateStructureAMapper.selectAll(
                controlAreaId, verId, companyId, costCenterType, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRateStructureA> selectNoPage(String controlAreaId, String verId, String companyId, String costCenterType, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwsNewRateStructureAMapper.selectNoPage(controlAreaId, verId, companyId, costCenterType, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
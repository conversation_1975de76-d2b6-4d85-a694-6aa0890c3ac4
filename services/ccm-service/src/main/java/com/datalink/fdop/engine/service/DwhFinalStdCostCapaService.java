package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostCapa;

import java.util.List;

public interface DwhFinalStdCostCapaService extends IService<DwhFinalStdCostCapa> {

    PageDataInfo<DwhFinalStdCostCapa> overview(
            String plantId, String verId, String controlAreaId, String productId, Integer batchQty, String baseYearMonth,
            String dateFrom, String dateTo, String sort, SearchVo searchVo);

    List<DwhFinalStdCostCapa> selectNoPage(String plantId,
                                           String verId,
                                           String controlAreaId,
                                           String productId,
                                           Integer batchQty,
                                           String baseYearMonth,
                                           String dateFrom,
                                           String dateTo,
                                           String sort,
                                           SearchVo searchVo);
}
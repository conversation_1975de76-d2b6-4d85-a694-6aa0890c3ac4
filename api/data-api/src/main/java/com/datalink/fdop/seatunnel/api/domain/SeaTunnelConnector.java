package com.datalink.fdop.seatunnel.api.domain;


import com.datalink.fdop.seatunnel.api.enums.SeaTunnelConnectorType;
import lombok.Data;

@Data
public class SeaTunnelConnector {

    /**
     * ST Connector类型
     */
    private SeaTunnelConnectorType connectorType;

    /**
     * ST 连接信息
     */
    private String connectorInfo;

    public SeaTunnelConnector() {
    }

    public SeaTunnelConnector(SeaTunnelConnectorType connectorType, String connectorInfo) {
        this.connectorType = connectorType;
        this.connectorInfo = connectorInfo;
    }
}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalVarcostLot;
import com.datalink.fdop.engine.api.domain.DwsNewVarcostLot;

import java.util.List;

public interface DwhFinalVarcostLotService extends IService<DwhFinalVarcostLot> {

    PageDataInfo<DwhFinalVarcostLot> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo);

    List<DwhFinalVarcostLot> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo);
}
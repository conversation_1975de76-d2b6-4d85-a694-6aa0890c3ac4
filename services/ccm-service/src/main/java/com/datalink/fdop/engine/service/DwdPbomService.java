package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.engine.api.domain.DwdPbom;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdPbomService extends IService<DwdPbom> {

    PageDataInfo<DwdPbom> overview(String plantId,String productId, String sort, SearchVo searchVo);

    List<DwdPbom> listAll();

    List<DwdPbom> selectNoPage(String plantId, String productId, String sort, SearchVo searchVo);
}

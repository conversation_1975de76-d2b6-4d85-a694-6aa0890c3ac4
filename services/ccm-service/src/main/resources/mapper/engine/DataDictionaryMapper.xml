<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DataDictionaryMapper">


    <select id="listPlantId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct node.plant_id as "key", dict.plant_desc as "value"
        from ${orgTableName} node
            left join ods.ods_ccms_ccm_plant dict
        on node.plant_id = dict.plant_id
        where node.plant_id is not null and node.plant_id &lt;> ''
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
        </if>
        order by node.plant_id asc
    </select>

    <select id="selectEvaluateAreaId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct node.evaluate_area_id as "key", dict.evaluate_area_desc as "value"
        from ${orgTableName} node
                 left join ods.ods_ccms_ccm_evaluate_area dict
                           on node.evaluate_area_id = dict.evaluate_area_id
        where node.evaluate_area_id is not null and node.evaluate_area_id &lt;> ''
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
        </if>
        order by node.evaluate_area_id asc
    </select>

    <select id="listVerId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct node.ver_id as "key", dict.ver_desc as "value"
        from ${orgTableName} node
                 left join ods.ods_ccms_ccm_ver dict
                           on node.ver_id = dict.ver_id
        where node.ver_id is not null and node.ver_id &lt;> ''
        <if test="searchVo != null">
            and
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
        </if>
        order by node.ver_id asc
    </select>

    <select id="listCompanyId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct node.company_id as "key", dict.company_desc as "value"
        from ${orgTableName} node
                 left join ods.ods_ccms_ccm_company dict
                           on node.company_id = dict.company_id
        where node.company_id is not null and node.company_id &lt;> ''
        <if test="searchVo != null">
            and
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
        </if>
        order by node.company_id asc
    </select>

    <select id="listCompanyIdAndFieldName" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct ${fieldName} as "key", dict.company_desc as "value"
        from ${orgTableName} node
        left join ods.ods_ccms_ccm_company dict
        on ${fieldName} = dict.company_id
        where ${fieldName} is not null and ${fieldName} &lt;> ''
        <if test="searchVo != null">
            and
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
        </if>
        order by ${fieldName} asc
    </select>

    <select id="listManageScope" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct node.control_area_id as "key", dict.control_area_desc as "value"
        from ${orgTableName} node
                 left join ods.ods_ccms_ccm_control_area dict
                           on node.control_area_id = dict.control_area_id
        where node.control_area_id is not null and node.control_area_id &lt;> ''
        <if test="searchVo != null">
            and
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
        </if>
        order by node.control_area_id asc
    </select>

    <select id="listCostCenterId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct node.cost_center_id as "key", dict.cost_center_desc as "value"
        from ${orgTableName} node
        left join ods.ods_ccms_ccm_cost_center dict
        on node.cost_center_id = dict.cost_center_id
        where node.cost_center_id is not null and node.cost_center_id &lt;> ''
        <if test="searchVo != null">
            and
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
        </if>
        order by node.cost_center_id asc
    </select>

</mapper>


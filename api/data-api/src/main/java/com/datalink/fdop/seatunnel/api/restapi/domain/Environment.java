package com.datalink.fdop.seatunnel.api.restapi.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class Environment {

    private String jobName;

    private String jobMode;

    @JSONField(name = "execution.parallelism")
    private int executionParallelism;

    public Environment() {
    }

    public Environment(String jobName, String jobMode) {
        this.jobName = jobName;
        this.jobMode = jobMode;
        // 默认值 2.3.3以后的版本不需要这个值
        // this.checkpointInterval = 10000;
        this.executionParallelism = 1;
    }

}

package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhFinalRevalueMat;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhFinalRevalueMatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwhFinalRevalueMat")
@Api(tags = "定版视图-物料账记数据")
public class DwhFinalRevalueMatController extends BaseController {

    @Autowired
    private DwhFinalRevalueMatService dwhFinalRevalueMatService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "定版视图-物料账记数据")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String yearMonth = MapUtils.getAsString(params , "yearMonth");
        String verId = MapUtils.getAsString(params , "verId");
        String controlAreaId = MapUtils.getAsString(params , "controlAreaId");
        String companyId = MapUtils.getAsString(params , "companyId");
        PageDataInfo<DwhFinalRevalueMat> overview = dwhFinalRevalueMatService.overview(yearMonth, verId, controlAreaId, companyId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhFinalRevalueMat.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "定版视图-物料账记数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,@RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String yearMonth = MapUtils.getAsString(params , "yearMonth");
        String verId = MapUtils.getAsString(params , "verId");
        String controlAreaId = MapUtils.getAsString(params , "controlAreaId");
        String companyId = MapUtils.getAsString(params , "companyId");
        List<DwhFinalRevalueMat> list = dwhFinalRevalueMatService.selectNoPage(yearMonth, verId, controlAreaId, companyId, sort, queryParam.getSearchVo());
        ExcelUtil<DwhFinalRevalueMat> util = new ExcelUtil<>(DwhFinalRevalueMat.class);
        util.exportExcel(response, list, "定版视图-物料账记数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhFinalRevalueMat> util = new ExcelUtil<>(DwhFinalRevalueMat.class);
        util.importTemplateExcel(response, "定版视图-物料账记模板");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_final_revalue_mat", null));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId() {
        return R.ok(dataDictionaryService.listCompanyId("dwh.dwh_final_revalue_mat", null));
    }

}
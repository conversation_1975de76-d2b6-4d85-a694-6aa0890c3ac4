package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.base.api.domain.CostElementGroupItem;
import com.datalink.fdop.base.api.model.CostElementGroupHeadTree;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CostElementGroupItemMapper extends BaseMapper<CostElementGroupItem> {

    int checkIdUnique(@Param("controlAreaId") String controlAreaId, @Param("costElementGroupId") String costElementGroupId, @Param("item") String item);

    int batchDeleteByKey(@Param("keyList") List<CostElementGroupItem> keyList);

    IPage<CostElementGroupItem> selectList(IPage<CostElementGroupItem> page, @Param("controlAreaId") String controlAreaId, @Param("costElementGroupId") String pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    List<CostElementGroupItem> selectListNoPage(@Param("controlAreaId") String controlAreaId, @Param("costElementGroupId") String pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    List<CostElementGroupItem> selectIdsByPid(@Param("controlAreaId") String controlAreaId, @Param("costElementGroupId") String costElementGroupId);

    int updateByKey(CostElementGroupItem CostElementGroupItem);

    boolean updateBatchStatus(@Param("enable") Boolean enable, @Param("ids") List<CostElementGroupItem> ids);

    List<CostElementGroupHeadTree> selectNodeTree(@Param("controlAreaId") String controlAreaId, @Param("code") String code , @Param("sort") String sort);

}

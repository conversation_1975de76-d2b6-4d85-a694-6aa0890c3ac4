package com.datalink.fdop.element.api.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 实体血缘关系描述
 */
@Data
public class DataEntityBloodRelationship {

    @ApiModelProperty(value = "目标实体ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetId;

    @ApiModelProperty(value = "目标实体结构ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetStructureId;

    @ApiModelProperty(value = "源实体ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sourceId;

    @ApiModelProperty(value = "源实体结构ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sourceStructureId;

}

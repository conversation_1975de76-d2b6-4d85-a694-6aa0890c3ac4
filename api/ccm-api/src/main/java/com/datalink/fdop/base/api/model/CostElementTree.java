package com.datalink.fdop.base.api.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.enums.MenuType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-10-27 14:43
 */
@Data
public class CostElementTree {

    private String menuId;

    private String pMenuId;

    private String menuDesc;

    private String menuText;

    private MenuType menuType;

    private List<CostElementTree> children = new ArrayList<>();

    private String controlAreaId;

    private String controlAreaDesc;

    private String costElementId;

    private String costElementDesc;

    private String costElementText;

    private String costElementType;

    private Boolean enable;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;

}

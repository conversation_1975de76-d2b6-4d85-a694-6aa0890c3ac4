package com.datalink.fdop.engine.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-02-15 15:34
 */
public interface TransService {

    Map<String, Object> stdCostRun(String verId, String controlAreaId,
                                   String factoryId, String effectiveDate,
                                   Long batchQty, String productId, Long productVer, String type, SearchVo searchVo);

    Map<String, Object> consumptionRun(String verId, String factoryId, Long yearMonth, String sort, SearchVo searchVo);

    void stdCostSave(String verId, String controlAreaId, String factoryId, String effectiveDate,
                     Long batchQty, String productId, Long productVe);

    void consumptionSave(String verId, String factoryId, Long yearMonth);

    Map<String, Object> accountReport(String reportVer, String verId, String groupId, String companyId, String factoryId, Long year, String date1, String date2, BigDecimal thisVer, BigDecimal fcstVer);

    Map<String, Object> stdCostOut(String verId, String controlAreaId,
                                   String factoryId, String startTime,
                                   String endTime, String productId,
                                   Long productVer, String type, SearchVo searchVo);
}

/*
 * Datart
 * <p>
 * Copyright 2021
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.datalink.fdop.datart.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.datart.api.domain.Download;
import com.datalink.fdop.datart.base.dto.ResponseData;
import com.datalink.fdop.datart.base.params.DownloadCreateParam;
import com.datalink.fdop.datart.service.DownloadService;
import datart.core.common.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.tomcat.util.http.fileupload.util.Streams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;


@Api
@RestController
@RequestMapping(value = "/datart/download")
public class DownloadController extends BaseController {

    @Autowired
    private DownloadService downloadService;

    @ApiOperation(value = "get download tasks")
    @PostMapping(value = "/tasks")
    public ResponseData<PageDataInfo<Download>> listDownloadTasks(@RequestBody(required = false) Download download) {
        return ResponseData.success(downloadService.listDownloadTasks(download));
    }

    @ApiOperation(value = "submit a new download task")
    @PostMapping(value = "/submit/task")
    public ResponseData<Download> submitDownloadTask(@RequestBody @Validated DownloadCreateParam createParam) {
        return ResponseData.success(downloadService.submitDownloadTask(createParam));
    }

    @ApiOperation(value = "submit a new download task")
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<String> ids) {
        return R.toResult(downloadService.delete(ids));
    }

    @ApiOperation(value = "get download file")
    @GetMapping(value = "/files/{id}")
    public void downloadFile(@PathVariable String id,
                             HttpServletResponse response) throws IOException {
        Download download = downloadService.downloadFile(id);
        response.setHeader("Content-Type", "application/octet-stream");
        File file = new File(FileUtils.withBasePath(download.getPath()));
        response.setHeader("Content-Disposition", String.format("attachment;filename=\"%s\"", URLEncoder.encode(file.getName(), "utf-8")));
        try (InputStream inputStream = new FileInputStream(file)) {
            Streams.copy(inputStream, response.getOutputStream(), true);
        }
    }

}
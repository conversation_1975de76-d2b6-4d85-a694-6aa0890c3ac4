<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.FactorMapper">

    <delete id="deleteBykey">
        delete from zjdata.ccm_factor
        where factor_id = #{factorId} AND control_area_id = #{controlAreaId}
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_factor
        WHERE factor_id = #{factorId} AND control_area_id = #{controlAreaId} limit 1
    </select>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.Factor" resultType="com.datalink.fdop.base.api.domain.Factor">
        SELECT a.*, controlArea.control_area_desc as control_area_desc,
            formulaManagement.formula as formula
        FROM zjdata.ccm_factor a left join zjdata.ccm_control_area controlArea on controlArea.control_area_id = a.control_area_id
        left join zjdata.ccm_formula formulaManagement on a.formula_id = formulaManagement.formula_id
        <where>
            <if test="factor.factorId != null and factor.factorId != ''">
                AND a.factor_id = #{factor.factorId}
            </if>
            <if test="factor.controlAreaId != null and factor.controlAreaId != ''">
                AND a.control_area_id = #{factor.controlAreaId}
            </if>
        </where>
        ORDER BY control_area_id, factor_id desc
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.Factor">
        SELECT a.*, controlArea.control_area_desc as control_area_desc, menu.menu_desc as menu_desc,
               formulaManagement.formula as formula
        FROM zjdata.ccm_factor a left join zjdata.ccm_control_area controlArea on controlArea.control_area_id = a.control_area_id and controlArea.del_flag = false
                                 left join zjdata.ccm_formula formulaManagement on a.formula_id = formulaManagement.formula_id and formulaManagement.del_flag = false
        where a.factor_id = #{factorId} AND a.control_area_id = #{controlAreaId}
        limit 1
    </select>

    <select id="selectIdsByPid" resultType="String">
        select factor_id from zjdata.ccm_factor
        where menu_id = #{pid} and del_flag = false
    </select>

    <update id="bacthUpdatePidById">
        update zjdata.ccm_factor
        set menu_id = (select p_menu_id from zjdata.ccm_factor_menu where menu_id = #{menuId} limit 1)
        where menu_id = #{menuId} and del_flag = false
    </update>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.Factor">
        update zjdata.ccm_factor
        set
        <if test="factorDesc != null">factor_desc = #{factorDesc},</if>
        <if test="factorText != null">factor_text = #{factorText},</if>
        <if test="unit != null">unit = #{unit},</if>
        <if test="unitDesc != null">unit_desc = #{unitDesc},</if>
        <if test="formulaId != null">formula_id = #{formulaId},</if>
        <if test="valueTypeP != null">value_type_p = #{valueTypeP},</if>
        <if test="verUsed != null">ver_used = #{verUsed},</if>
        <if test="valueTypeA != null">value_type_a = #{valueTypeA},</if>
        <if test="enable != null">enable = #{enable},</if>
        <if test="assignUsed != null">assign_used = #{assignUsed},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where factor_id = #{factorId} and control_area_id = #{controlAreaId}
    </update>

    <select id="selectControlAreaId" resultType="String" parameterType="String">
        select control_area_id from zjdata.ccm_control_area where control_area_id = #{id}  limit 1
    </select>

    <select id="select" resultType="com.datalink.fdop.base.api.domain.Factor">
        SELECT a.*, controlArea.control_area_desc as control_area_desc,
        formulaManagement.formula as formula
        FROM zjdata.ccm_factor a left join zjdata.ccm_control_area controlArea on controlArea.control_area_id = a.control_area_id
        left join zjdata.ccm_formula formulaManagement on a.formula_id = formulaManagement.formula_id
        <where>
            <if test="startCode != null and startCode != ''">
                AND
                a.factor_id >= #{startCode}
            </if>
            <if test="endCode != null and endCode != ''">
                AND
                a.factor_id &lt;= #{endCode}
            </if>
        </where>
        ORDER BY control_area_id, factor_id ${sort}
    </select>

    <select id="getAll" resultType="com.datalink.fdop.base.api.domain.Factor">
        SELECT a.*, controlArea.control_area_desc as control_area_desc,
               formulaManagement.formula as formula
        FROM zjdata.ccm_factor a left join zjdata.ccm_control_area controlArea on controlArea.control_area_id = a.control_area_id
                                 left join zjdata.ccm_formula formulaManagement on a.formula_id = formulaManagement.formula_id
        ORDER BY control_area_id, factor_id desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.Factor">
        SELECT a.*, controlArea.control_area_desc as control_area_desc,
        formulaManagement.formula as formula
        FROM zjdata.ccm_factor a left join zjdata.ccm_control_area controlArea on controlArea.control_area_id = a.control_area_id
        left join zjdata.ccm_formula formulaManagement on a.formula_id = formulaManagement.formula_id
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
        </where>
        ORDER BY control_area_id, factor_id ${sort}
    </select>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_factor
            SET enable = #{item.enable}
            where factor_id = #{item.factorId} and control_area_id = #{item.controlAreaId}
        </foreach>
    </update>


</mapper>


package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRateStructureP;
import com.datalink.fdop.engine.mapper.DwsNewRateStructurePMapper;
import com.datalink.fdop.engine.service.DwsNewRateStructurePService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRateStructurePServiceImpl extends ServiceImpl<DwsNewRateStructurePMapper, DwsNewRateStructureP> implements DwsNewRateStructurePService {

    @Autowired
    private DwsNewRateStructurePMapper dwsNewRateStructurePMapper;

    @Override
    public PageDataInfo<DwsNewRateStructureP> overview(
            String controlAreaId,
            String verId,
            String companyId,
            String costCenterType,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo) {
        Page<DwsNewRateStructureP> page = PageUtils.getPage(DwsNewRateStructureP.class);
        IPage<DwsNewRateStructureP> iPage = dwsNewRateStructurePMapper.selectAll(
                controlAreaId, verId, companyId, costCenterType, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRateStructureP> selectNoPage(String controlAreaId, String verId, String companyId, String costCenterType, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwsNewRateStructurePMapper.selectNoPage(controlAreaId, verId, companyId, costCenterType, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
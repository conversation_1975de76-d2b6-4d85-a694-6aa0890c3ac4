<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.ProjectMapper">

    <sql id="selectSql">
        SELECT
            COALESCE(c.project_id, co.project_id) AS project_id,
            COALESCE(c.project_desc, co.project_desc) AS project_desc,
            COALESCE(c.project_text, co.project_text) AS project_text,
            COALESCE(c.company_id, co.company_id) AS company_id,
            COALESCE(c.menu_id, co.menu_id) AS menu_id,
            COALESCE(c.enable, co.enable) AS enable,
            COALESCE(c.create_by, co.create_by) AS create_by,
            COALESCE(c.create_time, co.create_time) AS create_time,
            COALESCE(c.update_by, co.update_by) AS update_by,
            COALESCE(c.update_time, co.update_time) AS update_time,
            COALESCE(c.plant_id, co.plant_id) AS plant_id,
            COALESCE(c.control_area_id, co.control_area_id) AS control_area_id,
            COALESCE(c.delete_flag, false) AS delete_flag
        FROM zjdata.ccm_project_orig co
                 FULL OUTER JOIN zjdata.ccm_project c
                                 ON c.project_id = co.project_id
    </sql>

    <update id="deleteByKey">
        update zjdata.ccm_project
        set delete_flag = true
        where project_id = #{id}
    </update>

    <update id="batchDelete">
        update zjdata.ccm_project
        set delete_flag = true
        where project_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        select
        (SELECT COUNT(1)
        FROM zjdata.ccm_project
        WHERE project_id = #{id} and delete_flag = false)
        +
        (SELECT COUNT(1)
        FROM zjdata.ccm_project_orig
        WHERE project_id = #{id})
    </select>

    <select id="checkIdExist" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_project
        WHERE project_id = #{id} and delete_flag = false limit 1
    </select>

    <delete id="deleteById" parameterType="String">
        delete from zjdata.ccm_project
        where project_id = #{id}
    </delete>

<!--    <update id="deleteById" parameterType="String">-->
<!--        update zjdata.ccm_project-->
<!--        set delete_flag = true-->
<!--        where project_id = #{id}-->
<!--    </update>-->


    <select id="list" parameterType="com.datalink.fdop.base.api.domain.Project" resultType="com.datalink.fdop.base.api.domain.Project">
        SELECT a.*, company.company_desc as company_desc, controlArea.control_area_desc as control_area_desc, menu.menu_desc as menu_desc,b.plant_desc as plant_desc
        FROM (<include refid="selectSql"/>) a left join zjdata.ccm_company company on a.company_id = company.company_id
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        left join zjdata.ccm_plant b on a.plant_id = b.plant_id
        left join zjdata.ccm_wbs_menu menu on a.menu_id = menu.menu_id
        <where>
            and a.delete_flag = false
            <if test="project.wbsId != null and project.wbsId != ''">
                AND a.project_id = #{project.wbsId}
            </if>
            <if test="project.menuId != null and project.menuId != ''">
                AND a.menu_id = #{project.menuId}
            </if>
        </where>
        ORDER BY project_id desc
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.Project">
        SELECT a.*, company.company_desc as company_desc, controlArea.control_area_desc as control_area_desc, menu.menu_desc as menu_desc,b.plant_desc as plant_desc
        FROM (<include refid="selectSql"/>) a left join zjdata.ccm_company company on a.company_id = company.company_id
                              left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
                              left join zjdata.ccm_wbs_menu menu on a.menu_id = menu.menu_id
                              left join zjdata.ccm_plant b on a.plant_id = b.plant_id

        where a.project_id = #{id} and a.delete_flag = false
        limit 1
    </select>

    <select id="selectIdsByPid" resultType="String">
        select project_id from zjdata.ccm_project
        where menu_id = #{pid}
    </select>

    <update id="batchUpdatePidByIds">
        update zjdata.ccm_project
        set menu_id = #{pid}
        where project_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.Project">
        update zjdata.ccm_project
        set
        <if test="dateTo != null">
            date_to = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateTo)}',
        </if>
        <if test="projectDesc != null">project_desc = #{projectDesc},</if>
        <if test="projectText != null">wbs_text = #{projectText},</if>
        <if test="companyId != null">company_id = #{companyId},</if>
        <if test="controlAreaId != null">control_area_id = #{controlAreaId},</if>
        <if test="menuId != null">menu_id = #{menuId},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where project_id = #{projectId}
    </update>

    <select id="selectCompanyId" resultType="String" parameterType="String">
        select company_id from zjdata.ccm_company where company_id = #{id} limit 1
    </select>

    <select id="selectControlAreaId" resultType="String" parameterType="String">
        select control_area_id from zjdata.ccm_control_area where control_area_id = #{id} limit 1
    </select>

    <select id="select" resultType="com.datalink.fdop.base.api.domain.Project">
        SELECT a.*, company.company_desc as company_desc, controlArea.control_area_desc as control_area_desc, menu.menu_desc as menu_desc,b.plant_id as plant_desc
        FROM (<include refid="selectSql"/>) a left join zjdata.ccm_company company on a.company_id = company.company_id
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        left join zjdata.ccm_wbs_menu menu on a.menu_id = menu.menu_id
        left join zjdata.ccm_plant b on a.plant_id = b.plant_id
        <where>
            and a.delete_flag = false
            <if test="startCode != null and startCode != ''">
                AND
                a.project_id >= #{startCode}
            </if>
            <if test="endCode != null and endCode != ''">
                AND
                a.project_id &lt;= #{endCode}
            </if>
        </where>
        ORDER BY project_id ${sort}
    </select>

    <select id="getAll" resultType="com.datalink.fdop.base.api.domain.Project">
        SELECT a.*, company.company_desc as company_desc, controlArea.control_area_desc as control_area_desc, menu.menu_desc as menu_desc,b.plant_id as plant_desc
        FROM (<include refid="selectSql"/>) a left join zjdata.ccm_company company on a.company_id = company.company_id
                                  left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
                                  left join zjdata.ccm_wbs_menu menu on a.menu_id = menu.menu_id
                                  left join zjdata.ccm_plant b on a.plant_id = b.plant_id
        ORDER BY project_id desc
    </select>

    <sql id="selectListSql">
        SELECT
            node.*, company.company_desc as company_desc, controlArea.control_area_desc as control_area_desc, menu.name as menu_desc,b.plant_desc
        FROM ( <include refid="selectSql"/>
        ) node left join zjdata.ccm_company company on node.company_id = company.company_id
        left join zjdata.ccm_control_area controlArea on node.control_area_id = controlArea.control_area_id
        left join zjdata.ccm_project_menu menu on node.menu_id = menu.menu_id
        left join zjdata.ccm_plant b on node.plant_id = b.plant_id
        <where>
            node.delete_flag = false
            <if test="menuId != null and menuId != '-1'">
                AND node.menu_id = #{menuId}
            </if>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY project_id ${sort}
    </sql>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.Project">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.Project">
        <include refid="selectListSql"/>
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_project
        SET enable = #{enable}
        where project_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item.projectId}
        </foreach>
    </update>


    <select id="selectNodeTree" resultType="com.datalink.fdop.base.api.model.ProjectTree">
        select project_id as id, menu_id as pid, 'NODE' as menuType, project_desc as name, project_text as description from (
            <include refid="selectSql"/>
        ) node
        <where>
            and node.delete_flag = false
            <if test="code != null and code!= ''">
                and node.project_id like concat('%', #{code}, '%')
            </if>
        </where>
        ORDER BY id ${sort}
    </select>

    <select id="selectOrigProjectsByPid" resultType="com.datalink.fdop.base.api.domain.Project">
        select * from zjdata.ccm_project_orig where menu_id = #{menuId}
    </select>

    <select id="queryOrigList" resultType="com.datalink.fdop.base.api.domain.Project">
        select * from zjdata.ccm_project_orig node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY project_id  ${sort}
    </select>

    <select id="queryList" resultType="com.datalink.fdop.base.api.domain.Project">
        select * from zjdata.ccm_project node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY project_id ${sort}
    </select>

    <delete id="batchDeleteId">
        delete from zjdata.ccm_project
        where (project_id) in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            (#{item.projectId})
        </foreach>
    </delete>



</mapper>


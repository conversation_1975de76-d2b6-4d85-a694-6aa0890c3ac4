package com.datalink.fdop.base.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.ProjectMenu;
import com.datalink.fdop.base.api.model.ProjectTree;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-09-06 11:35
 */
public interface ProjectMenuService extends IService<ProjectMenu> {

    int create(ProjectMenu projectMenu);

    int update(ProjectMenu projectMenu);

    int delete(List<String> ids);

    List<ProjectTree> tree(String code, String sort, Boolean isQueryNode);

    ProjectMenu selectById(String id);

}

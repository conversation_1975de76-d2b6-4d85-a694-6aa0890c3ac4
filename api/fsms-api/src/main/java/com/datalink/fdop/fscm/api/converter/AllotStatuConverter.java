package com.datalink.fdop.fscm.api.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;

/**
 * <AUTHOR>
 * @Date 2022/11/16 13:56
 */
public class AllotStatuConverter implements  Converter<String> {


   @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 这里读的时候会调用
     *
     * @param context
     * @return
     */
    @Override
    public String convertToJavaData(ReadConverterContext<?> context) {
        return context.getReadCellData().toString();
    }

    /**
     * 这里是写的时候会调用 不用管
     *1-字符串；2-整数；3-浮点数；4-日期；5-时间
     * @return
     */
    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) {
        String paperType="";
        //0-采购订单；1-工单；2-出货计划；3-出货需求单；4-出货通知单；5-调拨单；6-领退料单； 7-报废单；8-过账凭证单；9-采购合同
        switch (context.getValue()){
            case "1":
                paperType="已创建";
                break;
            case "2":
                paperType="已发布到供应商";
                break;
            default:
                paperType="";
        }
        return new WriteCellData<>(paperType);
    }


}

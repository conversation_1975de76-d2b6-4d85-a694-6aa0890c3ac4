package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.engine.service.TransService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-02-15 15:34
 */
@RestController
@RequestMapping("/fccm/trans")
public class TransController {

    @Autowired
    private TransService transService;

    @ApiOperation("标准成本运算")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/stdCostRun")
    public R stdCostRun(@RequestParam(value = "verId") String verId,
                        @RequestParam(value = "controlAreaId") String controlAreaId,
                        @RequestParam(value = "factoryId") String factoryId,
                        @RequestParam(value = "effectiveDate") String effectiveDate,
                        @RequestParam(value = "batchQty", required = false, defaultValue = "1") Long batchQty,
                        @RequestParam(value = "productId", required = false) String productId,
                        @RequestParam(value = "productVer", required = false) Long productVer,
                        @RequestParam(value = "type", required = false, defaultValue = "ProductID") String type,
                        @RequestParam(required = false, defaultValue = "ASC") String sort,
                        @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(transService.stdCostRun(verId, controlAreaId, factoryId, effectiveDate, batchQty, productId, productVer, type, searchVo));
    }

    @ApiOperation("标准成本输出")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/stdCostOut")
    public R stdCostOut(@RequestParam(value = "verId") String verId,
                        @RequestParam(value = "controlAreaId") String controlAreaId,
                        @RequestParam(value = "factoryId") String factoryId,
                        @RequestParam(value = "startTime") String startTime,
                        @RequestParam(value = "endTime") String endTime,
                        @RequestParam(value = "productId") String productId,
                        @RequestParam(value = "productVer") Long productVer,
                        @RequestParam(value = "type", required = false, defaultValue = "ProductID") String type,
                        @RequestParam(required = false, defaultValue = "ASC") String sort,
                        @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(transService.stdCostOut(verId, controlAreaId, factoryId, startTime, endTime, productId, productVer, type, searchVo));
    }

    @ApiOperation("标准成本保存")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/stdCostSave")
    public R stdCostSave(@RequestParam(value = "verId") String verId,
                         @RequestParam(value = "controlAreaId") String controlAreaId,
                         @RequestParam(value = "factoryId") String factoryId,
                         @RequestParam(value = "effectiveDate") String effectiveDate,
                         @RequestParam(value = "batchQty", required = false, defaultValue = "1") Long batchQty,
                         @RequestParam(value = "productId", required = false) String productId,
                         @RequestParam(value = "productVer", required = false) Long productVer) {
        transService.stdCostSave(verId, controlAreaId, factoryId, effectiveDate, batchQty, productId, productVer);
        return R.ok();
    }

    @ApiOperation("材料耗用分摊运算")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/consumptionRun")
    public R consumptionRun(@RequestParam(value = "verId") String verId, @RequestParam(value = "factoryId") String factoryId,
                            @RequestParam(value = "yearMonth") Long yearMonth, @RequestParam(required = false, defaultValue = "ASC") String sort,
                            @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(transService.consumptionRun(verId, factoryId, yearMonth, sort, searchVo));
    }

    @ApiOperation("材料耗用分摊保存")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/consumptionSave")
    public R consumptionSave(@RequestParam(value = "verId") String verId, @RequestParam(value = "factoryId") String factoryId,
                             @RequestParam(value = "yearMonth") Long yearMonth) {
        transService.consumptionSave(verId, factoryId, yearMonth);
        return R.ok();
    }

    @ApiOperation("科目分析")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/accountReport")
    public R accountReport(@RequestParam(value = "reportVer", required = false) String reportVer,
                           @RequestParam(value = "verId", required = false) String verId,
                           @RequestParam(value = "groupId", required = false) String groupId,
                           @RequestParam(value = "companyId", required = false) String companyId,
                           @RequestParam(value = "factoryId", required = false) String factoryId,
                           @RequestParam(value = "year", required = false) Long year,
                           @RequestParam(value = "date1", required = false) String date1,
                           @RequestParam(value = "date2", required = false) String date2,
                           @RequestParam(value = "thisVer", required = false) BigDecimal thisVer,
                           @RequestParam(value = "fcstVer", required = false) BigDecimal fcstVer
                           ) {
        return R.ok(transService.accountReport(reportVer, verId, groupId, companyId,
                                                factoryId, year, date1, date2, thisVer, fcstVer));
    }

}

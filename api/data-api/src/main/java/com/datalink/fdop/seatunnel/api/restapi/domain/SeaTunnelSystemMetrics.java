package com.datalink.fdop.seatunnel.api.restapi.domain;

import lombok.Data;

/**
 * 代表系统监控指标的实体类，与给定的JSON数据结构相对应。
 */
@Data
public class SeaTunnelSystemMetrics {

    /**
     * 系统处理器数量。
     */
    private int processors;

    /**
     * 物理内存总大小（单位：GB）。
     */
    private double physicalMemoryTotal;

    /**
     * 可用物理内存大小（单位：GB）。
     */
    private double physicalMemoryFree;

    /**
     * 交换空间总大小（单位：GB）。当前值为0，可能表示系统未启用交换空间。
     */
    private double swapSpaceTotal;

    /**
     * 可用交换空间大小（单位：GB）。当前值为0，可能表示系统未启用交换空间。
     */
    private double swapSpaceFree;

    /**
     * 堆内存已使用大小（单位：GB）。
     */
    private double heapMemoryUsed;

    /**
     * 堆内存空闲大小（单位：GB）。
     */
    private double heapMemoryFree;

    /**
     * 堆内存总大小（单位：GB）。
     */
    private double heapMemoryTotal;

    /**
     * 堆内存最大允许大小（单位：GB）。
     */
    private double heapMemoryMax;

    /**
     * 堆内存使用率（已使用内存占总内存的比例，以百分比表示）。
     */
    private double heapMemoryUsageRatio;

    /**
     * 堆内存使用率相对于最大堆内存的比例（已使用内存占最大允许内存的比例，以百分比表示）。
     */
    private double heapMemoryUsageMaxRatio;

    /**
     * 进行过的小型垃圾回收次数。
     */
    private int minorGcCount;

    /**
     * 小型垃圾回收累计耗时（单位：毫秒）。
     */
    private String minorGcTime;

    /**
     * 进行过的大型垃圾回收次数。
     */
    private int majorGcCount;

    /**
     * 大型垃圾回收累计耗时（单位：毫秒）。
     */
    private String majorGcTime;

    /**
     * 当前进程负载（以百分比表示）。
     */
    private double loadProcess;

    /**
     * 系统整体负载（以百分比表示）。
     */
    private double loadSystem;

    /**
     * 系统平均负载（取值范围通常为0到1之间，表示系统在过去一段时间内的平均活跃进程数与系统可用处理器数量的比值）。
     */
    private double loadSystemAverage;

    /**
     * 当前线程总数。
     */
    private int threadCount;

    /**
     * 线程峰值数量。
     */
    private int threadPeakCount;

    /**
     * 集群时间差（具体含义取决于上下文，此处未给出详细说明）。
     */
    private double clusterTimeDiff;

    /**
     * 事件队列大小。
     */
    private int eventQSize;

    /**
     * 异步执行器队列大小。
     */
    private int executorQAsyncSize;

    /**
     * 客户端执行器队列大小。
     */
    private int executorQClientSize;

    /**
     * 客户端查询执行器队列大小。
     */
    private int executorQClientQuerySize;

    /**
     * 客户端阻塞执行器队列大小。
     */
    private int executorQClientBlockingSize;

    /**
     * 查询执行器队列大小。
     */
    private int executorQQuerySize;

    /**
     * 定时任务执行器队列大小。
     */
    private int executorQScheduledSize;

    /**
     * I/O执行器队列大小。
     */
    private int executorQIoSize;

    /**
     * 系统执行器队列大小。
     */
    private int executorQSystemSize;

    /**
     * 普通操作执行器队列大小。
     */
    private int executorQOperationsSize;

    /**
     * 优先级操作执行器队列大小。
     */
    private int executorQPriorityOperationSize;

    /**
     * 已完成的操作数量。
     */
    private long operationsCompletedCount;

    /**
     * 地图加载任务执行器队列大小。
     */
    private int executorQMapLoadSize;

    /**
     * 加载所有键地图任务执行器队列大小。
     */
    private int executorQMapLoadAllKeysSize;

    /**
     * 集群任务执行器队列大小。
     */
    private int executorQClusterSize;

    /**
     * 响应任务执行器队列大小。
     */
    private int executorQResponseSize;

    /**
     * 正在运行的操作数量。
     */
    private int operationsRunningCount;

    /**
     * 待处理调用的百分比。
     */
    private double operationsPendingInvocationsPercentage;

    /**
     * 待处理调用的数量。
     */
    private int operationsPendingInvocationsCount;

    /**
     * 代理数量。
     */
    private int proxyCount;

    /**
     * 客户端端点数量。
     */
    private int clientEndpointCount;

    /**
     * 活跃连接数。
     */
    private int connectionActiveCount;

    /**
     * 客户端连接数。
     */
    private int clientConnectionCount;

    /**
     * 总连接数。
     */
    private int connectionCount;


}
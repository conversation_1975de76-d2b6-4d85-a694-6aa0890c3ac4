package com.datalink.fdop.fscm.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(schema = "zjdata", value = "f_d_provisional_single_out")
@ApiModel(value = "挑选数据临时存储表")
public class ProvisionalSingleOut {
  @ApiModelProperty(value = "工单号")
  private String workOrderNum;
  @ApiModelProperty(value = "工单行号")
  private Long workOrderRowNum;
  @ApiModelProperty(value = "工单子件项目号")
  private Long workOrderChildrenNum;
  @ApiModelProperty(value = "发料物料编码")
  private String materialCode;
  @ApiModelProperty(value = "发料物料描述")
  private String materialDesc;
  @ApiModelProperty(value = "发料工厂")
  private String factoryCode;
  @ApiModelProperty(value = "发料库存地点")
  private String stockPCode;
  @ApiModelProperty(value = "发料库存描述")
  @TableField(exist = false)
  private String stockPDescription;
  @ApiModelProperty(value = "发料批次号")
  private String batchNumber;
  @ApiModelProperty(value = "发料片号")
  private String piece;
  @ApiModelProperty(value = "发料BIN号")
  private String binNum;
  @ApiModelProperty(value = "发料数量")
  private Double quantityDelivery;
  @ApiModelProperty(value = "历史发料数量")
  private Double quantityDeliveryVersions;
  @ApiModelProperty(value = "基本单位")
  private String basicUnit;

  @ApiModelProperty("调拨单号")
  private String transferOrderNum;
  @ApiModelProperty("调拨单行号")
  private Long transferOrderRowNum;

  @ApiModelProperty(value = "领退料单号")
  private String pickReturnOrderNum;
  @ApiModelProperty(value = "领退料单行号")
  private Long pickReturnOrderRowNum;

  @ApiModelProperty("报废单号")
  private String scrapOrderNum;
  @ApiModelProperty("报废单行号")
  private Long scrapOrderRowNum;

  @ApiModelProperty("出货需求单号")
  private String shipRequestOrderNum;
  @ApiModelProperty("出货需求单行号")
  private String shipRequestOrderRowNum;

  @ApiModelProperty(value = "用户名")
  private String userName;

  @ApiModelProperty(value = "盘盈亏行号")
  private Long voucherRowNum;
  @Override
  public String toString() {
    return "{" +
            "workOrderNum='" + workOrderNum + '\'' +
            ", workOrderRowNum=" + workOrderRowNum +
            ", workOrderChildrenNum=" + workOrderChildrenNum +
            ", materialCode='" + materialCode + '\'' +
            ", materialDesc='" + materialDesc + '\'' +
            ", factoryCode='" + factoryCode + '\'' +
            ", stockPCode='" + stockPCode + '\'' +
            ", stockPDescription='" + stockPDescription + '\'' +
            ", batchNumber='" + batchNumber + '\'' +
            ", piece='" + piece + '\'' +
            ", binNum='" + binNum + '\'' +
            ", quantityDelivery=" + quantityDelivery +
            ", quantityDeliveryVersions=" + quantityDeliveryVersions +
            ", basicUnit='" + basicUnit + '\'' +
            '}';
  }
}

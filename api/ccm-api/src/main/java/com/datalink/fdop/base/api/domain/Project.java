package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022-10-27 11:31
 */
@Data
@TableName(schema = "zjdata", value = "ccm_project")
@ApiModel
public class Project {

    @Excel(name = "项目号码", sort = 1)
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "项目号码")
    @NotBlank(message = "项目号码不能为空")
    private String projectId;

    @Excel(name = "项目号码描述", sort = 2)
    @ApiModelProperty(value = "项目号码描述")
    private String projectDesc;

    @Excel(name = "项目号码文本", sort = 3)
    @ApiModelProperty(value = "项目号码文本")
    private String projectText;

    @Excel(name = "工厂代码", sort = 4)
    @ApiModelProperty(value = "工厂代码")
    private String plantId;

    @TableField(exist = false)
    @ApiModelProperty(value = "工厂代码")
    private String plantDesc;

    @Excel(name = "公司代码", sort = 6)
    @ApiModelProperty(value = "公司代码", required = true)
    private String companyId;

    @TableField(exist = false)
    @ApiModelProperty(value = "公司代码")
    private String companyDesc;

    @Excel(name = "管理范围", sort = 7)
    @ApiModelProperty(value = "管理范围", required = true)
    private String controlAreaId;

    @TableField(exist = false)
    @ApiModelProperty(value = "管理范围描述")
    private String controlAreaDesc;

    @Excel(name = "上层组码", sort = 11)
    @ApiModelProperty(value = "上层组码")
    private String menuId;

    @TableField(exist = false)
    @ApiModelProperty(value = "上层组码")
    private String menuDesc;

    @Excel(name = "是否启用", sort = 13, readConverterExp = "true=启用,false=不启用")
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "创建人")
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更改人")
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除")
    private Boolean deleteFlag;

}

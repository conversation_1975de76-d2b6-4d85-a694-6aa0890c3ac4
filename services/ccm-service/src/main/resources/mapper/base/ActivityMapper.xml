<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.ActivityMapper">

    <delete id="deleteBykey">
        delete from zjdata.ccm_activity
        where control_area_id = #{controlAreaId} AND
              activity_id = #{id} AND date_to = '${date}'
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_activity
        WHERE control_area_id = #{controlAreaId} AND activity_id = #{id}
          AND date_to = '${date}' limit 1
    </select>

    <delete id="deleteById" parameterType="String">
        delete from zjdata.ccm_activity
        where control_area_id = #{controlAreaId} AND activity_id = #{id}
          AND date_to = '${date}'
    </delete>


    <select id="list" parameterType="com.datalink.fdop.base.api.domain.Activity" resultType="com.datalink.fdop.base.api.domain.Activity">
        SELECT a.*, b.control_area_desc as control_area_desc,c.cost_element_desc as cost_element_desc
        FROM zjdata.ccm_activity a
        left join zjdata.ccm_control_area b on a.control_area_id = b.control_area_id
        left join zjdata.ccm_cost_element c on a.cost_element_id = c.cost_element_id
        <where>
            <if test="activity.activityId != null and activity.activityId != ''">
                AND a.activity_id = #{activity.activityId}
            </if>
            <if test="activity.dateFrom != null">
                AND a.date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(Activity.dateFrom)}'
            </if>
        </where>
        ORDER BY a.control_area_id, a.activity_id, a.date_to desc
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.Activity">
        SELECT a.*, b.control_area_desc as control_area_desc,c.cost_element_desc as cost_element_desc
        FROM zjdata.ccm_activity a
        left join zjdata.ccm_cost_element b on a.cost_element_id = b.cost_element_id
        left join zjdata.ccm_control_area c on a.control_area_id = c.control_area_id
        WHERE a.control_area_id = #{controlAreaId}
        and a.activity_id = #{id} AND a.date_to = '${date}'
        limit 1
    </select>

    <select id="selectIdsByPid" resultType="String">
        select activity_id from zjdata.ccm_activity
        where menu_id = #{pid} and del_flag = false
    </select>

    <update id="bacthUpdatePidById">
        update zjdata.ccm_activity
        set menu_id = (select p_menu_id from zjdata.ccm_activity_menu where menu_id = #{menuId} limit 1)
        where menu_id = #{menuId} and del_flag = false
    </update>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.Activity">
        update zjdata.ccm_activity
        set 
            <if test="dateFrom != null">
                date_from = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateFrom)}',
            </if>
            <if test="activityDesc != null">activity_desc = #{activityDesc},</if>
            <if test="activityText != null">activity_text = #{activityText},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="costElementId != null">cost_element_id = #{costElementId},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId}
          and activity_id = #{activityId}
          and date_to = '${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(dateTo)}'
    </update>

    <select id="getAll" resultType="com.datalink.fdop.base.api.domain.Activity">
        SELECT a.*, b.control_area_desc as control_area_desc,c.cost_element_desc as cost_element_desc
        FROM zjdata.ccm_activity a
        left join zjdata.ccm_control_area b on a.control_area_id = b.control_area_id
        left join zjdata.ccm_cost_element c on a.cost_element_id = c.cost_element_id
        ORDER BY a.control_area_id, a.activity_id, a.date_to desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.Activity">
        SELECT a.*, b.control_area_desc as control_area_desc,c.cost_element_desc as cost_element_desc
        FROM zjdata.ccm_activity a
        left join zjdata.ccm_control_area b on a.control_area_id = b.control_area_id
        left join zjdata.ccm_cost_element c on a.cost_element_id = c.cost_element_id
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
        </where>
        ORDER BY a.control_area_id, a.activity_id, a.date_to ${sort}
    </select>
    <select id="selectControlAreaId" resultType="com.datalink.fdop.base.api.domain.ControlArea">
        select * from zjdata.ccm_control_area
        where control_area_id = #{controlAreaId} limit 1
    </select>

    <select id="listAll" resultType="String">
        select activity_id from zjdata.ccm_activity
        where enable = true
        <if test="activityId !=null and activityId !=''">
            and activity_id = #{activityId}
        </if>
    </select>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_activity
            SET enable = #{item.enable}
            where control_area_id = #{item.controlAreaId}
            and activity_id = #{item.activityId}
            and date_to = '${item.dateTo}'
        </foreach>
    </update>

    <select id="selectActivity" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        SELECT
            a.activity_id as key,
            a.activity_desc as value
        FROM zjdata.ccm_activity a
        WHERE a.control_area_id = #{controlAreaId} AND a.enable = true AND a.date_to >= '${@com.datalink.fdop.common.core.utils.DateUtils@getDate()}'
        ORDER BY a.control_area_id, a.activity_id, a.date_to desc
    </select>

</mapper>


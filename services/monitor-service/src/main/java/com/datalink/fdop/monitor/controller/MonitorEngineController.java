package com.datalink.fdop.monitor.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.monitor.api.domain.MonitorEngine;
import com.datalink.fdop.monitor.service.MonitorEngineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-09-06 11:27
 */
@RestController
@RequestMapping(value = "/monitor/monitor")
@Api(tags = "监控引擎API")
public class MonitorEngineController extends BaseController {
    
    @Autowired
    private MonitorEngineService monitorEngineService;

    @ApiOperation("创建监控引擎")
    @Log(title = "监控引擎", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody MonitorEngine monitorEngine) {
        return R.toResult(monitorEngineService.create(monitorEngine));
    }

    @ApiOperation("修改监控引擎")
    @Log(title = "监控引擎", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@Validated @RequestBody MonitorEngine monitorEngine) {
        return R.toResult(monitorEngineService.update(monitorEngine));
    }

    @ApiOperation("复制监控引擎")
    @Log(title = "监控引擎", businessType = BusinessType.INSERT)
    @PostMapping(value = "/copy/{pid}")
    public R copy(@PathVariable("pid") Long pid, @Validated @RequestBody List<MonitorEngine> monitorEngineList) {
        return R.toResult(monitorEngineService.copy(monitorEngineList.stream().map(monitorEngine -> {
            monitorEngine.setPid(pid);
            return monitorEngine;
        }).collect(Collectors.toList())));

    }

    @ApiOperation("删除监控引擎")
    @Log(title = "监控引擎", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_MONITOR_ENGINE_THAT_NEEDS_TO_BE_REMOVED);
        }
        return R.toResult(monitorEngineService.delete(ids));
    }

    @ApiOperation("查询监控引擎")
    @Log(title = "监控引擎")
    @PostMapping(value = "/list")
    public R<PageDataInfo> list(@RequestBody(required = false) MonitorEngine monitorEngine) {
        return R.ok(monitorEngineService.list(monitorEngine));
    }

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pid", value = "监控引擎菜单id", required = true, dataType = "Long", paramType = "query", defaultValue = "-1"),
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "监控引擎")
    @PostMapping(value = "/overview")
    public R<PageDataInfo> overview(
            @RequestParam(value = "pid", defaultValue = "-1") Long pid,
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(monitorEngineService.overview(pid, sort, searchVo));
    }

    @ApiOperation("根据id查询监控引擎")
    @Log(title = "监控引擎")
    @GetMapping(value = "/selectById/{id}")
    public R<MonitorEngine> selectById(@PathVariable(value = "id") Long id) {
        return R.ok(monitorEngineService.selectById(id));
    }

    @ApiOperation("根据code查询监控引擎")
    @Log(title = "监控引擎")
    @GetMapping(value = "/selectByCode")
    public R<MonitorEngine> selectByCode(@RequestParam(value = "code") String code) {
        return R.ok(monitorEngineService.selectByCode(code));
    }


}

package com.datalink.fdop.quality.api;

import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.quality.api.domain.DataQualityLog;
import com.datalink.fdop.quality.api.factory.RemoteQualityLogFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "remoteQualityLogService", path = "/quality/log", value = ServiceNameConstants.DATA_SERVICE,
        fallbackFactory = RemoteQualityLogFallbackFactory.class)
public interface RemoteQualityLogService {

    @PostMapping(value = "/create")
    public R<DataQualityLog> create(@RequestBody DataQualityLog dataQualityLog);

}

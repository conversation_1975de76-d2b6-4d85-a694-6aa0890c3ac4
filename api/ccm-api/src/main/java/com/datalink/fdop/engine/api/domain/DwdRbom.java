package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.datalink.fdop.common.core.annotation.SearchField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dwd", value = "dwd_rbom")
@ApiModel("工艺管理")
@PermissionColumn(module = "ccms")
public class DwdRbom {

    @ApiModelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空")
    @Excel(name = "工厂代码")
    @SearchField(value = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "Recipe ID")
    @NotBlank(message = "Recipe ID不能为空")
    @Excel(name = "Recipe ID")
    private String recipeId;

    @ApiModelProperty(value = "Recipe 描述")
    @Excel(name = "Recipe 描述")
    private String recipeDesc;

    @ApiModelProperty(value = "Recipe 文本")
    @Excel(name = "Recipe 文本")
    private String recipeText;

    @ApiModelProperty(value = "基础数量")
    @Excel(name = "基础数量")
    private String baseQty;

    @ApiModelProperty(value = "基础单位")
    @Excel(name = "基础单位")
    private String baseUnit;

    @ApiModelProperty(value = "组内层级")
    @NotBlank(message = "组内层级不能为空")
    @Excel(name = "组内层级")
    private String hierarchy;

    @ApiModelProperty(value = "层内序号")
    @NotBlank(message = "层内序号不能为空")
    @Excel(name = "层内序号")
    private String hierarchyInnerSeq;

    @ApiModelProperty(value = "原料编码")
    @NotBlank(message = "原料编码不能为空")
    @Excel(name = "原料编码")
    private String rawMaterialId;

    @ApiModelProperty(value = "原料描述")
    @Excel(name = "原料描述")
    private String rawMaterialDesc;

    @ApiModelProperty(value = "用量[使用单位]")
    @Excel(name = "用量[使用单位]")
    private String usageQty;

    @ApiModelProperty(value = "使用单位")
    @Excel(name = "使用单位")
    private String usageUnit;

    @ApiModelProperty(value = "用量[基本单位]")
    @Excel(name = "用量[基本单位]")
    private String baseQtySn;

    @ApiModelProperty(value = "基本单位")
    @Excel(name = "基本单位")
    private String baseUnitSn;

    @ApiModelProperty(value = "损耗 (%)")
    @Excel(name = "损耗 (%)")
    private String scrapRate;

    @ApiModelProperty(value = "[主]物料类型")
    @Excel(name = "[主]物料类型")
    private String materialType;

    @ApiModelProperty(value = "[主]物料类型描述")
    @Excel(name = "[主]物料类型描述")
    private String materialTypeDesc;

    @ApiModelProperty(value = "[主]物料组")
    @Excel(name = "[主]物料组")
    private String materialGroup;

    @ApiModelProperty(value = "[主]物料组描述")
    @Excel(name = "[主]物料组描述")
    private String materialGroupDesc;

    @ApiModelProperty(value = "[主]采购类型")
    @Excel(name = "[主]采购类型")
    private String purType01;

    @ApiModelProperty(value = "[主]特殊采购")
    @Excel(name = "[主]特殊采购")
    private String purType02;

    @ApiModelProperty(value = "[子]物料类型")
    @Excel(name = "[子]物料类型")
    private String materialTypeSn;

    @ApiModelProperty(value = "[子]物料类型描述")
    @Excel(name = "[子]物料类型描述")
    private String materialTypeSnDesc;

    @ApiModelProperty(value = "[子]物料组")
    @Excel(name = "[子]物料组")
    private String materialGroupSn;

    @ApiModelProperty(value = "[子]物料组描述")
    @Excel(name = "[子]物料组描述")
    private String materialGroupSnDesc;

    @ApiModelProperty(value = "[子]采购类型")
    @Excel(name = "[子]采购类型")
    private String purType01Sn;

    @ApiModelProperty(value = "[子]特殊采购")
    @Excel(name = "[子]特殊采购")
    private String purType02Sn;

    @ApiModelProperty(value = "条目类别")
    @Excel(name = "条目类别")
    private String classType;

    @ApiModelProperty(value = "项目编号")
    @Excel(name = "项目编号")
    private String item;

    @ApiModelProperty(value = "导入时间")
    @Excel(name = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;












}

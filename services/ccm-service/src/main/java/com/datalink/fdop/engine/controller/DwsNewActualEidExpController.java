package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwsNewActualEidExp;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwsNewActualEidExpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwsNewActualEidExp")
@Api(tags = "逻辑视图-设备编码部分")
public class DwsNewActualEidExpController extends BaseController {

    @Autowired
    private DwsNewActualEidExpService dwsNewActualEidExpService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "设备ID费用实际")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params , "verId");
        String plantId = MapUtils.getAsString(params , "plantId");
        String yearMonth = MapUtils.getAsString(params , "yearMonth");
        PageDataInfo<DwsNewActualEidExp> overview = dwsNewActualEidExpService.overview(verId, plantId, yearMonth, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwsNewActualEidExp.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "设备ID费用实际", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params , "verId");
        String plantId = MapUtils.getAsString(params , "plantId");
        String yearMonth = MapUtils.getAsString(params , "yearMonth");
        List<DwsNewActualEidExp> list = dwsNewActualEidExpService.selectNoPage(verId, plantId, yearMonth, sort, queryParam.getSearchVo());
        ExcelUtil<DwsNewActualEidExp> util = new ExcelUtil<>(DwsNewActualEidExp.class);
        util.exportExcel(response, list, "逻辑视图-设备编码数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsNewActualEidExp> util = new ExcelUtil<>(DwsNewActualEidExp.class);
        util.importTemplateExcel(response, "逻辑视图-设备编码模板");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dws.dws_new_actual_eid_exp", null));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId() {
        return R.ok(dataDictionaryService.listCompanyId("dws.dws_new_actual_eid_exp", null));
    }

    @ApiOperation(value = "查询eid和pid的工厂代码")
    @GetMapping("/selectEidAndPidPlantId")
    public R<List<String>> selectEidAndPidPlantId() {
        return R.ok(dwsNewActualEidExpService.selectEidAndPidPlantId());
    }

}
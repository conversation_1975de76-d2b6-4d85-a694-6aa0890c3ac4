package com.datalink.fdop.datart.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.datart.domain.View;
import com.datalink.fdop.datart.domain.ViewMenu;
import com.datalink.fdop.datart.domain.ViewTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ViewMenuMapper   {
    VlabelItem<ViewMenu> selectByCode(@Param("code") String code);

    int insertViewMenu(@Param("viewMenu") ViewMenu viewMenu);

    void createViewMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long>  ids);

    VlabelItem<ViewMenu> selectById(@Param("id") Long id);

    Boolean checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    int updateById(@Param("viewMenu") ViewMenu viewMenu);

    void deleteViewMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    List<Long> selectIdsByPid(@Param("pid") Long pid);

    int bacthUpdatePidById(@Param("menuIdList") List<Long> menuIdList, @Param("pid") Long pid);

    void createViewAndMenuEdge(@Param("pid") Long pid, @Param("elementIdList") List<Long> elementIdList);

    int deleteBatchIds(@Param("ids") List<Long> ids);


    List<ViewTree> selectMenuTree(@Param("sort")String sort,@Param("code") String code);

    IPage<View> overview(@Param("page") Page<View> page, @Param("pid") Long pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);
}

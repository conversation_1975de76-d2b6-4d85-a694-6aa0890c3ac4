package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@TableName(schema = "zjdata", value = "ccm_cost_structure_group_menu")
@ApiModel("fccm组件分组菜单")
public class StructureGroupMenu {

    @ApiModelProperty(value = "菜单ID")
    @TableId(type = IdType.INPUT)
    @NotBlank(message = "菜单ID不能为空")
    private String menuId;

    @ApiModelProperty(value = "菜单父id")
    @NotBlank(message = "请选择一个菜单")
    private String pMenuId;

    @ApiModelProperty(value = "菜单名称")
    private String name;

    @ApiModelProperty(value = "菜单描述")
    private String description;
}

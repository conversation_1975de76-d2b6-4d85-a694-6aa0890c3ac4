package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationCostA;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwsNewAllocationCostAMapper extends BaseMapper<DwsNewAllocationCostA> {

    /**
     * 查询定向分摊
     */
    IPage<DwsNewAllocationCostA> selectTargetedNewAllocation(
            @Param("controlAreaId") String controlAreaId,
            @Param("verId") String verId,
            @Param("companyId") String companyId,
            @Param("yearMonthFrom") String yearMonthFrom,
            @Param("yearMonthTo") String yearMonthTo,
            @Param("allocationType") String allocationType,
            @Param("page") IPage<DwsNewAllocationCostA> page,
            @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    /**
     * 查询费用分摊
     */
    IPage<DwsNewAllocationCostA> selectCostNewAllocation(
            @Param("controlAreaId") String controlAreaId,
            @Param("verId") String verId,
            @Param("companyId") String companyId,
            @Param("yearMonthFrom") String yearMonthFrom,
            @Param("yearMonthTo") String yearMonthTo,
            @Param("allocationType") String allocationType,
            @Param("page") IPage<DwsNewAllocationCostA> page,
            @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwsNewAllocationCostA> selectNoPage(@Param("controlAreaId") String controlAreaId,
                                             @Param("verId") String verId,
                                             @Param("companyId") String companyId,
                                             @Param("yearMonthFrom") String yearMonthFrom,
                                             @Param("yearMonthTo") String yearMonthTo,
                                             @Param("allocationType") String allocationType,
                                             @Param("sort") String sort,
                                             @Param("searchVo") SearchVo searchVo);
}
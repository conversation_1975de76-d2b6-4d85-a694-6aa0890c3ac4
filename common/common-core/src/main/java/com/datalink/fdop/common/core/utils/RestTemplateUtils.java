package com.datalink.fdop.common.core.utils;


import com.datalink.fdop.common.core.constant.Constants;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/5/24 16:15
 */
public class RestTemplateUtils {

    private static RestTemplate restTemplate;

    // 取消ssl认证
    static {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        TrustStrategy acceptingTrustStrategy = (x509Certificates, authType) -> true;
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (KeyStoreException e) {
            e.printStackTrace();
        }
        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());
        CloseableHttpClient httpClient = HttpClients.custom().setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).setSSLSocketFactory(csf).build();
        requestFactory.setHttpClient(httpClient);
        requestFactory.setConnectTimeout(15000);
        requestFactory.setReadTimeout(5000);
        restTemplate = new RestTemplate(requestFactory);
    }

    /**
     * @param url     url
     * @param method  请求类型
     * @param headers 请求头
     * @param param   请求体参数
     * @param tClass  返回类型
     * @return
     */
    public static <T> T execute(String url, String method, Map<String, Object> headers, Object param, Class<T> tClass) {
        HttpMethod httpMethod = HttpMethod.GET;
        switch (method) {
            case "GET":
                httpMethod = HttpMethod.GET;
                break;
            case "POST":
                httpMethod = HttpMethod.POST;
                break;
            case "PUT":
                httpMethod = HttpMethod.PUT;
                break;
            case "DELETE":
                httpMethod = HttpMethod.DELETE;
                break;
            case "HEAD":
                httpMethod = HttpMethod.HEAD;
                break;
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        if (MapUtils.isNotEmpty(headers)) {
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue().toString());
            }
        }
        httpHeaders.add(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(ObjectUtils.isEmpty(param) ? null : param, httpHeaders);
        ResponseEntity<T> response = restTemplate.exchange(url, httpMethod, requestEntity, tClass);
        return (T) response.getBody();
    }

    /**
     * @param url    请求url
     * @param tClass 返回参数类型
     * @param <T>
     * @return
     */
    public static <T> T doGet(String url, Class<T> tClass) {
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(null, null), tClass);
        return (T) response.getBody();
    }

    /**
     * @param url    请求url
     * @param tClass 返回参数类型
     * @param <T>
     * @return
     */
    public static <T> T doGet(String url, Map<String, Object> headers, Class<T> tClass) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if (MapUtils.isNotEmpty(headers)) {
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue().toString());
            }
        }
        httpHeaders.add(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(null, httpHeaders);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, tClass);
        return (T) response.getBody();
    }

    /**
     * @param url     url
     * @param headers 请求头
     * @param param   请求体参数
     * @param tClass  返回类型
     * @param <T>
     * @return
     */
    public static <T> T doPost(String url, Map<String, Object> headers, Object param, Class<T> tClass) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if (MapUtils.isNotEmpty(headers)) {
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue().toString());
            }
        }
        httpHeaders.add(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(ObjectUtils.isEmpty(param) ? null : param, httpHeaders);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, tClass);
        return (T) response.getBody();
    }


    /**
     * @param url    url
     * @param param  请求体参数
     * @param tClass 返回类型
     * @param <T>
     * @return
     */
    public static <T> T doPost(String url, Object param, Class<T> tClass) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(ObjectUtils.isEmpty(param) ? null : param, httpHeaders);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, tClass);
        return (T) response.getBody();
    }

    /**
     * @param url    url
     * @param param  请求体参数
     * @param tClass 返回类型
     * @param <T>
     * @return
     */
    public static <T> T doPut(String url, Object param, Class<T> tClass) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(ObjectUtils.isEmpty(param) ? null : param, httpHeaders);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.PUT, requestEntity, tClass);
        return (T) response.getBody();
    }

    /**
     * @param url    url
     * @param tClass 返回类型
     * @param <T>
     * @return
     */
    public static <T> T doDelete(String url, Class<T> tClass) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(null, httpHeaders);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, tClass);
        return (T) response.getBody();
    }

    /**
     * @param url    url
     * @param param  请求体参数
     * @param tClass 返回类型
     * @param <T>
     * @return
     */
    public static <T> T doDelete(String url, Object param, Class<T> tClass) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(ObjectUtils.isEmpty(param) ? null : param, httpHeaders);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, tClass);
        return (T) response.getBody();
    }

    /**
     * @param url     url
     * @param headers 请求头
     * @param param   请求体参数
     * @param tClass  返回类型
     * @param <T>
     * @return
     */
    public static <T> T doDelete(String url, Map<String, Object> headers, Object param, Class<T> tClass) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if (MapUtils.isNotEmpty(headers)) {
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue().toString());
            }
        }
        httpHeaders.add(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(ObjectUtils.isEmpty(param) ? null : param, httpHeaders);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, tClass);
        return (T) response.getBody();
    }

    /**
     * @param url     url
     * @param headers 请求头
     * @param tClass  返回类型
     * @param <T>
     * @return
     */
    public static <T> T doDelete(String url, Map<String, Object> headers, Class<T> tClass) {

        HttpHeaders httpHeaders = new HttpHeaders();
        if (MapUtils.isNotEmpty(headers)) {
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue().toString());
            }
        }
        httpHeaders.add(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(null, httpHeaders);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, tClass);
        return (T) response.getBody();
    }

    /**
     * 拼接url
     *
     * @param url url
     * @param map param参数
     * @return
     */
    public static String getUrlParamsByMap(String url, Map<String, Object> map) {
        if (map == null) {
            return "";
        }
        for (String key : map.keySet()) {
            if (url.indexOf("?") != -1) {
                url += "&" + key + "=" + map.get(key);
            } else {
                url += "?" + key + "=" + map.get(key);
            }
        }
        return url;
    }


}


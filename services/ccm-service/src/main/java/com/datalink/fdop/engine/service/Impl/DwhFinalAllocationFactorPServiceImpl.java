package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalAllocationFactorP;
import com.datalink.fdop.engine.mapper.DwhFinalAllocationFactorPMapper;
import com.datalink.fdop.engine.service.DwhFinalAllocationFactorPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalAllocationFactorPServiceImpl extends ServiceImpl<DwhFinalAllocationFactorPMapper, DwhFinalAllocationFactorP> implements DwhFinalAllocationFactorPService {

    @Autowired
    private DwhFinalAllocationFactorPMapper dwhFinalAllocationFactorPMapper;

    @Override
    public PageDataInfo<DwhFinalAllocationFactorP> overview(
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String costCenterId,
            String factorId,
            String allocationType,
            String sort,
            SearchVo searchVo) {
        Page<DwhFinalAllocationFactorP> page = PageUtils.getPage(DwhFinalAllocationFactorP.class);
        IPage<DwhFinalAllocationFactorP> iPage = dwhFinalAllocationFactorPMapper.selectAll(
                controlAreaId, verId, yearMonthFrom, yearMonthTo, costCenterType, costCenterId, factorId, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalAllocationFactorP> selectNoPage(String controlAreaId, String verId, String yearMonthFrom, String yearMonthTo, String costCenterType, String costCenterId, String factorId, String allocationType, SearchVo searchVo) {
        return dwhFinalAllocationFactorPMapper.selectNoPage(controlAreaId, verId, yearMonthFrom, yearMonthTo,
                costCenterType, costCenterId, factorId, allocationType, searchVo);
    }
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CostCenterGroupHeadMapper">

    <delete id="batchDeleteByKey">
        delete from zjdata.ccm_cost_center_group_head
        where (control_area_id, cost_center_group_id) in
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.costCenterGroupId})
        </foreach>
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM zjdata.ccm_cost_center_group_head where control_area_id = #{controlAreaId}  and cost_center_group_id = #{costCenterGroupId}
    </select>

    <select id="selectById" parameterType="String" resultType="com.datalink.fdop.base.api.domain.CostCenterGroupHead">
        SELECT * FROM zjdata.ccm_cost_center_group_head where control_area_id = #{controlAreaId}  and cost_center_group_id = #{costCenterGroupId}
    </select>

    <sql id="selectListSql">
        SELECT
            node.*, area.control_area_desc as control_area_desc
        FROM zjdata.ccm_cost_center_group_head node
            left join zjdata.ccm_control_area area on node.control_area_id = area.control_area_id
        <where>
            <if test="costCenterGroupId != null and costCenterGroupId != '' and costCenterGroupId != '-1'">
                AND node.control_area_id = #{controlAreaId}  AND node.cost_center_group_id = #{costCenterGroupId}
            </if>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY cost_center_group_id ${sort}
    </sql>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.CostCenterGroupHead">
        <include refid="selectListSql"/>
    </select>

    <select id="selectListNoPage" resultType="com.datalink.fdop.base.api.domain.CostCenterGroupHead">
        <include refid="selectListSql"/>
    </select>

    <select id="selectIdsByPid" resultType="com.datalink.fdop.base.api.domain.CostCenterGroupHead">
        select control_area_id, cost_center_group_id from zjdata.ccm_cost_center_group_head where control_area_id = #{controlAreaId} and pid = #{pid}
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CostCenterGroupHead">
        update zjdata.ccm_cost_center_group_head
        set
            <!--<if test="controlAreaId != null and controlAreaId != ''">control_area_id = #{controlAreaId},</if>
            <if test="costCenterGroupId != null and costCenterGroupId != ''">cost_center_group_id = #{costCenterGroupId},</if>-->
            <if test="costGroupDesc != null">cost_group_desc = #{costGroupDesc},</if>
            <if test="costGroupText != null">cost_group_text = #{costGroupText},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id =#{controlAreaId} and cost_center_group_id = #{costCenterGroupId}
    </update>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_cost_center_group_head
        SET enable = #{enable}
        where (control_area_id, cost_center_group_id) in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            (#{item.controlAreaId}, #{item.costCenterGroupId})
        </foreach>
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.CostCenterGroupHeadTree">
        select
            cost_center_group_id as id,
            control_area_id as control_area_id,
            pid as pid,
            'MENU' as menuType,
            cost_group_desc as name,
            cost_group_text as description
        from
            zjdata.ccm_cost_center_group_head
        <where>
            <if test="controlAreaId != null and controlAreaId != ''">
                and control_area_id = #{controlAreaId}
            </if>
            <if test="code != null and code != ''">
                and cost_center_group_id = #{code}
            </if>
        </where>
        order by cost_center_group_id ${sort}
    </select>

</mapper>


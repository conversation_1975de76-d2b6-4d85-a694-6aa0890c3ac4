package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdPurchasePrice;
import com.datalink.fdop.engine.mapper.DwdPurchasePriceMapper;
import com.datalink.fdop.engine.service.DwdPurchasePriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdPurchasePriceServiceImpl extends ServiceImpl<DwdPurchasePriceMapper, DwdPurchasePrice> implements DwdPurchasePriceService {

    @Autowired
    private DwdPurchasePriceMapper dwdPurchasePriceMapper;

    @Override
    public PageDataInfo<DwdPurchasePrice> overview(String materialId, String plantId, String sort, SearchVo searchVo) {
        Page<DwdPurchasePrice> page = PageUtils.getPage(DwdPurchasePrice.class);
        IPage<DwdPurchasePrice> iPage = dwdPurchasePriceMapper.selectAll(materialId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdPurchasePrice> selectNoPage(String materialId, String plantId, String sort, SearchVo searchVo) {
        return dwdPurchasePriceMapper.selectNoPage(materialId, plantId, sort, searchVo);
    }
}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdTraceWip;

import java.util.List;

public interface DwdTraceWipService extends IService<DwdTraceWip> {

    PageDataInfo<DwdTraceWip> overview(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo);

    List<DwdTraceWip> selectNoPage(String plantId,
                                   String dateFrom,
                                   String dateTo,
                                   String sort,
                                   SearchVo searchVo);
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CostElementMenuMapper">

    <update id="bacthUpdatePidById" parameterType="String">
        update zjdata.ccm_cost_element_menu
        set p_menu_id = (select p_menu_id from zjdata.ccm_cost_element_menu where menu_id = #{menuId} limit 1)
        where p_menu_id = #{menuId} and del_flag = false
    </update>

    <update id="deleteByIds" parameterType="String">
        update zjdata.ccm_cost_element_menu
        set del_flag = true
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag = false
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.CostElementTree">
        select null as control_area_id, null as cost_element_id, a.*, 'MENU' as menu_type from zjdata.ccm_cost_element_menu a
        where a.del_flag != true
        UNION
        select b.control_area_id as control_area_id, b.cost_element_id as cost_element_id, null as menu_id, b.menu_id as p_menu_id, null as menu_desc, null as menu_text,
               b.del_flag as del_flag, 'NODE' as menu_type from zjdata.ccm_cost_element b
        where b.del_flag != true
        order by menu_id ${sort}
    </select>

    <select id="selectByPids" resultType="String">
        select * from zjdata.ccm_cost_element
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag != true
        order by control_area_id, cost_element_id desc
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.CostElementMenu">
        select * from zjdata.ccm_cost_element_menu
        where menu_id = #{id} and del_flag != true limit 1
    </select>

</mapper>
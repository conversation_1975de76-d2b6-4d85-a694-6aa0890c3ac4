package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewVarcostEnt;

import java.util.List;

public interface DwsNewVarcostEntService extends IService<DwsNewVarcostEnt> {

    PageDataInfo<DwsNewVarcostEnt> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwsNewVarcostEnt> listAll();

    List<DwsNewVarcostEnt> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);
}
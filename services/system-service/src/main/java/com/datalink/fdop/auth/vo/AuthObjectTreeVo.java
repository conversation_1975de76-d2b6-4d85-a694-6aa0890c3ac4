package com.datalink.fdop.auth.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.datalink.fdop.common.core.enums.MenuType;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2022/4/21 15:27
 */
@Data
public class AuthObjectTreeVo implements Serializable {
    private static final long serialVersionUID = -2099116559000779600L;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;
    private MenuType menuType;
    private String name;
    private String code;
    private String description;
    private List<AuthObjectTreeVo> children = new ArrayList<AuthObjectTreeVo>();

}

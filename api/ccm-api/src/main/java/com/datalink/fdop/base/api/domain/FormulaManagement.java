package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-03-13 11:34
 */
@Data
@TableName(schema = "zjdata", value = "ccm_formula")
@ApiModel
public class FormulaManagement {

    @ApiModelProperty(value = "管理范围")
    @NotBlank(message = "管理范围不能为空")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @TableField(exist = false)
    @ApiModelProperty(value = "管理范围描述")
    private String controlAreaDesc;

    @NotBlank(message = "公式码不能为空")
    @ApiModelProperty(value = "公式码")
    @Excel(name = "公式码")
    private String formulaId;

    @ApiModelProperty(value = "公式")
    @Excel(name = "公式")
    private String formula;

    @ApiModelProperty(value = "公式描述")
    @Excel(name = "公式描述")
    private String formulaText;

    @ApiModelProperty(value = "公式文本")
    @Excel(name = "公式文本")
    private String formulaDesc;

    @ApiModelProperty(value = "是否启用")
    @Excel(name = "是否启用", readConverterExp = "true=启用,false=不启用")
    private Boolean enable;

    @ApiModelProperty(value = "创建人")
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更改人")
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;

}

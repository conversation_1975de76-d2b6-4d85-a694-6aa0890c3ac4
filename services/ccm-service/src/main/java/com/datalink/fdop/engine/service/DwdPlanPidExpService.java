package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwdPlanPidExp;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdPlanPidExpService extends IService<DwdPlanPidExp> {

    PageDataInfo<DwdPlanPidExp> overview(
            String verId, String plantId, String yearMonthFrom, String yearMonthTo,
            String sort, SearchVo searchVo);

    List<DwdPlanPidExp> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo);

}
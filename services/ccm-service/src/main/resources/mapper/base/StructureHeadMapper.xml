<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.StructureHeadMapper">

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_cost_structure_head
        WHERE structure_ver_id = #{structureVerId} and date_to = '${dateTo}' and cost_structure_id =#{costStructureId}  limit 1
    </select>

    <select id="listAll" resultType="java.lang.String">
        select distinct cost_structure_id from zjdata.ccm_cost_structure_head
        where enable = true
            <if test="costStructureId !=null and costStructureId !=''">
                and cost_structure_id =#{costStructureId}
            </if>
    </select>

    <delete id="deleteByIds" parameterType="String">
        delete from zjdata.ccm_cost_structure_head
        WHERE (structure_ver_id,date_to,cost_structure_id) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.structureVerId},'${item.dateTo}',#{item.costStructureId})
        </foreach>
    </delete>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_cost_structure_head
        SET enable = #{enable}
        WHERE (structure_ver_id,date_to,cost_structure_id) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.structureVerId},'${item.dateTo}',#{item.costStructureId})
        </foreach>
    </update>

    <update id="update">
        UPDATE zjdata.ccm_cost_structure_head
        set
        <if test="costStructureDesc != null ">cost_structure_desc =#{costStructureDesc},</if>
        <if test="costStructureText != null ">cost_structure_text=#{costStructureText},</if>
        <if test="costStructureAttribute != null ">cost_structure_attribute =#{costStructureAttribute},</if>
        <if test="costStructureType != null ">cost_structure_type =#{costStructureType},</if>
        <if test="costStructureElementId != null ">cost_structure_element_id =#{costStructureElementId},</if>
        <if test="activityId != null ">activity_id =#{activityId},</if>
        <if test="costLevel != null">cost_level =#{costLevel},</if>
        <if test="costStructureGroup != null ">cost_structure_group =#{costStructureGroup},</if>
        <if test="osStructureVerId != null ">os_structure_ver_id =#{osStructureVerId},</if>
        <if test="osCostStructureId != null ">os_cost_structure_id =#{osCostStructureId},</if>
        <if test="osCostStructureDesc != null ">os_cost_structure_desc =#{osCostStructureDesc},</if>
        <if test="osCostStructureElementId != null ">os_cost_structure_element_id =#{osCostStructureElementId},</if>
        <if test="dateFrom != null">date_from ='${dateFrom}',</if>
        <if test="enable != null">enable =#{enable},</if>
        update_by =#{updateBy},update_time=#{updateTime}
        WHERE structure_ver_id = #{structureVerId} and date_to = '${dateTo}' and cost_structure_id =#{costStructureId}
    </update>

    <select id="pageList" resultType="com.datalink.fdop.base.api.domain.StructureHead">
        select a.*,b.structure_ver_desc as structure_ver_desc from zjdata.ccm_cost_structure_head  a
        left join zjdata.ccm_structure_ver b on a.structure_ver_id = b.structure_ver_id
        <where>
            <if test="searchVo !=null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("a",searchVo)})
            </if>
        </where>
        order by a.structure_ver_id,a.date_to,a.cost_structure_id
        <if test="sort != null and sort != ''">
            ${sort}
        </if>
    </select>

</mapper>


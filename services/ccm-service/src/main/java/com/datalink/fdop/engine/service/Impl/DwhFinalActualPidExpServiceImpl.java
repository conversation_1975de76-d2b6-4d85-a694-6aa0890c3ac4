package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalActualPidExp;
import com.datalink.fdop.engine.mapper.DwhFinalActualPidExpMapper;
import com.datalink.fdop.engine.service.DwhFinalActualPidExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalActualPidExpServiceImpl extends ServiceImpl<DwhFinalActualPidExpMapper, DwhFinalActualPidExp> implements DwhFinalActualPidExpService {

    @Autowired
    private DwhFinalActualPidExpMapper dwhFinalActualPidExpMapper;

    @Override
    public PageDataInfo<DwhFinalActualPidExp> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwhFinalActualPidExp> page = PageUtils.getPage(DwhFinalActualPidExp.class);
        IPage<DwhFinalActualPidExp> iPage = dwhFinalActualPidExpMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalActualPidExp> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwhFinalActualPidExpMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
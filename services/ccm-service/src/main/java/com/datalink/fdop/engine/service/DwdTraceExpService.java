package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdTraceExp;

import java.util.List;

public interface DwdTraceExpService extends IService<DwdTraceExp> {

    PageDataInfo<DwdTraceExp> overview(String yearMonth, String controlAreaId, String companyId, String sort, SearchVo searchVo);

    List<DwdTraceExp> selectNoPage(String yearMonth,
                                   String controlAreaId,
                                   String companyId,
                                   String sort,
                                   SearchVo searchVo);
}
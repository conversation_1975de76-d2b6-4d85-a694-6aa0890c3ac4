<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwhFinalPbomMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwhFinalPbom">
        SELECT node.*,
        `default` as defaultValue
        FROM dwh.dwh_final_pbom node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="productId != null and productId != ''">
                and node.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.ver_id,node.plant_id, node.factory_id,
        node.product_id, node.product_cim_id,node.raw_material_id
        ${sort}
    </select>

    <select id="listAll" resultType="com.datalink.fdop.engine.api.domain.DwhFinalPbom">
        SELECT node.*,
               `default` as defaultValue
        FROM dwh.dwh_final_pbom node
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwhFinalPbom">
        SELECT node.*,
        `default` as defaultValue
        FROM dwh.dwh_final_pbom node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="productId != null and productId != ''">
                and node.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.ver_id,node.plant_id, node.factory_id,
        node.product_id, node.product_cim_id,node.raw_material_id
        ${sort}
    </select>

</mapper>


package com.datalink.fdop.graph.api.dto;

import lombok.Data;

/**
 * flink 任务基础参数
 */
@Data
public class TaskParam {


    /**
     * 是否缓存
     */
    private Boolean isCache;

    /**
     * 缓存数
     */
    private Integer cacheNumber;

    /**
     * slot
     */
    private Integer slot;

    /**
     * parallelism
     */
    private Integer parallelism;

    /**
     * jobManager的总进程内存大小
     */
    private Integer jobManagerMemory;

    /**
     * taskManager的总进程内存大小
     */
    private Integer taskManagerMemory;

    /**
     * taskManager数量
     */
    private Integer taskManagerNumber;

    // jobManager
    // JobManager的堆外内存大小
    private Integer jobManagerOffHeap;
    // JobManager的JVM元空间大小
    private Integer jobManagerMetaspace;
    // JobManager的最小JVM开销大小
    private Integer jobManagerOverheadMin;
    // JobManager的最大JVM开销大小
    private Integer jobManagerOverheadMax;
    // JobManager的JVM开销系数
    private Double jobManagerOverheadFraction;

    // taskManager
    // taskManager的任务堆内存大小
    private Integer taskManagerTaskHeap;
    // taskManager的托管内存大小
    private Integer taskManagerManaged;
    // taskManager的JVM元空间大小
    private Integer taskManagerMetaspace;
    // taskManager的最小JVM开销大小
    private Integer taskManagerOverheadMin;
    // taskManager的最大JVM开销大小
    private Integer taskManagerOverheadMax;
    // taskManager的JVM开销系数
    private Double taskManagerOverheadFraction;
    // taskManager的最小网络内存大小
    private Integer taskManagerNetworkMin;
    // taskManager的最大网络内存大小
    private Integer taskManagerNetworkMax;
    // taskManager的网络内存系数
    private Double taskManagerNetworkFraction;

    public TaskParam() {
        this.slot = 4;
        this.parallelism = 1;
        this.jobManagerMemory = 1600;
        this.taskManagerMemory = 1728;
    }

}

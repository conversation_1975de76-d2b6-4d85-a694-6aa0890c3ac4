package com.datalink.fdop.seatunnel.api.domain;

import com.datalink.fdop.common.core.domain.BaseDataModel;
import com.datalink.fdop.common.core.enums.DataModelType;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 数据模型选项（数据源、实体、SQL）
 */
@Data
public class DataModelOptions extends BaseDataModel {

    /**
     * 数据源连接信息
     */
    private String dataSourceBasicInfo;

    /**
     * 源表的Where条件
     */
    private String whereSql;

    /**
     * 数据模型列集合信息
     */
    private List<SeaTunnelField> seaTunnelFields;

    /**
     * 内置列
     */
    private List<SeaTunnelField> builtInFields;

    /**
     * 是否开启 upsert
     */
    private boolean isUpsert;

    /**
     * 源数据库类型
     */
    private String sourceDbType;

    /**
     * 目标数据库类型
     */
    private String sinkDbType;

    /**
     * iceberg表写入属性扩展
     * iceberg.table.write-props = {
     *       write.format.default="parquet"
     *       write.target-file-size-bytes=536870912
     *     }
     */
    private Map<String,Object> icebergTableWriteProps;

    /**
     * 自定义参数Map方便后续扩展
     */
    private Map<String,Object> extraParams;

    /**
     * SeaTunnel 版本号
     */
    private String version;

    /**
     * 数据源所属租户ID
     */
    private Long tenantId;

    public DataModelOptions() {
    }

    public DataModelOptions(Long id, DataModelType modelType) {
        super(id,modelType);
    }

    public DataModelOptions(Long id, DataModelType modelType, boolean upsert) {
        super(id, modelType);
        this.isUpsert = upsert;
    }

    public DataModelOptions(Long id, DataModelType modelType, String dataSourceBasicInfo) {
        super(id, modelType);
        this.dataSourceBasicInfo = dataSourceBasicInfo;
    }

    public DataModelOptions(Long id, DataModelType modelType, Boolean isView, String databaseName, String tableName, String dataSourceBasicInfo) {
        super(id, modelType, isView, databaseName, tableName);
        this.dataSourceBasicInfo = dataSourceBasicInfo;
    }

    public DataModelOptions(Long id, DataModelType modelType, Boolean isView, String databaseName, String tableName, String whereSql, List<SeaTunnelField> seaTunnelFields) {
        super(id, modelType, isView, databaseName, tableName);
        this.whereSql = whereSql;
        this.seaTunnelFields = seaTunnelFields;
    }

    public DataModelOptions(Long id, DataModelType modelType, Boolean isView, String databaseName, String dataSourceBasicInfo, String tableName, String whereSql, List<SeaTunnelField> seaTunnelFields, List<SeaTunnelField> builtInFields) {
        super(id, modelType, isView, databaseName, tableName);
        this.dataSourceBasicInfo = dataSourceBasicInfo;
        this.whereSql = whereSql;
        this.seaTunnelFields = seaTunnelFields;
        this.builtInFields = builtInFields;
    }

    public boolean getIsUpsert() {
        return isUpsert;
    }

    public void setIsUpsert(boolean upsert) {
        isUpsert = upsert;
    }
}

package com.datalink.fdop.seatunnel.api.enums;

/**
 * SeaTunnel支持的转换类型
 */
public enum SeaTunnelCastType {

    STRING("STRING"),
    INT("INT"),
    BIGINT("BIGINT"),
    BYTE("BYTE"),
    FLOAT("FLOAT"),
    DOUBLE("DOUBLE"),
    DECIMAL("DECIMAL"),
    TIMESTAMP("TIMESTAMP"),
    DATE("DATE"),
    TIME("TIME"),
    RAWTOHEX("RAWTOHEX"),
    ;

    private final String code;

    SeaTunnelCastType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewPbom;

import java.util.List;

public interface DwsNewPbomService extends IService<DwsNewPbom> {

    PageDataInfo<DwsNewPbom> overview(String verId,String  plantId,String productId,String sort, SearchVo searchVo);

    List<DwsNewPbom> listAll();

    List<DwsNewPbom> selectNoPage(String verId,
                                  String plantId,
                                  String productId,
                                  String sort,
                                  SearchVo searchVo);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.quality.mapper.DataQualityMapper">

    <select id="createQualityAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_menu), (node:d_q_data_quality)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_q_data_quality_menu',endTable:'d_q_data_quality'}]->(node)-[:node_menu_edge
        {startTable:'d_q_data_quality',endTable:'d_q_data_quality_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertQuality" parameterType="com.datalink.fdop.quality.api.domain.DataQuality" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_q_data_quality ${@com.datalink.fdop.quality.utils.DomainAgeUtils@getQualityAgeStr(dataQuality)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.quality.api.domain.DataQuality" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality)
        WHERE node.id = ${id}
        <set>
            <if test="pid != null">node.pid = ${pid},</if>
            <if test="code != null">node.code = '${code}',</if>
            <if test="name != null">node.name = '${name}',</if>
            <if test="description != null">node.description = '${description}',</if>
            <if test="dataQualityType != null">node.dataQualityType = '${dataQualityType}',</if>
            <if test="isCiteStandard != null">node.isCiteStandard = ${isCiteStandard},</if>
            <if test="isCiteStandard == null or isCiteStandard == false">
                <if test="isConfigType != null">node.isConfigType = ${isConfigType},</if>
            </if>
            <if test="isCiteStandard == null or isCiteStandard == false">
                <if test="isConfigType != null and isConfigType == true">
                    node.fieldType = '${fieldType}',
                </if>
            </if>
            <if test="isCiteStandard == null or isCiteStandard == false">
                <if test="isConfigType != null and isConfigType == true">
                    node.length = ${length},
                </if>
            </if>
            <if test="isCiteStandard == null or isCiteStandard == false">
                <if test="isConfigType != null and isConfigType == true">
                    node.decimalLength = ${decimalLength},
                </if>
            </if>
            <if test="regexRule != null">node.regexRule = ${regexRule.ageStr},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        <if test="isCiteStandard == null or isCiteStandard == false">
            remove node.standardId
        </if>
        <if test="isConfigType == null or isConfigType == false">
            remove node.fieldType,node.length,node.decimalLength
        </if>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteQualityAndStandardEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality) -[edge:d_q_data_quality_standard_edge]-(standard:d_g_data_standard)
            WHERE quality.id = ${qualityId}
            DETACH DELETE edge
            RETURN id(edge), properties(edge)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createQualityAndStandardEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality), (standard:d_g_data_standard)
                                   WHERE quality.id = ${qualityId} AND standard.id = ${standardId}
                                   CREATE (quality)-[edge:d_q_data_quality_standard_edge
                                   {startTable:'d_q_data_quality', endTable:'d_g_data_standard'}]->(standard)
        RETURN id(edge), properties(edge)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectStandardId" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality) -[edge:d_q_data_quality_standard_edge]->(standard:d_g_data_standard)
            WHERE quality.id = ${qualityId}
            RETURN standard.id $$) as (id BIGINT)
    </select>

    <select id="bacthUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteQualityAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.quality.api.domain.DataQuality"
            resultMap="dataQuality">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality)
        <where>
            <if test="dataQuality.id != null">AND node.id = ${dataQuality.id}</if>
            <if test="dataQuality.code != null and dataQuality.code != ''">AND node.code =~'.*${dataQuality.code}.*'
            </if>
            <if test="dataQuality.name != null and dataQuality.name != ''">AND node.name =~'.*${dataQuality.name}.*'
            </if>
            <if test="dataQuality.description != null and dataQuality.description != ''">AND node.description
                =~'.*${dataQuality.description}.*'
            </if>
            <if test="dataQuality.dataQualityType != null">AND node.dataQualityType =
                '${dataQuality.dataQualityType}',
            </if>
            <if test="dataQuality.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",dataQuality.searchCondition)})
            </if>
        </where>
        WITH node
        ORDER BY node.createTime DESC
        RETURN node.id, node.pid, node.code, node.name, node.description, node.dataQualityType, node.isCiteStandard,
        node.standardId, node.isConfigType, node.fieldType, node.length, node.decimalLength,
        node.regexRule
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, dataQualityType TEXT, isCiteStandard
        BOOLEAN,
        standardId BIGINT, isConfigType BOOLEAN, fieldType TEXT, length BIGINT, decimalLength BIGINT,
        regexRule VARCHAR)
    </select>

    <resultMap id="dataQuality" type="com.datalink.fdop.quality.api.domain.DataQuality">
        <id property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="dataQualityType" column="dataQualityType"/>
        <result property="isCiteStandard" column="isCiteStandard"/>
        <result property="standardId" column="standardId"/>
        <result property="isConfigType" column="isConfigType"/>
        <result property="fieldType" column="fieldType"/>
        <result property="length" column="length"/>
        <result property="decimalLength" column="decimalLength"/>
        <result property="regexRule" column="regexRule"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="selectById" resultMap="dataQuality">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_q_data_quality) WHERE node.id = ${id}
            RETURN node.id, node.pid, node.code, node.name, node.description, node.dataQualityType, node.isCiteStandard,
                               node.standardId, node.isConfigType, node.fieldType, node.length, node.decimalLength,
                               node.regexRule
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, dataQualityType TEXT, isCiteStandard BOOLEAN,
                               standardId BIGINT, isConfigType BOOLEAN, fieldType TEXT, length BIGINT, decimalLength BIGINT,
                               regexRule VARCHAR)
    </select>

    <select id="selectByCode" resultMap="dataQuality">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_q_data_quality) WHERE node.code = '${code}'
             RETURN node.id, node.pid, node.code, node.name, node.description, node.dataQualityType,
                               node.isCiteStandard,
                               node.standardId, node.isConfigType, node.fieldType, node.length, node.decimalLength,
                               node.regexRule
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, dataQualityType TEXT, isCiteStandard BOOLEAN,
                               standardId BIGINT, isConfigType BOOLEAN, fieldType TEXT, length BIGINT, decimalLength BIGINT,
                               regexRule VARCHAR)
    </select>

    <select id="checkCodeIsExists" resultMap="dataQuality">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_q_data_quality) WHERE node.code = '${code}' AND node.id &lt;&gt; ${id}
            RETURN node.id, node.pid, node.code, node.name, node.description, node.dataQualityType, node.isCiteStandard,
                               node.standardId, node.isConfigType, node.fieldType, node.length, node.decimalLength,
                               node.regexRule
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, dataQualityType TEXT, isCiteStandard BOOLEAN,
                               standardId BIGINT, isConfigType BOOLEAN, fieldType TEXT, length BIGINT, decimalLength BIGINT,
                               regexRule VARCHAR)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_q_data_quality_menu) -[nme:node_menu_edge]->(nodeE:d_q_data_quality)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="selectQualityTree" resultType="com.datalink.fdop.quality.api.domain.DataQualityTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality)
        <where>
            <if test="code != null and code != ''">node.code =~'.*${code}.*'</if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, 'NODE' as menuType, node.dataQualityType
        ORDER BY node.code ${sort}
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT,dataQualityType TEXT)
    </select>

    <select id="overview" resultType="com.datalink.fdop.quality.api.domain.DataQuality">
        WITH overview as (
        <if test="pid == -1">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_q_data_quality)
            WHERE node.pid = -1
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description,'顶级菜单' as
            menuName,node.createTime,node.updateTime,node.dataQualityType
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime
            TEXT,updateTime TEXT,dataQualityType TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality) -[:node_menu_edge]->(menu:d_q_data_quality_menu)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as
        menuName,node.createTime,node.updateTime,node.dataQualityType
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime TEXT,updateTime
        TEXT,dataQualityType TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,code ${sort}
    </select>

    <select id="selectQualityElement" resultType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo">
        select distinct *
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (quality:d_q_data_quality) -[edge:d_q_data_quality_element_edge]->(element:d_e_data_element)
        <where>
            <if test="id != null">AND quality.id = ${id}</if>
            <if test="dataElementIdList != null and dataElementIdList.size() != 0">
                AND element.id IN
                <foreach collection="dataElementIdList" item="dataElementId" open="[" separator="," close="]">
                    ${dataElementId}
                </foreach>
            </if>
            <if test="code != null and code != ''">AND element.code =~'.*${code}.*'</if>
            <if test="name != null and name != ''">AND element.name =~'.*${name}.*'</if>
            <if test="isTree != null">AND edge.isTree = ${isTree}</if>
        </where>
        RETURN element.id, element.dataElementType, element.pid, element.code, element.name, element.description,
        element.dataElementType,
        edge.isTree,
        edge.parentDataElementId,
        element.fieldType,
        element.length,
        element.decimalLength, element.isPk, edge.seq
        ORDER BY element.code ASC
        $$) as (id BIGINT,menuType TEXT,pid BIGINT,code TEXT,name TEXT,description TEXT,dataElementType TEXT,isTree
        BOOLEAN,parentDataElementId BIGINT,
        fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2)
    </select>

    <select id="selectQualityFieldElement" resultType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo">
        select distinct *
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (quality:d_q_data_quality) -[edge:d_q_data_quality_element_edge]->(element:d_e_data_element)
        WHERE edge.isTree = true AND element.dataElementType = 'FIELD'
        <if test="dataElementIdList != null and dataElementIdList.size() != 0">
            AND element.id IN
            <foreach collection="dataElementIdList" item="dataElementId" open="[" separator="," close="]">
                ${dataElementId}
            </foreach>
        </if>
        RETURN element.id, element.dataElementType, element.pid, element.code, element.name, element.description,
        element.dataElementType,
        edge.isTree,
        edge.parentDataElementId,
        element.fieldType,
        element.length,
        element.decimalLength, element.isPk, edge.seq
        ORDER BY element.code ASC
        $$) as (id BIGINT,menuType TEXT,pid BIGINT,code TEXT,name TEXT,description TEXT,dataElementType TEXT,isTree
        BOOLEAN,parentDataElementId BIGINT,
        fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2)
    </select>

    <select id="checkQualityElementEdge" resultType="boolean">
        WITH flag as (
        select count(*) as total
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (quality:d_q_data_quality) -[edge:d_q_data_quality_element_edge]->(element:d_e_data_element)
        WHERE quality.id = ${id} AND element.id = ${dataElementId}
        <if test="parentDataElementId != null">
            AND edge.parentDataElementId = ${parentDataElementId}
        </if>
        AND edge.isTree = ${isTree}
        RETURN quality.id
        $$) as (id BIGINT)
        )
        select case when total = 0 then true else false end
        from flag
    </select>

    <select id="insertTreeQualityElementEdge"
            parameterType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality) WHERE quality.id = ${id}
            WITH quality
            MATCH (element:d_e_data_element) WHERE element.id = ${dataElementStructureVo.id}
            WITH quality, element CREATE (quality)-[edgeS:d_q_data_quality_element_edge {
        startTable:'d_q_data_quality', endTable:'d_e_data_element', isTree: true }]->(element)-[edgeE:d_q_data_quality_element_edge{
        startTable:'d_e_data_element', endTable:'d_q_data_quality',isTree: true
        }]->(quality)
        RETURN id(edgeS), properties(edgeS)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertQualityElementEdge" parameterType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (quality:d_q_data_quality) WHERE quality.id = ${id}
        WITH quality
        MATCH (element:d_e_data_element) WHERE element.id = ${dataElementStructureVo.id}
        WITH quality, element CREATE (quality)-[edgeS:d_q_data_quality_element_edge {
        startTable:'d_q_data_quality', endTable:'d_e_data_element', seq:${dataElementStructureVo.seq},isTree: false
        <if test="dataElementStructureVo.parentDataElementId != null">
            ,parentDataElementId:${dataElementStructureVo.parentDataElementId}
        </if>
        }]->(element)-[edgeE:d_q_data_quality_element_edge{
        startTable:'d_e_data_element', endTable:'d_q_data_quality',
        seq:${dataElementStructureVo.seq},isTree: false
        <if test="dataElementStructureVo.parentDataElementId != null">
            ,parentDataElementId:${dataElementStructureVo.parentDataElementId}
        </if>
        }]->(quality)
        RETURN id(edgeS), properties(edgeS)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectQualityAllMaxSeq" resultType="Integer">
        WITH seq as (select *
                     from ag_catalog.cypher('zjdata_graph', $$
                         MATCH (quality:d_q_data_quality) -[edge:d_q_data_quality_element_edge]->(element:d_e_data_element)
                         WHERE quality.id = ${id} AND element.isPk = ${isPk}
                         RETURN MAX (edge.seq)
                         $$) as (seq INT2)
            )
        select case when seq is null then 0 else seq end
        from seq
    </select>

    <select id="deleteQualityElementEdge" parameterType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (quality:d_q_data_quality) -[edge:d_q_data_quality_element_edge]-(element:d_e_data_element)
        WHERE quality.id = ${id} AND element.id = ${dataElementStructureVo.id}
        <if test="dataElementStructureVo.parentDataElementId != null">
            AND edge.parentDataElementId = ${dataElementStructureVo.parentDataElementId}
        </if>
        AND edge.isTree = ${dataElementStructureVo.isTree}
        DETACH DELETE edge
        RETURN id(quality), properties(quality)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectQualityByElementId" resultType="com.datalink.fdop.quality.api.model.dto.DataQualityDto">
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (element:d_e_data_element) -[edge:d_q_data_quality_element_edge]->(quality:d_q_data_quality)
            WHERE element.id = ${dataElementId} AND edge.isTree = false
            RETURN DISTINCT (quality.id), quality.code, quality.name, quality.description, true as status, 'AUTO' as citeType
                                   $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,status BOOLEAN,citeType TEXT)
        UNION
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (element:d_e_data_element) -[edge:d_q_data_quality_element_edge]->(quality:d_q_data_quality)
            WHERE element.id = ${dataElementId} AND edge.isTree = true and element.dataElementType = 'FIELD'
            RETURN DISTINCT (quality.id), quality.code, quality.name, quality.description, true as status, 'AUTO' as citeType
                                   $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,status BOOLEAN,citeType TEXT)
    </select>

</mapper> 
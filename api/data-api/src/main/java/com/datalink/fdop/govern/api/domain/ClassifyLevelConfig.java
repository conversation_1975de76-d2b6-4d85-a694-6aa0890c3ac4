package com.datalink.fdop.govern.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.web.domain.BaseDomain;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据安全分类分级配置
 */
@Data
@TableName(value = "classify_level_config")
public class ClassifyLevelConfig extends BaseDomain {

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "分组")
    @Excel(name = "分组")
    private String groupName;

    @ApiModelProperty(value = "数据源")
    @Excel(name = "数据源")
    private String dataSource;

    @ApiModelProperty(value = "Schema")
    @Excel(name = "Schema")
    private String schemaName;

    @ApiModelProperty(value = "表名")
    @Excel(name = "表名")
    private String tableName;

    @ApiModelProperty(value = "表注释")
    @Excel(name = "表注释")
    private String tableComment;

    @ApiModelProperty(value = "数据量")
    @Excel(name = "数据量")
    private Integer dataVolume;

    @ApiModelProperty(value = "字段名")
    @Excel(name = "字段名")
    private String fieldName;

    @ApiModelProperty(value = "字段业务类型")
    @Excel(name = "字段业务类型")
    private String fieldBusinessType;

    @ApiModelProperty(value = "分类")
    @Excel(name = "分类")
    private String category;

    @ApiModelProperty(value = "分级")
    @Excel(name = "分级")
    private String level;

    @ApiModelProperty(value = "是否敏感")
    @Excel(name = "是否敏感")
    private String isSensitive;

    @ApiModelProperty(value = "列注释")
    @Excel(name = "列注释")
    private String columnComment;

    @ApiModelProperty(value = "技术类型")
    @Excel(name = "技术类型")
    private String technicalType;

    @ApiModelProperty(value = "是否主键")
    @Excel(name = "是否主键")
    private String isPrimaryKey;

    @ApiModelProperty(value = "列长度")
    @Excel(name = "列长度")
    private Integer columnLength;

    @ApiModelProperty(value = "样本数据")
    @Excel(name = "样本数据")
    private String sampleData;
}

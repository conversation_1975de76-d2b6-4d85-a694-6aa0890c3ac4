package com.datalink.fdop.element.api;

import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityTableVo;
import com.datalink.fdop.element.api.factory.RemoteEntityFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(contextId = "remoteEntityService", path = "/element/entity",
        value = ServiceNameConstants.DATA_SERVICE, fallbackFactory = RemoteEntityFallbackFactory.class)
public interface RemoteEntityService {

    @GetMapping(value = "/selectById/{id}")
    public R<DataEntity> selectById(@PathVariable(value = "id") Long id);


    @GetMapping(value = "/selectByCode")
    public R<DataEntity> selectByCode(@RequestParam(value = "code") String code);

    @PostMapping(value = "/selectDataEntityTableVoByTenantIdAndIds/{tenantId}")
    public R<List<DataEntityTableVo>> selectDataEntityTableVoByTenantIdAndIds(@PathVariable(value = "tenantId") Long tenantId, @RequestBody List<Long> dataEntityIds);

}

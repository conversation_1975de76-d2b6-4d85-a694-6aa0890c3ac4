package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalPlanExp;
import com.datalink.fdop.engine.mapper.DwhFinalPlanExpMapper;
import com.datalink.fdop.engine.service.DwhFinalPlanExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalPlanExpServiceImpl extends ServiceImpl<DwhFinalPlanExpMapper, DwhFinalPlanExp> implements DwhFinalPlanExpService {

    @Autowired
    private DwhFinalPlanExpMapper dwhFinalPlanExpMapper;

    @Override
    public PageDataInfo<DwhFinalPlanExp> overview(
            String verId, String companyId, String yearMonthFrom,
            String yearMonthTo, String sort, SearchVo searchVo) {
        Page<DwhFinalPlanExp> page = PageUtils.getPage(DwhFinalPlanExp.class);
        IPage<DwhFinalPlanExp> iPage = dwhFinalPlanExpMapper.selectAll(verId, companyId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalPlanExp> selectNoPage(String verId, String companyId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwhFinalPlanExpMapper.selectNoPage(verId, companyId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }

}
package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdPlanEidExp;
import com.datalink.fdop.engine.mapper.DwdPlanEidExpMapper;
import com.datalink.fdop.engine.service.DwdPlanEidExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdPlanEidExpServiceImpl extends ServiceImpl<DwdPlanEidExpMapper, DwdPlanEidExp> implements DwdPlanEidExpService {

    @Autowired
    private DwdPlanEidExpMapper dwdPlanEidExpMapper;

    @Override
    public PageDataInfo<DwdPlanEidExp> overview(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        Page<DwdPlanEidExp> page = PageUtils.getPage(DwdPlanEidExp.class);
        IPage<DwdPlanEidExp> iPage = dwdPlanEidExpMapper.selectAll(verId, plantId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdPlanEidExp> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwdPlanEidExpMapper.selectNoPage(verId, plantId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
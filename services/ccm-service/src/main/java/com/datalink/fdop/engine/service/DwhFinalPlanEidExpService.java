package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalPlanEidExp;

import java.util.List;

public interface DwhFinalPlanEidExpService extends IService<DwhFinalPlanEidExp> {

    PageDataInfo<DwhFinalPlanEidExp> overview(
            String verId, String plantId, String yearMonthFrom,
            String yearMonthTo, String sort, SearchVo searchVo);

    List<DwhFinalPlanEidExp> selectNoPage(String verId,
                                          String plantId,
                                          String yearMonthFrom,
                                          String yearMonthTo,
                                          String sort,
                                          SearchVo searchVo);
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewActualPidExp;

import java.util.List;

public interface DwsNewActualPidExpService extends IService<DwsNewActualPidExp> {

    PageDataInfo<DwsNewActualPidExp> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwsNewActualPidExp> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);
}
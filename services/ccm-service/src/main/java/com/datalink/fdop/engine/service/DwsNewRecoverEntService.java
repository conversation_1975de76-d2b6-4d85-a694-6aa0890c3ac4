package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRecoverEnt;

import java.util.List;

public interface DwsNewRecoverEntService extends IService<DwsNewRecoverEnt> {

    PageDataInfo<DwsNewRecoverEnt> overview(String verId,String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwsNewRecoverEnt> listAll();

    List<DwsNewRecoverEnt> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);
}
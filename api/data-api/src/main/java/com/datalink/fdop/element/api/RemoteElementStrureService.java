package com.datalink.fdop.element.api;

import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.factory.RemoteElementStrureFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/5 10:51
 */
@FeignClient(contextId = "remoteElementStrureService", path = "/element/structure/",
        value = ServiceNameConstants.DATA_SERVICE,
        fallbackFactory = RemoteElementStrureFallbackFactory.class)
public interface RemoteElementStrureService {


    @GetMapping(value = "/{dataElementId}/selectStructureByDataElementId")
    public R<List<DataElementStructure>> list(@PathVariable(value = "dataElementId") Long dataElementId);

    @GetMapping(value = "/{dataElementId}/getIterateStructure")
    public R<List<DataElementStructure>> getIterateStructure(@PathVariable(value = "dataElementId") Long dataElementId);
}

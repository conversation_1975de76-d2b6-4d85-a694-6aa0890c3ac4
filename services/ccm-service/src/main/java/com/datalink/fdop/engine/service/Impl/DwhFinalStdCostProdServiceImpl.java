package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostProd;
import com.datalink.fdop.engine.mapper.DwhFinalStdCostProdMapper;
import com.datalink.fdop.engine.service.DwhFinalStdCostProdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalStdCostProdServiceImpl extends ServiceImpl<DwhFinalStdCostProdMapper, DwhFinalStdCostProd> implements DwhFinalStdCostProdService {

    @Autowired
    private DwhFinalStdCostProdMapper dwhFinalStdCostProdMapper;

    @Override
    public PageDataInfo<DwhFinalStdCostProd> overview(
            String plantId, String verId, String controlAreaId, String productId, Integer batchQty, String baseYearMonth,
            String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        Page<DwhFinalStdCostProd> page = PageUtils.getPage(DwhFinalStdCostProd.class);
        IPage<DwhFinalStdCostProd> iPage = dwhFinalStdCostProdMapper.selectAll(plantId, verId, controlAreaId, productId, batchQty, baseYearMonth, dateFrom, dateTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalStdCostProd> listAll() {
        return dwhFinalStdCostProdMapper.listAll();
    }

    @Override
    public List<DwhFinalStdCostProd> selectNoPage(String plantId, String verId, String controlAreaId, String productId, Integer batchQty, String baseYearMonth, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        return dwhFinalStdCostProdMapper.selectNoPage(plantId, verId, controlAreaId, productId, batchQty,
                baseYearMonth, dateFrom, dateTo, sort, searchVo);
    }
}
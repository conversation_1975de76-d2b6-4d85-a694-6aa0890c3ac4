package com.datalink.fdop.element.api;

import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import com.datalink.fdop.element.api.domain.DataEntityTableVo;
import com.datalink.fdop.element.api.factory.RemoteEntityTableFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(contextId = "remoteEntityTableService", path = "/element/entity/table",
        value = ServiceNameConstants.DATA_SERVICE, fallbackFactory = RemoteEntityTableFallbackFactory.class)
public interface RemoteEntityTableService {

    @GetMapping(value = "/{dataEntityId}/selectById")
    public R<DataEntityTable> selectById(@PathVariable(value = "dataEntityId") Long dataEntityId);


    @GetMapping(value = "/{dataEntityId}/selectTableMapping")
    public R<List<DataEntityTableMapping>> selectTableMapping(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestParam(value = "tableId") Long tableId);

    @GetMapping(value = "/{dataEntityId}/selectEntityTableMapping")
    public R<List<DataEntityTableMapping>> selectEntityTableMapping(@PathVariable(value = "dataEntityId") Long dataEntityId);


    @GetMapping(value = "/{dataEntityId}/selectByIdAndTenantId/{tenantId}")
    public R<DataEntityTable> selectByIdAndTenantId(@PathVariable(value = "dataEntityId") Long dataEntityId, @PathVariable(value = "tenantId") Long tenantId);

    @GetMapping(value = "/{dataEntityId}/selectTableMappingByTenantId/{tenantId}")
    public R<List<DataEntityTableMapping>> selectTableMappingByTenantId(@PathVariable(value = "dataEntityId") Long dataEntityId, @PathVariable(value = "tenantId") Long tenantId, @RequestParam(value = "tableId") Long tableId);

    @GetMapping(value = "/{dataEntityId}/selectEntityTableMappingByTenantId/{tenantId}")
    public R<List<DataEntityTableMapping>> selectEntityTableMappingByTenantId(@PathVariable(value = "dataEntityId") Long dataEntityId, @PathVariable(value = "tenantId") Long tenantId);

}

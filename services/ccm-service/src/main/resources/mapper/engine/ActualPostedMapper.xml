<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.ActualPostedMapper">

    <select id="getOrg" resultType="com.datalink.fdop.engine.api.domain.ActualPosted">
        select a.* from fccm.actual_posted a
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchCondition)})
        </if>
    </select>

    <select id="getEdit" resultType="com.datalink.fdop.engine.api.domain.ActualPosted">
        select a.* from fccm.actual_posted_edit a
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchCondition)})
        </if>
    </select>

    <select id="getAll" resultType="com.datalink.fdop.engine.api.domain.ActualPosted">
        select a.* from fccm.actual_posted a
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchCondition)})
        </if>
        UNION ALL
        select b.* from fccm.actual_posted_edit b
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("b",searchCondition)})
        </if>
    </select>

    <select id="add" parameterType="String">
        insert into fccm.actual_posted_edit values (${sql})
    </select>

    <select id="updateByKey">
        update fccm.actual_posted_edit
        set
        <if test="verId !=null and verId != ''">ver_id = '${verId}',</if>
        <if test="companyId !=null and companyId != ''">company_id = '${companyId}',</if>
        <if test="costElementId !=null and costElementId != ''">cost_element_id = '${costElementId}',</if>
        <if test="costElementDesc !=null and costElementDesc != ''">cost_element_desc = '${costElementDesc}',</if>
        <if test="costCenterId !=null and costCenterId != ''">cost_center_id = '${costCenterId}',</if>
        <if test="costCenterDesc !=null and costCenterDesc != ''">cost_center_desc = '${costCenterDesc}',</if>
        <if test="sourceType !=null and sourceType != ''">source_type = '${sourceType}',</if>
        <if test="wbsId !=null and wbsId != ''">wbs_id = '${wbsId}',</if>
        <if test="wbsDesc !=null and wbsDesc != ''">wbs_desc = '${wbsDesc}',</if>
        <if test="yearMonth !=null">year_month = ${yearMonth},</if>
        <if test="postingDate !=null">posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(postingDate)}','yyyy-mm-dd'),</if>
        <if test="currency !=null and currency != ''">currency = '${currency}',</if>
        <if test="amount !=null">amount = ${amount},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time = to_timestamp('${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}','yyyy-mm-dd hh24:mi:ss')
        <if test="syncStatus != null and syncStatus != ''">, sync_status = '${syncStatus}'</if>
        <if test="text != null and text != ''">, text = '${text}'</if>
        where ver_id = '${verId}' and company_id = '${companyId}' and cost_element_id = '${costElementId}'
        and cost_center_id = '${costCenterId}' and wbs_id = '${wbsId}'
        and posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(postingDate)}','yyyy-mm-dd')
        and currency = '${currency}'
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.engine.api.domain.ActualPosted">
        select * from fccm.actual_posted_edit
        where ver_id = '${verId}' and company_id = '${companyId}' and cost_element_id = '${costElementId}'
          and cost_center_id = '${costCenterId}' and wbs_id = '${wbsId}'
          and posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(postingDate)}','yyyy-mm-dd')
          and currency = '${currency}' limit 1
    </select>

    <delete id="deleteByKey">
        delete from fccm.actual_posted_edit
        where ver_id = '${verId}' and company_id = '${companyId}' and cost_element_id = '${costElementId}'
          and cost_center_id = '${costCenterId}' and wbs_id = '${wbsId}'
          and posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(postingDate)}','yyyy-mm-dd')
          and currency = '${currency}'
    </delete>

</mapper>


package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRateActivityA;
import com.datalink.fdop.engine.mapper.DwsNewRateActivityAMapper;
import com.datalink.fdop.engine.service.DwsNewRateActivityAService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRateActivityAServiceImpl extends ServiceImpl<DwsNewRateActivityAMapper, DwsNewRateActivityA> implements DwsNewRateActivityAService {

    @Autowired
    private DwsNewRateActivityAMapper dwsNewRateActivityAMapper;

    @Override
    public PageDataInfo<DwsNewRateActivityA> overview(
            String controlAreaId,
            String verId,
            String companyId,
            String costCenterType,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo) {
        Page<DwsNewRateActivityA> page = PageUtils.getPage(DwsNewRateActivityA.class);
        IPage<DwsNewRateActivityA> iPage = dwsNewRateActivityAMapper.selectAll(
                controlAreaId, verId,companyId, costCenterType,yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRateActivityA> selectNoPage(String controlAreaId, String verId, String companyId, String costCenterType, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwsNewRateActivityAMapper.selectNoPage(controlAreaId, verId, companyId, costCenterType, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
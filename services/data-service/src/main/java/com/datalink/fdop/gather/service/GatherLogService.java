package com.datalink.fdop.gather.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.gather.api.domain.GatherLog;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/7/7 9:16
 */
public interface GatherLogService {

    GatherLog insertLog(GatherLog gatherLog);

    int updateLog(GatherLog gatherLog);

    GatherLog selectById(Long logId);

    GatherLog selectById(Long tenantId, Long logId);

    PageDataInfo<GatherLog> list(Long templateId);

    PageDataInfo<Map<String, Object>> getDataByBatchId(Long templateId, String batchId, Integer pageNo, Integer pageSize);

    PageDataInfo<Map<String, Object>> getErrorLogDataByBatchId(Long templateId, String batchId, Integer pageNo, Integer pageSize);

     Boolean checkGatherLogStatus(Long templateId, String batchId);

}

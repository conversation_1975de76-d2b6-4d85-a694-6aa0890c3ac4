package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.ActualActivity;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:32
 */
public interface ActualActivityService extends IService<ActualActivity> {

    PageDataInfo<ActualActivity> getOrg(String sort, SearchVo searchVo);

    int saveData(ActualActivity actualActivity);

    PageDataInfo<ActualActivity> getEdit(String sort, SearchVo searchVo);

    PageDataInfo<ActualActivity> getAll(String sort, SearchVo searchVo);

    int deleteByList(List<ActualActivity> list);
}

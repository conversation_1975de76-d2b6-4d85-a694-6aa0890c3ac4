<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.FactoryMapper">

    <select id="checkFactoryIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_plant
        WHERE plant_id = #{id}
        limit 1
    </select>

    <delete id="deleteFactoryById" parameterType="String">
        delete
        from zjdata.ccm_plant
        where plant_id = #{id}
    </delete>

    <delete id="deleteFactoryByIds" parameterType="String">
        delete from zjdata.ccm_plant
        WHERE plant_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectFactoryList" resultType="com.datalink.fdop.base.api.domain.Factory">
        select factory.*, company.company_desc as company_desc, evaluateArea.evaluate_area_desc as
        evaluate_area_desc,b.control_area_desc as control_area_desc
        FROM zjdata.ccm_plant factory
        left join zjdata.ccm_company company on company.company_id = factory.company_id
        left join zjdata.ccm_evaluate_area evaluateArea on factory.evaluate_area_id = evaluateArea.evaluate_area_id
        left join zjdata.ccm_control_area b on factory.control_area_id = b.control_area_id

        <where>
            <if test="ft.plantId != null ">
                AND factory.plant_id = #{ft.plantId}
            </if>
            <if test="ft.plantDesc != null ">
                AND factory.plant_desc like '%${ft.plantDesc}%'
            </if>
            <if test="ft.plantText != null ">
                AND factory.plant_text like '%${ft.plantText}%'
            </if>
            <if test="ft.companyId != null ">
                AND factory.company_id like '%{ft.companyId}%'
            </if>
        </where>
        ORDER BY plant_id ${sort}
    </select>

    <update id="updateCompanyByDeleteCompany" parameterType="String">
        update zjdata.ccm_plant
        <set>
            company_id = null,
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        WHERE company_id = #{companyId}
    </update>

    <update id="updateCompanyByDeleteCompanys" parameterType="String">
        update zjdata.ccm_plant
        <set>
            company_id = null,
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        WHERE company_id IN
        <foreach collection="companyIds" item="companyId" open="(" separator="," close=")">
            #{companyId}
        </foreach>
    </update>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.Factory">
        select factory.*,
               company.company_desc            as company_desc,
               evaluateArea.evaluate_area_desc as evaluate_area_desc,
               b.control_area_desc             as control_area_desc
        FROM zjdata.ccm_plant factory
                 left join zjdata.ccm_company company on company.company_id = factory.company_id
                 left join zjdata.ccm_evaluate_area evaluateArea
                           on factory.evaluate_area_id = evaluateArea.evaluate_area_id
                 left join zjdata.ccm_control_area b on factory.control_area_id = b.control_area_id
        WHERE factory.plant_id = #{id}
        limit 1
    </select>

    <select id="query" resultType="com.datalink.fdop.base.api.domain.Factory">
        select a.*, company.company_desc as company_desc, evaluateArea.evaluate_area_desc as
        evaluate_area_desc,b.control_area_desc as control_area_desc
        FROM zjdata.ccm_plant a
        left join zjdata.ccm_company company on company.company_id = a.company_id
        left join zjdata.ccm_evaluate_area evaluateArea on a.evaluate_area_id = evaluateArea.evaluate_area_id
        left join zjdata.ccm_control_area b on a.control_area_id = b.control_area_id
        <where>
            <if test="searchVo != null and searchVo != ''">
                and ${searchVo}
            </if>
        </where>
        order by plant_id ${sort}
    </select>

    <select id="getControlAreaIdByPlantId" resultType="java.lang.String">
        SELECT control_area_id
        FROM zjdata.ccm_plant
        WHERE plant_id = #{plantId}
          and enable = true
    </select>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_plant
            SET enable = #{item.enable}
            WHERE plant_id = #{item.plantId}
        </foreach>
    </update>

    <select id="listPlantId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct node.plant_id as "key", dict.plant_desc as "value"
        from ${orgTableName} node
                 left join zjdata.ccm_plant dict
                           on node.plant_id = dict.plant_id
        where node.plant_id is not null and node.plant_id &lt;> ''
        order by node.plant_id asc
    </select>

</mapper>
package com.datalink.fdop.base.service.impl;


import com.alibaba.druid.pool.DruidDataSource;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.base.service.IHealthCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 应用健康检查Service
 */
@Service
@Slf4j
public class HealthCheckServiceImpl implements IHealthCheckService {


    @Autowired
    public RedisTemplate redisTemplate;

    @Autowired
    private DruidDataSource druidDataSource;

    private static final String VALIDATION_QUERY = "SELECT 1";

    @Override
    public boolean checkDBHealth() {
        boolean dbHealth = true;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            // 加载数据库驱动
            Class.forName(druidDataSource.getDriverClassName());
            conn = DriverManager.getConnection(druidDataSource.getUrl(), druidDataSource.getUsername(), druidDataSource.getPassword());
            stmt = conn.createStatement();
            rs = stmt.executeQuery(VALIDATION_QUERY);
            // 验证查询结果（此处简化处理，实际应用可能需要更详细的错误检查）
            if (!rs.next() || rs.getInt(1) != 1) {
                dbHealth = false;
            }
        } catch (Exception e){
           dbHealth = false;
        } finally {
            // 关闭资源
            try {
                if (rs != null) {
                    rs.close();
                }
                if (stmt != null) {
                    stmt.close();
                }
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                }
            } catch (Exception ex) {
                dbHealth = false;
                log.error("关闭数据库资源时发生异常: " + ex.getMessage());
            }
        }
       return dbHealth;
    }

    @Override
    public boolean checkRedisHealth() {
        try {
            // 执行一个简单的PING命令来测试连接是否可用
            String pingResult = (String) redisTemplate.execute((RedisConnection connection) -> connection.ping());
            // 如果收到"PONG"响应，则表示连接正常
            return "PONG".equals(pingResult);
        } catch (Exception e) {
            throw new ServiceException("Redis连接异常");
        }
    }

}

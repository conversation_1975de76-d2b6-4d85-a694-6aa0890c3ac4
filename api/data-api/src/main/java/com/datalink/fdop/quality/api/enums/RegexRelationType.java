package com.datalink.fdop.quality.api.enums;

/**
 * <AUTHOR>
 * @date 2022/5/17 17:31
 */
public enum RegexRelationType {

    AND("0", "and"),
    OR("1", "or"),
    ;

    private final String code;
    private final String desc;

    RegexRelationType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}

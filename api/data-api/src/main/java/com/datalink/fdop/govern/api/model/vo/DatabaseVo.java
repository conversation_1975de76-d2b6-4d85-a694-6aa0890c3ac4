package com.datalink.fdop.govern.api.model.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DatabaseVo {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    private String DatabaseName;


    private String description;


    private String property;



    private List<SchemaVo> schemas =new ArrayList<>();

}

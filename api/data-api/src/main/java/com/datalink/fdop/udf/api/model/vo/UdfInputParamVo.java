package com.datalink.fdop.udf.api.model.vo;

import com.datalink.fdop.udf.api.domain.ScriptInputParam;
import com.datalink.fdop.udf.api.enums.UdfType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/24 10:31
 */
@Data
public class UdfInputParamVo implements Serializable {

    private static final long serialVersionUID = -8248139491290772645L;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String code;

    private String name;

    private String description;

    private UdfType udfType;

    private List<ScriptInputParam> scriptInputParamList;

    public UdfInputParamVo() {
    }

    public UdfInputParamVo(Long id, String code, String name, String description, UdfType udfType, List<ScriptInputParam> scriptInputParamList) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.description = description;
        this.udfType = udfType;
        this.scriptInputParamList = scriptInputParamList;
    }
}


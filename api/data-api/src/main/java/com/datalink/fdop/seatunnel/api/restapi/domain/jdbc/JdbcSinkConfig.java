package com.datalink.fdop.seatunnel.api.restapi.domain.jdbc;

import com.alibaba.fastjson.annotation.JSONField;
import com.datalink.fdop.seatunnel.api.restapi.domain.SinkConfig;
import lombok.Data;

import java.util.List;

/**
 * SeaTunnel Jdbc Sink 配置
 */
@Data
public class JdbcSinkConfig extends CommonJdbcConfig implements SinkConfig {

    /**
     * 在SeaTunnel中定义一个伪表供下游transform sink 引用
     */
    @JSONField(name = "result_table_name")
    private String resultTableName;

    /**
     * 数据库名称
     */
    private String database;

    /**
     * 表名称，使用库名.表名
     */
    private String table;

    /**
     * 主键列
     */
    @JSONField(name = "primary_keys")
    private List<String> primaryKeys;

    /**
     * 是否自动生成sink insert sql
     */
    @JSONField(name = "generate_sink_sql")
    private Boolean generateSinkSql = true;

    /**
     * 是否开启upsert
     */
    @JSONField(name = "support_upsert_by_query_primary_key_exist")
    private Boolean isUpsert;

}

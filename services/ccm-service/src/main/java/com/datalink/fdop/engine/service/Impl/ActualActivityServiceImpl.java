package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.ActualActivity;
import com.datalink.fdop.engine.mapper.ActualActivityMapper;
import com.datalink.fdop.engine.service.ActualActivityService;
import com.datalink.fdop.engine.utils.TrinoSqlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:33
 */
@Service
public class ActualActivityServiceImpl extends ServiceImpl<ActualActivityMapper, ActualActivity> implements ActualActivityService {

    @Autowired
    private ActualActivityMapper actualActivityMapper;

    @Override
    public PageDataInfo<ActualActivity> getOrg(String sort, SearchVo searchVo) {
        List<ActualActivity> iPage = actualActivityMapper.getOrg(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<ActualActivity> getEdit(String sort, SearchVo searchVo) {
        List<ActualActivity> iPage = actualActivityMapper.getEdit(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<ActualActivity> getAll(String sort, SearchVo searchVo) {
        List<ActualActivity> iPage = actualActivityMapper.getAll(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public int saveData(ActualActivity actualActivity) {
        if (actualActivityMapper.selectByKey(actualActivity) == null) {
            actualActivityMapper.add(TrinoSqlUtils.actualActivityVoSql(actualActivity));
        } else {
            actualActivityMapper.updateByKey(actualActivity);
        }
        return 1;
    }

    @Override
    public int deleteByList(List<ActualActivity> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("请指定要删除的目标");
        }
        int i = 0;
        for (ActualActivity actualActivity : list) {
            i = i + actualActivityMapper.deleteByKey(actualActivity);
        }
        return i;
    }

}

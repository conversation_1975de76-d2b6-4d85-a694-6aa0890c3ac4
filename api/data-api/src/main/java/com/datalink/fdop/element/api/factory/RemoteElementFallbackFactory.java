package com.datalink.fdop.element.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.RemoteElementService;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/4 17:18
 */
public class RemoteElementFallbackFactory implements FallbackFactory<RemoteElementService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteElementFallbackFactory.class);

    @Override
    public RemoteElementService create(Throwable cause) {
        log.error("数据元素服务调用失败:{}", cause.getMessage());
        return new RemoteElementService() {
            @Override
            public R<DataElement> selectById(Long id) {
                return R.fail("查询数据元素失败" + cause.getMessage());
            }

            @Override
            public R<DataElement> selectByCode(String code) {
                return R.fail("查询数据元素失败" + cause.getMessage());
            }

            @Override
            public R<List<DataElementStructure>> list(Long dataElementId) {
                return R.fail("查询数据元素结构失败" + cause.getMessage());
            }
        };
    }
}

package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.ControlArea;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-09-05 11:47
 */
public interface ControlAreaService extends IService<ControlArea> {

    Page<ControlArea> pageList(Page<ControlArea> page, String a, String sort);

    String checkIdUnique(String id);

    int deleteByIds(List<String> ids);

    ControlArea selectById(String id);

    int create(ControlArea controlArea);

    int update(ControlArea controlArea);

    boolean updateBatchStatus(List<ControlArea> statusList);

    void importData(List<ControlArea> list);

}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.ProductStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:32
 */
public interface ProductStatusService extends IService<ProductStatus> {

    PageDataInfo<ProductStatus> getOrg(String sort, SearchVo searchVo);

    int saveData(ProductStatus productStatus);

    PageDataInfo<ProductStatus> getEdit(String sort, SearchVo searchVo);

    PageDataInfo<ProductStatus> getAll(String sort, SearchVo searchVo);

    int deleteByList(List<ProductStatus> list);
}

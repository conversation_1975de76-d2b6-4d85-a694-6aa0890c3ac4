package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.engine.api.domain.DwsNewTraceAct;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.mapper.DwsNewTraceActMapper;
import com.datalink.fdop.engine.service.DwsNewTraceActService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewTraceActServiceImpl extends ServiceImpl<DwsNewTraceActMapper, DwsNewTraceAct> implements DwsNewTraceActService {

    @Autowired
    private DwsNewTraceActMapper dwsNewTraceActMapper;

    @Override
    public PageDataInfo<DwsNewTraceAct> overview(String verId, String plantId,String productId,
                                                 String  dateFrom,String  dateTo,
                                                 String sort, SearchVo searchVo) {
        Page<DwsNewTraceAct> page = PageUtils.getPage(DwsNewTraceAct.class);
        IPage<DwsNewTraceAct> iPage = dwsNewTraceActMapper.selectAll(verId, plantId,productId,dateFrom,dateTo,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewTraceAct> selectNoPage(String verId, String plantId, String productId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        return dwsNewTraceActMapper.selectNoPage(verId, plantId, productId, dateFrom, dateTo, sort, searchVo);
    }


}

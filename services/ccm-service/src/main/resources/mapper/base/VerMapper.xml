<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.VerMapper">

    <select id="checkUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_ver
        WHERE ver_id = #{id} limit 1
    </select>

    <delete id="deleteById" parameterType="Long">
        delete from zjdata.ccm_ver
        where ver_id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="Long">
        delete from zjdata.ccm_ver
        WHERE ver_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.Ver" resultType="com.datalink.fdop.base.api.domain.Ver">
        SELECT ls.*
        FROM zjdata.ccm_ver ls
        <where>
            <if test="ver.verId != null">
                AND ls.ver_id = #{ver.id}
            </if>
            <if test="ver.verDesc != null and ver.verDesc != ''">
                AND ls.ver_desc like '%${ver.verDesc}%'
            </if>
            <if test="ver.verText != null and ver.verText != ''">
                AND ls.ver_text like '%${ver.verText}%'
            </if>
            <if test="ver.enable != null">
                AND ls.enable = #{ver.enable}
            </if>
        </where>
        ORDER BY ver_id desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.Ver">
        SELECT ls.*
        FROM zjdata.ccm_ver ls
        <where>
            <if test="searchCondition != null ">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("ls",searchCondition)})
            </if>
        </where>
        ORDER BY ver_id ${sort}
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.Ver">
        SELECT ls.*
        FROM zjdata.ccm_ver ls
        where ls.ver_id = #{id} limit 1
    </select>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_ver
            SET enable = #{item.enable}
            WHERE ver_id = #{item.verId}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_ver (
        ver_id, ver_desc, ver_text,value_type_p,value_type_a, ver_group, enable,create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.verId},#{item.verDesc},#{item.verText},
            #{item.valueTypeP},#{item.valueTypeA},#{item.verGroup},
            #{item.enable},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>

    <select id="listVerId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select distinct node.ver_id as "key", dict.ver_desc as "value"
        from ${orgTableName} node
                 left join zjdata.ccm_ver dict
                           on node.ver_id = dict.ver_id
        where node.ver_id is not null and node.ver_id &lt;> ''
        order by node.ver_id asc
    </select>

</mapper>


package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.engine.api.domain.DwdWph;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdWphService extends IService<DwdWph> {

    PageDataInfo<DwdWph> overview(String plantId, String equipGroupId, String sort, SearchVo searchVo);

    List<DwdWph> selectNoPage(String plantId,
                              String equipGroupId,
                              String sort,
                              SearchVo searchVo);
}

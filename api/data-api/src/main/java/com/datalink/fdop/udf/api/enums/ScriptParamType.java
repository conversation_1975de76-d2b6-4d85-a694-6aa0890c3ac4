package com.datalink.fdop.udf.api.enums;

/**
 * <AUTHOR>
 * @date 2022/5/17 17:31
 */
public enum ScriptParamType {

    // 字符串
    STRING(0, "STRING"),
    // 整数
    INTEGER(1, "INTEGER"),
    // 小数
    DECIMAL(2, "DECIMAL"),
    // 布尔
    BOOLEAN(3, "BOOLEAN"),
    // JSON
    JSON(4, "JSON"),
    // 仅支持输出参数使用
    JSON2TABLE(5, "JSON2TABLE"),
    ;

    private final int code;
    private final String desc;

    ScriptParamType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwdTraceSamMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwdTraceSam">
        SELECT *
        FROM dwd.dwd_trace_sam node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dateFrom != null and dateFrom != ''">
                and node.date >= #{dateFrom}
            </if>
            <if test="dateTo != null and dateTo != ''">
                and node.date &lt;= #{dateTo}
            </if>
        </where>
        ORDER BY
        node.plant_id ,
        node.factory_id ,
        node.base_lot_id ,
        history_step_no ,
        node.date ,
        node.timestamp ,
        node.fr_lot_id ,
        node.to_lot_id ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwdTraceSam">
        SELECT *
        FROM dwd.dwd_trace_sam node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dateFrom != null and dateFrom != ''">
                and node.date >= #{dateFrom}
            </if>
            <if test="dateTo != null and dateTo != ''">
                and node.date &lt;= #{dateTo}
            </if>
        </where>
        ORDER BY
        node.plant_id ,
        node.factory_id ,
        node.base_lot_id ,
        history_step_no ,
        node.date ,
        node.timestamp ,
        node.fr_lot_id ,
        node.to_lot_id ${sort}
    </select>
</mapper>
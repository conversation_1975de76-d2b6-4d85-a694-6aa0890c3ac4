<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CcmRepcCapaeidMapper">

    <delete id="deleteBykey">
        delete
        from zjdata.ccm_repc_capaeid
        WHERE factory_id = #{factoryId}
          and old_equip_id = #{oldEquipId}
          and old_equip_group_id = #{oldEquipGroupId}
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_repc_capaeid
        WHERE factory_id = #{factoryId}
          and old_equip_id = #{oldEquipId}
          and old_equip_group_id = #{oldEquipGroupId}
        limit 1
    </select>

    <select id="overview" resultType="com.datalink.fdop.base.api.domain.CcmRepcCapaeid">
        SELECT ls.*
        FROM zjdata.ccm_repc_capaeid ls
        <where>
            <if test="searchVo != null ">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("ls",searchVo)})
            </if>
        </where>
        ORDER BY factory_id,old_equip_id,old_equip_group_id ${sort}
    </select>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CcmRepcCapaeid">
        update zjdata.ccm_repc_capaeid
        set
        <if test="equipId != null and equipId != '' ">equip_id = #{equipId},</if>
        <if test="equipGroupId != null and equipGroupId != '' ">equip_group_id = #{equipGroupId},</if>
        <if test="resDept != null and resDept != '' ">res_dept = #{resDept},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where factory_id=#{factoryId} and old_equip_id = #{oldEquipId} and old_equip_group_id = #{oldEquipGroupId}
    </update>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_repc_capaeid
        SET enable = #{enable}
        where (factory_id, old_equip_id,  old_equip_group_id ) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.factoryId},#{item.oldEquipId},#{item.oldEquipGroupId})
        </foreach>
    </update>
</mapper>
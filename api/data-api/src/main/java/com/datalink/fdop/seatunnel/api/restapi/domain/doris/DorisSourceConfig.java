package com.datalink.fdop.seatunnel.api.restapi.domain.doris;

import com.alibaba.fastjson.annotation.JSONField;
import com.datalink.fdop.seatunnel.api.restapi.domain.SourceConfig;
import lombok.Data;

@Data
public class DorisSourceConfig extends CommonDorisConfig implements SourceConfig {

    /**
     * 引用SeaTunnel 上下文配置 sink/transform 中定义的result_table_name
     */
    @JSONField(name = "source_table_name")
    private String sourceTableName;

    /**
     * 库名
     */
    private String database;

    /**
     * 表名
     */
    private String table;

}

package com.datalink.fdop.govern.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.DataSecurityClassification;
import com.datalink.fdop.govern.api.domain.DataSecurityClassificationTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataSecurityClassificationMapper {

    int insert(@Param("classification") DataSecurityClassification dataSecurityClassification);

    int updateById(DataSecurityClassification dataSecurityClassification);

    VlabelItem<DataSecurityClassification> selectById(Long id);

    VlabelItem<DataSecurityClassification> selectByCode(@Param("code") String code);

    VlabelItem<DataSecurityClassification> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    IPage<VlabelItem<DataSecurityClassification>> selectList(IPage<VlabelItem> page, @Param("classification") DataSecurityClassification dataSecurityClassification);

    List<SelectVo> selectListAll(@Param("classification") DataSecurityClassification dataSecurityClassification);

    List<DataSecurityClassificationTree> selectDataSecurityClassificationTree(@Param("sort") String sort, @Param("code") String code, @Param("description") String description);

}

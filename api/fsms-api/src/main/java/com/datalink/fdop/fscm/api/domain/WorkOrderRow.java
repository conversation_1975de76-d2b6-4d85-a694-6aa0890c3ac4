package com.datalink.fdop.fscm.api.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "zjdata", value = "f_d_work_order_row")
@ApiModel
public class WorkOrderRow {
  @ApiModelProperty(value = "工单号")
  private String workOrderNum;
  @ApiModelProperty(value = "工单行号")
  private Long workOrderRowNum;
  @ApiModelProperty(value = "采购合同/订单行号")
  private Long orderRowNum;
  @ApiModelProperty(value = "采购合同/订单号")
  private String orderNum;
  @ApiModelProperty(value = "删除标识")
  private String delFlag;
  @ApiModelProperty(value = "收货物料编码")
  private String materialCode;
  @ApiModelProperty(value = "收货物料描述")
  private String materialDesc;
  @ApiModelProperty(value = "收货库存地点")
  private String stockPCode;
  @ApiModelProperty(value = "收货库存地点描述")
  private String stockPDescription;
  @ApiModelProperty(value = "预计收货数量")
  private Double quantityReceive;
  @ApiModelProperty(value = "基本单位")
  private String basicUnit;
  @ApiModelProperty(value = "包装方式")
  private String packageWay;
  @ApiModelProperty(value = "芯片丝印")
  private String mark;
  @ApiModelProperty(value = "交货限制")
  private String deliveryLimit;
  @ApiModelProperty(value = "过量交货容差")
  private Double excessiveDelivery;
  @ApiModelProperty(value = "交货不足容差")
  private Double insufficientDelivery;
  @ApiModelProperty(value = "需求交货日期")
  private Date deliveryDate;
  @ApiModelProperty(value = "封裝信息版本")
  private String encapsulationVersions;
  @ApiModelProperty(value = "线材")
  private String wireRod;
  @ApiModelProperty(value = "打线图（BD图）")
  private String bdGraph;
  @ApiModelProperty(value = "打线图版本")
  private String bdGraphVersions;
  @ApiModelProperty(value = "焊丝尺寸（线径）")
  private String wireSize;
  @ApiModelProperty(value = "印章文件编号")
  private String number;
  @ApiModelProperty(value = "取片方式")
  private String slicesWay;
  @ApiModelProperty(value = "框架是否专用")
  private String dedicated;
  @ApiModelProperty(value = "贴膜方式")
  private String stickerWay;
  @ApiModelProperty(value = "封装形式")
  private String encapsulationMode;
  @ApiModelProperty(value = "测试程序")
  private String testRoutines;
  @ApiModelProperty(value = "BOM版本")
  private String bomVersions;
  @ApiModelProperty(value = "BOM状态")
  private String status;
  @ApiModelProperty(value = "收货批次号")
  private String batchNumber;
  @ApiModelProperty(value = "行项目备注")
  private String rowNote;
  @ApiModelProperty(value = "是否最后产出品")
  private String isFinal;
  @ApiModelProperty(value = "上层行号")
  private Long upLayerNum;

  @ApiModelProperty(value = "关闭标识")
  private String isClose;


  @ApiModelProperty(value = "是否被冲销")
  @TableField(exist = false)
  private String writeOff;



}

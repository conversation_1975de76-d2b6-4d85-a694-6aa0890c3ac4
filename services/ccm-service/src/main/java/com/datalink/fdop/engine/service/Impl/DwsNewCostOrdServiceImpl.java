package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewCostOrd;
import com.datalink.fdop.engine.mapper.DwsNewCostOrdMapper;
import com.datalink.fdop.engine.service.DwsNewCostOrdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewCostOrdServiceImpl extends ServiceImpl<DwsNewCostOrdMapper, DwsNewCostOrd> implements DwsNewCostOrdService {

    @Autowired
    private DwsNewCostOrdMapper dwsNewCostOrdMapper;

    @Override
    public PageDataInfo<DwsNewCostOrd> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwsNewCostOrd> page = PageUtils.getPage(DwsNewCostOrd.class);
        IPage<DwsNewCostOrd> iPage = dwsNewCostOrdMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewCostOrd> listAll() {
        return dwsNewCostOrdMapper.listAll();
    }

    @Override
    public List<DwsNewCostOrd> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwsNewCostOrdMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
package com.datalink.fdop.govern.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.TreeVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.DataProcessFramework;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataProcessFrameworkMenuMapper {

    int createMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertMenu(@Param("processFrameworkMenu") DataProcessFrameworkMenu processFrameworkMenu);

    int updateById(DataProcessFrameworkMenu processFrameworkMenu);

    int batchUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<DataProcessFrameworkMenu> selectById(Long id);

    VlabelItem<DataProcessFrameworkMenu> selectByCode(String code);

    VlabelItem<DataProcessFrameworkMenu> selectByPid(Long pid);

    VlabelItem<DataProcessFrameworkMenu> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    IPage<DataProcessFramework> overview(@Param("page") Page<DataProcessFramework> page, @Param("pid") Long pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    List<TreeVo> selectMenuTree(@Param("sort") String sort, @Param("code") String code);

    Integer querySerialNumber();

}

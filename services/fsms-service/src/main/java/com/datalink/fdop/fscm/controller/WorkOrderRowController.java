package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.*;
import com.datalink.fdop.fscm.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:32
 */
@RestController
@RequestMapping("/work/order/row")
@Transactional
@Api(tags = "工单行")
public class WorkOrderRowController {

    @Autowired
    private WorkOrderHeadService workOrderHeadService;
    @Autowired
    private WorkOrderRowService workOrderRowService;
    @Autowired
    private WorkOrderChildService workOrderChildService;
    @Autowired
    private WorkOrderTestService workOrderTestService;
    @Autowired
    private ProvisionalSingleOutService provisionalSingleOutService;
    @Autowired
    private ProvisionalSingleOutCacheService provisionalSingleOutCacheService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;


    @ApiOperation(value = "获取行号")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/get/row/num")
    public R getWorkOrderRowNum(@RequestParam String workOrderNum) {
        return R.ok(workOrderRowService.getWorkOrderRowNum(workOrderNum));
    }



    @ApiOperation(value = "批量修改删除标识")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/batch/update")
    public R batchUpdate(@RequestBody List<WorkOrderRow> workOrderRows,@RequestParam String screenMode) {

        workOrderRows.stream().forEach(workOrderRow -> {
            workOrderRowService.update(Wrappers.lambdaUpdate(WorkOrderRow.class)
                    .set(WorkOrderRow::getDelFlag,workOrderRow.getDelFlag())
                    .eq(WorkOrderRow::getWorkOrderNum, workOrderRow.getWorkOrderNum())
                    .eq(WorkOrderRow::getWorkOrderRowNum, workOrderRow.getWorkOrderRowNum())
            );

        });

        return R.ok();
    }
    @ApiOperation(value = "批量修改关闭标识")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/batch/update/close")
    public R batchUpdateClose(@RequestBody List<WorkOrderRow> workOrderRows) {

        workOrderRows.stream().forEach(workOrderRow -> {
            workOrderRowService.update(Wrappers.lambdaUpdate(WorkOrderRow.class)
                    .set(WorkOrderRow::getIsClose,workOrderRow.getIsClose())
                    .eq(WorkOrderRow::getWorkOrderNum, workOrderRow.getWorkOrderNum())
                    .eq(WorkOrderRow::getWorkOrderRowNum, workOrderRow.getWorkOrderRowNum())
            );

        });

        return R.ok();
    }

    @ApiOperation(value = "工单行查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/row/query")
    public R workOrderHeadQuery(@RequestBody(required = false) WorkOrderRow orderRow,@RequestParam(required = false) String delFlag) {
        Page<WorkOrderRow> page = PageUtils.getPage(WorkOrderRow.class);
        Page<WorkOrderRow> dataPage= workOrderRowService.page(page,new QueryWrapper<WorkOrderRow>()
                .lambda()
                .eq(StringUtils.isNotEmpty(orderRow.getWorkOrderNum()),WorkOrderRow::getWorkOrderNum,orderRow.getWorkOrderNum())
                .eq(StringUtils.isNotEmpty(delFlag),WorkOrderRow::getDelFlag,delFlag)
        );

        List<WorkOrderRow> records = dataPage.getRecords();
        records.stream().forEach(r->{
            List<PostCertificateRow> list = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>().lambda()
                    .eq(PostCertificateRow::getWorkOrderNum, r.getWorkOrderNum())
                    .eq(PostCertificateRow::getWorkOrderRowNum, r.getWorkOrderRowNum())
            );
            list = list.stream().filter(t -> t.getWriteOff().equals("0")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                r.setWriteOff("0");
            }

        });
        return R.ok(PageUtils.getPageInfo(records,(int)dataPage.getTotal()));
    }


    @ApiOperation(value = "删除工单下挑片和临时挑片")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/row/del/all")
    public R delAll(@RequestParam String workOrderNum) {

            //删除挑片
            provisionalSingleOutService.remove(new QueryWrapper<ProvisionalSingleOut>().lambda()
                    .eq(ProvisionalSingleOut::getWorkOrderNum,workOrderNum)
            );
            //删除缓存挑片
            provisionalSingleOutCacheService.remove(new QueryWrapper<ProvisionalSingleOutCache>().lambda()
                    .eq(ProvisionalSingleOutCache::getWorkOrderNum,workOrderNum)
            );

        return R.ok();
    }

    @ApiOperation(value = "删除工单行下的子件，测试程序，挑片")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/row/del")
    public R del(@RequestParam String workOrderNum,@RequestBody List<Long> workOrderRowNums ) {
        for (Long workOrderRowNum : workOrderRowNums) {
            //删子件
            workOrderChildService.remove(new QueryWrapper<WorkOrderChild>().lambda()
                    .eq(WorkOrderChild::getWorkOrderNum,workOrderNum)
                    .eq(WorkOrderChild::getWorkOrderRowNum,workOrderRowNum)
            );
            //删除测试程序
            workOrderTestService.remove(new QueryWrapper<WorkOrderTest>().lambda()
                    .eq(WorkOrderTest::getWorkOrderNum,workOrderNum)
                    .eq(WorkOrderTest::getWorkOrderRowNum,workOrderRowNum)
            );
            //删除挑片
            provisionalSingleOutService.remove(new QueryWrapper<ProvisionalSingleOut>().lambda()
                    .eq(ProvisionalSingleOut::getWorkOrderNum,workOrderNum)
                    .eq(ProvisionalSingleOut::getWorkOrderRowNum,workOrderRowNum)
            );
            //删除缓存挑片
            provisionalSingleOutCacheService.remove(new QueryWrapper<ProvisionalSingleOutCache>().lambda()
                    .eq(ProvisionalSingleOutCache::getWorkOrderNum,workOrderNum)
                    .eq(ProvisionalSingleOutCache::getWorkOrderRowNum,workOrderRowNum)
            );
        }
        workOrderRowNums.stream().forEach(workOrderRow -> {
            workOrderRowService.update(Wrappers.lambdaUpdate(WorkOrderRow.class)
                    .set(WorkOrderRow::getDelFlag,"1")
                    .eq(WorkOrderRow::getWorkOrderNum, workOrderNum)
                    .eq(WorkOrderRow::getWorkOrderRowNum, workOrderRow)
            );

        });
        return R.ok();
    }
    @ApiOperation(value = "删除工单行下挑片")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/row/del/provisional/single/out")
    public R delProvisionalSingleOut(@RequestParam String workOrderNum,@RequestBody List<Long> workOrderRowNums ) {
        for (Long workOrderRowNum : workOrderRowNums) {
            //删除挑片
            provisionalSingleOutService.remove(new QueryWrapper<ProvisionalSingleOut>().lambda()
                    .eq(ProvisionalSingleOut::getWorkOrderNum,workOrderNum)
                    .eq(ProvisionalSingleOut::getWorkOrderRowNum,workOrderRowNum)
            );
            //删除缓存挑片
            provisionalSingleOutCacheService.remove(new QueryWrapper<ProvisionalSingleOutCache>().lambda()
                    .eq(ProvisionalSingleOutCache::getWorkOrderNum,workOrderNum)
                    .eq(ProvisionalSingleOutCache::getWorkOrderRowNum,workOrderRowNum)
            );
        }
        return R.ok();
    }

}

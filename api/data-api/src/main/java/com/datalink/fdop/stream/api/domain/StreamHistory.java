package com.datalink.fdop.stream.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.datalink.fdop.graph.api.dto.TaskInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName(schema = "zjdata", value = "s_c_stream_history")
@ApiModel("Flink流任务历史记录")
public class StreamHistory extends BaseField {

    @ApiModelProperty(value = "记录记录id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "序号")
    private Integer sort;

    @ApiModelProperty(value = "Flink流任务ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long streamId;

    @ApiModelProperty(value = "JobId")
    private String jobId;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "异常信息")
    private String errorInfo;

    @ApiModelProperty(value = "流任务信息JSON")
    private String stream;

    @ApiModelProperty(value = "Flink任务信息JSON")
    private String taskInfo;

    @ApiModelProperty(value = "结束时间", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endTime;

    private Stream streamObj;

    private TaskInfo taskInfoObj;

    public StreamHistory() {

    }

    public StreamHistory(Long streamId, String jobId, String status, String errorInfo, String stream, String taskInfo, Date endTime) {
        this.streamId = streamId;
        this.jobId = jobId;
        this.status = status;
        this.errorInfo = errorInfo;
        this.stream = stream;
        this.taskInfo = taskInfo;
        this.endTime = endTime;
    }
}

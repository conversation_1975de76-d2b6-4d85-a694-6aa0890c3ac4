<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.TeepMapper">

    <sql id="selectSql">
        SELECT
            COALESCE(c.equip_id, co.equip_id) AS equip_id,
            COALESCE(c.UPT, co.UPT) AS UPT,
            COALESCE(c.EFF, co.EFF) AS EFF,
            COALESCE(c.UTIL, co.UTIL) AS UTIL,
            COALESCE(c.OEE, co.OEE) AS OEE,
            COALESCE(c.TEEP, co.TEEP) AS TEEP,
            COALESCE(c.enable, co.enable) AS enable,
            COALESCE(c.create_by, co.create_by) AS create_by,
            COALESCE(c.create_time, co.create_time) AS create_time,
            COALESCE(c.update_by, co.update_by) AS update_by,
            COALESCE(c.update_time, co.update_time) AS update_time,
            COALESCE(c.delete_flag, false) AS delete_flag
        FROM zjdata.ccm_teep_orig co
                 FULL OUTER JOIN zjdata.ccm_teep c
                                 ON c.equip_id = co.equip_id
    </sql>

    <update id="deleteBykey">
        update zjdata.ccm_teep
        set delete_flag = false
        WHERE equip_id = #{equipId}
    </update>

    <update id="deleteBykeys">
        update zjdata.ccm_teep
        set delete_flag = false
        WHERE equip_id in (
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
            )
    </update>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        select
        (SELECT COUNT(1)
        FROM zjdata.ccm_teep_orig
        WHERE equip_id = #{equipId}) + (SELECT COUNT(1)
        FROM zjdata.ccm_teep
        WHERE equip_id = #{equipId} and delete_flag = false)
    </select>

    <delete id="deleteById" parameterType="String">
        delete from zjdata.ccm_teep
        where equip_id = #{equipId}
    </delete>

    <select id="checkIdExist" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_teep
        WHERE equip_id = #{equipId} and delete_flag = false limit 1
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.Teep">
        update zjdata.ccm_teep
        set
            <if test="upt != null">upt = #{upt},</if>
            <if test="eff != null">eff = #{eff},</if>
            <if test="util != null">util = #{util},</if>
            <if test="oee != null">oee = #{oee},</if>
            <if test="teep != null">teep = #{teep},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where equip_id = #{equipId}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.Teep">
        SELECT a.*
        FROM (<include refid="selectSql"/>) a
        <where>
            and a.delete_flag = false
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
        </where>
        ORDER BY a.equip_id ${sort}
    </select>

    <select id="listAll" resultType="String">
        select activity_id from (<include refid="selectSql"/>) a
        where a.enable = true and a.delete_flag = false
        <if test="activityId !=null and activityId !=''">
            and a.activity_id = #{activityId}
        </if>
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_teep
        SET enable = #{enable}
        where equip_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
             #{item.equipId}
        </foreach>
    </update>

    <select id="queryOrigList" resultType="com.datalink.fdop.base.api.domain.Teep">
        select * from zjdata.ccm_teep_orig node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY equip_id ${sort}
    </select>

    <select id="queryList" resultType="com.datalink.fdop.base.api.domain.Teep">
        select * from zjdata.ccm_teep node
        <where>
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY equip_id ${sort}
    </select>

    <delete id="batchDelete">
        delete from zjdata.ccm_teep
        where equip_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            (#{item.equipId})
        </foreach>
    </delete>




</mapper>


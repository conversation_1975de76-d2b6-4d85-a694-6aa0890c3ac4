package com.datalink.fdop.fscm.api.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/12 16:37
 */
@Data
public class AllotQueryVo {


    @ExcelProperty("调拨单号")
    private List<String> transferOrderNum;
    @ApiModelProperty("单据状态")
    private List<String> statu;
    @ApiModelProperty("单据创建人")
    private List<String> username;
    @ApiModelProperty("凭证日期")
    private List<Date> paperDate;
    @ApiModelProperty("内部物料编码")
    private List<String> materialCode;
    @ApiModelProperty("内部物料描述")
    private List<String> materialDesc;
    @ApiModelProperty("工厂")
    private List<String> factoryCode;
    @ApiModelProperty("发出库存地点")
    private List<String> stockPCodeShip;
    @ApiModelProperty("收回库存地点")
    private List<String> stockPCodeReceive;
    @ApiModelProperty("发出批次号")
    private List<String> batchNumberShip;
    @ApiModelProperty("收回批次号")
    private List<String> batchNumberReceive;
    @ApiModelProperty("发出片号")
    private List<String> pieceShip;
    @ApiModelProperty("收回片号")
    private List<String> pieceReceive;
    @ApiModelProperty("发出BIN号")
    private List<String> binNumShip;
    @ApiModelProperty("收回BIN号")
    private List<String> binNumReceive;
    @ApiModelProperty("高级搜索传参")
    private SearchVo searchVo;
}

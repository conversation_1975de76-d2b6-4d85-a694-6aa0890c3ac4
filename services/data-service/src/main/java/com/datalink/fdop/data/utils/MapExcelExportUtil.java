package com.datalink.fdop.data.utils;

import com.datalink.fdop.common.core.domain.DynamicColumn;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class MapExcelExportUtil {

    private static final Logger log = LoggerFactory.getLogger(MapExcelExportUtil.class);

    /**
     * 导出Map数据（动态列）的Excel
     */
    public static void exportMapDataWithDynamicColumns(HttpServletResponse response,
                                                       List<Map<String, Object>> dataList,
                                                       List<DynamicColumn> dynamicColumns,
                                                       String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + title + ".xlsx");

        try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) {
            // 创建样式
            Map<String, CellStyle> styles = createStyles(workbook);

            // 如果没有提供动态列，尝试从数据中自动推断列名
            if ((dynamicColumns == null || dynamicColumns.isEmpty()) && dataList != null && !dataList.isEmpty()) {
                dynamicColumns = new ArrayList<>();
                for (String key : dataList.get(0).keySet()) {
                    dynamicColumns.add(new DynamicColumn(key, key));
                }
            }

            // 如果 dynamicColumns 仍然为空，生成一个空的 Excel 文件
            if (dynamicColumns == null || dynamicColumns.isEmpty()) {
                workbook.createSheet(title);
                workbook.write(response.getOutputStream());
                return;
            }


            // 创建Sheet页
            Sheet sheet = workbook.createSheet(title);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.setHeight((short) 280);
            for (int i = 0; i < dynamicColumns.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(dynamicColumns.get(i).getColumnDesc());
                cell.setCellStyle(styles.get("header"));
            }

            // 写入数据
            int rowIndex = 1;
            for (Map<String, Object> rowData : dataList) {
                Row row = sheet.createRow(rowIndex++);
                row.setHeight((short) 280);
                for (int i = 0; i < dynamicColumns.size(); i++) {
                    Cell cell = row.createCell(i);
                    String columnName = dynamicColumns.get(i).getColumnName();
                    Object value = rowData.get(columnName);

                    if (value == null) {
                        cell.setCellValue("");
                        cell.setCellStyle(styles.get("data"));
                    } else if (value instanceof Date) {
                        cell.setCellValue((Date) value);
                        cell.setCellStyle(styles.get("date"));
                    } else if (value instanceof Number) {
                        cell.setCellValue(((Number) value).doubleValue());
                        cell.setCellStyle(styles.get("data"));
                    } else {
                        cell.setCellValue(value.toString());
                        cell.setCellStyle(styles.get("data"));
                    }
                }
            }

            // 设置列宽
            for (int i = 0; i < dynamicColumns.size(); i++) {
                sheet.setColumnWidth(i, (int) (15.89 * 256));
            }

            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("Exception when exporting Map data: ", e);
        }
    }

    /**
     * 创建样式
     */
    private static Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();

        // 表头样式
        CellStyle headerStyle = wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Font headerFont = wb.createFont();
        headerFont.setFontName("Microsoft YaHei");
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        styles.put("header", headerStyle);

        // 数据单元格样式
        CellStyle dataStyle = wb.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        Font dataFont = wb.createFont();
        dataFont.setFontName("Microsoft YaHei");
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);
        styles.put("data", dataStyle);

        // 日期单元格样式
        CellStyle dateStyle = wb.createCellStyle();
        dateStyle.cloneStyleFrom(dataStyle);
        CreationHelper createHelper = wb.getCreationHelper();
        dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd"));
        styles.put("date", dateStyle);

        return styles;
    }
}
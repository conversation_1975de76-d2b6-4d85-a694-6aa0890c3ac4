package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewActualExp;
import com.datalink.fdop.engine.mapper.DwsNewActualExpMapper;
import com.datalink.fdop.engine.service.DwsNewActualExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewActualExpServiceImpl extends ServiceImpl<DwsNewActualExpMapper, DwsNewActualExp> implements DwsNewActualExpService {

    @Autowired
    private DwsNewActualExpMapper dwsNewActualExpMapper;

    @Override
    public PageDataInfo<DwsNewActualExp> overview(String verId, String companyId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwsNewActualExp> page = PageUtils.getPage(DwsNewActualExp.class);
        IPage<DwsNewActualExp> iPage = dwsNewActualExpMapper.selectAll(verId, companyId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewActualExp> selectNoPage(String verId, String companyId, String yearMonth, String sort, SearchVo searchVo) {
        return dwsNewActualExpMapper.selectNoPage(verId, companyId, yearMonth, sort, searchVo);
    }

}
package com.datalink.fdop.permissions.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.permissions.api.domain.RelevancyBack;
import com.datalink.fdop.permissions.api.domain.RelevancyFront;
import com.datalink.fdop.permissions.service.IRelevancyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/20 15:43
 */
@RequestMapping(value = "/permissions/relevancy")
@RestController
@Api(tags = "匹配关系api")
public class RelevancyController extends BaseController {

    @Autowired
    private IRelevancyService relevancyService;

    @ApiOperation("添加权限元素")
    @Log(title = "内置权限", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addPermissionsElement")
    public R addPermissionsElement(@RequestBody List<DataElement> dataElementList) {
        return R.toResult(relevancyService.addPermissionsElement(dataElementList));
    }

    @ApiOperation("删除权限元素")
    @Log(title = "内置权限", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/deletePermissionsElement")
    public R deletePermissionsElement(@RequestBody List<Long> ids) {
        return R.toResult(relevancyService.deletePermissionsElement(ids));
    }

    @ApiOperation("查询权限元素列表")
    @Log(title = "内置权限")
    @PostMapping(value = "/selectPermissionsElementList")
    public R<PageDataInfo<DataElement>> selectPermissionsElementList(@RequestBody(required = false) DataElement dataElement) {
        return R.ok(relevancyService.selectPermissionsElementList(dataElement));
    }

    @ApiOperation("添加前台的关联字段")
    @Log(title = "内置权限", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addRelevancyFront/{elementId}")
    public R addRelevancyFront(@PathVariable("elementId") Long elementId, @Validated @RequestBody List<RelevancyFront> relevancyFrontList) {
        return R.toResult(relevancyService.addRelevancyFront(elementId, relevancyFrontList));
    }

    @ApiOperation("删除前台的关联字段")
    @Log(title = "内置权限", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/deleteRelevancyFront/{elementId}")
    public R deleteRelevancyFront(@PathVariable("elementId") Long elementId, @RequestBody List<Long> ids) {
        return R.toResult(relevancyService.deleteRelevancyFront(elementId, ids));
    }

    @ApiOperation("添加后台的关联字段")
    @Log(title = "内置权限", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addRelevancyBack/{elementId}")
    public R addRelevancyBack(@PathVariable("elementId") Long elementId, @Validated @RequestBody List<RelevancyBack> relevancyBackList) {
        return R.toResult(relevancyService.addRelevancyBack(elementId, relevancyBackList));
    }

    @ApiOperation("删除后台的关联字段")
    @Log(title = "内置权限", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/deleteRelevancyBack/{elementId}")
    public R deleteRelevancyBack(@PathVariable("elementId") Long elementId, @RequestBody List<Long> ids) {
        return R.toResult(relevancyService.deleteRelevancyBack(elementId, ids));
    }

    @ApiOperation("查询前台的权限关联字段列表")
    @Log(title = "内置权限")
    @PostMapping(value = "/selectRelevancyFrontList/{elementId}")
    public R<PageDataInfo<RelevancyFront>> selectRelevancyFrontList(@PathVariable("elementId") Long elementId, @RequestBody(required = false) RelevancyFront relevancyFront) {
        return R.ok(relevancyService.selectRelevancyFrontList(elementId, relevancyFront));
    }

    @ApiOperation("查询后台的权限关联字段列表")
    @Log(title = "内置权限")
    @PostMapping(value = "/selectRelevancyBackList/{elementId}")
    public R<PageDataInfo<RelevancyBack>> selectRelevancyBackList(@PathVariable("elementId") Long elementId, @RequestBody(required = false) RelevancyBack relevancyBack) {
        return R.ok(relevancyService.selectRelevancyBackList(elementId, relevancyBack));
    }

}

package com.datalink.fdop.seatunnel.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/8/17 18:29
 */
@Data
@NoArgsConstructor
@TableName(schema = "zjdata", value = "s_c_seatunnel_cmd_menu")
@ApiModel("SeaTunnelCmd任务菜单")
public class SeaTunnelCmdMenu extends BaseField {
    private static final long serialVersionUID = 1437055644644893842L;
    @ApiModelProperty(value = "任务菜单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @ApiModelProperty(value = "任务上级菜单id")
    @NotNull(message = "请选择一个菜单")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

}

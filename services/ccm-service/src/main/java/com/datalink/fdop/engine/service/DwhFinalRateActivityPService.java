package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRateActivityP;

import java.util.List;

public interface DwhFinalRateActivityPService extends IService<DwhFinalRateActivityP> {

    PageDataInfo<DwhFinalRateActivityP> overview(
            String costCenterType,
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo);

    List<DwhFinalRateActivityP> selectNoPage(
            String costCenterType,
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo);
}
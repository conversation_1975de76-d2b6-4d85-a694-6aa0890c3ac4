package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewActualPidExp;
import com.datalink.fdop.engine.mapper.DwsNewActualPidExpMapper;
import com.datalink.fdop.engine.service.DwsNewActualPidExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewActualPidExpServiceImpl extends ServiceImpl<DwsNewActualPidExpMapper, DwsNewActualPidExp> implements DwsNewActualPidExpService {

    @Autowired
    private DwsNewActualPidExpMapper dwsNewActualPidExpMapper;

    @Override
    public PageDataInfo<DwsNewActualPidExp> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwsNewActualPidExp> page = PageUtils.getPage(DwsNewActualPidExp.class);
        IPage<DwsNewActualPidExp> iPage = dwsNewActualPidExpMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewActualPidExp> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwsNewActualPidExpMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.BasisMapper">

    <select id="selectByPid" resultType="com.datalink.fdop.base.model.Basis">
        SELECT ls.*, cp.code as saknr_code
        FROM zjdata.p_d_rule_basis ls left join zjdata.p_d_saknr cp on ls.saknr_id = cp.id
        where ls.pid = #{pid} limit 1
    </select>

</mapper>


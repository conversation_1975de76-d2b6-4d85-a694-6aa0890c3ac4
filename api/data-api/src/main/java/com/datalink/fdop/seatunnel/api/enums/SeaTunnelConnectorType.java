package com.datalink.fdop.seatunnel.api.enums;

/**
 * FDOP目前支持的SeaTunnel Connector
 */
public enum SeaTunnelConnectorType {

    JDBC("Jdbc"),
    ICEBERG("Iceberg"),
    HIVE("Hive"),
    DORIS("Doris"),
    SFTP("SftpFile"),
    FTP("FtpFile"),
    ORACLE_CDC("Oracle-CDC"),
    DM_CDC("Dm-CDC"),
    POSTGRESQL_CDC("Postgres-CDC"),
    MYSQL_CDC("MySQL-CDC"),
    SQLSERVER_CDC("SqlServer-CDC"),
    MONGO_CDC("MongoDB-CDC"),
    LocalFile("LocalFile"),
    ;

    private final String code;

    SeaTunnelConnectorType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}

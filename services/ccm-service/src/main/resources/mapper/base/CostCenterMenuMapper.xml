<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CostCenterMenuMapper">

    <update id="bacthUpdatePidById" parameterType="String">
        update zjdata.ccm_cost_center_menu
        set p_menu_id = (select p_menu_id from zjdata.ccm_cost_center_menu where menu_id = #{menuId} limit 1)
        where p_menu_id = #{menuId}
    </update>

    <update id="deleteByIds" parameterType="String">
        update zjdata.ccm_cost_center_menu
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.CostCenterTree">
        select null as cost_center_id,null as control_area_id, a.*, 'MENU' as menu_type, null as date_to from zjdata.ccm_cost_center_menu a
        UNION
        select b.cost_center_id as cost_center_id,b.control_area_id as control_area_id, null as menu_id, b.menu_id as p_menu_id, null as menu_desc, null as menu_text,
               'NODE' as menu_type, b.date_to as date_to from zjdata.ccm_cost_center b
        order by menu_id ${sort}
    </select>

    <select id="selectByPids" resultType="String">
        select * from zjdata.ccm_cost_center
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by cost_center_id, date_to,control_area_id desc
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.CostCenterMenu">
        select * from zjdata.ccm_cost_center_menu
        where menu_id = #{id} limit 1
    </select>

</mapper>
package com.datalink.fdop.udf.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.udf.api.domain.Udf;
import com.datalink.fdop.udf.api.domain.UdfMenu;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/18 9:31
 */
public class DomainAgeUtils {

    public static String getUdfMenuAgeStr(UdfMenu udfMenu) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (udfMenu.getId() != null ? udfMenu.getId() : IdWorker.getId()))
                .append(", pid: " + (udfMenu.getPid() != null ? udfMenu.getPid() : -1))
                .append((StringUtils.isNotEmpty(udfMenu.getCode()) ? ", code: '" + udfMenu.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(udfMenu.getName()) ? ", name: '" + udfMenu.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(udfMenu.getDescription()) ? ", description: '" + udfMenu.getDescription() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(udfMenu.getCreateBy()) ? udfMenu.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (udfMenu.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(udfMenu.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getUdfAgeStr(Udf udf) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (udf.getId() != null ? udf.getId() : IdWorker.getId()))
                .append(", pid: " + (udf.getPid() != null ? udf.getPid() : -1))
                .append((StringUtils.isNotEmpty(udf.getCode()) ? ", code: '" + udf.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(udf.getName()) ? ", name: '" + udf.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(udf.getDescription()) ? ", description: '" + udf.getDescription() + "'" : ""))
                .append((udf.getUdfType() != null ? ", udfType: '" + udf.getUdfType() + "'" : ""))
                .append((StringUtils.isNotEmpty(udf.getScriptInputParam()) ? ", scriptInputParam: '" + udf.getScriptInputParam() + "'" : ""))
                .append((udf.getResourceId() != null ? ", resourceId: " + udf.getResourceId() : ""))
                .append((StringUtils.isNotEmpty(udf.getScriptLogic()) ? ", scriptLogic: '" + udf.getScriptLogic() + "'" : ""))
                .append((StringUtils.isNotEmpty(udf.getScriptOutputParam()) ? ", scriptOutputParam: '" + udf.getScriptOutputParam() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(udf.getCreateBy()) ? udf.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (udf.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(udf.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

}

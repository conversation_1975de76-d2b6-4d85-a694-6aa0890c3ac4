package com.datalink.fdop.gateway.filter;

import com.datalink.fdop.common.core.constant.CacheConstants;
import com.datalink.fdop.common.core.constant.HttpStatus;
import com.datalink.fdop.common.core.constant.SecurityConstants;
import com.datalink.fdop.common.core.constant.TokenConstants;
import com.datalink.fdop.common.core.utils.JwtUtils;
import com.datalink.fdop.common.core.utils.ServletUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.redis.service.RedisService;
import com.datalink.fdop.gateway.config.properties.IgnoreWhiteProperties;
import com.datalink.fdop.gateway.service.LicenseVerificationService;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 网关鉴权
 *
 * <AUTHOR>
 */
@Component
public class AuthFilter implements GlobalFilter, Ordered {
    private static final Logger log = LoggerFactory.getLogger(AuthFilter.class);

    // 排除过滤的 uri 地址，nacos自行添加
    @Autowired
    private IgnoreWhiteProperties ignoreWhite;

    @Autowired
    private RedisService redisService;

    @Autowired
    private  LicenseVerificationService licenseService;


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // TODO:高松电子
        // // 获取当前日期
        // LocalDateTime currentDate = LocalDateTime.now();
        // // 指定比较日期
        // LocalDateTime targetDate = LocalDateTime.of(2023, 6, 18, 0, 0, 0);
        // // 比较当前日期是否s在目标日期之后
        // if (currentDate.isAfter(targetDate)) {
        //     throw new ServiceException("账号生效");
        // }

        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();

        String url = request.getURI().getPath();
        // 跳过不需要验证的路径
        if (StringUtils.matches(url, ignoreWhite.getWhites())) {
            log.info("[网关鉴权-白名单]请求路径----:{}", exchange.getRequest().getPath());
            return chain.filter(exchange);
        }
        log.info("[网关鉴权]请求路径:{}", exchange.getRequest().getPath());
        /*if (false) {
            // 校验接口权限
            RemotePermissionLogicService remotePermissionLogicService = exchange.getApplicationContext().getBean(RemotePermissionLogicService.class);
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            if (remotePermissionLogicService != null) {
                // WebFlux异步调用，同步会报错
                Future future = executorService.submit(() -> (R<List<ButtonPermissions>>) remotePermissionLogicService.selectAllNotButtonPermissionsList());
                try {
                    R<List<ButtonPermissions>> selectNotButtonPermissionsListR = (R<List<ButtonPermissions>>) future.get();
                    List<ButtonPermissions> buttonPermissionsList = selectNotButtonPermissionsListR.getData();
                    if (CollectionUtils.isNotEmpty(buttonPermissionsList)) {
                        // 获取接口集合
                        List<String> interfaceAddressList = buttonPermissionsList.stream().map(ButtonPermissions::getInterfaceAddress).collect(Collectors.toList());
                        // 调用无权限的按钮，报错
                        if (StringUtils.matches(url, interfaceAddressList)) {
                            return unauthorizedResponse(exchange, "按钮无权限，无法调用");
                        }
                    }
                } catch (Exception e) {
                    return unauthorizedResponse(exchange, "获取按钮权限失败");
                } finally {
                    executorService.shutdown();
                }
            }
        }*/

        String token = getToken(request);
        if (StringUtils.isEmpty(token)) {
            return unauthorizedResponse(exchange, "令牌不能为空");
        }
        Claims claims = JwtUtils.parseToken(token);
        if (claims == null) {
            return unauthorizedResponse(exchange, "令牌已过期或验证不正确！");
        }
        String userkey = JwtUtils.getUserKey(claims);
        boolean islogin = redisService.hasKey(getTokenKey(userkey));
        if (!islogin) {
            return unauthorizedResponse(exchange, "登录状态已过期");
        }
        String tenantId = JwtUtils.getTenantId(claims);
        String userid = JwtUtils.getUserId(claims);
        String username = JwtUtils.getUserName(claims);
        if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(userid) || StringUtils.isEmpty(username)) {
            return unauthorizedResponse(exchange, "令牌验证失败");
        }


        // 执行实时License验证
//        if (!licenseService.performFullValidation()) {
//            exchange.getResponse().setStatusCode(org.springframework.http.HttpStatus.FORBIDDEN);
//            exchange.getResponse().getHeaders().add("X-License-Error", "Invalid license");
//            return exchange.getResponse().setComplete();
//        }

        // 设置用户信息到请求
        addHeader(mutate, SecurityConstants.USER_KEY, userkey);
        addHeader(mutate, SecurityConstants.DETAILS_TENANT_ID, tenantId);
        addHeader(mutate, SecurityConstants.DETAILS_USER_ID, userid);
        addHeader(mutate, SecurityConstants.DETAILS_USERNAME, username);
        // 内部请求来源参数清除
        removeHeader(mutate, SecurityConstants.FROM_SOURCE);
        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }

    private void addHeader(ServerHttpRequest.Builder mutate, String name, Object value) {
        if (value == null) {
            return;
        }
        String valueStr = value.toString();
        String valueEncode = ServletUtils.urlEncode(valueStr);
        mutate.header(name, valueEncode);
    }

    private void removeHeader(ServerHttpRequest.Builder mutate, String name) {
        mutate.headers(httpHeaders -> httpHeaders.remove(name)).build();
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String msg) {
        log.error("[鉴权异常处理]请求路径:{}", exchange.getRequest().getPath());
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.UNAUTHORIZED);
    }

    /**
     * 获取缓存key
     */
    private String getTokenKey(String token) {
        return CacheConstants.LOGIN_TOKEN_KEY + token;
    }

    /**
     * 获取请求token
     */
    private String getToken(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(TokenConstants.AUTHENTICATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, StringUtils.EMPTY);
        }
        return token;
    }

    @Override
    public int getOrder() {
        return -200;
    }
}
package com.datalink.fdop.quality.api;

import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.quality.api.factory.RemoteQualityFallbackFactory;
import com.datalink.fdop.quality.api.model.dto.DataEntityQuality;
import com.datalink.fdop.quality.api.model.vo.CheckRegexVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(contextId = "remoteQualityService", value = ServiceNameConstants.DATA_SERVICE,
        path = "/quality", fallbackFactory = RemoteQualityFallbackFactory.class)
public interface RemoteQualityService {

    @PostMapping(value = "/update")
    public R update(@RequestBody DataQuality dataQuality);

    @GetMapping(value = "/selectById/{id}")
    public R<DataQuality> selectById(@PathVariable("id") Long id);

    @GetMapping(value = "/getRegex/{id}")
    public R<String> getRegex(@PathVariable("id") Long id, @RequestParam(value = "prefix") String prefix);

    @PostMapping(value = "/checkRegex")
    public R<Boolean> checkRegex(@RequestBody CheckRegexVo checkRegexVo);

    @GetMapping(value = "/parseEntityQuality/{dataEntityId}")
    public R<List<DataEntityQuality>> parseEntityQuality(@PathVariable(value = "dataEntityId") Long dataEntityId);

    @GetMapping(value = "/parseElementQuality/{dataElementId}")
    public R<List<DataEntityQuality>> parseElementQuality(@PathVariable(value = "dataElementId") Long dataElementId);

}

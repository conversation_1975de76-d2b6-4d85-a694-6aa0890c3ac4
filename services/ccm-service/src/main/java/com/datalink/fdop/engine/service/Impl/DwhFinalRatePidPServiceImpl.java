package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRatePidP;
import com.datalink.fdop.engine.mapper.DwhFinalRatePidPMapper;
import com.datalink.fdop.engine.service.DwhFinalRatePidPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRatePidPServiceImpl extends ServiceImpl<DwhFinalRatePidPMapper, DwhFinalRatePidP> implements DwhFinalRatePidPService {

    @Autowired
    private DwhFinalRatePidPMapper dwhFinalRatePidPMapper;

    @Override
    public PageDataInfo<DwhFinalRatePidP> overview(
            String verId,
            String plantId,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo) {
        Page<DwhFinalRatePidP> page = PageUtils.getPage(DwhFinalRatePidP.class);
        IPage<DwhFinalRatePidP> iPage = dwhFinalRatePidPMapper.selectAll(verId, plantId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRatePidP> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwhFinalRatePidPMapper.selectNoPage(verId, plantId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewVarcost;
import com.datalink.fdop.engine.mapper.DwsNewVarcostMapper;
import com.datalink.fdop.engine.service.DwsNewVarcostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewVarcostServiceImpl extends ServiceImpl<DwsNewVarcostMapper, DwsNewVarcost> implements DwsNewVarcostService {

    @Autowired
    private DwsNewVarcostMapper dwsNewVarcostMapper;

    @Override
    public PageDataInfo<DwsNewVarcost> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwsNewVarcost> page = PageUtils.getPage(DwsNewVarcost.class);
        IPage<DwsNewVarcost> iPage = dwsNewVarcostMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewVarcost> listAll() {
        return dwsNewVarcostMapper.listAll();
    }

    @Override
    public List<DwsNewVarcost> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwsNewVarcostMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
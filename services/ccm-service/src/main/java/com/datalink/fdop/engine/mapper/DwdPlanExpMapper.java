package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwdPlanExp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwdPlanExpMapper extends BaseMapper<DwdPlanExp> {

    IPage<DwdPlanExp> selectAll(
            @Param("verId") String verId, @Param("companyId") String companyId,
            @Param("yearMonthFrom") String yearMonthFrom, @Param("yearMonthTo") String yearMonthTo,
            @Param("page") IPage<DwdPlanExp> page, @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwdPlanExp> selectNoPage(
            @Param("verId") String verId, @Param("companyId") String companyId,
            @Param("yearMonthFrom") String yearMonthFrom, @Param("yearMonthTo") String yearMonthTo,
            @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);
}
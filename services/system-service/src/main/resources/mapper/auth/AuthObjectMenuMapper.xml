<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.auth.mapper.AuthObjectMenuMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="AuthObjectMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>




    <select id="save" parameterType="string" resultType="int">
        select count(1)
        from ag_catalog.cypher('zjdata_graph', $$ CREATE (u:a_m_auth_object_menu ${ageStr}) RETURN id(u), properties(u)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype);
    </select>

    <select id="findByCode" parameterType="java.lang.String" resultMap="vlabelItem">
         SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
             MATCH(n:a_m_auth_object_menu)
                WHERE n.code='${code}'
             RETURN id(n),properties(n)
                $$)
            as(id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="findAll" parameterType="java.lang.String" resultType="com.datalink.fdop.auth.vo.AuthObjectTreeVo">
        WITH graph_query as (
        SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:a_m_auth_object_menu)
        WITH n
        ORDER BY n.code
        <if test="sort != null and sort != ''">
            ${sort}
        </if>
        return  n.id ,n.code,n.pid,n.name,n.description
        $$)
        as(id TEXT,code TEXT,pid TEXT,name TEXT,description TEXT))
        SELECT  	id,
        code,
        pid,
        description,
        name,
        'MENU' as menuType
        FROM graph_query
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.auth.api.domain.AuthObjectMenu"  resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:a_m_auth_object_menu {id: ${id}})
        <set>
            <if test="pid != null ">u.pid = ${pid},</if>
            <if test="name != null and name != ''">u.name = '${name}',</if>
            <if test="code != null  and code != ''">u.code = '${code}',</if>
            <if test="description != null and  description != ''">u.description = '${description}',</if>
        </set>
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="deleteById" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:a_m_auth_object_menu)
        WHERE u.id =${id}
        DETACH DELETE u
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findAuthGroupMenu" resultMap="vlabelItem">
        SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
	    MATCH(n:a_m_auth_object_menu)
        <if test="conditionSql != null and conditionSql != ''">
            where ${conditionSql}
        </if>
	    RETURN id(n),properties(n)
        ORDER BY id(n)
        <if test="pagingSql != null and pagingSql != ''">
            ${pagingSql}
        </if>
        $$)
    as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findAuthObjectMenuTotal" resultType="int">
        SELECT count(1) FROM ag_catalog.cypher( 'zjdata_graph',$$
	    MATCH(n:a_m_auth_object_menu)
        <if test="conditionSql != null and conditionSql != ''">
            where ${conditionSql}
        </if>
	    RETURN id(n),properties(n)
        ORDER BY id(n)
        $$)
    as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="deleteAllEdgeById" resultType="int">
        SELECT count(1) FROM ag_catalog.cypher( 'zjdata_graph',$$
	     MATCH (g:a_m_auth_object )- [r:node_menu_edge]-(m:a_m_auth_object_menu)
		WHERE m.id in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
   DETACH DELETE g,m,r
$$) as (a agtype)
    </select>


    <select id="saveMenuRelation" resultType="int">
        SELECT count(1) FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH (o:a_m_auth_object_menu), (m:a_m_auth_object_menu)
            WHERE o.id = ${id} AND m.id = ${pid}
            CREATE om = ((o)-[:menu_menu_edge {startTable:'a_m_auth_object_menu',endTable:'a_m_auth_object_menu'}]->(m)-[:menu_menu_edge {startTable:'a_m_auth_object_menu',endTable:'a_m_auth_object_menu'}]->(o))
            RETURN om
        $$) as (om agtype)
    </select>

    <select id="findMenuByPidAndDelRelation" resultMap="vlabelItem">
        SELECT  FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:a_m_auth_object_menu)-[r:menu_menu_edge]-(m:a_m_auth_object_menu)
        where n.id=${id} and m.pid=${id}
        DETACH DELETE r
        RETURN DISTINCT id(m),properties(m)
        $$)
        as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="delRelationByPid" resultType="int">
        SELECT count (1) FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:a_m_auth_object_menu)-[r:menu_menu_edge]-(m:a_m_auth_object_menu)
        where n.id=${id} and m.id=${pid}
       DETACH DELETE r
        $$)
        as (a ag_catalog.agtype)
    </select>
    <select id="findById" resultMap="vlabelItem">
        SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
             MATCH(n:a_m_auth_object_menu)
                WHERE n.id=${id}
             RETURN id(n),properties(n)
                $$)
            as(id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:a_m_auth_object_menu) -[mme:menu_menu_edge]->(menuE:a_m_auth_object_menu)
            WHERE menuE.pid = ${pid}
            RETURN DISTINCT (menuE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById"  resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:a_m_auth_object_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createAuthObjectMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:a_m_auth_object_menu), (menuE:a_m_auth_object_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'a_m_auth_object_menu',endTable:'a_m_auth_object_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'a_m_auth_object_menu',endTable:'a_m_auth_object_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:a_m_auth_object_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.PostgresMapper.EventRuleMapper">

    <select id="list" resultType="com.datalink.fdop.base.api.domain.EventRules">
        select * from zjdata.ccm_event_rules where del_flag = false
        order by control_area_id, trigger_code, change_flag desc
    </select>

</mapper>
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.engine.api.domain.DwdRbom;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdRbomService extends IService<DwdRbom> {

    PageDataInfo<DwdRbom> overview(String plantId,String recipeId, String sort, SearchVo searchVo);

    List<DwdRbom> selectNoPage(String plantId, String recipeId, String sort, SearchVo searchVo);

    List<DwdRbom> listAll();
}

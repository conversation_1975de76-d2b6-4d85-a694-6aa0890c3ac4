package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.EvaluateArea;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-09-05 11:47
 */
public interface EvaluateAreaService extends IService<EvaluateArea> {

    Page<EvaluateArea> pageList(Page<EvaluateArea> page, String a, String sort);

    String checkIdUnique(String code);

    int deleteByIds(List<String> ids);

    PageDataInfo<EvaluateArea> queryAll();

    boolean updateBatchStatus(List<EvaluateArea> statusList);

    String importData(List<EvaluateArea> list,Boolean isUpdateSupport, String operName);
}

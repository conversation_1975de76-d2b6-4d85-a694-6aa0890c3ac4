package com.datalink.fdop.gather.api.model;

import com.datalink.fdop.gather.api.enums.ReadBelowEndType;
import com.datalink.fdop.gather.api.enums.ReadType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/18 10:33
 */
@Data
public class TemplateFieldConfig {

    @ApiModelProperty(value = "列文本")
    private String fieldText;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "读取方式")
    private ReadType readType;

    @ApiModelProperty(value = "列位置")
    private String belowPosition;

    @ApiModelProperty(value = "开始位置")
    private String startPosition;

    @ApiModelProperty(value = "向下时结束类型")
    private ReadBelowEndType endType;

    @ApiModelProperty(value = "结束位置")
    private String endPosition;

}

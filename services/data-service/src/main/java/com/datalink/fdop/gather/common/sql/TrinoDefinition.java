package com.datalink.fdop.gather.common.sql;

import cn.hutool.core.collection.CollectionUtil;
import com.datalink.fdop.auth.api.vo.CalculateValueVo;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.SpringUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.element.api.RemoteElementService;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import com.datalink.fdop.gather.api.domain.Template;
import com.datalink.fdop.gather.api.domain.TemplateField;
import com.datalink.fdop.gather.utils.FieldCheckUtil;
import com.datalink.fdop.gather.utils.JointSqlUtils;
import com.datalink.fdop.gather.utils.TmpTableUtils;
import com.datalink.fdop.permissions.api.model.vo.PermissionsElementVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/7/8 16:01
 */
@Slf4j
public class TrinoDefinition extends SqlDefinition {

    @Override
    public Map<String, Object> check(Object value, TemplateField templateField) {
        Map<String, Object> checkMap = new HashMap<>();
        checkMap.put(TmpTableUtils.VERIFY, true);
        checkMap.put(TmpTableUtils.LOG, "");

        // 验证类型
        Map<String, Object> checkTypeMap = checkType(value, templateField);
        Boolean checkTypeVerify = MapUtils.getBoolean(checkTypeMap, TmpTableUtils.VERIFY);
        if (!checkTypeVerify) {
            return checkTypeMap;
        }

        // 验证长度
        Map<String, Object> checkLengthMap = checkLength(value, templateField);
        Boolean checkLengthVerify = MapUtils.getBoolean(checkLengthMap, TmpTableUtils.VERIFY);
        if (!checkLengthVerify) {
            return checkLengthMap;
        }

        return checkMap;
    }

    @Override
    public SqlDefinition checkIsPk(String databaseName, String tableName, Map<String, Object> pkMap) {
        StringBuffer sql = new StringBuffer();
        sql.append("select count(1) as count from " + databaseName + "." + tableName + " where ");
        for (Map.Entry<String, Object> entry : pkMap.entrySet()) {
            sql.append("\"" + entry.getKey() + "\" = " + entry.getValue().toString() + " and ");
        }

        this.setSql(new StringBuffer(sql.substring(0, sql.length() - 5)));
        return this;
    }

    @Override
    public SqlDefinition checkIsPk(String databaseName, String tableName, String fieldName, Object value) {
        StringBuffer sql = new StringBuffer();
        sql.append("select count(1) from " + databaseName + "." + tableName + " where " + fieldName + " = '" + value.toString() + "'");
        this.setSql(sql);
        return this;
    }

    @Override
    public Map<String, Object> checkType(Object value, TemplateField templateField) {
        Map<String, Object> result = new HashMap<>();
        if (value == null) {
            result.put(TmpTableUtils.VERIFY, true);
            result.put(TmpTableUtils.LOG, "");
            return result;
        }
        FieldType fieldType = templateField.getFieldType();
        String valueStr = value.toString();
        Boolean verify = true;
        String log = "";
        switch (fieldType) {
            case 整数类型:
            case 短整数类型:
            case 长整数类型:
                verify = valueStr.matches("-?[0-9]+([0-9]+)?");
                if (!verify) {
                    log = "[{" + templateField.getFieldText() + "}]只能是数字";
                }
                break;
            case 浮点类型:
            case 长浮点类型:
            case 数值类型:
                verify = valueStr.matches("-?[0-9]+(\\.[0-9]+)?");
                if (!verify) {
                    log = "[{" + templateField.getFieldText() + "}]只能是数字或小数";
                }
                break;
            case 字符类型:
            case 文本类型:
                break;
            case 时间类型:
            case 时戳类型:
                try {
                    DateUtils.parseDate(valueStr, "yyyy-MM-dd HH:mm:ss");
                } catch (Exception e) {
                    verify = false;
                    log = "[{" + templateField.getFieldText() + "}]:" + e.getMessage();
                }
                break;
            case 时刻类型:
                try {
                    DateUtils.parseDate(valueStr, "HH:mm:ss");
                } catch (Exception e) {
                    verify = false;
                    log = "时间解析异常[{" + templateField.getFieldText() + "}],只能是[HH:mm:ss]";
                }
                break;
            case 日期类型:
                try {
                    DateUtils.parseDate(valueStr, "yyyy-MM-dd");
                } catch (Exception e) {
                    verify = false;
                    log = "[{" + templateField.getFieldText() + "}]:" + e.getMessage();
                }
                break;
            case 布尔类型:
                try {
                    Integer integer = Integer.valueOf(valueStr);
                    if (!integer.equals(0) && !integer.equals(1)) {
                        verify = false;
                        log = "[{" + templateField.getFieldText() + "}]的值只能是true|false|0|1";
                    }
                } catch (Exception e) {
                    if (!"true".equalsIgnoreCase(valueStr) || "false".equalsIgnoreCase(valueStr)) {
                        verify = false;
                        log = "[{" + templateField.getFieldText() + "}]的值只能是true|false|0|1";
                    }
                }
                break;
            case 二进制类型:
                break;
        }

        result.put(TmpTableUtils.VERIFY, verify);
        result.put(TmpTableUtils.LOG, StringUtils.isNotEmpty(log) ? log + "\n" : log);
        return result;
    }

    @Override
    public Map<String, Object> checkLength(Object value, TemplateField templateField) {
        Map<String, Object> result = new HashMap<>();
        if (value == null) {
            result.put(TmpTableUtils.VERIFY, true);
            result.put(TmpTableUtils.LOG, "");
            return result;
        }
        FieldType fieldType = templateField.getFieldType();
        String valueStr = value.toString();
        Boolean verify = true;
        String log = "";
        switch (fieldType) {
            case 数值类型:
                // String precision = "";
                // String scale = "";
                // if (valueStr.contains(".")) {
                //     String[] split = valueStr.split("\\.");
                //     precision = split[0];
                //     scale = split[1];
                // } else {
                //     precision = valueStr;
                // }
                //
                // if (precision.length() + scale.length() > templateField.getLength()) {
                //     verify = false;
                //     log = "[{" + templateField.getFieldText() + "}]只能存储最多" + templateField.getLength() + "位数字";
                //     break;
                // }
                //
                // if (scale.length() > templateField.getDecimalLength()) {
                //     verify = false;
                //     log = "[{" + templateField.getFieldText() + "}]超过小数长度:" + templateField.getDecimalLength();
                //     break;
                // }
                break;
            case 字符类型:
                if (valueStr.length() > templateField.getLength()) {
                    verify = false;
                    log = "[{" + templateField.getFieldText() + "}]超过总长度:" + templateField.getLength();
                    break;
                }
                break;
            case 文本类型:
            case 短整数类型:
            case 整数类型:
            case 长整数类型:
            case 浮点类型:
            case 长浮点类型:
            case 时间类型:
            case 时戳类型:
            case 日期类型:
            case 时刻类型:
            case 布尔类型:
            case 二进制类型:
                break;
        }

        result.put(TmpTableUtils.VERIFY, verify);
        result.put(TmpTableUtils.LOG, StringUtils.isNotEmpty(log) ? log + "\n" : log);
        return result;
    }

    @Override
    public SqlDefinition templateEntityTableInsertSql(List<TemplateField> fields,
                                                      List<Map<String, Object>> values,
                                                      String databaseName,
                                                      String tableName) {
        StringBuffer sql = new StringBuffer();
        sql.append("insert into ");
        sql.append(databaseName + "." + tableName + " (");
        //拼列
        String columnSql = "";
        String valSqls = "";
        List<Map<String, Object>> vars = values;
        // for (Map<String, Object> value : values) {
        //     if (fieldCheckUtil.checkField(value, fields, template, calculateValueVos)) {
        //         vars.add(value);
        //     } else {
        //         throw new ServiceException("权限校验失败");
        //     }
        // }
        // for (Map<String, Object> value : vars) {
        //     for (TemplateField field : fields) {
        //         if (value.get(field.getFieldText()) != null && !value.get(field.getFieldText()).equals("")) {
        //             for (DataEntityTableMapping mapping : mappings) {
        //                 if (field.getRelevanceId().equals(mapping.getFieldId())) {
        //                     columnSql += mapping.getFieldName() + ",";
        //                 }
        //             }
        //         }
        //     }
        //     break;
        // }
        columnSql = columnSql.substring(0, columnSql.length() - 1);
        sql.append(columnSql + ") values ");

        if (CollectionUtils.isEmpty(vars)) {
            throw new ServiceException("添加数据无权限,请联系管理员配置数据权限");
        }
        for (Map<String, Object> value : vars) {
            String valSql = "(";
            Object var = null;
            for (TemplateField field : fields) {
                var = value.get(field.getFieldText());
                if (var != null && !var.equals("")) {
                    Object val = value.get(field.getFieldText());
                    if (val == null) {
                        val = "";
                    }
                    valSql += "'" + val + "',";
                }
            }
            if (valSql.length() - 1 > 0) {
                valSqls += valSql.substring(0, valSql.length() - 1) + "),";
            }
        }
        sql.append(valSqls.substring(0, valSqls.length() - 1));
        this.setSql(sql);
        return this;
    }

    @Override
    public SqlDefinition entityTableInsertSql(List<TemplateField> fields, List<Map<String, Object>> values, String databaseName, String tableName) {
        return null;
    }

    @Override
    public SqlDefinition templateEntityTableInsert2TargetSql(String batchId, List<Field> fieldList, String tmpDatabaseName, String tmpTableName, String databaseName, String tableName) {
        StringBuffer sql = new StringBuffer();
        sql.append("insert into ");
        sql.append(databaseName + "." + tableName + " select ");
        // 转换成目标表的字段类型
        String columnSql = fieldList.stream()
                .map(field -> "cast(" + field.getFieldName() + " as " + field.getFieldType() + ")")
                .collect(Collectors.joining(","));
        sql.append(columnSql);

        sql.append(" from " + tmpDatabaseName + "." + tmpTableName + " where \"" + TmpTableUtils.BATCHID + "\" = '" + batchId + "' and " + TmpTableUtils.VERIFY + " = 'true'");
        this.setSql(sql);
        return this;
    }

    @Override
    public SqlDefinition templateEntityTableUpdateSql(List<TemplateField> fields,
                                                      List<Map<String, Object>> values,
                                                      String databaseName,
                                                      String tableName) {
        StringBuffer sql = new StringBuffer();
        for (Map<String, Object> value : values) {
            String pk = MapUtils.getString(value, "pk");
            sql.append("update  ");
            sql.append(databaseName + "." + tableName + " set ");
            String updateSql = "";
            String updateSqlfragment = "";
            for (TemplateField field : fields) {
                // for (DataEntityTableMapping mapping : mappings) {
                //     if (field.getRelevanceId().equals(mapping.getFieldId())) {
                Object val = value.get(field.getFieldText());
                updateSqlfragment += " " + field.getMapFieldName() + "='" + val + "',";
                // }
                // }
            }
            updateSql += updateSqlfragment.substring(0, updateSqlfragment.length() - 1) + ",";
            sql.append(updateSql.substring(0, updateSql.length() - 1));
            sql.append(" where ");
            sql.append(pk.replaceAll(",", " and "));
            sql.append(";");
        }
        sql.substring(0, sql.length() - 1);
        this.setSql(sql);
        return this;
    }

    @Override
    public SqlDefinition templateEntityTableDeleteSql(List<TemplateField> templateFieldList, List<Map<String, Object>> values, String databaseName, String tableName) {
        StringBuffer sql = new StringBuffer();
        sql.append("delete from ");
        sql.append(databaseName + "." + tableName + " where ");
        String where = "";
        for (Map<String, Object> value : values) {
            String delSql = " (";
            for (TemplateField templateField : templateFieldList) {
                if (!templateField.getIsPk()) {
                    continue;
                }
                String type = "";
                switch (templateField.getFieldType()) {
                    case 整数类型:
                    case 长整数类型:
                        type = "INTEGER";
                        break;
                    case 数值类型:
                        type = "DECIMAL(" + templateField.getLength() + "," + templateField.getDecimalLength() + ")";
                        break;
                    case 浮点类型:
                        type = "FLOAT";
                        break;
                    case 长浮点类型:
                        type = "DOUBLE";
                        break;
                    default:
                        type = "VARCHAR";
                        break;
                }
                delSql += "\"" + templateField.getMapFieldName() + "\" = CAST('" + MapUtils.getString(value, templateField.getMapFieldName()) + "' AS " + type + ") and ";
            }
            delSql = delSql.substring(0, delSql.length() - 5);
            where += delSql + ") or";
        }
        sql.append(where.substring(0, where.length() - 2));
        this.setSql(sql);
        return this;
    }

    @Override
    public String templateEntityTableSelectSql(List<DataEntityTableMapping> mappings,
                                               List<CalculateValueVo> calculateValueVos,
                                               List<TemplateField> fieldInfo,
                                               DataEntityTable entityTable,
                                               FieldCheckUtil fieldCheckUtil,
                                               Template template, SearchVo searchVo,
                                               Boolean flag, List<PermissionsElementVo> permissionsElementVos) {
        RemoteElementService remoteElementService = SpringUtils.getBean(RemoteElementService.class);
        StringBuffer sql1 = new StringBuffer();
        sql1.append("select * from ");
        // TODO:暂时写死
        sql1.append("iceberg." + entityTable.getDatabaseName() + "." + entityTable.getTableName() + " as n where 1=1");
        List<String> fields = fieldInfo.stream().map(e -> e.getFieldText()).collect(Collectors.toList());
        String sql = sql1.toString();
        if (!CollectionUtil.isEmpty(permissionsElementVos)) {
            Map<String, String> primaryValues = Maps.newHashMap();
            //先添加单主键
            String sqlJoint = "";

            for (PermissionsElementVo permissionsElementVo : permissionsElementVos) {
                if (!fields.contains(permissionsElementVo.getCode())) {
                    continue;
                }
                String primary = permissionsElementVo.getCode();
                //拼接参数
                sqlJoint += " or (" + JointSqlUtils.jointSinglePrimarySql(permissionsElementVo) + ")";
                primaryValues.put(primary, sqlJoint);
            }
            if (MapUtils.isNotEmpty(primaryValues)) {
                sql = sql.substring(0, sql.length() - 4);
                int i = 0;
                for (Map.Entry<String, String> entry : primaryValues.entrySet()) {
                    String value = entry.getValue();
                    if (i == 0) {
                        value = value.substring(3);
                    }
                    sql += value;
                    i++;
                }
            }
        } else {
            if (!flag) {
                sql = sql + " and 1=2 ";
            }
        }


        //先拼接多主键
//        String whereSql = " ";
//        if (CollectionUtils.isNotEmpty(calculateValueVos)) {
//            List<CalculateValueVo> morePs = calculateValueVos.stream().filter(calculateValueVo -> calculateValueVo.isMorePrimary()).collect(Collectors.toList());
//            List<String> primaryKeyAll = Lists.newArrayList();
//            for (CalculateValueVo moreP : morePs) {
//                List<String> primaryKeys = moreP.getPrimaryKey();
//                //找到主键列的code
//                Map<String, String> primaryColumn = Maps.newHashMap();
//                //先判断字段权限是否包含多主键
//                Boolean falg = false;
//                for (TemplateField templateField : fieldInfo) {
//                    if (templateField.getElementId().equals(moreP.getElementId())) {
//                        falg = true;
//                    }
//                }
//                if (falg) {
//                    fieldInfo.stream().forEach(field -> {
//                        //先判断权限是否包含多主键
//                        primaryKeys.stream().forEach(primaryKey -> {
//                            //通过元素的code获取元素
//                            R<DataElement> r = remoteElementService.selectByCode(primaryKey);
//                            if (r.getCode() == 200 && r.getData() == null) {
//                                throw new ServiceException("权限绑定元素不存在");
//                            }
//                            DataElement data = r.getData();
//                            if (moreP.getElementId().equals(field.getElementId()) && data.getId().equals(field.getElementId())) {
//                                for (DataEntityTableMapping mapping : mappings) {
//                                    if (mapping.getFieldId().equals(field.getRelevanceId())) {
//                                        primaryColumn.put(primaryKey, mapping.getFieldName());
//                                        primaryKeyAll.add(field.getCode());
//                                    }
//                                }
//
//                            }
//                        });
//
//                    });
//                    List<Map<String, Object>> moreVal = (List<Map<String, Object>>) moreP.getValue().get(moreP.getElementCode());
//                    if (CollectionUtils.isNotEmpty(moreVal)) {
//                        String condition = "(";
//                        for (Map<String, Object> map : moreVal) {
//                            String conditionFragment = "(";
//                            for (Map.Entry<String, String> entry : primaryColumn.entrySet()) {
//                                if (map.get(entry.getKey()) != null) {
//                                    conditionFragment += " " + entry.getValue() + "='" + map.get(entry.getKey()) + "' and";
//                                }
//                            }
//                            if (StringUtils.isNotEmpty(conditionFragment.substring(0, condition.length() - 1))) {
//                                condition = " " + conditionFragment.substring(0, condition.length() - 3) + ") or";
//                            }
//                        }
//                        if (StringUtils.isNotEmpty(condition.substring(0, condition.length() - 1))) {
//                            whereSql += " " + condition.substring(0, condition.length() - 2) + ") or";
//                        }
//                    }
//                }
//
//            }
//            //拼接单主键
//            List<CalculateValueVo> onePs = calculateValueVos.stream().filter(calculateValueVo -> !calculateValueVo.isMorePrimary()).collect(Collectors.toList());
//            for (CalculateValueVo oneP : onePs) {
//                String primaryKey = oneP.getElementCode();
//                if (!primaryKeyAll.contains(primaryKey)) {
//                    for (TemplateField field : fieldInfo) {
//                        String mappingField = "";
//                        R<DataElement> r = remoteElementService.selectByCode(primaryKey);
//                        if (r.getCode() == 200 && r.getData() == null) {
//                            throw new ServiceException("权限绑定元素不存在");
//                        }
//                        DataElement data = r.getData();
//                        if (data.getId().equals(field.getElementId())) {
//                            for (DataEntityTableMapping mapping : mappings) {
//                                if (mapping.getFieldId().equals(field.getRelevanceId())) {
//                                    mappingField = mapping.getFieldName();
//                                }
//                            }
//                            List<Object> oneVals = (List<Object>) oneP.getValue().get(oneP.getElementCode());
//                            for (Object oneVal : oneVals) {
//                                whereSql += " " + mappingField + "='" + oneVal + "' or";
//                            }
//                        }
//                    }
//                }
//            }
//
//
//        } else {
//            if (!flag) {
//                whereSql += " 1=2 or";
//            }
//        }
//        if (!flag) {
//            whereSql += " 1=2 or";
//        }
//        sql.append(" where 1=1 ");
//        if (StringUtils.isNotEmpty(whereSql)) {
//            whereSql = whereSql.substring(0, whereSql.length() - 2);
//            sql.append(" and (");
//            sql.append(whereSql);
//            sql.append(" ) ");
//        }
        if (searchVo != null) {
            String a = SearchUtils.parseSqlSearchCondition("n", searchVo);
            sql = sql + " and ( " + a + ")";
        }

        //拼接列权限
        if (CollectionUtils.isEmpty(fieldInfo)) {
            return sql;
        }
        String column = "";
        for (TemplateField templateField : fieldInfo) {
            for (DataEntityTableMapping mapping : mappings) {
                if (templateField.getRelevanceId().equals(mapping.getFieldId())) {
                    column += "\"" + mapping.getFieldName() + "\" as \"" + templateField.getFieldText() + "\",";

                }
            }
        }
        if (StringUtils.isNotEmpty(column)) {
            column = column.substring(0, column.length() - 1);
            return sql.replace("*", column);
        }
        return sql;
    }

    @Override
    public SqlDefinition templateEntityTableExcelUpdateSql(List<DataEntityTableMapping> mappings, List<CalculateValueVo> calculateValueVos, List<TemplateField> fields, Map<String, Object> value, DataEntityTable entityTable, FieldCheckUtil fieldCheckUtil, Template template) {

        StringBuffer sql = new StringBuffer();
        if (fieldCheckUtil.checkField(value, fields, template, calculateValueVos)) {
            sql.append("update  ");
            sql.append(entityTable.getDatabaseName() + "." + entityTable.getTableName() + " set ");
            String updateSql = "";
            String updateSqlfragment = "";
            List<TemplateField> fieldList = fields.stream().filter(field -> !field.getIsPk()).collect(Collectors.toList());
            for (TemplateField field : fieldList) {
                for (Map.Entry<String, Object> entry : value.entrySet()) {
                    if (entry.getKey().equals(field.getFieldText())) {
                        for (DataEntityTableMapping mapping : mappings) {
                            if (field.getRelevanceId().equals(mapping.getFieldId())) {
                                Object val = value.get(field.getCode());
                                updateSqlfragment += " " + mapping.getFieldName() + "='" + val + "',";
                            }
                        }
                    }
                }
            }
            updateSql += updateSqlfragment.substring(0, updateSqlfragment.length() - 1) + ",";
            sql.append(updateSql.substring(0, updateSql.length() - 1));
            sql.append(" where ");
            String whereSql = "";
            List<TemplateField> templateFields = fields.stream().filter(field -> field.getIsPk()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(templateFields)) {
                throw new ServiceException("未获取到主键列");
            }
            String whereSqlfragment = "";
            for (TemplateField field : templateFields) {
                for (Map.Entry<String, Object> entry : value.entrySet()) {
                    if (entry.getKey().equals(field.getFieldText())) {
                        for (DataEntityTableMapping mapping : mappings) {
                            if (field.getRelevanceId().equals(mapping.getFieldId())) {
                                Object val = value.get(field.getFieldText());
                                whereSqlfragment += " " + mapping.getFieldName() + "='" + val + "' and";
                            }
                        }
                    }
                }
            }
            whereSql = whereSqlfragment.substring(0, whereSqlfragment.length() - 3);
            sql.append(whereSql);
        } else {
            throw new ServiceException("权限校验失败");
        }
        this.setSql(sql);
        return this;
    }

    @Override
    public SqlDefinition templateEntityTableExcelDelDataSql(DataEntityTable entityTable) {
        StringBuffer sql = new StringBuffer();
        sql.append("delete from ");
        sql.append(entityTable.getDatabaseName() + "." + entityTable.getTableName());
        sql.append(" where 1=1 ");
        this.setSql(sql);
        return this;
    }

    @Override
    public SqlDefinition templateEntityTableExcelDelDataSql(String databaseName, String tableName) {
        StringBuffer sql = new StringBuffer();
        sql.append("delete from ");
        sql.append(databaseName + "." + tableName);
        sql.append(" where 1=1 ");
        this.setSql(sql);
        return this;
    }

    @Override
    public String jointPaging(String sql, Integer pageNo, Integer pageSize) {
        sql = "select * from (" + sql + ") aabb where 1 = 1 offset " + (pageNo - 1) * pageSize + " limit " + pageSize;
        return sql;
    }

    @Override
    public String templateEntityTableSelectPriMarySql(List<DataEntityTableMapping> mappings, List<TemplateField> fields, Map<String, Object> val, DataEntityTable entityTable, Template template) {
        StringBuffer sql = new StringBuffer();
        sql.append("select * from ");
        sql.append(entityTable.getDatabaseName() + "." + entityTable.getTableName() + " ");
        sql.append(" where ");
        String whereSql = "";
        List<TemplateField> templateFields = fields.stream().filter(field -> field.getIsPk()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(templateFields)) {
            throw new ServiceException("未获取到主键列");
        }
        String whereSqlfragment = "";
        for (TemplateField field : templateFields) {
            for (DataEntityTableMapping mapping : mappings) {
                if (field.getRelevanceId().equals(mapping.getFieldId())) {
                    Object value = val.get(field.getFieldText());
                    whereSqlfragment += " " + mapping.getFieldName() + "='" + value + "' and";
                }
            }
        }
        whereSql = whereSqlfragment.substring(0, whereSqlfragment.length() - 3);
        sql.append(whereSql);
        return sql.toString();
    }

    @Override
    public String templateEntityTableUpdateInsertSql(
            List<DataEntityTableMapping> mappings,
            List<CalculateValueVo> calculateValueVos,
            List<TemplateField> fields,
            Map<String, Object> value,
            DataEntityTable entityTable,
            FieldCheckUtil fieldCheckUtil,
            Template template) {
        StringBuffer sql = new StringBuffer();
        sql.append("insert into ");
        sql.append(entityTable.getDatabaseName() + "." + entityTable.getTableName() + " (");
        //拼列
        String columnSql = "";
        String valSqls = "";
        List<Map<String, Object>> vars = Lists.newArrayList();
        if (fieldCheckUtil.checkField(value, fields, template, calculateValueVos)) {
            vars.add(value);
        } else {
            throw new ServiceException("权限校验失败");
        }

        for (TemplateField field : fields) {
            if (value.get(field.getFieldText()) != null && !value.get(field.getFieldText()).equals("")) {
                for (DataEntityTableMapping mapping : mappings) {
                    if (field.getRelevanceId().equals(mapping.getFieldId())) {
                        Object val = value.get(field.getFieldText());
                        if (val != null) {
                            columnSql += "`" + mapping.getFieldName() + "`,";
                        }
                    }
                }
            }
        }

        columnSql = columnSql.substring(0, columnSql.length() - 1);
        sql.append(columnSql + ") values ");

        if (CollectionUtils.isEmpty(vars)) {
            throw new ServiceException("添加数据无权限,请联系管理员配置数据权限");
        }
        String valSql = "(";
        Object var = null;
        for (TemplateField field : fields) {
            var = value.get(field.getFieldText());
            if (var != null && !var.equals("")) {
                Object val = value.get(field.getFieldText());
                if (val != null) {
                    valSql += "'" + value.get(field.getFieldText()) + "',";
                }
            }
        }
        if (valSql.length() - 1 > 0) {
            valSqls += valSql.substring(0, valSql.length() - 1) + "),";
        }

        sql.append(valSqls.substring(0, valSqls.length() - 1));
        sql.append(" ON DUPLICATE KEY UPDATE ");
        String updateSql = "";
        String updateSqlfragment = "";
        List<TemplateField> fieldList = fields.stream().filter(field -> !field.getIsPk()).collect(Collectors.toList());
        for (TemplateField field : fieldList) {
            for (Map.Entry<String, Object> entry : value.entrySet()) {
                if (entry.getKey().equals(field.getFieldText())) {
                    for (DataEntityTableMapping mapping : mappings) {
                        if (field.getRelevanceId().equals(mapping.getFieldId())) {
                            Object val = value.get(field.getFieldText());
                            if (val != null) {
                                updateSqlfragment += " `" + mapping.getFieldName() + "`='" + val + "',";
                            }
                        }
                    }
                }
            }
        }
        updateSql += updateSqlfragment.substring(0, updateSqlfragment.length() - 1) + ",";
        sql.append(updateSql.substring(0, updateSql.length() - 1));
        return sql.toString();
    }

    @Override
    public String templateEntityTableUpdateInsertSql(
            List<TemplateField> fields,
            Map<String, Object> value,
            String databaseName,
            String tableName
    ) {
        StringBuffer sql = new StringBuffer();
        sql.append("insert into ");
        sql.append(databaseName + "." + tableName + " (");

        String valSqls = "";

        //拼列
        String columnSql = fields.stream()
                .map(templateField -> "\"" + templateField.getMapFieldName() + "\"")
                .collect(Collectors.joining(","));
        sql.append(columnSql + ") values ");


        String valSql = "(";
        Object var = null;
        for (TemplateField field : fields) {
            var = value.get(field.getFieldText());
            if (var != null && !var.equals("")) {
                Object val = value.get(field.getFieldText());
                if (val != null) {
                    valSql += "'" + value.get(field.getFieldText()) + "',";
                }
            }
        }
        if (valSql.length() - 1 > 0) {
            valSqls += valSql.substring(0, valSql.length() - 1) + "),";
        }

        sql.append(valSqls.substring(0, valSqls.length() - 1));
        sql.append(" ON DUPLICATE KEY UPDATE ");
        String updateSql = "";
        String updateSqlfragment = "";
        List<TemplateField> fieldList = fields.stream().filter(field -> !field.getIsPk()).collect(Collectors.toList());
        for (TemplateField field : fieldList) {
            Object val = value.get(field.getFieldText());
            if (val != null) {
                updateSqlfragment += " `" + field.getMapFieldName() + "`='" + val + "',";
            }
        }
        updateSql += updateSqlfragment.substring(0, updateSqlfragment.length() - 1) + ",";
        sql.append(updateSql.substring(0, updateSql.length() - 1));
        return sql.toString();
    }

    @Override
    public SqlDefinition selectGatherTmpTable(String tmpDatabaseName, String tableName, String batchId, Integer pageNo, Integer pageSize) {
        StringBuffer sql = new StringBuffer();
        sql.append("select * from " + tmpDatabaseName + "." + tableName + " where \"" + TmpTableUtils.BATCHID + "\" = '" + batchId + "' limit " + (pageNo - 1) * pageSize + "," + pageSize);
        this.setSql(sql);
        return this;
    }

    @Override
    public SqlDefinition selectTotal(String databaseName, String tableName, String batchId) {
        StringBuffer sql = new StringBuffer();
        sql.append("select count(1) as count from " + databaseName + "." + tableName + " where \"" + TmpTableUtils.BATCHID + "\" = '" + batchId + "'");
        this.setSql(sql);
        return this;
    }

    @Override
    public SqlDefinition templateEntityTableTruncateSql(String databaseName, String tableName) {
        StringBuffer sql = new StringBuffer();
        sql.append(String.format("TRUNCATE TABLE %s.%s", databaseName, tableName));
        this.setSql(sql);
        return this;
    }

}

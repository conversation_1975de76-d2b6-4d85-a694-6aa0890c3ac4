package com.datalink.fdop.data.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

public class MapExcelExportUtil {

    private static final Logger log = LoggerFactory.getLogger(MapExcelExportUtil.class);

    /**
     * 导出Map数据（动态列）的Excel
     */
    public static void exportMapDataWithDynamicColumns(HttpServletResponse response,
                                                       List<Map<String, Object>> dataList,
                                                       List<DynamicColumn> dynamicColumns,
                                                       String title) {
        try {

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 如果没有提供动态列，尝试从数据中自动推断列名
            if ((dynamicColumns == null || dynamicColumns.isEmpty()) && dataList != null && !dataList.isEmpty()) {
                dynamicColumns = new ArrayList<>();
                for (String key : dataList.get(0).keySet()) {
                    dynamicColumns.add(new DynamicColumn(key, key));
                }
            }

            // 如果 dynamicColumns 仍然为空，创建空的 Excel 文件
            if (dynamicColumns == null || dynamicColumns.isEmpty()) {
                List<List<String>> head = new ArrayList<>();
                head.add(Arrays.asList("暂无数据"));
                EasyExcel.write(response.getOutputStream())
                        .head(head)
                        .sheet(title)
                        .doWrite(new ArrayList<>());
                return;
            }

            // 构建表头
            List<List<String>> head = new ArrayList<>();
            for (DynamicColumn column : dynamicColumns) {
                head.add(Arrays.asList(column.getColumnDesc()));
            }
            // 构建数据
            List<List<Object>> excelData = new ArrayList<>();
            if (dataList != null) {
                for (Map<String, Object> rowData : dataList) {
                    List<Object> row = new ArrayList<>();
                    for (DynamicColumn column : dynamicColumns) {
                        Object value = rowData.get(column.getColumnName());
                        row.add(value);
                    }
                    excelData.add(row);
                }
            }
            // 创建样式策略
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = createCellStyleStrategy();

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream())
                    .head(head)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .registerWriteHandler(createColumnWidthStrategy())
                    .sheet(title)
                    .doWrite(excelData);
        } catch (Exception e) {
            log.error("Excel导出异常：", e);
            throw new RuntimeException("Excel导出失败", e);
        }
    }

    /**
     * 创建EasyExcel样式策略
     */
    private static HorizontalCellStyleStrategy createCellStyleStrategy() {
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置表头背景色为灰色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        // 设置表头字体
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 14);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置内容字体
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 13);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 设置内容居中对齐
        contentWriteCellStyle.setHorizontalAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 创建列宽策略 - 针对中文优化
     */
    private static WriteHandler createColumnWidthStrategy() {
        return new SheetWriteHandler() {
            @Override
            public void beforeSheetCreate(com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder writeWorkbookHolder,
                                        com.alibaba.excel.write.metadata.holder.WriteSheetHolder writeSheetHolder) {
            }

            @Override
            public void afterSheetCreate(com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder writeWorkbookHolder,
                                       com.alibaba.excel.write.metadata.holder.WriteSheetHolder writeSheetHolder) {
                org.apache.poi.ss.usermodel.Sheet sheet = writeSheetHolder.getSheet();
                for (int i = 0; i < 50; i++) {
                    sheet.setColumnWidth(i, 15 * 256);
                }
            }
        };
    }
}
package com.datalink.fdop.seatunnel.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * SeaTunnel CMD任务配置
 */
@Data
@NoArgsConstructor
@TableName(schema = "zjdata", value = "s_c_seatunnel_cmd")
@ApiModel("SeaTunnel CMD任务")
public class SeaTunnelCmd extends BaseField {

    @ApiModelProperty(value = "SeaTunnel任务ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "SeaTunnel任务ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private String jobId;

    @ApiModelProperty(value = "SeaTunnel任务菜单ID")
    @NotNull(message = "请选择一个菜单")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    /**
     * 菜单Code
     */
    private String menuName;

    @ApiModelProperty(value = "SeaTunnel任务名称")
    private String jobName;

    @ApiModelProperty(value = "SeaTunnel任务状态")
    private String status;

    @ApiModelProperty(value = "SeaTunnel任务执行配置")
    private String command;

    @ApiModelProperty(value = "SeaTunnel任务日志文件路径")
    private String logFilePath;

}

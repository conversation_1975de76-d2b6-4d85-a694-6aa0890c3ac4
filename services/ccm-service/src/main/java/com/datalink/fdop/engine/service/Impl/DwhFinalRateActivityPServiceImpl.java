package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRateActivityP;
import com.datalink.fdop.engine.mapper.DwhFinalRateActivityPMapper;
import com.datalink.fdop.engine.service.DwhFinalRateActivityPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRateActivityPServiceImpl extends ServiceImpl<DwhFinalRateActivityPMapper, DwhFinalRateActivityP> implements DwhFinalRateActivityPService {

    @Autowired
    private DwhFinalRateActivityPMapper dwhFinalRateActivityPMapper;

    @Override
    public PageDataInfo<DwhFinalRateActivityP> overview(
            String costCenterType,
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo)  {
        Page<DwhFinalRateActivityP> page = PageUtils.getPage(DwhFinalRateActivityP.class);
        IPage<DwhFinalRateActivityP> iPage = dwhFinalRateActivityPMapper.selectAll(
                costCenterType,controlAreaId, verId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRateActivityP> selectNoPage(String costCenterType, String controlAreaId, String verId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwhFinalRateActivityPMapper.selectNoPage(costCenterType, controlAreaId, verId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
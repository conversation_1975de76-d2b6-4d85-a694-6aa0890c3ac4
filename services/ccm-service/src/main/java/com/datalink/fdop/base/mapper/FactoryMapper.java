package com.datalink.fdop.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.base.api.domain.Factory;
import com.datalink.fdop.common.core.domain.SelectVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-09-05 14:55
 */

public interface FactoryMapper extends BaseMapper<Factory> {

    int checkFactoryIdUnique(@Param("id") String id);

    int deleteFactoryById(@Param("id") String id);

    int deleteFactoryByIds(@Param(value = "ids") List<String> ids);

    Page<Factory> selectFactoryList(IPage<Factory> page, @Param(value = "ft") Factory factory, @Param("sort") String sort);

    int updateCompanyByDeleteCompany(@Param(value = "companyId") String companyId);

    int updateCompanyByDeleteCompanys(@Param(value = "companyIds") List<String> companyIds);

    Factory selectById(@Param("id") String id);

    Page<Factory> query(IPage<Factory> page, @Param("searchVo") String searchVo, @Param("sort") String sort);

    boolean updateBatchStatus(@Param("list") List<Factory> list);

    String getControlAreaIdByPlantId(@Param("plantId") String plantId);

    List<SelectVo> listPlantId(@Param("orgTableName") String orgTableName);

}

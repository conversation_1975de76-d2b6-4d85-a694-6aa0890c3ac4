<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.ShipRequestHeadMapper">
    <select id="queryData" resultType="com.datalink.fdop.fscm.api.model.vo.ShipRequestShowVo">
        SELECT
        t3.*,
        t4.material_desc,
        t5.stock_p_description
        FROM
        (
        SELECT
        t1.ship_request_order_num,
        t1.sale_order_num,
        t1.ship_plan_order_num,
        t1.customer_purchase_order_num,
        t1.sale_to,
        t1.ship_to,
        t1.ship_to_addr,
        t1.ship_to_linkman,
        t1.ship_to_phone,
        t1.incoterms,
        t1.incoterms_position,
        t1.statu,
        t1.del_flag as del_flag_head,
        t1.temporary,
        t1.username,
        t1.paper_date,
        t1.rise_text,
        t2.ship_request_order_row_num,
        t2.ship_plan_order_row_num,
        t2.sale_order_row_num,
        t2.sale_order_plan_row_num,
        t2.del_flag,
        t2.delivery_date,
        t2.material_code,
        t2.device_name,
        t2.factory_code,
        t2.stock_p_code,
        t2.batch_number,
        t2.piece,
        t2.bin_num,
        t2.quantity_delivery,
        t2.basic_unit,
        t2.is_return
        FROM
        zjdata.f_d_ship_request_head t1 LEFT JOIN
        zjdata.f_d_ship_request_row t2
        ON
        t1.ship_request_order_num = t2.ship_request_order_num
        ) t3
        LEFT JOIN zjdata.p_d_material t4 ON t3.material_code = t4.material_code
        LEFT JOIN zjdata.org_stock_place t5 ON t3.stock_p_code=t5.stock_p_code
        where 1=1
        <if test="shipRequestQueryVo.shipRequestOrderNum !=null and shipRequestQueryVo.shipRequestOrderNum.size() !=0" >
            AND t3.ship_request_order_num
            IN
            <foreach collection="shipRequestQueryVo.shipRequestOrderNum" item="shipRequestOrderNum" open="(" close=")" separator="," >
                #{shipRequestOrderNum}
            </foreach>
        </if>
        <if test="shipRequestQueryVo.shipPlanOrderNum !=null and shipRequestQueryVo.shipPlanOrderNum.size() !=0" >
            AND t3.ship_plan_order_num  IN
            <foreach collection="shipRequestQueryVo.shipPlanOrderNum" item="shipPlanOrderNum" open="(" close=")" separator="," >
                #{shipPlanOrderNum}
            </foreach>
        </if>
        <if test="shipRequestQueryVo.saleOrderNum !=null and shipRequestQueryVo.saleOrderNum.size() !=0" >
            AND t3.sale_order_num  IN
            <foreach collection="shipRequestQueryVo.saleOrderNum" item="saleOrderNum" open="(" close=")" separator="," >
                #{saleOrderNum}
            </foreach>
        </if>
        <if test="shipRequestQueryVo.customerPurchaseOrderNum !=null and shipRequestQueryVo.customerPurchaseOrderNum.size() !=0" >
            AND t3.customer_purchase_order_num            IN
            <foreach collection="shipRequestQueryVo.customerPurchaseOrderNum" item="customerPurchaseOrderNum" open="(" close=")" separator="," >
                #{customerPurchaseOrderNum}
            </foreach>
        </if>
        <if test="shipRequestQueryVo.saleTo !=null and shipRequestQueryVo.saleTo.size() !=0" >
            AND t3.sale_to LIKE concat('%', #{shipRequestQueryVo.saleTo[0]}, '%')
        </if>
        <if test="shipRequestQueryVo.shipTo !=null and shipRequestQueryVo.shipTo.size() !=0" >
            AND t3.ship_to            LIKE concat('%', #{shipRequestQueryVo.shipTo[0]}, '%')
        </if>
        <if test="shipRequestQueryVo.statu !=null and shipRequestQueryVo.statu.size() !=0" >
            AND t3.statu  IN
            <foreach collection="shipRequestQueryVo.statu" item="statu" open="(" close=")" separator="," >
                #{statu}
            </foreach>
        </if>
        <if test="shipRequestQueryVo.username !=null and shipRequestQueryVo.username.size() !=0" >
            AND t3.username    LIKE concat('%', #{shipRequestQueryVo.username[0]}, '%')
        </if>
        <if test="shipRequestQueryVo.isReturn !=null and shipRequestQueryVo.isReturn.size() !=0" >
            AND t3.is_return            IN
            <foreach collection="shipRequestQueryVo.isReturn" item="isReturn" open="(" close=")" separator="," >
                #{isReturn}
            </foreach>
        </if>
        <if test="shipRequestQueryVo.materialCode !=null and shipRequestQueryVo.materialCode.size() !=0" >
            AND t3.material_code            IN
            <foreach collection="shipRequestQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                #{materialCode}
            </foreach>
        </if>
        <if test="shipRequestQueryVo.materialDesc !=null and shipRequestQueryVo.materialDesc.size() !=0" >
            AND t4.material_desc            LIKE concat('%', #{shipRequestQueryVo.materialDesc[0]}, '%')
        </if>
        <if test="shipRequestQueryVo.deviceName !=null and shipRequestQueryVo.deviceName.size() !=0" >
            AND t3.device_name            LIKE concat('%', #{shipRequestQueryVo.deviceName[0]}, '%')
        </if>
        <if test="shipRequestQueryVo.paperDate !=null and shipRequestQueryVo.paperDate.size() !=0" >
            AND t3.paper_date
            IN
            <foreach collection="shipRequestQueryVo.paperDate" item="paperDate" open="(" close=")" separator="," >
                #{paperDate}
            </foreach>
        </if>
        <if test="delFlag!=null and delFlag!=''">
            and t3.del_flag_head=#{delFlag}
            and t3.del_flag=#{delFlag}
        </if>
        <if test="temporary!=null and temporary!=''">
            and t3.temporary=#{temporary}
        </if>
        ORDER BY t3.ship_request_order_num,t3.ship_request_order_row_num
    </select>
</mapper>


package com.datalink.fdop.seatunnel.api.domain;

import lombok.Data;
import org.datanucleus.store.types.wrappers.Map;

import java.util.List;

/**
 * 实时数据模型选项（数据源[Oracle Mysql Postgresql SqlServer MongoDb]）
 */
@Data
public class CdcDataModelOptions {

    /**
     * 数据源连接信息
     */
    private String dataSourceBasicInfo;

    /**
     * 已选中的表信息
     */
    List<CdcObject> cdcObjects;

    /**
     * debezium 配置 key value
     */
    private Map<String, String> debezium;

    /**
     * 自定义配置 key value
     */
    private Map<String, String> customConfig;

    /**
     * 源数据库类型
     */
    private String sourceDbType;

    /**
     * 目标数据库类型
     */
    private String sinkDbType;


}

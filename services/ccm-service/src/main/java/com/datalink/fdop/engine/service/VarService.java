package com.datalink.fdop.engine.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-03-01 17:11
 */
public interface VarService {

    Map<String, Object> ppv(String verId, String companyId, Long year, Long month, String businessType, SearchVo searchVo, String str);

    Map<String, Object> mpv(String verId, String companyId, Long year, Long month, String businessType, SearchVo searchVo, String str);

    Map<String, Object> posting(String verId, String companyId, Long year, Long month, String businessType, SearchVo searchVo, String str);

    int varSave(String verId, String companyId, Long year, Long month, String businessType, String str);

    int varUndo(String verId, String companyId, Long year, Long month, String businessType, String str);

}

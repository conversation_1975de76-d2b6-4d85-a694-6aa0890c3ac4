<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.MaterialConsumptionMapper">

    <select id="getOrg" resultType="com.datalink.fdop.engine.api.domain.MaterialConsumption">
        select a.* from fccm.material_consumption a
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchCondition)})
        </if>
    </select>

    <select id="getEdit" resultType="com.datalink.fdop.engine.api.domain.MaterialConsumption">
        select a.* from fccm.material_consumption_edit a
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchCondition)})
        </if>
    </select>

    <select id="getAll" resultType="com.datalink.fdop.engine.api.domain.MaterialConsumption">
        select a.* from fccm.material_consumption a
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("a",searchCondition)})
        </if>
        UNION ALL
        select b.* from fccm.material_consumption_edit b
        where 1=1
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("b",searchCondition)})
        </if>
    </select>

    <select id="add" parameterType="String">
        insert into fccm.material_consumption_edit values (${sql})
    </select>

    <select id="updateByKey">
        update fccm.material_consumption_edit
        set
        <if test="verId !=null and verId != ''">ver_id = '${verId}',</if>
        <if test="companyId !=null and companyId != ''">company_id = '${companyId}',</if>
        <if test="factoryId !=null and factoryId != ''">factory_id = '${factoryId}',</if>
        <if test="materialId !=null and materialId != ''">material_id = '${materialId}',</if>
        <if test="materialDesc !=null and materialDesc != ''">material_desc = '${materialDesc}',</if>
        <if test="yearMonth !=null">year_month = ${yearMonth},</if>
        <if test="postingDate !=null">posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(postingDate)}','yyyy-mm-dd'),</if>
        <if test="unitUsage !=null and unitUsage != ''">unit_usage = '${unitUsage}',</if>
        <if test="qtyUsage !=null">qty_usage = ${qtyUsage},</if>
        <if test="unitUsage !=null and unitUsage != ''">unit_usage = '${unitUsage}',</if>
        <if test="unitBasic !=null">unit_basic = ${unitBasic},</if>
        <if test="qtyBasic !=null">qty_basic = ${qtyBasic},</if>
        <if test="costCenterId !=null and costCenterId != ''">cost_center_id = '${costCenterId}',</if>
        <if test="costCenterDesc !=null and costCenterDesc != ''">cost_center_desc = '${costCenterDesc}',</if>
        <if test="wbsId !=null and wbsId != ''">wbs_id = '${wbsId}',</if>
        <if test="wbsDesc !=null and wbsDesc != ''">wbs_desc = '${wbsDesc}',</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time = to_timestamp('${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}','yyyy-mm-dd hh24:mi:ss')
        <if test="syncStatus != null and syncStatus != ''">, sync_status = '${syncStatus}'</if>
        <if test="text != null and text != ''">, text = '${text}'</if>
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}' and material_id = '${materialId}'
        and posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(postingDate)}','yyyy-mm-dd')
        and cost_center_id = '${costCenterId}'
        and wbs_id = '${wbsId}'
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.engine.api.domain.MaterialConsumption">
        select * from fccm.material_consumption_edit
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}' and material_id = '${materialId}'
          and posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(postingDate)}','yyyy-mm-dd')
          and cost_center_id = '${costCenterId}'
          and wbs_id = '${wbsId}' limit 1
    </select>

    <delete id="deleteByKey">
        delete from fccm.material_consumption_edit
        where ver_id = '${verId}' and company_id = '${companyId}' and factory_id = '${factoryId}' and material_id = '${materialId}'
          and posting_date = to_date('${@com.datalink.fdop.common.core.utils.DateUtils@dateTime(postingDate)}','yyyy-mm-dd')
          and cost_center_id = '${costCenterId}'
          and wbs_id = '${wbsId}'
    </delete>

</mapper>


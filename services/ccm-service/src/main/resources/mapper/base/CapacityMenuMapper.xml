<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CapacityMenuMapper">

    <update id="batchUpdatePidByIds" parameterType="String">
        update zjdata.ccm_capacity_menu
        set p_menu_id = #{pid}
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.CapacityTree">
        select menu_id as id, p_menu_id as pid, 'MENU' as menuType, name as name, description as description from zjdata.ccm_capacity_menu order by menu_id ${sort}
    </select>

    <select id="selectIdsByPid" resultType="String">
        select menu_id from zjdata.ccm_capacity_menu where p_menu_id = #{pid}
    </select>

</mapper>
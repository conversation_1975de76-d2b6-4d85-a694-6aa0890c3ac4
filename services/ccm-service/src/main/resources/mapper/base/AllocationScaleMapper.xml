<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.AllocationScaleMapper">



    <sql id="selectCostCenterSql">
        SELECT
            COALESCE(ccc.control_area_id, ccco.control_area_id) AS control_area_id,
            COALESCE(ccc.cost_center_id, ccco.cost_center_id) AS cost_center_id,
            COALESCE(ccc.date_from, ccco.date_from) AS date_from,
            COALESCE(ccc.date_to, ccco.date_to) AS date_to,
            COALESCE(ccc.cost_center_desc, ccco.cost_center_desc) AS cost_center_desc,
            COALESCE(ccc.cost_center_text, ccco.cost_center_text) AS cost_center_text,
            COALESCE(ccc.company_id, ccco.company_id) AS company_id,
            COALESCE(ccc.cost_center_group_id, ccco.cost_center_group_id) AS cost_center_group_id,
            COALESCE(ccc.enable, ccco.enable) AS enable,
            COALESCE(ccc.create_by, ccco.create_by) AS create_by,
            COALESCE(ccc.create_time, ccco.create_time) AS create_time,
            COALESCE(ccc.update_by, ccco.update_by) AS update_by,
            COALESCE(ccc.update_time, ccco.update_time) AS update_time,
            COALESCE(ccc.expense_type, ccco.expense_type) AS expense_type,
            COALESCE(ccc.plant_id, ccco.plant_id) AS plant_id,
            COALESCE(ccc.currency, ccco.currency) AS currency,
            COALESCE(ccc.cost_center_type, ccco.cost_center_type) AS cost_center_type,
            COALESCE(ccc.delete_flag, false) AS delete_flag
        FROM zjdata.ccm_cost_center_orig ccco
                 FULL OUTER JOIN zjdata.ccm_cost_center ccc
                                 ON ccco.control_area_id = ccc.control_area_id
                                     AND ccco.cost_center_id = ccc.cost_center_id
                                     AND ccco.date_to = ccc.date_to
    </sql>

    <delete id="deleteScalesBySegmentKey">
        delete from zjdata.ccm_allocation_scale where control_area_id = #{controlAreaId} and  keyid = #{keyId}
    </delete>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.AllocationScale">
        select
            node.control_area_id as control_area_id,
            '${keyId}' as keyid,
            '${allocationSegmentId}' as allocation_segment_id,
            '${allocationMethodId}' as  allocation_method_id,
            node.cost_center_id as cost_center_id,
            '${scale}' as scale
        from (
            <include refid="selectCostCenterSql"/>
        ) node
        where
            node.control_area_id = #{controlAreaId}
          and node.cost_center_id between #{costCenterFrom} and #{costCenterTo}
          and node.date_to >= '${@com.datalink.fdop.common.core.utils.DateUtils@getDate()}'
          and node.enable = true
        order by node.cost_center_id ${sort}
    </select>

    <select id="selectListByCostCenterGroupItems" resultType="com.datalink.fdop.base.api.domain.AllocationScale">
        select
            node.control_area_id as control_area_id,
            '${keyId}' as keyid,
            '${allocationSegmentId}' as allocation_segment_id,
            '${allocationMethodId}' as  allocation_method_id,
            node.cost_center_id as cost_center_id,
            '${scale}' as scale
        from (
            <include refid="selectCostCenterSql"/>
        ) node
            left join zjdata.ccm_cost_center_group_item cccgi on node.control_area_id = cccgi.control_area_id and
            node.cost_center_id between cccgi.cost_center_fr  and cccgi.cost_center_to and node.enable = true
            and node.date_to >= '${@com.datalink.fdop.common.core.utils.DateUtils@getDate()}'
        where
            cccgi.cost_center_group_id = #{costCenterGroup}
            and cccgi.control_area_id = #{controlAreaId}
        order by node.cost_center_id ${sort}
    </select>

</mapper>


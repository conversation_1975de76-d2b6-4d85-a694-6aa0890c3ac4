package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdPlanPidExp;
import com.datalink.fdop.engine.mapper.DwdPlanPidExpMapper;
import com.datalink.fdop.engine.service.DwdPlanPidExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdPlanPidExpServiceImpl extends ServiceImpl<DwdPlanPidExpMapper, DwdPlanPidExp> implements DwdPlanPidExpService {

    @Autowired
    private DwdPlanPidExpMapper dwdPlanPidExpMapper;

    @Override
    public PageDataInfo<DwdPlanPidExp> overview(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        Page<DwdPlanPidExp> page = PageUtils.getPage(DwdPlanPidExp.class);
        IPage<DwdPlanPidExp> iPage = dwdPlanPidExpMapper.selectAll(verId, plantId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdPlanPidExp> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwdPlanPidExpMapper.selectNoPage(verId, plantId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
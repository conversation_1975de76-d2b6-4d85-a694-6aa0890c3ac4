package com.datalink.fdop.element.api;

import com.datalink.fdop.common.core.constant.ServiceNameConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.factory.RemoteElementFallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(contextId = "remoteElementService", path = "/element/element",
        value = ServiceNameConstants.DATA_SERVICE,
        fallbackFactory = RemoteElementFallbackFactory.class)
public interface RemoteElementService {

    @GetMapping(value = "/selectById/{id}")
    R<DataElement> selectById(@PathVariable(value = "id") Long id);

    @GetMapping(value = "/selectByCode")
    R<DataElement> selectByCode(@RequestParam(value = "code") String code);

    @ApiOperation("查询数据元素的表结构")
    @GetMapping(value = "/structure/{dataElementId}/selectStructureByDataElementId")
    R<List<DataElementStructure>> list(@PathVariable(value = "dataElementId") Long dataElementId);

}

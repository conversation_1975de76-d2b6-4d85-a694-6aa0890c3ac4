package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostStep;
import com.datalink.fdop.engine.mapper.DwsNewStdCostStepMapper;
import com.datalink.fdop.engine.service.DwsNewStdCostStepService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewStdCostStepServiceImpl extends ServiceImpl<DwsNewStdCostStepMapper, DwsNewStdCostStep> implements DwsNewStdCostStepService {

    @Autowired
    private DwsNewStdCostStepMapper dwsNewStdCostStepMapper;

    @Override
    public PageDataInfo<DwsNewStdCostStep> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwsNewStdCostStep> page = PageUtils.getPage(DwsNewStdCostStep.class);
        IPage<DwsNewStdCostStep> iPage = dwsNewStdCostStepMapper.selectAll(controlAreaId, productId,  verId, plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewStdCostStep> listAll() {
        return dwsNewStdCostStepMapper.listAll();
    }

    @Override
    public List<DwsNewStdCostStep> selectNoPage(String controlAreaId, String productId, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwsNewStdCostStepMapper.selectNoPage(controlAreaId, productId, verId, plantId, date, sort, searchVo);
    }
}
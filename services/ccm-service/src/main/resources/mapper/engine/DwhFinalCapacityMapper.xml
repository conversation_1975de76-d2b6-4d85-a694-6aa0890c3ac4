<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwhFinalCapacityMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwhFinalCapacity">
        SELECT *
        FROM dwh.dwh_final_capacity node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id =  #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.primary_key, node.ver_id
        ${sort}
    </select>

    <select id="listAll" resultType="com.datalink.fdop.engine.api.domain.DwhFinalCapacity">
        SELECT *
        FROM dwh.dwh_final_capacity node
        ORDER BY node.primary_key, node.ver_id
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwhFinalCapacity">
        SELECT *
        FROM dwh.dwh_final_capacity node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id =  #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.primary_key, node.ver_id
        ${sort}
    </select>

</mapper>


package com.datalink.fdop.udf.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.param.ParamsUtils;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.udf.api.domain.*;
import com.datalink.fdop.udf.api.enums.ScriptParamType;
import com.datalink.fdop.udf.api.enums.UdfType;
import com.datalink.fdop.udf.api.model.vo.UdfCopyVo;
import com.datalink.fdop.udf.api.model.vo.UdfInputParamVo;
import com.datalink.fdop.udf.api.model.vo.UdfStringVo;
import com.datalink.fdop.udf.mapper.UdfMapper;
import com.datalink.fdop.udf.mapper.UdfMenuMapper;
import com.datalink.fdop.udf.service.IUdfService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.dolphinscheduler.fdop.api.RemoteResourceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
public class UdfService implements IUdfService {

    private static final Logger logger = LoggerFactory.getLogger(UdfService.class);

    @Autowired
    private UdfMapper udfMapper;

    @Autowired
    private UdfMenuMapper udfMenuMapper;

    @Autowired
    private RemoteResourceService remoteResourceService;

    /**
     * 校验Udf
     *
     * @param id
     * @return
     */
    private Udf checkUdf(Long id) {
        VlabelItem<Udf> vlabelItem = udfMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.UDFS_DO_NOT_EXIST);
        }
        Udf udf = vlabelItem.getProperties();
        return udf;
    }

    // 校验输入参数
    private Udf checkScriptInputParam(Udf udf, Boolean isEscape) {
        // 校验输入参数
        if (StringUtils.isNotEmpty(udf.getScriptInputParam())) {
            udf.setScriptInputParam(udf.getScriptInputParam().replace("'", "\\'"));
            List<ScriptInputParam> scriptInputParamList = JSONObject.parseArray(udf.getScriptInputParam(), ScriptInputParam.class);
            for (ScriptInputParam scriptInputParam : scriptInputParamList) {
                if (scriptInputParam.getScriptParamType() == ScriptParamType.JSON2TABLE) {
                    throw new ServiceException(Status.THE_SCRIPT_INPUT_PARAMETER_CANNOT_BE_OF_TYPE_JSON2TABLE);
                }
                if (scriptInputParam.getIsNecessary() && scriptInputParam.getDefaultValue() == null) {
                    throw new ServiceException(MessageFormat.format(Status.THE_SCRIPT_INPUT_PARAMETER_MUST_HAVE_A_DEFAULT_VALUE.getMsg(), scriptInputParam.getCode()));
                }
                String value = scriptInputParam.getDefaultValue().toString();
                if (scriptInputParam.getScriptParamType() == ScriptParamType.BOOLEAN && (!"true".equalsIgnoreCase(value) && !"false".equalsIgnoreCase(value))) {
                    throw new ServiceException(Status.BOOLEAN_TYPE_PARAMETER_CAN_ONLY_BE_TRUE_OR_FALSE);
                }
                if (scriptInputParam.getScriptParamType() == ScriptParamType.JSON || scriptInputParam.getScriptParamType() == ScriptParamType.STRING) {
                    // 是否转义
                    if (isEscape) {
                        value = value.replace("\"", "\\\"");
                    }
                    // 转换默认值
                    scriptInputParam.setDefaultValue(value);
                    // 转换模拟值
                    Object analogValue = scriptInputParam.getAnalogValue();
                    if (analogValue != null) {
                        String analogValueStr = analogValue.toString();
                        // 是否转义
                        if (isEscape) {
                            analogValueStr = analogValueStr.replace("\"", "\\\"");
                        }
                        scriptInputParam.setAnalogValue(analogValueStr);
                    }
                    if (scriptInputParam.getScriptParamType() == ScriptParamType.JSON && !isJson(value)) {
                        throw new ServiceException(Status.PARAMETER_VALUE_IS_NOT_IN_JSON_FORMAT);
                    }
                }
                if (scriptInputParam.getScriptParamType() == ScriptParamType.INTEGER) {
                    try {
                        Integer.parseInt(value);
                    } catch (Exception e) {
                        throw new ServiceException(Status.THE_PARAMETER_VALUE_IS_NOT_AN_INTEGER_TYPE);
                    }
                }
                if (scriptInputParam.getScriptParamType() == ScriptParamType.DECIMAL) {
                    try {
                        Double.parseDouble(value);
                    } catch (Exception e) {
                        throw new ServiceException(Status.THE_PARAMETER_VALUE_IS_NOT_OF_TYPE_DECIMAL);
                    }
                }
            }
            udf.setScriptInputParam(JSONObject.toJSONString(scriptInputParamList));
        }
        return udf;
    }

    /**
     * 校验输出参数
     *
     * @param udf
     * @return
     */
    private Udf checkScriptOutputParam(Udf udf) {
        // 校验输入参数
        if (StringUtils.isNotEmpty(udf.getScriptOutputParam())) {
            udf.setScriptOutputParam(udf.getScriptOutputParam().replace("'", "\\'"));
            List<ScriptOutputParam> scriptOutputParamList = JSONObject.parseArray(udf.getScriptOutputParam(), ScriptOutputParam.class);
            for (ScriptOutputParam scriptOutputParam : scriptOutputParamList) {
                if (scriptOutputParam.getScriptParamType() == ScriptParamType.JSON) {
                    throw new ServiceException(Status.SCRIPT_OUTPUT_PARAMETERS_CANNOT_BE_OF_TYPE_JSON);
                }
                if (scriptOutputParam.getScriptParamType() == ScriptParamType.JSON2TABLE) {
                    if (StringUtils.isEmpty(scriptOutputParam.getJson2TableParam())) {
                        throw new ServiceException(Status.SCRIPT_PARAMETERS_OF_TYPE_JSON2TABLE_MUST_DEFINE_TRANSLATION_RULES);
                    }
                    // 反转义json2TableParam
                    String json2TableParam = scriptOutputParam.getJson2TableParam();
                    scriptOutputParam.setJson2TableParam(json2TableParam.replace("\"", "\\\""));
                }
                // 过滤结果，不需要存储结果
                scriptOutputParam.setResult(null);
            }
            udf.setScriptOutputParam(JSONObject.toJSONString(scriptOutputParamList));
        }
        return udf;
    }

    /**
     * 校验逻辑
     */
    private Udf checkLogic(Udf udf) {
        // 校验逻辑
        switch (udf.getUdfType()) {
            case PYTHON:
                if (StringUtils.isEmpty(udf.getScriptLogic())) {
                    throw new ServiceException(MessageFormat.format(Status.THE_SCRIPT_LOGIC_CANNOT_BE_EMPTY.getMsg(), udf.getUdfType().name()));
                }
                udf.setScriptLogic(udf.getScriptLogic().replace("'", "\\'")
                        .replace("\"", "\\\""));
                break;
            case JAVA:
                if (udf.getResourceId() == null) {
                    throw new ServiceException(MessageFormat.format(Status.THE_SCRIPT_LOGIC_CANNOT_BE_EMPTY.getMsg(), udf.getUdfType().name()));
                }
                break;
            default:
                throw new ServiceException(Status.UNKNOWN_UDF_TYPE);
        }
        return udf;
    }

    /**
     * 判断字符串是否是json
     *
     * @param body
     * @return
     */
    private boolean isJson(String body) {
        boolean result = false;
        if (body.startsWith("{") && body.endsWith("}")) {
            result = true;
        } else if (body.startsWith("[") && body.endsWith("]")) {
            result = true;
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Udf create(Udf udf) {
        if (udf.getPid() != -1L && udfMenuMapper.selectById(udf.getPid()) == null) {
            throw new ServiceException(Status.THE_UDF_MENU_DOES_NOT_EXIST);
        }
        if (udfMapper.selectByCode(udf.getCode()) != null) {
            throw new ServiceException(Status.UDF_ALREADY_EXISTS);
        }
        udf.setId(IdWorker.getId());
        // 存在图中的特殊字符需要转义
        if (StringUtils.isNotEmpty(udf.getScriptLogic())) {
            String scriptLogic = udf.getScriptLogic();
            if (scriptLogic.contains("\\")) {
                scriptLogic = scriptLogic.replace("\\", "\\\\");
            }
            if (scriptLogic.contains("'")) {
                scriptLogic = scriptLogic.replace("'", "\\\'");
            }
            if (scriptLogic.contains("$")) {
                scriptLogic = scriptLogic.replace("$", "\\\\$");
            }
            udf.setScriptLogic(scriptLogic);
        }
        int insert = udfMapper.insertUdf(udf);
        // 创建元素边关系
        if (insert > 0 && udf.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            udfMapper.createUdfAndMenuEdge(udf.getPid(), Arrays.asList(udf.getId()));
        }
        return udf;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(Udf udf) {
        VlabelItem<Udf> vlabelItem = udfMapper.selectById(udf.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.UDFS_DO_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(udf.getCode()) && udfMapper.checkCodeIsExists(udf.getId(), udf.getCode()) != null) {
            throw new ServiceException(Status.UDF_ALREADY_EXISTS);
        }
        // 不能拖拽到节点里面
        if (udf.getPid() != null && udf.getPid() != -1L && udfMenuMapper.selectById(udf.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_UDF_MENU);
        }
        // 校验输入参数
        checkScriptInputParam(udf, true);
        // 校验输出参数
        checkScriptOutputParam(udf);
        // 存在图中的特殊字符需要转义
        if (StringUtils.isNotEmpty(udf.getScriptLogic())) {
            String scriptLogic = udf.getScriptLogic();
            if (scriptLogic.contains("\\")) {
                scriptLogic = scriptLogic.replace("\\", "\\\\");
            }
            if (scriptLogic.contains("'")) {
                scriptLogic = scriptLogic.replace("'", "\\\'");
            }
            if (scriptLogic.contains("$")) {
                scriptLogic = scriptLogic.replace("$", "\\\\$");
            }
            udf.setScriptLogic(scriptLogic);
        }
        int update = udfMapper.updateById(udf);
        if (update > 0 && udf.getPid() != null) {
            // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
            udfMapper.deleteUdfAndMenuEdge(Arrays.asList(udf.getId()), vlabelItem.getProperties().getPid());
            if (udf.getPid() != -1L) {
                // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                udfMapper.createUdfAndMenuEdge(udf.getPid(), Arrays.asList(udf.getId()));
            }
        }

        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copy(Long pid, List<UdfCopyVo> udfCopyVoList) {
        // 检查菜单
        VlabelItem<UdfMenu> udfMenuVlabelItem = udfMenuMapper.selectById(pid);
        if (udfMenuVlabelItem == null) {
            throw new ServiceException(Status.THE_UDF_MENU_DOES_NOT_EXIST);
        }

        if (CollectionUtils.isEmpty(udfCopyVoList)) {
            throw new ServiceException(Status.THE_COPIED_UDF_INFORMATION_CANNOT_BE_EMPTY);
        }

        for (UdfCopyVo udfCopyVo : udfCopyVoList) {
            // 检查UDF
            Udf udf = checkUdf(udfCopyVo.getCodeNodeId());
            // 设置修改后的pid
            udf.setPid(pid);
            // 修改基本信息
            udf.setCode(udfCopyVo.getCode());
            udf.setName(udfCopyVo.getName());
            udf.setDescription(udfCopyVo.getDescription());
            // 创建节点
            this.create(udf);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        return udfMapper.deleteBatchIds(ids);
    }


    @Override
    public Udf selectById(Long id) {
        // 校验UDF
        Udf udf = checkUdf(id);
        return udf;
    }

    @Override
    public List<ScriptOutputParam> runUdfScript(Long id) {
        // 校验UDF
        Udf udf = checkUdf(id);

        // 运行UDF
        return runUdfScript(udf, false);
    }

    @Override
    public List<ScriptOutputParam> runUdfScript(Udf udf, Boolean isTrialRun) {
        // 校验输入参数
        checkScriptInputParam(udf, isWindows());

        // 校验逻辑
        checkLogic(udf);

        // 校验脚本输出参数
        if (StringUtils.isEmpty(udf.getScriptOutputParam())) {
            throw new ServiceException(Status.SCRIPT_OUTPUT_PARAMETERS_CANNOT_BE_EMPTY);
        }

        // 获取脚本输入参数
        List<ScriptInputParam> scriptInputParamList = new ArrayList<>();
        if (StringUtils.isNotEmpty(udf.getScriptInputParam())) {
            scriptInputParamList.addAll(JSONObject.parseArray(udf.getScriptInputParam(), ScriptInputParam.class));
        }
        // 获取脚本输出参数
        List<ScriptOutputParam> scriptOutputParamList = JSONObject.parseArray(udf.getScriptOutputParam(), ScriptOutputParam.class);

        List<String> command = new ArrayList<>();
        switch (udf.getUdfType()) {
            case PYTHON:
                if (StringUtils.isEmpty(udf.getScriptLogic())) {
                    throw new ServiceException(Status.SCRIPT_LOGIC_OF_TYPE_PYTHON_CANNOT_BE_EMPTY);
                }
                // 获取java执行命令
                command = getPythonCommand(command, udf.getScriptLogic(), scriptInputParamList, isTrialRun);
                break;
            case JAVA:
                if (udf.getResourceId() == null) {
                    throw new ServiceException(Status.JAR_PACKAGES_OF_JAVA_TYPE_CANNOT_BE_EMPTY);
                }
                // 获取java执行命令
                command = getJavaCommand(command, udf.getResourceId(), scriptInputParamList, isTrialRun);
                break;
            default:
                throw new ServiceException(Status.UNKNOWN_UDF_TYPE);
        }

        return execCommand(udf.getUdfType(), command, scriptOutputParamList, isTrialRun);
    }

    private List<String> getPythonCommand(List<String> pythonCommand, String scriptLogic, List<ScriptInputParam> scriptInputParamList, Boolean isTrialRun) {
        // 替换值
        if (CollectionUtils.isNotEmpty(scriptInputParamList)) {
            for (ScriptInputParam scriptInputParam : scriptInputParamList) {
                // 过滤非必输的参数
                if (!scriptInputParam.getIsNecessary()) {
                    continue;
                }
                Object value = scriptInputParam.getDefaultValue();
                if (isTrialRun) {
                    Object analogValue = scriptInputParam.getAnalogValue();
                    value = analogValue != null && StringUtils.isNotEmpty(analogValue.toString()) ? analogValue : value;
                }

                String valueStr = value.toString();
                // python的布尔类型的值是: True/False
                // if (scriptInputParam.getScriptParamType() == ScriptParamType.BOOLEAN) {
                //     valueStr = valueStr.substring(0, 1).toUpperCase() + valueStr.substring(1, valueStr.length());
                // }
                valueStr = valueStr.replace("true", "True").replace("false", "False");
                if ((scriptInputParam.getScriptParamType() == ScriptParamType.JSON || scriptInputParam.getScriptParamType() == ScriptParamType.STRING) && !isWindows()) {
                    valueStr = valueStr.replace("\"", "\\\"");
                }
                scriptLogic = scriptLogic.replace("{@" + scriptInputParam.getCode() + "@}", valueStr);
            }
        }

        // 先将python脚本逻辑写入到临时文件
        // 创建配置文件
        String pythonFileName = "";
        if (isWindows()) {
            pythonFileName = "E:\\tmp\\udf\\python\\python_" + IdWorker.getIdStr() + ".py";
        } else {
            pythonFileName = "/tmp/python/python_" + IdWorker.getIdStr() + ".py";
        }

        File file = new File(pythonFileName);
        try {
            File pafile = file.getParentFile();
            //不存在父路径就创建
            if (!pafile.exists()) {
                pafile.mkdirs();
            }
            //创建文件
            file.createNewFile();
            FileWriter fw = new FileWriter(file, true);
            // 写入前去除转义
            scriptLogic = StringEscapeUtils.unescapeJava(scriptLogic);
            // 写入文件
            fw.write(scriptLogic);
            fw.close();
        } catch (Exception e) {
            logger.error("创建python脚本文件错误:{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }

        // 根据输入参数拼接python执行命令
        // 示例: python E:\\1.python
        if (isWindows()) {
            pythonCommand.add("python");
        } else {
            pythonCommand.add("/usr/bin/python3");
        }
        pythonCommand.add(pythonFileName);
        logger.info("执行的python命令:{}", pythonCommand.stream().collect(Collectors.joining(" ")));
        return pythonCommand;
    }

    private List<String> getJavaCommand(List<String> javaCommand, Long resourceId, List<ScriptInputParam> scriptInputParamList, Boolean isTrialRun) {
        // 根据输入参数拼接java执行命令
        javaCommand.add("java");
        javaCommand.add("-jar");

        if (CollectionUtils.isNotEmpty(scriptInputParamList)) {
            // 先将java输入参数写入到临时文件
            // 创建配置文件
            String javaFileName = "";
            if (isWindows()) {
                javaFileName = "E:\\tmp\\udf\\java\\java_param_" + IdWorker.getIdStr();
            } else {
                javaFileName = "/tmp/java/java_param_" + IdWorker.getIdStr();
            }

            File file = new File(javaFileName);
            try {
                File pafile = file.getParentFile();
                //不存在父路径就创建
                if (!pafile.exists()) {
                    pafile.mkdirs();
                }
                //创建文件
                file.createNewFile();
                FileWriter fw = new FileWriter(file, true);
                for (ScriptInputParam scriptInputParam : scriptInputParamList) {
                    // 先过滤出所有必输的参数
                    if (!scriptInputParam.getIsNecessary()) {
                        continue;
                    }
                    Object value = scriptInputParam.getDefaultValue();
                    // 试运行使用模拟值,如果不存在模拟值则使用默认值
                    if (isTrialRun) {
                        Object analogValue = scriptInputParam.getAnalogValue();
                        value = analogValue != null && StringUtils.isNotEmpty(analogValue.toString()) ? analogValue : value;
                    }

                    // json类型参数需要转换成字符串形式
                    // if (scriptInputParam.getScriptParamType() == ScriptParamType.JSON) {
                    //     value = JSONObject.toJSONString(value);
                    // }
                    // 写入文件
                    fw.write(scriptInputParam.getCode() + "=" + value + "\n");
                }
                fw.close();
            } catch (Exception e) {
                logger.error("创建python脚本文件错误:{}", e.getMessage());
                throw new ServiceException(e.getMessage());
            }

            // 输入参数
            javaCommand.add("-Dpath=" + javaFileName);
        }

        // 示例: java -jar -Df=123 -Dg=456  E:\work\test\demo\target\demo-1.0-SNAPSHOT.jar
        // 拼接jar包位置
        R result = remoteResourceService.getDownloadFileLocation(Integer.parseInt(resourceId.toString()));
        if (result.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(result.getMsg());
        }
        String resourcePath = result.getData().toString();
        // Windows操作系统
        if (isWindows()) {
            resourcePath = resourcePath.substring(1, resourcePath.length());
        }
        javaCommand.add(resourcePath);

        logger.info("执行的java命令:{}", javaCommand.stream().collect(Collectors.joining(" ")));
        return javaCommand;
    }

    private List<ScriptOutputParam> execCommand(UdfType udfType, List<String> command, List<ScriptOutputParam> scriptOutputParamList, Boolean isTrialRun) {
        List<String> outputValueList = new ArrayList<>();
        List<String> errorValueList = new ArrayList<>();

        try {
            Process process = Runtime.getRuntime().exec(command.stream().toArray(String[]::new));
            // 用输入输出流来截取结果
            BufferedReader outputBuff = new BufferedReader(new InputStreamReader(process.getInputStream(), Charset.forName(isWindows() ? "GBK" : "UTF-8")));
            String outputLine = null;
            while ((outputLine = outputBuff.readLine()) != null) {
                outputValueList.add(outputLine);
            }
            outputBuff.close();

            // 获取错误信息
            BufferedReader errorBuff = new BufferedReader(new InputStreamReader(process.getErrorStream(), Charset.forName(isWindows() ? "GBK" : "UTF-8")));
            String errorLine = null;
            while ((errorLine = errorBuff.readLine()) != null) {
                errorValueList.add(errorLine);
            }
            errorBuff.close();
            process.waitFor();

            // 如果存在错误信息直接抱错
            if (CollectionUtils.isNotEmpty(errorValueList)) {
                throw new ServiceException(errorValueList.stream().collect(Collectors.joining("\n")));
            }

            // 获取输出结果,返回值需要遵循 key=value的结果
            Map<String, String> resultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(outputValueList)) {
                for (String outputValue : outputValueList) {
                    if (outputValue.contains("=")) {
                        String key = outputValue.substring(0, outputValue.indexOf("="));
                        String value = outputValue.substring(outputValue.indexOf("=") + 1, outputValue.length());
                        resultMap.put(key, value);
                    }
                }
            }

            logger.info("运行结果:{}", resultMap);

            // 返回结果
            if (MapUtils.isEmpty(resultMap)) {
                throw new ServiceException(Status.THE_RETURN_RESULT_OF_THE_UDF_IS_EMPTY);
            }

            // 设置返回值
            for (ScriptOutputParam scriptOutputParam : scriptOutputParamList) {
                String correspondParam = scriptOutputParam.getCorrespondParam();
                if (resultMap.containsKey(correspondParam)) {
                    String value = resultMap.get(correspondParam);

                    // 如果是JSON2TABLE格式，并且不是试运行就转换结果
                    if (scriptOutputParam.getScriptParamType() == ScriptParamType.JSON2TABLE && !isTrialRun) {
                        // 校验结果是否是二维表结构
                        try {
                            JSONObject.parseArray(value, Map.class);
                        } catch (Exception e) {
                            throw new ServiceException(MessageFormat.format(Status.THE_OUTPUT_OF_PARAMETER_IS_NOT_A_2D_TABLE_STRUCTURE.getMsg(), scriptOutputParam.getCode()));
                        }

                        List<Map> valueMapList = JSONObject.parseArray(value, Map.class);
                        // 获取二维表的格式
                        List<Json2TableParam> json2TableParamList = JSONObject.parseArray(scriptOutputParam.getJson2TableParam(), Json2TableParam.class);
                        for (Map valueMap : valueMapList) {
                            for (Json2TableParam json2TableParam : json2TableParamList) {
                                // 获取对应属性  输出参数编码.属性名
                                String attribute = json2TableParam.getAttribute();
                                String[] split = attribute.split("\\.");
                                // 获取属性名
                                String jsonAttributeName = split[1];
                                // 保存值
                                Object objValue = valueMap.get(jsonAttributeName);
                                // 先删除
                                valueMap.remove(jsonAttributeName);
                                // 后添加
                                valueMap.put(json2TableParam.getCode(), objValue);
                            }
                        }
                        scriptOutputParam.setResult(JSONObject.toJSONString(valueMapList));
                    } else {
                        if (scriptOutputParam.getScriptParamType() == ScriptParamType.INTEGER) {
                            try {
                                Integer.parseInt(value);
                            } catch (Exception e) {
                                throw new ServiceException(Status.THE_PARAMETER_VALUE_IS_NOT_AN_INTEGER_TYPE);
                            }
                        }
                        if (scriptOutputParam.getScriptParamType() == ScriptParamType.DECIMAL) {
                            try {
                                Double.parseDouble(value);
                            } catch (Exception e) {
                                throw new ServiceException(Status.THE_PARAMETER_VALUE_IS_NOT_OF_TYPE_DECIMAL);
                            }
                        }
                        if (scriptOutputParam.getScriptParamType() == ScriptParamType.BOOLEAN && (!"true".equalsIgnoreCase(value) && !"false".equalsIgnoreCase(value))) {
                            throw new ServiceException(Status.BOOLEAN_TYPE_PARAMETER_CAN_ONLY_BE_TRUE_OR_FALSE);
                        }
                        scriptOutputParam.setResult(value);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
        return scriptOutputParamList;
    }

    /**
     * 判断是是windows系统
     *
     * @return
     */
    private Boolean isWindows() {
        String os = System.getProperty("os.name");
        if (StringUtils.isNotEmpty(os) && os.toLowerCase().startsWith("windows")) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public String parseUdf(UdfStringVo udfStringVo) {
        // 获取需要替换的字符串
        String string = udfStringVo.getString();

        // 获取需要解析的udf
        List<String> udfPlaceholders = ParamsUtils.getUdfPlaceholder(string);
        if (CollectionUtils.isEmpty(udfPlaceholders)) {
            return string;
        }

        // 去重
        udfPlaceholders = udfPlaceholders.stream().distinct().collect(Collectors.toList());

        for (String udfPlaceholder : udfPlaceholders) {
            // 解析出udf编码
            String udfCode = udfPlaceholder.replace("{U", "").replace("{", "").replace("}", "");

            // aa.bb[1].cc[2].aa[3].aa
            List<String> udfList = Arrays.asList(udfCode.split("\\."));

            udfCode = udfList.get(0);

            // 获取udf对象
            VlabelItem<Udf> vlabelItem = udfMapper.selectByCode(udfCode);
            if (vlabelItem == null) {
                throw new ServiceException(MessageFormat.format(Status.UDF_SCRIPT_DOES_NOT_EXIST.getMsg(), udfCode));
            }
            Udf udf = vlabelItem.getProperties();

            // 不存在的udf跳过
            if (udf == null) {
                throw new ServiceException(MessageFormat.format(Status.UDF_SCRIPT_DOES_NOT_EXIST.getMsg(), udfCode));
            }

            // 替换输入参数
            List<UdfInputParamVo> udfInputParamVoList = udfStringVo.getUdfInputParamVoList();
            for (UdfInputParamVo udfInputParamVo : udfInputParamVoList) {
                if (udf.getId().equals(udfInputParamVo.getId())) {
                    // 用户自定义的输入参数
                    List<ScriptInputParam> scriptInputParamList = udfInputParamVo.getScriptInputParamList();
                    scriptInputParamList = scriptInputParamList.stream().map(scriptInputParam -> {
                        // 如果用户指定的输入值不为空，则替换默认值
                        Object inputValue = scriptInputParam.getInputValue();
                        if (inputValue != null) {
                            String inputValueStr = inputValue.toString();
                            if (StringUtils.isNotEmpty(inputValueStr)) {
                                scriptInputParam.setDefaultValue(inputValueStr);
                            }
                        }
                        return scriptInputParam;
                    }).collect(Collectors.toList());
                    udf.setScriptInputParam(JSONObject.toJSONString(scriptInputParamList));
                    break;
                }
            }

            List<ScriptOutputParam> scriptOutputParamList = this.runUdfScript(udf, false);

            // 如果是{U{UDF}}，则返回全部数据
            if (udfList.size() == 1) {
                // 替换
                string = string.replace(udfPlaceholder, JSONObject.toJSONString(scriptOutputParamList));
            }

            // 如果是{U{UDF.A}}，则返回输出参数A的值
            if (udfList.size() == 2) {
                // 获取参数编码
                String outputParamCode = udfList.get(1);
                // 不存在输出参数
                if (!scriptOutputParamList.stream().anyMatch(scriptOutputParam -> {
                    if (outputParamCode.equals(scriptOutputParam.getCode())) {
                        return true;
                    }
                    return false;
                })) {
                    throw new ServiceException(MessageFormat.format(Status.NO_OUTPUT_PARAMETER_EXISTS.getMsg(), outputParamCode));
                }
                // 获取参数的值
                String result = scriptOutputParamList.stream().filter(scriptOutputParam -> {
                    if (outputParamCode.equals(scriptOutputParam.getCode())) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList()).get(0).getResult().toString();

                // 替换
                string = string.replace(udfPlaceholder, result);
            }

            // 如果是{U{UDF.A.a1}}，则说明A是JSON2TABLE类型的参数，并且返回a1的值集
            if (udfList.size() == 3) {
                // 获取参数编码
                String outputParamCode = udfList.get(1);
                // 不存在输出参数
                if (!scriptOutputParamList.stream().anyMatch(outputParam -> outputParamCode.equals(outputParam.getCode()))) {
                    throw new ServiceException(MessageFormat.format(Status.NO_OUTPUT_PARAMETER_EXISTS.getMsg(), outputParamCode));
                }
                // 获取输出参数
                ScriptOutputParam scriptOutputParam = scriptOutputParamList.stream().filter(outputParam -> outputParamCode.equals(outputParam.getCode())).collect(Collectors.toList()).get(0);
                // 类型不对
                if (scriptOutputParam.getScriptParamType() != ScriptParamType.JSON2TABLE) {
                    throw new ServiceException(Status.THE_SCRIPT_OUTPUT_PARAMETER_TYPE_IS_NOT_JSON2TABLE);
                }
                // 获取二维表中的列名
                String field = udfList.get(2);
                // 获取值
                List<Map> valueMapList = JSONObject.parseArray(scriptOutputParam.getResult().toString(), Map.class);
                if (!valueMapList.stream().anyMatch(valueMap -> valueMap.containsKey(field))) {
                    throw new ServiceException(MessageFormat.format(Status.THE_FIELD_DOES_NOT_EXIST_IN_THE_OUTPUT_PARAMETER_JUNCTION_RESULT.getMsg(), field));
                }
                List<String> resultList = new ArrayList<>();
                for (Map valueMap : valueMapList) {
                    resultList.add(MapUtils.getString(valueMap, field));
                }

                // 替换
                string = string.replace(udfPlaceholder, resultList.stream().collect(Collectors.joining(",")));
            }

        }
        return string;
    }

    @Override
    public R<List<UdfInputParamVo>> parseTaskParams(String string) {

        List<UdfInputParamVo> udfInputParamVoList = new ArrayList<>();

        // 不存在的参数信息信息
        List<String> errParamList = new ArrayList<>();

        // 获取需要解析的udf
        List<String> udfPlaceholders = ParamsUtils.getUdfPlaceholder(string);
        if (CollectionUtils.isEmpty(udfPlaceholders)) {
            return R.ok(udfInputParamVoList);
        }

        // 去重
        udfPlaceholders = udfPlaceholders.stream().distinct().collect(Collectors.toList());

        for (String udfPlaceholder : udfPlaceholders) {
            // 解析出udf编码
            String udfCode = udfPlaceholder.replace("{U", "").replace("{", "").replace("}", "");

            // aa.bb[1].cc[2].aa[3].aa
            List<String> udfList = Arrays.asList(udfCode.split("\\."));

            udfCode = udfList.get(0);

            // 获取udf对象
            VlabelItem<Udf> vlabelItem = udfMapper.selectByCode(udfCode);
            if (vlabelItem == null) {
                errParamList.add("{{" + udfCode + "}}");
                continue;
            }
            Udf udf = vlabelItem.getProperties();
            // 获取脚本输入参数
            List<ScriptInputParam> scriptInputParamList = new ArrayList<>();
            if (StringUtils.isNotEmpty(udf.getScriptInputParam())) {
                scriptInputParamList.addAll(JSONObject.parseArray(udf.getScriptInputParam(), ScriptInputParam.class));
            }
            udfInputParamVoList.add(new UdfInputParamVo(udf.getId(), udf.getCode(), udf.getName(), udf.getDescription(), udf.getUdfType(), scriptInputParamList));
        }

        if (CollectionUtils.isNotEmpty(errParamList)) {
            return R.fail(udfInputParamVoList, "参数" + errParamList.stream().collect(Collectors.joining(",")) + "无效");
        }
        return R.ok(udfInputParamVoList);
    }
}

package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewActualEidExp;
import com.datalink.fdop.engine.mapper.DwsNewActualEidExpMapper;
import com.datalink.fdop.engine.service.DwsNewActualEidExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewActualEidExpServiceImpl extends ServiceImpl<DwsNewActualEidExpMapper, DwsNewActualEidExp> implements DwsNewActualEidExpService {

    @Autowired
    private DwsNewActualEidExpMapper dwsNewActualEidExpMapper;

    @Override
    public PageDataInfo<DwsNewActualEidExp> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwsNewActualEidExp> page = PageUtils.getPage(DwsNewActualEidExp.class);
        IPage<DwsNewActualEidExp> iPage = dwsNewActualEidExpMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<String> selectEidAndPidPlantId() {
        return dwsNewActualEidExpMapper.selectEidAndPidPlantId();
    }

    @Override
    public List<DwsNewActualEidExp> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwsNewActualEidExpMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
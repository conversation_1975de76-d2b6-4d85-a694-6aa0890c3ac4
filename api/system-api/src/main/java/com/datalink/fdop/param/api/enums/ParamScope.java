package com.datalink.fdop.param.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2022/9/22 10:41
 */
public enum ParamScope {

    // 系统参数
    SYSTEM(0, "SYSTEM"),
    // 全局参数
    GLOBAL(1, "GLOBAL"),
    // 局部参数
    PART(2, "PART"),
    ;

    private final int code;
    @EnumValue
    private final String desc;

    ParamScope(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

package com.datalink.fdop.base.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2022-11-07 10:36
 */
@Data
@TableName(schema = "zjdata", value = "p_d_as")
public class As {

    @ApiModelProperty(value = "id")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @ApiModelProperty(value = "父id")
    @NotNull(message = "请选择一个菜单")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long saknrFromId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long saknrToId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long saknrPid;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long bwkeyId;

    @TableField(exist = false)
    private String saknrFromCode;

    @TableField(exist = false)
    private String saknrToCode;

    @TableField(exist = false)
    private String saknrPCode;

    @TableField(exist = false)
    private String bwkeyCode;

}

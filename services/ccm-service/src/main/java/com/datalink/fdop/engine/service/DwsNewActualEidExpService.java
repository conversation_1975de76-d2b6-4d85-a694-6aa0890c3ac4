package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewActualEidExp;

import java.util.List;

public interface DwsNewActualEidExpService extends IService<DwsNewActualEidExp> {

    PageDataInfo<DwsNewActualEidExp> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<String> selectEidAndPidPlantId();

    List<DwsNewActualEidExp> selectNoPage(String verId,
                                          String plantId,
                                          String yearMonth,
                                          String sort,
                                          SearchVo searchVo);
}
package com.datalink.fdop.seatunnel.api.restapi.domain;

import lombok.Data;

import java.util.List;

/**
 * SeaTunnel JSON 配置类
 */
@Data
public class JobConfiguration {

    /**
     * 环境
     */
    private Environment env;

    /**
     * 源
     */
    private List<SourceConfig> source;


    /**
     * 转换 restapi暂时没想好怎么支持
     */
    private List<TransformConfig> transform;

    /**
     * 目标
     */
    private List<SinkConfig> sink;

    public JobConfiguration() {

    }

    public JobConfiguration(Environment env, List<SourceConfig> source, List<TransformConfig> transform, List<SinkConfig> sink) {
        this.env = env;
        this.source = source;
        this.transform = transform;
        this.sink = sink;
    }
}

package com.datalink.fdop.element.api.domain;

import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 实体血缘信息
 */
@Data
public class DataEntityBloodExport {

    // 新增字段、
    @ApiModelProperty(value = "目标实体ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetModelId;

    @ApiModelProperty(value = "目标实体编码")
    @Excel(name = "实体编码", sort = 1)
    private String targetModelCode;

    @ApiModelProperty(value = "目标实体名称")
    @Excel(name = "实体名称", sort = 2)
    private String targetModelName;

    @ApiModelProperty(value = "目标实体类型")
    @Excel(name = "实体类型", sort = 3)
    private String targetModelType;

    @ApiModelProperty(value = "目标表名称")
    @Excel(name = "表名", sort = 3)
    private String targetTableName;

    @ApiModelProperty(value = "目标字段编码")
    @Excel(name = "字段编码", sort = 4)
    private String targetCode;

    @ApiModelProperty(value = "目标字段名称")
    @Excel(name = "字段名称", sort = 5)
    private String targetName;

    @ApiModelProperty(value = "目标描述")
    @Excel(name = "字段描述", sort = 6)
    private String targetDescription;

    @ApiModelProperty(value = "目标字段类型")
    @Excel(name = "字段类型", sort = 7)
    private String targetFieldType;

    @ApiModelProperty(value = "目标长度")
    @Excel(name = "字段长度", sort = 8)
    private Integer targetLength;

    @ApiModelProperty(value = "目标精度")
    @Excel(name = "字段精度", sort = 9)
    private Integer targetDecimalLength;

    @ApiModelProperty(value = "目标是否为主键")
    @Excel(name = "是否主键", sort = 10, readConverterExp ="true=是,false=否")
    private Boolean targetIsPk;

    @ApiModelProperty(value = "源实体ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sourceModelId;

    @ApiModelProperty(value = "源实体编码")
    @Excel(name = "来源实体编码", sort = 11)
    private String sourceModelCode;

    @ApiModelProperty(value = "源实体名称")
    @Excel(name = "来源实体名称", sort = 12)
    private String sourceModelName;

    @ApiModelProperty(value = "源实体类型")
    @Excel(name = "来源实体类型", sort = 13)
    private String sourceModelType;

    @ApiModelProperty(value = "源表名称")
    @Excel(name = "来源表名", sort = 14)
    private String sourceTableName;

    @ApiModelProperty(value = "源编码")
    @Excel(name = "来源字段编码", sort = 15)
    private String sourceCode;

    @ApiModelProperty(value = "源名称")
    @Excel(name = "来源字段名称", sort = 16)
    private String sourceName;

    @ApiModelProperty(value = "源描述")
    @Excel(name = "来源字段描述", sort = 17)
    private String sourceDescription;

    @ApiModelProperty(value = "源字段类型")
    @Excel(name = "来源字段类型", sort = 18)
    private String sourceFieldType;

    @ApiModelProperty(value = "源长度")
    @Excel(name = "来源字段长度", sort = 19)
    private Integer sourceLength;

    @ApiModelProperty(value = "源精度")
    @Excel(name = "来源字段精度", sort = 20)
    private Integer sourceDecimalLength;

    @ApiModelProperty(value = "源是否为主键")
    @Excel(name = "来源字段是否主键", sort = 21, readConverterExp ="true=是,false=否")
    private Boolean sourceIsPk;

}
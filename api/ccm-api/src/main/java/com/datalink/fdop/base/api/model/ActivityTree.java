package com.datalink.fdop.base.api.model;

import com.datalink.fdop.common.core.enums.MenuType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-29 10:51
 */
@Data
public class ActivityTree {

    private String menuId;

    private String pMenuId;

    private String menuDesc;

    private String menuText;

    private MenuType menuType;

    private List<ActivityTree> children = new ArrayList<>();

    private String controlAreaId;

    private String controlAreaDesc;

    private String activityId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date dateFrom;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date dateTo;

    private String activityDesc;

    private String activityText;

    private String unit;

    private Boolean enable;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;

}

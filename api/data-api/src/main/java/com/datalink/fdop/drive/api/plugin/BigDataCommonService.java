package com.datalink.fdop.drive.api.plugin;

import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.drive.api.domain.dto.Field;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

/**
 * 文件系统数据存储通用工具类
 * <AUTHOR>
 * @date 2022/6/9 14:12
 */
public interface BigDataCommonService extends CommonService {

    /**
     * 测试连接
     *
     * @return
     */
    boolean connect(DataSourceBasicInfo dataSourceBasicInfo);

    // 获取所有数据库
    Map<String, Object> getDataBases(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, Integer pageNo, Integer pageSize);


    Map<String, Object> getSchemasByDatabase(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, Integer pageNo, Integer pageSize);

    // 获取数据库下的所有表
    Map<String, Object> getTables(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName, Integer pageNo, Integer pageSize);

    // 获取数据库下的所有表详情（表名称，描述）
    Map<String, Object> getTableDetails(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName, Integer pageNo, Integer pageSize);

    // 获取表的所有字段
    List<Field> getFields(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName);

    // 自定义创表语句
    void createTable(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName, List<Field> fieldList, Map<String, Object> specialMap);

    // 删除表
    void dropTable(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName);

    // 判断表是否存在
    Boolean isExistTable(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName);

    // 清空表
    void truncate(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName);

    void addField(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName, List<Field> fields);

    void batchInsert(DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName, List<Map<String, Object>> datas);

    void createSchema(DataSourceBasicInfo dataSourceBasicInfo, String databaseName);

    void execDMlSql(JdbcTemplate jdbcTemplate, String sql);

    void execDdlSql(JdbcTemplate jdbcTemplate, String sql);

    // DQL语句
    List<Map<String, Object>> execDqlSql(JdbcTemplate jdbcTemplate, String sql);



}

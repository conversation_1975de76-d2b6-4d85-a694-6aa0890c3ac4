package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.base.api.domain.ProductVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwdMaterialMara;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwdMaterialMaraMapper extends BaseMapper<DwdMaterialMara> {

    IPage<DwdMaterialMara> selectAll(@Param("materialType") String materialType,
                                     @Param("materialGroup") String materialGroup,
                                     @Param("materialId") String materialId, IPage<DwdMaterialMara> page, @Param("sort") String sort,
                                     @Param("searchVo") SearchVo searchVo);

    List<DwdMaterialMara> selectNoPage(@Param("materialType") String materialType,
                                  @Param("materialGroup") String materialGroup,
                                  @Param("materialId") String materialId,
                                  @Param("sort") String sort,
                                  @Param("searchVo") SearchVo searchVo);

    List<String> selectProduct(String productId);

    List<String> selectMaterialGroup(String materialGroup);

    List<String> selectMaterialType(String materialType);

    List<String> selectMaterialId(String materialId);

    IPage<ProductVo> selectProductList(@Param("productId") String productId,
                                       @Param("productDesc") String productDesc,
                                       @Param("productCimId") String productCimId, IPage<ProductVo> page, @Param("sort") String sort,
                                       @Param("searchVo") SearchVo searchVo);

    IPage<ProductVo> selectProductListByTableName(@Param("productId") String productId,
                                                  @Param("productDesc") String productDesc,
                                                  @Param("productCimId") String productCimId,
                                                  @Param("tableName") String tableName,
                                                  IPage<ProductVo> page, @Param("sort") String sort,
                                                  @Param("searchVo") SearchVo searchVo);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DataBalanceMapper">

    <select id="selectHeadByProductStatus" resultType="com.datalink.fdop.engine.model.vo.DataBalanceVo">
        select factory_id, product_id, lot_type, count(qty1) as qty
        from fccm.product_status
        where datatime &lt;= to_timestamp('${time}','yyyy-mm-dd hh24:mi:ss')
        group by factory_id, product_id, lot_type
    </select>

    <select id="selectItemByProductStatus" resultType="com.datalink.fdop.engine.model.vo.DataBalanceVo">
        select factory_id, product_id, lot_type, lot_id, count(qty1) as qty
        from fccm.product_status
        where datatime &lt;= to_timestamp('${time}','yyyy-mm-dd hh24:mi:ss')
        group by factory_id, product_id, lot_type, lot_id
    </select>

    <select id="selectHeadByEventStatus" resultType="com.datalink.fdop.engine.model.vo.DataBalanceVo">
        select factory_id, product_id, lot_type, count(qty1) as qty, event_code
        from fccm.event_status
        where trigger_time &lt;= to_timestamp('${beginTime}','yyyy-mm-dd hh24:mi:ss')
          and trigger_time >= to_timestamp('${endTime}','yyyy-mm-dd hh24:mi:ss')
        group by factory_id, product_id, lot_type, event_code
    </select>

    <select id="selectItemByEventStatus" resultType="com.datalink.fdop.engine.model.vo.DataBalanceVo">
        select factory_id, product_id, lot_type, lot_id, count(qty1) as qty, event_code
        from fccm.event_status
        where trigger_time &lt;= to_timestamp('${beginTime}','yyyy-mm-dd hh24:mi:ss')
          and trigger_time >= to_timestamp('${endTime}','yyyy-mm-dd hh24:mi:ss')
        group by factory_id, product_id, lot_type, lot_id, event_code
    </select>

</mapper>
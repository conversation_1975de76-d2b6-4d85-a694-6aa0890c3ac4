package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwdMaterialMarm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwdMaterialMarmMapper extends BaseMapper<DwdMaterialMarm> {

    IPage<DwdMaterialMarm> selectAll(
                                     @Param("materialId") String materialId, @Param("page") IPage<DwdMaterialMarm> page, @Param("sort") String sort,
                                     @Param("searchVo") SearchVo searchVo);

    List<DwdMaterialMarm> selectNoPage(@Param("materialId") String materialId,
                                      @Param("sort") String sort,
                                      @Param("searchVo") SearchVo searchVo);
}

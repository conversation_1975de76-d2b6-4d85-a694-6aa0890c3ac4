package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdPlanExp;

import java.util.List;

public interface DwdPlanExpService extends IService<DwdPlanExp> {

    PageDataInfo<DwdPlanExp> overview(
            String verId, String companyId, String dateFrom, String dateTo,
            String sort, SearchVo searchVo);

    List<DwdPlanExp> selectNoPage(String verId, String companyId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo);
}
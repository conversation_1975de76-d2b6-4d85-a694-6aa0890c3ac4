package com.datalink.fdop.drive.plugin.datasource.sftp.service.impl;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.drive.api.domain.FileSystem;
import com.datalink.fdop.drive.api.plugin.FileCommonService;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SFTPFileCommonServiceImpl implements FileCommonService {

    private static final Logger logger = LoggerFactory.getLogger(SFTPFileCommonServiceImpl.class);


    @Override
    public String getDbType() {
        return "sftp";
    }

    @Override
    public String getVersion() {
        return "2.0.0";
    }


    /**
     * 打开SSH连接
     * @param dataSourceBasicInfo 连接信息
     * @return session
     * @throws Exception
     */
    private Session openSession(DataSourceBasicInfo dataSourceBasicInfo) {
        JSch jsch = new JSch();
        Session session = null;
        try {
            session = jsch.getSession(dataSourceBasicInfo.getUsername(), dataSourceBasicInfo.getHost(), dataSourceBasicInfo.getPort());
        } catch (JSchException e) {
            throw new RuntimeException("SFTP登录失败:" + e.getMessage());
        }
        session.setPassword(dataSourceBasicInfo.getPassword());
        // Avoid asking for key confirmation
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);
        try {
            session.connect();
        } catch (JSchException e) {
            throw new RuntimeException("SFTP连接失败:" + e.getMessage());
        }
        return session;
    }

    @Override
    public DataSourceBasicInfo getDataSourceBasicInfo(DataSourceBasicInfo dataSourceBasicInfo) {
        return dataSourceBasicInfo;
    }


    @Override
    public boolean connect(DataSourceBasicInfo dataSourceBasicInfo) {
        Session session = openSession(dataSourceBasicInfo);
        try {
            ChannelSftp sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
            sftpChannel.disconnect();
            return true;
        } catch (JSchException e) {
            throw new RuntimeException("SFTP连接失败:" + e.getMessage());
        }
    }

    @Override
    public List<FileSystem> searchDirectoryAndFiles(DataSourceBasicInfo dataSourceBasicInfo, String folderPath, SearchVo searchVo, boolean useDir) {
        List<FileSystem> fileSystems = new ArrayList<>();
        Session session = openSession(dataSourceBasicInfo);
        try {
            ChannelSftp sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
            Vector<ChannelSftp.LsEntry> entries = sftpChannel.ls(folderPath);
            for (ChannelSftp.LsEntry entry : entries) {
                if (entry.getFilename().equals(".") || entry.getFilename().equals("..")){
                    continue;
                }
                FileSystem fileSystem = new FileSystem();
                fileSystem.setPath(folderPath + "/" + entry.getFilename());
                fileSystem.setFileName(entry.getFilename());
                fileSystem.setFileSize(entry.getAttrs().getSize() + "");
                fileSystem.setType(entry.getAttrs().isDir() ? "dir" : "file");
                fileSystem.setFileModifyTime(DateUtils.getTime(new Date(entry.getAttrs().getMTime() * 1000L)));
                fileSystems.add(fileSystem);
            }
            // 只查询目录
            if (useDir){
                return fileSystems.stream().filter(fileSystem -> fileSystem.getType().equals("dir")).collect(Collectors.toList());
            }
            sftpChannel.disconnect();
        } catch (Exception e) {
            logger.error("检索目录失败", e);
            throw new RuntimeException("检索目录失败:" + e.getMessage());
        } finally {
            session.disconnect();
        }
        return fileSystems;
    }


    @Override
    public boolean deleteFile(DataSourceBasicInfo dataSourceBasicInfo, String filePath) {
        Session session = openSession(dataSourceBasicInfo);
        try {
            ChannelSftp sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
            sftpChannel.rm(filePath);
            sftpChannel.disconnect();
            return true;
        } catch (Exception e) {
            logger.error("删除文件失败", e);
            throw new RuntimeException("删除文件失败:" + e.getMessage());
        } finally {
            session.disconnect();
        }
    }

    @Override
    public boolean createDirectory(DataSourceBasicInfo dataSourceBasicInfo, String folderPath) {
        Session session = openSession(dataSourceBasicInfo);
        try {
            ChannelSftp sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
            sftpChannel.mkdir(folderPath);
            sftpChannel.disconnect();
            return true;
        } catch (Exception e) {
            logger.error("创建目录失败", e);
            throw new RuntimeException("创建目录失败:" + e.getMessage());
        } finally {
            session.disconnect();
        }
    }

}

package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.CapacityAttribute;
import com.datalink.fdop.common.core.search.vo.SearchVo;

import java.util.List;

public interface CapacityAttributeService extends IService<CapacityAttribute> {

    Page<CapacityAttribute> pageList(Page<CapacityAttribute> page, SearchVo searchVo, String sort);

    String checkIdUnique(String equipId,  String attributeId);

    int deleteByIds(List<CapacityAttribute> ids);

    CapacityAttribute selectById(String id);

    int create(CapacityAttribute capacityAttribute);

    int update(CapacityAttribute capacityAttribute);

    boolean updateBatchStatus(Boolean enable, List<CapacityAttribute> statusList);

    String importData(List<CapacityAttribute> list, Boolean isUpdateSupport, String operName);
}

package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.engine.service.SettleService;
import com.datalink.fdop.engine.service.VarService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-02-28 14:11
 */
@RestController
@RequestMapping("/fccm/var")
public class VarController {

    @Autowired
    private VarService varService;

    @ApiOperation("差异分摊结算 - 凭证 - temp")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/postingTemp")
    public R postingTemp(@RequestParam(value = "verId") String verId,
                     @RequestParam(value = "companyId") String companyId,
                     @RequestParam(value = "year") Long year,
                     @RequestParam(value = "month") Long month,
                     @RequestParam(value = "businessType") String businessType,
                     @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(varService.posting(verId, companyId, year, month, businessType, searchVo, "temp"));
    }

    @ApiOperation("差异分摊结算 - 凭证 - s1")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/postingS1")
    public R postingS1(@RequestParam(value = "verId") String verId,
                   @RequestParam(value = "companyId") String companyId,
                   @RequestParam(value = "year") Long year,
                   @RequestParam(value = "month") Long month,
                   @RequestParam(value = "businessType") String businessType,
                   @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(varService.posting(verId, companyId, year, month, businessType, searchVo, "s1"));
    }

    @ApiOperation("差异分摊结算 - 凭证 - s2")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/postingS2")
    public R postingS2(@RequestParam(value = "verId") String verId,
                   @RequestParam(value = "companyId") String companyId,
                   @RequestParam(value = "year") Long year,
                   @RequestParam(value = "month") Long month,
                   @RequestParam(value = "businessType") String businessType,
                   @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(varService.posting(verId, companyId, year, month, businessType, searchVo, "s2"));
    }

    @ApiOperation("差异分摊结算 - PPV分摊 - temp")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/ppvTemp")
    public R ppvTemp(@RequestParam(value = "verId") String verId,
                @RequestParam(value = "companyId") String companyId,
                @RequestParam(value = "year") Long year,
                @RequestParam(value = "month") Long month,
                @RequestParam(value = "businessType") String businessType,
                @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(varService.ppv(verId, companyId, year, month, businessType, searchVo, "temp"));
    }

    @ApiOperation("差异分摊结算 - PPV分摊 - s1")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/ppvS1")
    public R ppvS1(@RequestParam(value = "verId") String verId,
                @RequestParam(value = "companyId") String companyId,
                @RequestParam(value = "year") Long year,
                @RequestParam(value = "month") Long month,
                @RequestParam(value = "businessType") String businessType,
                @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(varService.ppv(verId, companyId, year, month, businessType, searchVo, "s1"));
    }

    @ApiOperation("差异分摊结算 - PPV分摊 - s2")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/ppvS2")
    public R ppvS2(@RequestParam(value = "verId") String verId,
                  @RequestParam(value = "companyId") String companyId,
                  @RequestParam(value = "year") Long year,
                  @RequestParam(value = "month") Long month,
                  @RequestParam(value = "businessType") String businessType,
                  @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(varService.ppv(verId, companyId, year, month, businessType, searchVo, "s2"));
    }

    @ApiOperation("差异分摊结算 - MPV分摊 - temp")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/mpvTemp")
    public R mpvTemp(@RequestParam(value = "verId") String verId,
                  @RequestParam(value = "companyId") String companyId,
                  @RequestParam(value = "year") Long year,
                  @RequestParam(value = "month") Long month,
                  @RequestParam(value = "businessType") String businessType,
                  @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(varService.mpv(verId, companyId, year, month, businessType, searchVo, "temp"));
    }

    @ApiOperation("差异分摊结算 - MPV分摊 - s1")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/mpvS1")
    public R mpvS1(@RequestParam(value = "verId") String verId,
                     @RequestParam(value = "companyId") String companyId,
                     @RequestParam(value = "year") Long year,
                     @RequestParam(value = "month") Long month,
                     @RequestParam(value = "businessType") String businessType,
                     @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(varService.mpv(verId, companyId, year, month, businessType, searchVo, "s1"));
    }

    @ApiOperation("差异分摊结算 - MPV分摊 - s2")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/mpvS2")
    public R mpvS2(@RequestParam(value = "verId") String verId,
                     @RequestParam(value = "companyId") String companyId,
                     @RequestParam(value = "year") Long year,
                     @RequestParam(value = "month") Long month,
                     @RequestParam(value = "businessType") String businessType,
                     @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(varService.mpv(verId, companyId, year, month, businessType, searchVo, "s2"));
    }

    @ApiOperation("差异分摊结算 - 保存")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/varS1Save")
    public R varS1Save(@RequestParam(value = "verId") String verId,
                   @RequestParam(value = "companyId") String companyId,
                   @RequestParam(value = "year") Long year,
                   @RequestParam(value = "month") Long month,
                   @RequestParam(value = "businessType") String businessType) {
        return R.ok(varService.varSave(verId, companyId, year, month, businessType, "s1"));
    }

    @ApiOperation("差异分摊结算 - 结算")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/varS2Save")
    public R varS2Save(@RequestParam(value = "verId") String verId,
                   @RequestParam(value = "companyId") String companyId,
                   @RequestParam(value = "year") Long year,
                   @RequestParam(value = "month") Long month,
                   @RequestParam(value = "businessType") String businessType) {
        return R.ok(varService.varSave(verId, companyId, year, month, businessType, "s2"));
    }

    @ApiOperation("差异分摊结算 - 保存")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/undoS1Save")
    public R undoS1Save(@RequestParam(value = "verId") String verId,
                       @RequestParam(value = "companyId") String companyId,
                       @RequestParam(value = "year") Long year,
                       @RequestParam(value = "month") Long month,
                       @RequestParam(value = "businessType") String businessType) {
        return R.ok(varService.varUndo(verId, companyId, year, month, businessType, "s1"));
    }

    @ApiOperation("差异分摊结算 - 结算")
    @Log(title = "FCCM功能报表")
    @PostMapping(value = "/undoS2Save")
    public R undoS2Save(@RequestParam(value = "verId") String verId,
                       @RequestParam(value = "companyId") String companyId,
                       @RequestParam(value = "year") Long year,
                       @RequestParam(value = "month") Long month,
                       @RequestParam(value = "businessType") String businessType) {
        return R.ok(varService.varUndo(verId, companyId, year, month, businessType, "s2"));
    }


}

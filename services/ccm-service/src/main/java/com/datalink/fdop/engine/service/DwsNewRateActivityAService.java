package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRateActivityA;

import java.util.List;

public interface DwsNewRateActivityAService extends IService<DwsNewRateActivityA> {

    PageDataInfo<DwsNewRateActivityA> overview(
            String controlAreaId,
            String verId,
            String companyId,
            String costCenterType,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo);

    List<DwsNewRateActivityA> selectNoPage(String controlAreaId,
                                           String verId,
                                           String companyId,
                                           String costCenterType,
                                           String yearMonthFrom,
                                           String yearMonthTo,
                                           String sort,
                                           SearchVo searchVo);
}
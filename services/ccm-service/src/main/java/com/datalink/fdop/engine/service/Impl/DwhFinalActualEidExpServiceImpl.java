package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalActualEidExp;
import com.datalink.fdop.engine.mapper.DwhFinalActualEidExpMapper;
import com.datalink.fdop.engine.service.DwhFinalActualEidExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalActualEidExpServiceImpl extends ServiceImpl<DwhFinalActualEidExpMapper, DwhFinalActualEidExp> implements DwhFinalActualEidExpService {

    @Autowired
    private DwhFinalActualEidExpMapper dwhFinalActualEidExpMapper;

    @Override
    public PageDataInfo<DwhFinalActualEidExp> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwhFinalActualEidExp> page = PageUtils.getPage(DwhFinalActualEidExp.class);
        IPage<DwhFinalActualEidExp> iPage = dwhFinalActualEidExpMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalActualEidExp> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwhFinalActualEidExpMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
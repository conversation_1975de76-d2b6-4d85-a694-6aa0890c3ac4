package com.datalink.fdop.stream.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.stream.api.domain.StreamSavePoint;

import java.util.List;

/**
 * 流任务保存点Service
 */
public interface IStreamSavePointService {

    int create(StreamSavePoint savePoint);

    int update(StreamSavePoint savePoint);

    int delete(List<Long> ids, Long streamId);

    PageDataInfo<StreamSavePoint> selectSavePointByStreamId(Long streamId, String sort, SearchVo searchVo);

}


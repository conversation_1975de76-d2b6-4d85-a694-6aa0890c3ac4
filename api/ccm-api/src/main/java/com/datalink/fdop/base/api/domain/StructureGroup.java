package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "zjdata", value = "ccm_cost_structure_group")
@ApiModel
public class StructureGroup {

    @Excel(name = "组件分组")
    @ApiModelProperty(value = "组件分组")
    @NotBlank(message = "组件分组不能为空")
    @TableId
    private String costStructureGroupId;

    @Excel(name = "组件层级")
    @ApiModelProperty(value = "组件层级")
    private Long costLevel;

    @Excel(name = "组件分组描述")
    @ApiModelProperty(value = "组件分组描述")
    private String costStructureGroupDesc;

    @Excel(name = "组件分组文本")
    @ApiModelProperty(value = "组件分组文本")
    private String costStructureGroupText;

    @Excel(name = "上层组码")
    @ApiModelProperty(value = "上层组码")
    @NotBlank(message = "上层组码不能为空")
    private String menuId;

    @TableField(exist = false)
    @ApiModelProperty(value = "上层组码描述")
    private String menuDesc;

    @Excel(name = "是否启用", readConverterExp = "true=启用,false=不启用")
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "创建人")
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更改人")
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;
}

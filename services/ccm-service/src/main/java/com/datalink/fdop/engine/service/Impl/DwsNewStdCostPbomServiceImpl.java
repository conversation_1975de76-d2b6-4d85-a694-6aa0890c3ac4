package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostPbom;
import com.datalink.fdop.engine.mapper.DwsNewStdCostPbomMapper;
import com.datalink.fdop.engine.service.DwsNewStdCostPbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewStdCostPbomServiceImpl extends ServiceImpl<DwsNewStdCostPbomMapper, DwsNewStdCostPbom> implements DwsNewStdCostPbomService {

    @Autowired
    private DwsNewStdCostPbomMapper dwsNewStdCostPbomMapper;

    @Override
    public PageDataInfo<DwsNewStdCostPbom> overview(
            String controlAreaId, String productId,
            String verId, String plantId, String date,
            String sort, SearchVo searchVo) {
        Page<DwsNewStdCostPbom> page = PageUtils.getPage(DwsNewStdCostPbom.class);
        IPage<DwsNewStdCostPbom> iPage = dwsNewStdCostPbomMapper.selectAll(controlAreaId, productId, verId, plantId, date, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewStdCostPbom> listAll() {
        return dwsNewStdCostPbomMapper.listAll();
    }

    @Override
    public List<DwsNewStdCostPbom> selectNoPage(String controlAreaId, String productId, String verId, String plantId, String date, String sort, SearchVo searchVo) {
        return dwsNewStdCostPbomMapper.selectNoPage(controlAreaId, productId, verId, plantId, date, sort, searchVo);
    }


}

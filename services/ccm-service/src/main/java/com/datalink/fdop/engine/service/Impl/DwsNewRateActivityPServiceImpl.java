package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRateActivityP;
import com.datalink.fdop.engine.mapper.DwsNewRateActivityPMapper;
import com.datalink.fdop.engine.service.DwsNewRateActivityPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRateActivityPServiceImpl extends ServiceImpl<DwsNewRateActivityPMapper, DwsNewRateActivityP> implements DwsNewRateActivityPService {

    @Autowired
    private DwsNewRateActivityPMapper dwsNewRateActivityPMapper;

    @Override
    public PageDataInfo<DwsNewRateActivityP> overview(
            String controlAreaId,
            String verId,
            String companyId,
            String costCenterType,
            String yearMonthFrom,
            String yearMonthTo,
            String sort,
            SearchVo searchVo) {
        Page<DwsNewRateActivityP> page = PageUtils.getPage(DwsNewRateActivityP.class);
        IPage<DwsNewRateActivityP> iPage = dwsNewRateActivityPMapper.selectAll(
                controlAreaId, verId, companyId, costCenterType, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRateActivityP> selectNoPage(String controlAreaId, String verId, String companyId, String costCenterType, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwsNewRateActivityPMapper.selectNoPage(controlAreaId, verId, companyId, costCenterType, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}
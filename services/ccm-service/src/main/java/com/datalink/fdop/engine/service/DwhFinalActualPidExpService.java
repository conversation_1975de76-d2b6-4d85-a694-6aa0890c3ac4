package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalActualPidExp;

import java.util.List;

public interface DwhFinalActualPidExpService extends IService<DwhFinalActualPidExp> {

    PageDataInfo<DwhFinalActualPidExp> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwhFinalActualPidExp> selectNoPage(String verId,
                                            String plantId,
                                            String yearMonth,
                                            String sort,
                                            SearchVo searchVo);
}
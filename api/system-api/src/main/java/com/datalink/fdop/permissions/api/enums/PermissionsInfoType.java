package com.datalink.fdop.permissions.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:47
 */
public enum PermissionsInfoType {

    // 页面
    PAGE(0, "PAGE"),
    // 组件
    COMPONENT(1, "COMPONENT"),
    // 按钮
    BUTTON(2, "BUTTON"),
    // 接口
    INTERFACE(3, "INTERFACE"),
    // 查询ID
    MAPPER(4, "MAPPER"),
    // 字段
    FIELD(5, "FIELD"),
    ;

    private final int code;
    @EnumValue
    private final String desc;

    PermissionsInfoType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}

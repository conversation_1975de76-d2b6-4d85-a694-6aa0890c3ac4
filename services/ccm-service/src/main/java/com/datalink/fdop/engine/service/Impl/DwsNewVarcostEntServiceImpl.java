package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewVarcostEnt;
import com.datalink.fdop.engine.mapper.DwsNewVarcostEntMapper;
import com.datalink.fdop.engine.service.DwsNewVarcostEntService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewVarcostEntServiceImpl extends ServiceImpl<DwsNewVarcostEntMapper, DwsNewVarcostEnt> implements DwsNewVarcostEntService {

    @Autowired
    private DwsNewVarcostEntMapper dwsNewVarcostEntMapper;

    @Override
    public PageDataInfo<DwsNewVarcostEnt> overview(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwsNewVarcostEnt> page = PageUtils.getPage(DwsNewVarcostEnt.class);
        IPage<DwsNewVarcostEnt> iPage = dwsNewVarcostEntMapper.selectAll(verId, plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewVarcostEnt> listAll() {
        return dwsNewVarcostEntMapper.listAll();
    }

    @Override
    public List<DwsNewVarcostEnt> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwsNewVarcostEntMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
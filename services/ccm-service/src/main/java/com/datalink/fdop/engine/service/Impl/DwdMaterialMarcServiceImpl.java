package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdMaterialMarc;
import com.datalink.fdop.engine.mapper.DwdMaterialMarcMapper;
import com.datalink.fdop.engine.service.DwdMaterialMarcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdMaterialMarcServiceImpl extends ServiceImpl<DwdMaterialMarcMapper, DwdMaterialMarc> implements DwdMaterialMarcService {

    @Autowired
    private DwdMaterialMarcMapper dwdMaterialMarcMapper;

    @Override
    public PageDataInfo<DwdMaterialMarc> overview(
                                                  String materialId,String plantId,String sort, SearchVo searchVo) {
        Page<DwdMaterialMarc> page = PageUtils.getPage(DwdMaterialMarc.class);
        IPage<DwdMaterialMarc> iPage = dwdMaterialMarcMapper.selectAll( materialId,plantId,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdMaterialMarc> selectNoPage(String materialId, String plantId, String sort, SearchVo searchVo) {
        return dwdMaterialMarcMapper.selectNoPage(materialId, plantId, sort, searchVo);
    }
}

package com.datalink.fdop.common.core.utils.age;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class MenuToAgeCypher {

    public static final List<String> pMenuCodes = Arrays.asList(
            "DataRuleManagement",
            "StandardCostSystem",
            "ActualCostSystem",
            "ManagementAnalysisSystem",
            "RootCauseAnalysisSystem"
    );

    public static void main(String[] args) {
        String filePath = "D:\\luzongzhu\\develop\\data-platform\\common\\common-core\\src\\main\\java\\com\\datalink\\fdop\\common\\core\\utils\\age\\sys_menu_date.json";
        List<Menu> menus = parseJsonFile(filePath);
        // visible = 0 显示 1隐藏
        // 构建菜单树
        Map<Integer, MenuNode> menuMap = new HashMap<>();
        for (Menu menu : menus) {
            if (!"0".equals(menu.visible)) {
                continue;
            }
            MenuNode node = new MenuNode(menu);
            menuMap.put(menu.getMenuId(), node);
        }

        MenuNode root = new MenuNode(new Menu()); // 假设有一个虚拟根节点
        for (MenuNode node : menuMap.values()) {
            if (!"0".equals(node.menu.visible)) {
                continue;
            }
            if (node.menu.getParentId() == 0 && pMenuCodes.contains(node.menu.getMenuCode())) {
                root.children.add(node);
            } else {
                MenuNode parent = menuMap.get(node.menu.getParentId());
                if (parent != null) {
                    parent.children.add(node);
                }
            }
        }

        // 按照 orderNum 排序子节点
        sortMenuTree(root);

        // 构建 Cypher 语句
        StringBuilder cypherBuilder = new StringBuilder();
        cypherBuilder.append("SELECT COUNT(1) from ag_catalog.cypher('zjdata_graph', $$\n");

        buildCypher(root, cypherBuilder);

        cypherBuilder.append("$$) as (m ag_catalog.agtype);");
        System.out.println(cypherBuilder.toString());
    }

    private static void sortMenuTree(MenuNode node) {
        node.children.sort(Comparator.comparingInt(m -> m.menu.getOrderNum()));
        for (MenuNode child : node.children) {
            sortMenuTree(child);
        }
    }

    private static void buildCypher(MenuNode node, StringBuilder cypherBuilder) {
        for (MenuNode child : node.children) {
            cypherBuilder.append("\tCREATE(:sys_menu {")
                    .append("path: '").append(child.menu.getPath()).append("', ")
                    .append("menuId: ").append(child.menu.getMenuId()).append(", ")
                    .append("status: '").append(child.menu.getStatus()).append("', ")
                    .append("isFrame: '").append(child.menu.getIsFrame()).append("', ")
                    .append("visible: '").append(child.menu.getVisible()).append("', ")
                    .append("createBy: '").append(child.menu.getCreateBy()).append("', ")
                    .append("menuCode: '").append(child.menu.getMenuCode()).append("', ")
                    .append("menuName: '").append(child.menu.getMenuName()).append("', ")
                    .append("menuType: '").append(child.menu.getMenuType()).append("', ")
                    .append("orderNum: ").append(child.menu.getOrderNum()).append(", ")
                    .append("parentId: ").append(child.menu.getParentId()).append(", ")
                    .append("createTime: '").append(child.menu.getCreateTime()).append("', ")
                    .append("updateTime: '").append(child.menu.getUpdateTime()).append("'");
            if (child.menu.getIcon() != null) {
                cypherBuilder.append(", icon: '").append(child.menu.getIcon()).append("'");
            }
            if (child.menu.getPageType() != null) {
                cypherBuilder.append(", pageType: '").append(child.menu.getPageType()).append("'");
            }
            if (child.menu.getDefaultOpen() != null) {
                cypherBuilder.append(", defaultOpen: '").append(child.menu.getDefaultOpen()).append("'");
            }
            if (child.menu.getReportId() != null) {
                cypherBuilder.append(", reportId: '").append(child.menu.getReportId()).append("'");
            }
            if (child.menu.getPerms() != null) {
                cypherBuilder.append(", perms: '").append(child.menu.getPerms()).append("'");
            }
            if (child.menu.getIsCache() != null) {
                cypherBuilder.append(", isCache: '").append(child.menu.getIsCache()).append("'");
            }
            cypherBuilder.append("})\n");

            // 递归构建子节点
            buildCypher(child, cypherBuilder);
        }
    }

    private static List<Menu> parseJsonFile(String filePath) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Menu> menus = new ArrayList<>();

        try {
            File file = new File(filePath);
            JsonNode rootNode = objectMapper.readTree(file);
            for (JsonNode node : rootNode) {
                Menu menu = new Menu();
                menu.setPath(node.path("path").asText());
                menu.setMenuId(node.path("menuId").asInt());
                menu.setStatus(node.path("status").asText());
                menu.setIsFrame(node.path("isFrame").asText());
                menu.setVisible(node.path("visible").asText());
                menu.setCreateBy(node.path("createBy").asText());
                menu.setMenuCode(node.path("menuCode").asText());
                menu.setMenuName(node.path("menuName").asText());
                menu.setMenuType(node.path("menuType").asText());
                menu.setOrderNum(node.path("orderNum").asInt());
                menu.setParentId(node.path("parentId").asInt());
                menu.setCreateTime(node.path("createTime").asText());
                menu.setUpdateTime(node.path("updateTime").asText());
                menu.setIcon(node.path("icon").asText(null));
                menu.setPageType(node.path("pageType").asText(null));
                menu.setDefaultOpen(node.path("defaultOpen").asText(null));
                menu.setReportId(node.path("reportId").asText(null));
                menu.setPerms(node.path("perms").asText(null));
                menu.setIsCache(node.path("isCache").asText(null));
                menus.add(menu);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return menus;
    }

    static class Menu {
        private String path;
        private int menuId;
        private String status;
        private String isFrame;
        private String visible;
        private String createBy;
        private String menuCode;
        private String menuName;
        private String menuType;
        private int orderNum;
        private int parentId;
        private String createTime;
        private String updateTime;
        private String icon;
        private String pageType;
        private String defaultOpen;
        private String reportId;
        private String perms;
        private String isCache;

        // Getters and Setters
        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public int getMenuId() {
            return menuId;
        }

        public void setMenuId(int menuId) {
            this.menuId = menuId;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getIsFrame() {
            return isFrame;
        }

        public void setIsFrame(String isFrame) {
            this.isFrame = isFrame;
        }

        public String getVisible() {
            return visible;
        }

        public void setVisible(String visible) {
            this.visible = visible;
        }

        public String getCreateBy() {
            return createBy;
        }

        public void setCreateBy(String createBy) {
            this.createBy = createBy;
        }

        public String getMenuCode() {
            return menuCode;
        }

        public void setMenuCode(String menuCode) {
            this.menuCode = menuCode;
        }

        public String getMenuName() {
            return menuName;
        }

        public void setMenuName(String menuName) {
            this.menuName = menuName;
        }

        public String getMenuType() {
            return menuType;
        }

        public void setMenuType(String menuType) {
            this.menuType = menuType;
        }

        public int getOrderNum() {
            return orderNum;
        }

        public void setOrderNum(int orderNum) {
            this.orderNum = orderNum;
        }

        public int getParentId() {
            return parentId;
        }

        public void setParentId(int parentId) {
            this.parentId = parentId;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getPageType() {
            return pageType;
        }

        public void setPageType(String pageType) {
            this.pageType = pageType;
        }

        public String getDefaultOpen() {
            return defaultOpen;
        }

        public void setDefaultOpen(String defaultOpen) {
            this.defaultOpen = defaultOpen;
        }

        public String getReportId() {
            return reportId;
        }

        public void setReportId(String reportId) {
            this.reportId = reportId;
        }

        public String getPerms() {
            return perms;
        }

        public void setPerms(String perms) {
            this.perms = perms;
        }

        public String getIsCache() {
            return isCache;
        }

        public void setIsCache(String isCache) {
            this.isCache = isCache;
        }
    }

    static class MenuNode {
        Menu menu;
        List<MenuNode> children;

        public MenuNode(Menu menu) {
            this.menu = menu;
            this.children = new ArrayList<>();
        }
    }
}

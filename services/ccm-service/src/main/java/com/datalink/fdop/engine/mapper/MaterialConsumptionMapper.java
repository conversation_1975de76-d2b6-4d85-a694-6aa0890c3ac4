package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.MaterialConsumption;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:34
 */
public interface MaterialConsumptionMapper extends BaseMapper<MaterialConsumption> {

    List<MaterialConsumption> getOrg(@Param("sort") String sort, @Param("searchCondition") SearchVo searchCondition);

    List<MaterialConsumption> getEdit(@Param("sort") String sort, @Param("searchCondition") SearchVo searchVo);

    List<MaterialConsumption> getAll(@Param("sort") String sort, @Param("searchCondition") SearchVo searchVo);

    void add(@Param("sql") String sql);

    void updateByKey(MaterialConsumption materialConsumption);

    MaterialConsumption selectByKey(MaterialConsumption materialConsumption);

    int deleteByKey(MaterialConsumption materialConsumption);
}

package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.MaterialPrice;
import com.datalink.fdop.base.api.model.FlowTree;
import com.datalink.fdop.base.api.domain.Material;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface MatnrPriceService extends IService<MaterialPrice> {

    boolean insert(MaterialPrice materialPrice);

    String checkUnique(MaterialPrice materialPrice);

    PageDataInfo<MaterialPrice> list(MaterialPrice materialPrice);

    int deleteByIds(List<MaterialPrice> materialPriceList);

    String importData(List<MaterialPrice> list, Boolean isUpdateSupport, String operName);

    boolean copy(Long pid, List<FlowTree> list);

    PageDataInfo<MaterialPrice> overview(Long pid, String sort, SearchVo searchVo);

    boolean isInbom(MaterialPrice materialPrice);

    int update(MaterialPrice materialPrice);

    PageDataInfo<Material> tree(String sort, SearchVo searchVo);

    MaterialPrice selectPrice(Long materialId, Long verId, Long factoryId);

    int updateByKey(MaterialPrice materialPrice);

}

package com.datalink.fdop.permissions.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2023/3/23 16:21
 */
public enum OperatorType {

    // 等于
    EQ(0, "EQ"),
    // 不等于
    NEQ(1, "NEQ"),
    // 大于
    GT(2, "GT"),
    // 小于
    LT(3, "LT"),
    // 大于等于
    GE(4, "GE"),
    // 小于等于
    LE(5, "LE"),
    // 包含
    LIKE(6, "LIKE"),
    // 不包含
    NOT_LIKE(7, "NOT_LIKE"),
    // 区间
    INTERVAL(8, "INTERVAL"),
    IN(9, "IN"),
    NOT_IN(10, "NOT_IN"),
    ;

    private final int code;
    @EnumValue
    private final String desc;

    OperatorType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.FormulaManagementMapper">

    <select id="query" resultType="com.datalink.fdop.base.api.domain.FormulaManagement">
        select a.*, controlArea.control_area_desc as control_area_desc from zjdata.ccm_formula a
        left join zjdata.ccm_control_area controlArea on controlArea.control_area_id = a.control_area_id
        <where>
            <if test="searchVo != null and searchVo != ''">
                and ${searchVo}
            </if>
        </where>
        order by control_area_id, formula_id ${sort}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.FormulaManagement">
        select a.*, controlArea.control_area_desc as control_area_desc from zjdata.ccm_formula a
            left join zjdata.ccm_control_area controlArea on controlArea.control_area_id = a.control_area_id
        where a.control_area_id = #{controlAreaId} and a.formula_id = #{formulaId}
        limit 1
    </select>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.FormulaManagement">
        select a.*, controlArea.control_area_desc as control_area_desc from zjdata.ccm_formula a
            left join zjdata.ccm_control_area controlArea on controlArea.control_area_id = a.control_area_id
        order by control_area_id, formula_id desc
    </select>

    <select id="checkIdUnique" resultType="java.lang.Integer">
        select count(1) from zjdata.ccm_formula
        where control_area_id = #{controlAreaId} and formula_id = #{formulaId}
    </select>

    <update id="update" parameterType="com.datalink.fdop.base.api.domain.FormulaManagement">
        update zjdata.ccm_formula
        set
        <if test="formula != null">formula = #{formula},</if>
        <if test="formulaDesc != null">formula_desc = #{formulaDesc},</if>
        <if test="formulaText != null">formula_text = #{formulaText},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId} and formula_id = #{formulaId}
    </update>

    <update id="delete" parameterType="com.datalink.fdop.base.api.domain.FormulaManagement">
        delete from zjdata.ccm_formula
        where control_area_id = #{controlAreaId} and formula_id = #{formulaId}
    </update>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_formula
        SET enable = #{enable}
        where (control_area_id,formula_id) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.controlAreaId},#{item.formulaId})
        </foreach>
    </update>

    <select id="queryByPlantId" resultType="String">
        select
            distinct formula
        from
            zjdata.ccm_formula formula
        where formula.formula IS NOT NULL and formula.formula &lt;&gt; '' and formula.control_area_id in (select control_area_id from zjdata.ccm_plant plant where plant.plant_id in
                <foreach item="item" collection="plantId.split(',')" separator="," close=")" open="(" index="index">
                    #{item}
                </foreach>
        )
        order by formula.formula asc
    </select>

    <select id="queryByControlAreaId" resultType="String">
        select
        distinct formula
        from
        zjdata.ccm_formula formula
        where formula.formula IS NOT NULL and formula.formula &lt;&gt; '' and formula.control_area_id = #{controlAreaId}
        order by formula.formula asc
    </select>

</mapper>
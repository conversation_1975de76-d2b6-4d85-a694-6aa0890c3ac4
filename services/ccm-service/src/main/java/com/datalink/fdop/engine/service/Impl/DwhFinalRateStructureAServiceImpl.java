package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRateStructureA;
import com.datalink.fdop.engine.mapper.DwhFinalRateStructureAMapper;
import com.datalink.fdop.engine.service.DwhFinalRateStructureAService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRateStructureAServiceImpl extends ServiceImpl<DwhFinalRateStructureAMapper, DwhFinalRateStructureA> implements DwhFinalRateStructureAService {

    @Autowired
    private DwhFinalRateStructureAMapper dwhFinalRateStructureAMapper;

    @Override
    public PageDataInfo<DwhFinalRateStructureA> overview(
            String controlAreaId,
            String verId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String sort,
            SearchVo searchVo) {
        Page<DwhFinalRateStructureA> page = PageUtils.getPage(DwhFinalRateStructureA.class);
        IPage<DwhFinalRateStructureA> iPage = dwhFinalRateStructureAMapper.selectAll(
                controlAreaId, verId, yearMonthFrom, yearMonthTo, page, costCenterType, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRateStructureA> selectNoPage(String controlAreaId, String verId, String yearMonthFrom, String yearMonthTo, String costCenterType, String sort, SearchVo searchVo) {
        return dwhFinalRateStructureAMapper.selectNoPage(controlAreaId, verId, yearMonthFrom, yearMonthTo,
                costCenterType, sort, searchVo);
    }
}
package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationFactorA;
import com.datalink.fdop.engine.mapper.DwsNewAllocationFactorAMapper;
import com.datalink.fdop.engine.service.DwsNewAllocationFactorAService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewAllocationFactorAServiceImpl extends ServiceImpl<DwsNewAllocationFactorAMapper, DwsNewAllocationFactorA> implements DwsNewAllocationFactorAService {

    @Autowired
    private DwsNewAllocationFactorAMapper dwsNewAllocationFactorAMapper;

    @Override
    public PageDataInfo<DwsNewAllocationFactorA> overview(
            String controlAreaId,
            String verId,
            String companyId,
            String yearMonthFrom,
            String yearMonthTo,
            String costCenterType,
            String allocationType,
            String sort,
            SearchVo searchVo) {
        Page<DwsNewAllocationFactorA> page = PageUtils.getPage(DwsNewAllocationFactorA.class);
        IPage<DwsNewAllocationFactorA> iPage = dwsNewAllocationFactorAMapper.selectAll(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, costCenterType, allocationType, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewAllocationFactorA> selectNoPage(String controlAreaId, String verId, String companyId, String yearMonthFrom, String yearMonthTo, String allocationType, String costCenterType, String sort, SearchVo searchVo) {
        return dwsNewAllocationFactorAMapper.selectNoPage(controlAreaId, verId, companyId, yearMonthFrom,
                yearMonthTo, allocationType, costCenterType, sort, searchVo);
    }

}
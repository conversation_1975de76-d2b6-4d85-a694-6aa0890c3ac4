package com.datalink.fdop.engine.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.engine.service.DrillDowntService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-03-16 16:43
 */
@RestController
@RequestMapping("/fccm/DrillDownt")
public class DrillDowntController {

    @Autowired
    private DrillDowntService drillDowntService;

    @ApiOperation("查询1")
    @Log(title = "FCCM功能报表")
    @PostMapping("/query1")
    public R<PageDataInfo> query1(@RequestParam(value = "verId") String verId,
                                  @RequestParam(value = "companyId") String companyId,
                                  @RequestParam(value = "factoryId") String factoryId,
                                  @RequestParam(value = "yearMonth") Long yearMonth,
                                  @RequestParam(value = "level") String level,
                                  @RequestParam(value = "var1") String var1,
                                  @RequestParam(value = "var2") String var2,
                                  @RequestParam(required = false, defaultValue = "ASC") String sort,
                                  @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(drillDowntService.query1(verId, companyId, factoryId, yearMonth,
                level, var1, var2, sort, searchVo));
    }

    @ApiOperation("查询2")
    @Log(title = "FCCM功能报表")
    @PostMapping("/query2")
    public R<PageDataInfo> query2(@RequestParam(value = "verId") String verId,
                    @RequestParam(value = "companyId") String companyId,
                    @RequestParam(value = "factoryId") String factoryId,
                    @RequestParam(value = "yearMonth") Long yearMonth,
                    @RequestParam(value = "level") String level,
                    @RequestParam(value = "var1") String var1,
                    @RequestParam(value = "var2") String var2,
                    @RequestParam(required = false, defaultValue = "ASC") String sort,
                    @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(drillDowntService.query2(verId, companyId, factoryId, yearMonth,
                level, var1, var2, sort, searchVo));
    }

    @ApiOperation("查询3")
    @Log(title = "FCCM功能报表")
    @PostMapping("/query3")
    public R<PageDataInfo> query3(@RequestParam(value = "verId") String verId,
                    @RequestParam(value = "companyId") String companyId,
                    @RequestParam(value = "factoryId") String factoryId,
                    @RequestParam(value = "yearMonth") Long yearMonth,
                    @RequestParam(value = "level") String level,
                    @RequestParam(value = "var1") String var1,
                    @RequestParam(value = "var2") String var2,
                    @RequestParam(required = false, defaultValue = "ASC") String sort,
                    @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(drillDowntService.query3(verId, companyId, factoryId, yearMonth,
                level, var1, var2, sort, searchVo));
    }

    @ApiOperation("查询4")
    @Log(title = "FCCM功能报表")
    @PostMapping("/query4")
    public R<PageDataInfo> query4(@RequestParam(value = "verId") String verId,
                    @RequestParam(value = "companyId") String companyId,
                    @RequestParam(value = "factoryId") String factoryId,
                    @RequestParam(value = "yearMonth") Long yearMonth,
                    @RequestParam(value = "level") String level,
                    @RequestParam(value = "var1") String var1,
                    @RequestParam(value = "var2") String var2,
                    @RequestParam(required = false, defaultValue = "ASC") String sort,
                    @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(drillDowntService.query4(verId, companyId, factoryId, yearMonth,
                level, var1, var2, sort, searchVo));
    }

    @ApiOperation("查询5")
    @Log(title = "FCCM功能报表")
    @PostMapping("/query5")
    public R<PageDataInfo> query5(@RequestParam(value = "verId") String verId,
                    @RequestParam(value = "companyId") String companyId,
                    @RequestParam(value = "factoryId") String factoryId,
                    @RequestParam(value = "yearMonth") Long yearMonth,
                    @RequestParam(value = "level") String level,
                    @RequestParam(value = "var1") String var1,
                    @RequestParam(value = "var2") String var2,
                    @RequestParam(required = false, defaultValue = "ASC") String sort,
                    @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(drillDowntService.query5(verId, companyId, factoryId, yearMonth,
                level, var1, var2, sort, searchVo));
    }


}

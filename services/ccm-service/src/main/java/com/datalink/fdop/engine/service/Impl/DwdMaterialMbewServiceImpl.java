package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdMaterialMbew;
import com.datalink.fdop.engine.mapper.DwdMaterialMbewMapper;
import com.datalink.fdop.engine.service.DwdMaterialMbewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdMaterialMbewServiceImpl extends ServiceImpl<DwdMaterialMbewMapper, DwdMaterialMbew> implements DwdMaterialMbewService {

    @Autowired
    private DwdMaterialMbewMapper dwdMaterialMbewMapper;

    @Override
    public PageDataInfo<DwdMaterialMbew> overview(String materialType, String materialGroup, String materialId, String evaluateAreaId, String sort, SearchVo searchVo) {
        Page<DwdMaterialMbew> page = PageUtils.getPage(DwdMaterialMbew.class);
        IPage<DwdMaterialMbew> iPage = dwdMaterialMbewMapper.selectAll(materialType, materialGroup, materialId, evaluateAreaId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdMaterialMbew> selectNoPage(String materialType, String materialGroup, String materialId, String evaluateAreaId, String sort, SearchVo searchVo) {
        return dwdMaterialMbewMapper.selectNoPage(materialType, materialGroup, materialId, evaluateAreaId, sort, searchVo);
    }
}
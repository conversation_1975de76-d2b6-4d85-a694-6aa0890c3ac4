package com.datalink.fdop.base.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.CostStructureMenu;
import com.datalink.fdop.base.api.model.CostStructureTree;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-09-06 11:35
 */
public interface CostStructureMenuService extends IService<CostStructureMenu> {

    int create(CostStructureMenu costStructureMenu);

    int update(CostStructureMenu costStructureMenu);

    int delete(List<String> ids);

    List<String> getChildrenMenuId(List<String> ids);

    List<CostStructureTree> tree(String sort);

    List<CostStructureTree> menuTree(String sort);

    CostStructureMenu selectById(String id);

}

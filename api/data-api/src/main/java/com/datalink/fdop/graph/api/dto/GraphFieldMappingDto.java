package com.datalink.fdop.graph.api.dto;

import com.datalink.fdop.graph.api.domain.graph.GraphField;
import lombok.Data;

/**
 * 图属性映射DTO（替换时用）
 */
@Data
public class GraphFieldMappingDto {

    /**
     * 旧属性(被替换图节点的属性)
     */
    GraphField oldField;

    /**
     * 新属性(替换图节点的属性)
     */
    GraphField newField;

    public GraphFieldMappingDto() {
    }

    public GraphFieldMappingDto(GraphField oldField, GraphField newField) {
        this.oldField = oldField;
        this.newField = newField;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.ActivityMenuMapper">

    <update id="bacthUpdatePidById" parameterType="String">
        update zjdata.ccm_activity_menu
        set p_menu_id = (select p_menu_id from zjdata.ccm_activity_menu where menu_id = #{menuId} limit 1)
        where p_menu_id = #{menuId} and del_flag = false
    </update>

    <delete id="deleteByIds" parameterType="String">
        delete from zjdata.ccm_activity_menu
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag = false
    </delete>

    <select id="selectMenuTree" resultType="com.datalink.fdop.base.api.model.ActivityTree">
        select null as activity_id, null as control_area_id, a.*, 'MENU' as menu_type, null as date_from from zjdata.ccm_activity_menu a
        where a.del_flag != true
        UNION
        select b.activity_id as activity_id, b.control_area_id as control_area_id, null as menu_id, b.menu_id as p_menu_id, null as menu_desc, null as menu_text,
               b.del_flag as del_flag, 'NODE' as menu_type, b.date_from as date_from from zjdata.ccm_activity b
        where b.del_flag != true
        order by menu_id ${sort}
    </select>

    <select id="selectByPids" resultType="String">
        select * from zjdata.ccm_activity
        where menu_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag != true
        order by activity_id, date_from desc
    </select>

    <select id="selectById" resultType="com.datalink.fdop.base.api.domain.ActivityMenu">
        select * from zjdata.ccm_activity_menu
        where menu_id = #{id} and del_flag != true limit 1
    </select>

</mapper>
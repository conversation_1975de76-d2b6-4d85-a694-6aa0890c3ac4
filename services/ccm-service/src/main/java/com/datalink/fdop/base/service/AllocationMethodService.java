package com.datalink.fdop.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.AllocationMethod;
import com.datalink.fdop.base.api.domain.AllocationMethodVo;
import com.datalink.fdop.base.api.model.AllocationMethodTree;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface AllocationMethodService extends IService<AllocationMethod> {

    boolean insert(AllocationMethod allocationMethod);

    String checkUnique(String controlAreaId, String allocationMethodId);

    PageDataInfo<AllocationMethod> selectList(String sort, SearchVo searchVo);

    List<AllocationMethod> selectListNoPage(String sort, SearchVo searchVo);

    int deleteByList(List<AllocationMethod> keyList);

    String importData(List<AllocationMethod> list, Boolean isUpdateSupport, String operName);

    boolean copy(List<AllocationMethod> list);

    int updateByKey(AllocationMethod allocationMethod);

    boolean updateBatchStatus(Boolean enable, List<AllocationMethod> ids);

    List<AllocationMethodTree> selectMenuTree(String controlAreaId, String code, String sort);

    List<String> getAllocationMethod(String controlAreaId);

    List<AllocationMethodVo> selectListByControlAreaIdAndIds(String controlAreaId, List<String> allocationMethodIdList);

}

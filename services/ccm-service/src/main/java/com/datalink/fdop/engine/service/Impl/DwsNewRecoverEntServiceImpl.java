package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRecoverEnt;
import com.datalink.fdop.engine.mapper.DwsNewRecoverEntMapper;
import com.datalink.fdop.engine.service.DwsNewRecoverEntService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRecoverEntServiceImpl extends ServiceImpl<DwsNewRecoverEntMapper, DwsNewRecoverEnt> implements DwsNewRecoverEntService {

    @Autowired
    private DwsNewRecoverEntMapper dwsNewRecoverEntMapper;

    @Override
    public PageDataInfo<DwsNewRecoverEnt> overview(String verId,String plantId, String yearMonth, String sort, SearchVo searchVo) {
        Page<DwsNewRecoverEnt> page = PageUtils.getPage(DwsNewRecoverEnt.class);
        IPage<DwsNewRecoverEnt> iPage = dwsNewRecoverEntMapper.selectAll(verId,plantId, yearMonth, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRecoverEnt> listAll() {
        return dwsNewRecoverEntMapper.listAll();
    }

    @Override
    public List<DwsNewRecoverEnt> selectNoPage(String verId, String plantId, String yearMonth, String sort, SearchVo searchVo) {
        return dwsNewRecoverEntMapper.selectNoPage(verId, plantId, yearMonth, sort, searchVo);
    }
}
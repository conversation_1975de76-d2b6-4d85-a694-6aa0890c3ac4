package com.datalink.fdop.udf.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.udf.api.domain.Udf;
import com.datalink.fdop.udf.api.domain.UdfMenu;
import com.datalink.fdop.udf.api.model.vo.UdfTree;
import com.datalink.fdop.udf.service.IUdfMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/11 14:22
 */
@RequestMapping(value = "/udf/menu")
@RestController
@Api(tags = "菜单api")
public class UdfMenuController extends BaseController {

    @Autowired
    private IUdfMenuService udfMenuService;

    @ApiOperation("创建菜单")
    @Log(title = "UDF", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody UdfMenu udfMenu) {
        if (udfMenu.getPid() == null) {
            udfMenu.setPid(-1L);
        }
        return R.toResult(udfMenuService.create(udfMenu));
    }

    @ApiOperation("修改菜单")
    @Log(title = "UDF", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody UdfMenu udfMenu) {
        if (udfMenu.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(udfMenuService.update(udfMenu));
    }

    @ApiOperation("删除菜单")
    @Log(title = "UDF", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.SPECIFY_THE_UDF_MENU_TO_BE_DELETED);
        }
        return R.toResult(udfMenuService.delete(ids));
    }

    @ApiOperation("UDF树结构")
    @Log(title = "UDF")
    @GetMapping(value = "/tree")
    public R<List<UdfTree>> tree(@RequestParam(value = "sort", required = false, defaultValue = "ASC") String sort,
                                 @RequestParam(value = "code", required = false) String code,
                                 @RequestParam(value = "isQueryNode", required = false, defaultValue = "true") Boolean isQueryNode) {
        return R.ok(udfMenuService.tree(sort, code, isQueryNode));
    }

    @ApiOperation("UDF总览")
    @Log(title = "UDF")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<Udf>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                         @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                         @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(udfMenuService.overview(pid, sort, searchVo));
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.AsMapper">

    <select id="selectByPid" resultType="com.datalink.fdop.base.model.As">
        SELECT ls.*, sf.code as saknr_from_code, st.code as saknr_to_code, evaluateArea.code as bwkey_code
        FROM zjdata.p_d_as ls
            left join zjdata.p_d_saknr sf on ls.saknr_from_id = sf.id
            left join zjdata.p_d_saknr st on ls.saknr_to_id = st.id
            left join zjdata.org_bwkey evaluateArea on bwkey_id = evaluateArea.id
        where ls.pid = #{pid} limit 1
    </select>

</mapper>


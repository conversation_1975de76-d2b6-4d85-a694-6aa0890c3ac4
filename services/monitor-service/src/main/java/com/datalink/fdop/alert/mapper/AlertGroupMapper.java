package com.datalink.fdop.alert.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.alert.api.domain.AlertGroup;
import com.datalink.fdop.alert.api.model.vo.AlertGroupInstanceVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/24 10:43
 */
public interface AlertGroupMapper extends BaseMapper<AlertGroup> {

    int insertAlertGroup(@Param("alertGroup") AlertGroup alertGroup);

    int updateById(AlertGroup alertGroup);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<AlertGroup> selectById(@Param("id") Long id);

    VlabelItem<AlertGroup> selectByCode(@Param("code") String code);

    VlabelItem<AlertGroup> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    IPage<VlabelItem<AlertGroup>> selectList(IPage<VlabelItem> page, @Param(value = "alertGroup") AlertGroup alertGroup);

    int boundGroupAndUserRef(@Param("id") Long id, @Param("userIds") List<Long> userIds);

    int unbundGroupAndUserRef(@Param("id") Long alertGroupId, @Param("userIds") List<Long> userIds);

    List<SysUser> selectAuthedUser(Long id);

    int deleteGroupInstanceRef(@Param("id") Long id);

    int saveAlertGroupInstanceRef(@Param("id") Long id,
                                 @Param("alertInstanceId") Long alertInstanceId,
                                 @Param("enable") Boolean enable);

    List<AlertGroupInstanceVo> selectGroupInstance(@Param("id") Long id);

}

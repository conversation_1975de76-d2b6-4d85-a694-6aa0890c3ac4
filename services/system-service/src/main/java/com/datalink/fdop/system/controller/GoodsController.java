package com.datalink.fdop.system.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.system.api.domain.Goods;
import com.datalink.fdop.system.service.IGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 14:21
 */
@RestController
@RequestMapping("/system/goods")
@Api(tags = "商品信息")
public class GoodsController extends BaseController {

    @Autowired
    private IGoodsService goodsService;

    @ApiOperation("创建商品信息")
    @PostMapping(value = "/create")
    public R create(
            @RequestParam(value = "goodsSn") String goodsSn,
            @RequestParam(value = "productNo") String productNo,
            @RequestParam(value = "goodsName") String goodsName,
            @RequestParam(value = "marketPrice") Double marketPrice,
            @RequestParam(value = "shopPrice") Double shopPrice,
            @RequestParam(value = "warnStock") Integer warnStock,
            @RequestParam(value = "goodsStock") Integer goodsStock,
            @RequestParam(value = "goodsUnit") String goodsUnit,
            @RequestParam(value = "goodsTips") String goodsTips,
            @RequestParam(value = "img", required = false) MultipartFile img
    ) {
        return R.toResult(goodsService.create(goodsSn, productNo, goodsName, marketPrice, shopPrice, warnStock, goodsStock, goodsUnit, goodsTips, img));
    }

    @ApiOperation("修改商品信息")
    @PostMapping(value = "/update")
    public R update(
            @RequestParam(value = "goodsId") Integer goodsId,
            @RequestParam(value = "goodsSn", required = false) String goodsSn,
            @RequestParam(value = "productNo", required = false) String productNo,
            @RequestParam(value = "goodsName", required = false) String goodsName,
            @RequestParam(value = "marketPrice", required = false) Double marketPrice,
            @RequestParam(value = "shopPrice", required = false) Double shopPrice,
            @RequestParam(value = "warnStock", required = false) Integer warnStock,
            @RequestParam(value = "goodsStock", required = false) Integer goodsStock,
            @RequestParam(value = "goodsUnit", required = false) String goodsUnit,
            @RequestParam(value = "goodsTips", required = false) String goodsTips,
            @RequestParam(value = "img", required = false) MultipartFile img
    ) {
        return R.toResult(goodsService.update(goodsId, goodsSn, productNo, goodsName, marketPrice, shopPrice, warnStock, goodsStock, goodsUnit, goodsTips, img));
    }

    @ApiOperation("删除商品信息")
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Integer> ids) {
        return R.toResult(goodsService.delete(ids));
    }

    @ApiOperation("展示商品信息")
    @PostMapping(value = "/list")
    public R<PageDataInfo<Goods>> list(@RequestParam(required = false) Goods goods) {
        startPage();
        List<Goods> list = goodsService.list(goods);
        return R.ok(getPageInfo(list));
    }

    @ApiOperation("上传图片文件")
    @PostMapping(value = "/uploadImg/{id}")
    public R uploadImg(@PathVariable(value = "id") Integer id, @RequestParam(value = "img") MultipartFile img) {
        return R.toResult(goodsService.uploadImg(id, img));
    }

    @ApiOperation("查看图片文件")
    @GetMapping(value = "/viewImg/{id}")
    public ResponseEntity viewImg(@PathVariable(value = "id") Integer id) throws Exception {
        Goods goods = goodsService.viewImg(id);

        // hdfs路径
        String goodsImg = goods.getGoodsImg();
        String[] split = goodsImg.split("\\.");
        String suffix = split[split.length - 1];

        Path path = new Path(goodsImg);
        FileSystem fs = path.getFileSystem(new Configuration());
        FSDataInputStream inputStream = fs.open(path);
        byte[] bytes = new byte[1024];

        int b;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bytes1 = null;
        try {
            while ((b = inputStream.read(bytes)) != -1) {
                byteArrayOutputStream.write(bytes, 0, b);
            }
            bytes1 = byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        if ("png".equalsIgnoreCase(suffix)) {
            httpHeaders.setContentType(MediaType.IMAGE_PNG);
        } else if ("jpeg".equalsIgnoreCase(suffix) || "jpg".equalsIgnoreCase(suffix)) {
            httpHeaders.setContentType(MediaType.IMAGE_JPEG);
        } else if ("gif".equalsIgnoreCase(suffix)) {
            httpHeaders.setContentType(MediaType.IMAGE_GIF);
        }
        return new ResponseEntity<>(bytes1, httpHeaders, HttpStatus.OK);
    }

}

package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewVarcostLot;
import com.datalink.fdop.engine.mapper.DwsNewVarcostLotMapper;
import com.datalink.fdop.engine.service.DwsNewVarcostLotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewVarcostLotServiceImpl extends ServiceImpl<DwsNewVarcostLotMapper, DwsNewVarcostLot> implements DwsNewVarcostLotService {

    @Autowired
    private DwsNewVarcostLotMapper dwsNewVarcostLotMapper;

    @Override
    public PageDataInfo<DwsNewVarcostLot> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwsNewVarcostLot> page = PageUtils.getPage(DwsNewVarcostLot.class);
        IPage<DwsNewVarcostLot> iPage = dwsNewVarcostLotMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewVarcostLot> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwsNewVarcostLotMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}
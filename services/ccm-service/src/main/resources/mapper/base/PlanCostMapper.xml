<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.PlanCostMapper">

    <select id="checkCodeUnique" parameterType="com.datalink.fdop.base.api.domain.PlanCost" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_plan_cost
        WHERE control_area_id = #{planCost.controlAreaId}
          AND ver_id = #{planCost.verId}
          AND cost_center_id = #{planCost.costCenterId}
          AND cost_center_date_from = #{planCost.costCenterDateFrom}
          AND cost_element_id = #{planCost.costElementId}
          AND year_month = #{planCost.yearMonth}
          AND del_flag = false limit 1
    </select>

    <update id="deleteByIds" parameterType="Long">
        update zjdata.ccm_plan_cost
        SET del_flag = true
        WHERE
        <foreach collection="planCostList" item="planCost" separator=" or ">
            ( control_area_id = #{planCost.controlAreaId}
            AND ver_id = #{planCost.verId}
            AND cost_center_id = #{planCost.costCenterId}
            AND cost_center_date_from = #{planCost.costCenterDateFrom}
            AND cost_element_id = #{planCost.costElementId}
            AND year_month = #{planCost.yearMonth} )
        </foreach>
        and del_flag = false
    </update>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.PlanCost">
        update zjdata.ccm_plan_cost
        <set>
            <if test="amount != null">amount = #{amount},</if>
            <if test="multiple != null">multiple = #{multiple},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        where control_area_id = #{controlAreaId}
        AND ver_id = #{verId}
        AND cost_center_id = #{costCenterId}
        AND cost_center_date_from = #{costCenterDateFrom}
        AND cost_element_id = #{costElementId}
        AND year_month = #{yearMonth} AND del_flag = false
    </update>

    <select id="list" parameterType="com.datalink.fdop.base.api.domain.PlanCost" resultType="com.datalink.fdop.base.api.domain.PlanCost">
        SELECT *
        FROM zjdata.ccm_plan_cost
        WHERE del_flag = false
        ORDER BY control_area_id desc
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.PlanCost">
        SELECT ls.*
        FROM zjdata.ccm_plan_cost ls
        WHERE ls.del_flag = false
        <if test="a != null and a != ''">
            AND ${a}
        </if>
        ORDER BY control_area_id ${sort}
    </select>

    <select id="selectTableIdByCode" resultType="java.lang.Long">
        SELECT id FROM zjdata.${tableName} where
        <choose>
            <when test="tableName.equals('org_company')">company_code</when>
            <otherwise>code</otherwise>
        </choose>
        = #{code} limit 1
    </select>

</mapper>


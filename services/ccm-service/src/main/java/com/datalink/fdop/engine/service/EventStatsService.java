package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.EventStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:32
 */
public interface EventStatsService extends IService<EventStatus> {

    PageDataInfo<EventStatus> getOrg(String sort, SearchVo searchVo);

    int saveData(EventStatus eventStatus);

    PageDataInfo<EventStatus> getEdit(String sort, SearchVo searchVo);

    PageDataInfo<EventStatus> getAll(String sort, SearchVo searchVo);

    int deleteByList(List<EventStatus> list);
}

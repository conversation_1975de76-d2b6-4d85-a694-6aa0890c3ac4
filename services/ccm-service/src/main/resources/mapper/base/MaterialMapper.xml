<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.MaterialMapper">

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_material
        WHERE control_area_id = #{controlAreaId} AND material_id = #{materialId} AND del_flag = false limit 1
    </select>

    <update id="deleteByKey" parameterType="com.datalink.fdop.base.api.domain.Material">
        update zjdata.ccm_material
        set del_flag = true
        where control_area_id = #{controlAreaId} AND material_id = #{materialId} and del_flag = false
    </update>

    <update id="updateByKey">
        update zjdata.ccm_material
        set
        <if test="materialDesc != null">material_desc = #{materialDesc},</if>
        <if test="materialText != null">material_text = #{materialText},</if>
        <if test="unitBasic != null">unit_basic = #{unitBasic},</if>
        <if test="unitUsage != null">unit_usage = #{unitUsage},</if>
        <if test="rateUnitBasic != null">rate_unit_basic = #{rateUnitBasic},</if>
        <if test="rateUnitUsage != null">rate_unit_usage = #{rateUnitUsage},</if>
        <if test="materialType != null">material_type = #{materialType},</if>
        <if test="materialTypeDesc != null">material_type_desc = #{materialTypeDesc},</if>
        <if test="materialGroup != null">material_group = #{materialGroup},</if>
        <if test="materialGroupDesc != null">material_group_desc = #{materialGroupDesc},</if>
        <if test="materialStatus != null">material_status = #{materialStatus},</if>
        <if test="materialStatusDesc != null">material_status_desc = #{materialStatusDesc},</if>
        <if test="enable != null">enable = #{enable},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        update_time = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId} AND material_id = #{materialId} and del_flag = false
    </update>

    <select id="selectList" resultType="com.datalink.fdop.base.api.domain.Material">
        SELECT material.*, controlArea.control_area_desc as control_area_desc
        FROM zjdata.ccm_material material
        left join zjdata.ccm_control_area controlArea on material.control_area_id = controlArea.control_area_id  and controlArea.del_flag = false
        WHERE material.del_flag = false
        <if test="mater.materialId != null and mater.materialId != ''">
            AND material.material_id like '%${mater.materialId}%'
        </if>
        <if test="mater.materialDesc != null and mater.materialDesc != ''">
            AND material.material_desc like '%${mater.materialDesc}%'
        </if>
        <if test="mater.controlAreaId != null and mater.controlAreaId != ''">
            AND material.control_area_id like '%${mater.controlAreaId}%'
        </if>
        ORDER BY control_area_id, material_id desc
    </select>

    <select id="query" resultType="com.datalink.fdop.base.api.domain.Material">
        select a.*, controlArea.control_area_desc as control_area_desc
        FROM zjdata.ccm_material a
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
        WHERE a.del_flag = false
        <if test="searchVo != null and searchVo != ''">
            and ${searchVo}
        </if>
        order by control_area_id, material_id desc
    </select>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.Material">
        select a.*, controlArea.control_area_desc as control_area_desc
        FROM zjdata.ccm_material a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id and controlArea.del_flag = false
        WHERE a.del_flag = false
        order by control_area_id, material_id desc
    </select>

</mapper>
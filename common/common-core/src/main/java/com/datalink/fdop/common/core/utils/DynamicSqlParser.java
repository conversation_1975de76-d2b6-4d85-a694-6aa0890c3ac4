package com.datalink.fdop.common.core.utils;

import org.mvel2.MVEL;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DynamicSqlParser {
    private static final Pattern EXPRESSION_PATTERN = Pattern.compile("\\$\\{(.*?)\\}");

    public static String render(String sql, Map<String, Object> context) {
        if (sql == null || context == null) {
            throw new IllegalArgumentException("SQL 或上下文不能为空");
        }

        Matcher matcher = EXPRESSION_PATTERN.matcher(sql);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String expr = matcher.group(1).trim();
            try {
                // 使用 MVEL 解析表达式
                Object value = MVEL.eval(expr, context);
                String replacement = "";
                if (value != null) {
                    replacement = value.toString();
                }
                matcher.appendReplacement(result, replacement);
            } catch (Exception e) {
                throw new RuntimeException("MVEL 表达式解析失败: " + expr, e);
            }
        }
        matcher.appendTail(result);
        return result.toString();
    }

    public static void main(String[] args) {
        String sql = "insert into dwh.dwh_final_std_cost_prod\n" +
                "(\n" +
                "primary_key,\n" +
                "ver_id,\n" +
                "effective_date,\n" +
                "control_area_id,\n" +
                "base_date,\n" +
                "plant_id,\n" +
                "cost_code,\n" +
                "`timestamp`,\n" +
                "batch_qty,\n" +
                "product_cim_id,\n" +
                "product_erp_id,\n" +
                "product_id,\n" +
                "product_desc,\n" +
                "cost_structure_003,\n" +
                "step_move_cost,\n" +
                "cost_structure_002,\n" +
                "cost_structure_001,\n" +
                "cost_structure_004,\n" +
                "cost_structure_006,\n" +
                "cost_structure_007,\n" +
                "cost_structure_008,\n" +
                "cost_structure_005,\n" +
                "cost_structure_011,\n" +
                "cost_structure_016,\n" +
                "cost_structure_014,\n" +
                "cost_structure_010,\n" +
                "cost_structure_012,\n" +
                "cost_structure_009,\n" +
                "cost_structure_013,\n" +
                "cost_structure_017,\n" +
                "cost_structure_019,\n" +
                "cost_structure_018,\n" +
                "cost_structure_021,\n" +
                "cost_structure_020,\n" +
                "cost_structure_015,\n" +
                "cost_structure_026,\n" +
                "cost_structure_023,\n" +
                "cost_structure_024,\n" +
                "cost_structure_027,\n" +
                "cost_structure_022,\n" +
                "cost_structure_028,\n" +
                "cost_structure_035,\n" +
                "cost_structure_025,\n" +
                "cost_structure_029,\n" +
                "cost_structure_031,\n" +
                "cost_structure_033,\n" +
                "cost_structure_030,\n" +
                "cost_structure_032,\n" +
                "cost_structure_036,\n" +
                "cost_structure_034,\n" +
                "cost_structure_037,\n" +
                "cost_structure_040,\n" +
                "cost_structure_041,\n" +
                "cost_structure_046,\n" +
                "cost_structure_038,\n" +
                "cost_structure_039,\n" +
                "cost_structure_047,\n" +
                "cost_structure_043,\n" +
                "cost_structure_049,\n" +
                "cost_structure_044,\n" +
                "cost_structure_052,\n" +
                "cost_structure_045,\n" +
                "cost_structure_042,\n" +
                "cost_structure_051,\n" +
                "cost_structure_056,\n" +
                "cost_structure_053,\n" +
                "cost_structure_054,\n" +
                "cost_structure_048,\n" +
                "cost_structure_050,\n" +
                "cost_structure_057,\n" +
                "cost_structure_059,\n" +
                "cost_structure_055,\n" +
                "cost_structure_058,\n" +
                "cost_structure_060,\n" +
                "fdop_import_time\n" +
                ")\n" +
                "select\n" +
                "primary_key,\n" +
                "ver_id,\n" +
                "effective_date,\n" +
                "control_area_id,\n" +
                "base_date,\n" +
                "plant_id,\n" +
                "cost_code,\n" +
                "`timestamp`,\n" +
                "batch_qty,\n" +
                "product_cim_id,\n" +
                "product_erp_id,\n" +
                "product_id,\n" +
                "product_desc,\n" +
                "cost_structure_003,\n" +
                "step_move_cost,\n" +
                "cost_structure_002,\n" +
                "cost_structure_001,\n" +
                "cost_structure_004,\n" +
                "cost_structure_006,\n" +
                "cost_structure_007,\n" +
                "cost_structure_008,\n" +
                "cost_structure_005,\n" +
                "cost_structure_011,\n" +
                "cost_structure_016,\n" +
                "cost_structure_014,\n" +
                "cost_structure_010,\n" +
                "cost_structure_012,\n" +
                "cost_structure_009,\n" +
                "cost_structure_013,\n" +
                "cost_structure_017,\n" +
                "cost_structure_019,\n" +
                "cost_structure_018,\n" +
                "cost_structure_021,\n" +
                "cost_structure_020,\n" +
                "cost_structure_015,\n" +
                "cost_structure_026,\n" +
                "cost_structure_023,\n" +
                "cost_structure_024,\n" +
                "cost_structure_027,\n" +
                "cost_structure_022,\n" +
                "cost_structure_028,\n" +
                "cost_structure_035,\n" +
                "cost_structure_025,\n" +
                "cost_structure_029,\n" +
                "cost_structure_031,\n" +
                "cost_structure_033,\n" +
                "cost_structure_030,\n" +
                "cost_structure_032,\n" +
                "cost_structure_036,\n" +
                "cost_structure_034,\n" +
                "cost_structure_037,\n" +
                "cost_structure_040,\n" +
                "cost_structure_041,\n" +
                "cost_structure_046,\n" +
                "cost_structure_038,\n" +
                "cost_structure_039,\n" +
                "cost_structure_047,\n" +
                "cost_structure_043,\n" +
                "cost_structure_049,\n" +
                "cost_structure_044,\n" +
                "cost_structure_052,\n" +
                "cost_structure_045,\n" +
                "cost_structure_042,\n" +
                "cost_structure_051,\n" +
                "cost_structure_056,\n" +
                "cost_structure_053,\n" +
                "cost_structure_054,\n" +
                "cost_structure_048,\n" +
                "cost_structure_050,\n" +
                "cost_structure_057,\n" +
                "cost_structure_059,\n" +
                "cost_structure_055,\n" +
                "cost_structure_058,\n" +
                "cost_structure_060,\n" +
                "now() as fdop_import_time\n" +
                "from (\n" +
                "\tselect\n" +
                "\ta.*,\n" +
                "\trank() over (partition by cost_code order by effective_date desc ) as num\n" +
                "\tfrom dws.dws_new_std_cost_prod a\n" +
                "\twhere 1=1 ${(ver_id != null && ver_id != \"\") ? \" and ver_id = '{{ver_id}}' \" : \"\"}" +
                ") temp\n" +
                "where num = 1";
        System.out.println(getParamPlaceholder(sql));
        // 替换参数
        sql = sql.replace("{{ver_id}}", "");
        Map<String, Object> params = new HashMap<>();
        params.put("ver_id", null); // 使用整数类型以匹配 MVEL 表达式中的比较
        String renderedSql = render(sql, params);
        System.out.println(renderedSql);

    }

    /**
     * 获取参数中的占位符 {{param}}
     *
     * @param string
     * @return
     */
    public static List<String> getParamPlaceholder(String string) {
        List<String> list = new ArrayList<>();
        // 参数内的占位符: {{参数编码.值的列}}
        String regex = "\\{\\{(.*?)\\}\\}";
        Pattern pattern = Pattern.compile(regex);
        // 创建 matcher 对象
        Matcher matcher = pattern.matcher(string);
        while (matcher.find()) {
            list.add(matcher.group(0));
        }
        return list;
    }

}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalPlanExp;

import java.util.List;

public interface DwhFinalPlanExpService extends IService<DwhFinalPlanExp> {

    PageDataInfo<DwhFinalPlanExp> overview(
            String verId, String companyId, String yearMonthFrom,
            String yearMonthTo, String sort, SearchVo searchVo);

    List<DwhFinalPlanExp> selectNoPage(String verId,
                                       String companyId,
                                       String yearMonthFrom,
                                       String yearMonthTo,
                                       String sort,
                                       SearchVo searchVo);
}
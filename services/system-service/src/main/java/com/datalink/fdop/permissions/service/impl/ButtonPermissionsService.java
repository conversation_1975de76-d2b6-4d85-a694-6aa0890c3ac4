package com.datalink.fdop.permissions.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.permissions.api.domain.ButtonPermissions;
import com.datalink.fdop.permissions.mapper.ButtonPermissionsMapper;
import com.datalink.fdop.permissions.service.IButtonPermissionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/20 15:43
 */
@Service
public class ButtonPermissionsService implements IButtonPermissionsService {

    @Autowired
    private ButtonPermissionsMapper buttonPermissionsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addButtonPermissions(Long permissionsId, List<ButtonPermissions> buttonPermissionsList) {
        for (ButtonPermissions buttonPermissions : buttonPermissionsList) {
            // 判断按钮权限是否重复
            if (buttonPermissionsMapper.checkButtonPermissions(permissionsId, buttonPermissions.getId(), buttonPermissions.getPageId(), buttonPermissions.getButtonId()) != null) {
                throw new ServiceException(Status.BUTTON_PERMISSION_ALREADY_EXISTS);
            }
            buttonPermissions.setId(IdWorker.getId());
        }
        // 添加按钮权限
        buttonPermissionsMapper.insertButtonPermissions(buttonPermissionsList);
        // 获取id集合
        List<Long> buttonPermissionsIds = buttonPermissionsList.stream().map(ButtonPermissions::getId).collect(Collectors.toList());
        // 先删除边关系再添加
        buttonPermissionsMapper.deleteButtonPermissionsEdge(permissionsId, buttonPermissionsIds);
        // 添加权限对象和按钮的关系
        buttonPermissionsMapper.createButtonPermissionsEdge(permissionsId, buttonPermissionsIds);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteButtonPermissions(Long permissionsId, List<Long> buttonIds) {
        return buttonPermissionsMapper.deleteButtonPermissions(permissionsId, buttonIds);
    }

    @Override
    public PageDataInfo<ButtonPermissions> selectButtonPermissionsList(Long permissionsId, ButtonPermissions buttonPermissions) {
        // 获取分页参数
        Page<ButtonPermissions> page = PageUtils.getPage(ButtonPermissions.class);
        IPage<ButtonPermissions> buttonPermissionsIPage = buttonPermissionsMapper.selectButtonPermissionsList(page, permissionsId, buttonPermissions);

        return PageUtils.getPageInfo(buttonPermissionsIPage.getRecords(), (int) buttonPermissionsIPage.getTotal());
    }

}

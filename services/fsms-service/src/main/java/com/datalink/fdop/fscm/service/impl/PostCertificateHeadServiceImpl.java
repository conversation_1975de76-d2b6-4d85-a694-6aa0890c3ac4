package com.datalink.fdop.fscm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.fscm.api.domain.*;
import com.datalink.fdop.fscm.api.model.vo.PostCertificateVo;
import com.datalink.fdop.fscm.mapper.PostCertificateHeadMapper;
import com.datalink.fdop.fscm.service.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Time;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/11/29 10:39
 */
@Service
@Transactional
public class PostCertificateHeadServiceImpl extends ServiceImpl<PostCertificateHeadMapper, PostCertificateHead> implements PostCertificateHeadService {


    @Autowired
    private PostCertificateHeadMapper postCertificateHeadMapperMapper;
    @Autowired
    private RepertoryService repertoryService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;

    @Autowired
    private MoveTypeService moveTypeService;

    @Autowired
    private WorkOrderStoreIssueService workOrderStoreIssueService;

    @Override
    public String getMaxNum() {
        String dateTimeNow = DateUtils.dateTimeNow(DateUtils.YYYY);
        String maxNum = postCertificateHeadMapperMapper.getMaxNum(dateTimeNow);
        if (StringUtils.isEmpty(maxNum)) {
            maxNum="0";
        }
        return maxNum;
    }

    @Override
    public void checkData(PostCertificateVo postCertificateVo) {
        //分组
        PostCertificateHead postCertificateHead = postCertificateVo.getPostCertificateHead();
        List<PostCertificateRow> postCertificateRows = postCertificateVo.getPostCertificateRows();
        postCertificateRows.stream().forEach(postCertificateRow -> {
            if (StringUtils.isNotEmpty(postCertificateRow.getWriteOff())&&postCertificateRow.getWriteOff().equals("1")) {
                throw new ServiceException("过账凭证行&"+postCertificateRow.getVoucherRowNum()+"&已冲销或为冲销行，无法再取消，请检查！");
            }
        });
        Map<String, List<PostCertificateRow>> map = this.grouping(postCertificateRows);
        List<PostCertificateRow> oneGroup = map.get("oneGroup");
        List<PostCertificateRow> twoGroup = map.get("twoGroup");
        List<PostCertificateRow> threeGroup = map.get("threeGroup");

        List<PostCertificateRow> oneThreeGroup =Lists.newArrayList();
        oneThreeGroup.addAll(oneGroup);
        oneThreeGroup.addAll(threeGroup);
        oneThreeGroup.stream().forEach(postCertificateRow -> {
            Repertory repertory = repertoryService.getOne(new QueryWrapper<Repertory>()
                    .lambda()
                    .eq(Repertory::getFactoryCode,postCertificateRow.getFactoryCodeShip())
                    .eq(Repertory::getStockPCode,postCertificateRow.getStockPCodeShip())
                    .eq(Repertory::getMaterialCode,postCertificateRow.getMaterialCode())
                    .eq(Repertory::getBatchNumber,postCertificateRow.getBatchNumberShip())
                    .eq(Repertory::getPiece,StringUtils.isNotEmpty(postCertificateRow.getPieceShip())?postCertificateRow.getPieceShip(): Constants.DATA_DEFAULT_VALUE)
                    .eq(Repertory::getBinNum,StringUtils.isNotEmpty(postCertificateRow.getBinNumShip())?postCertificateRow.getBinNumShip(): Constants.DATA_DEFAULT_VALUE)
            );
            String turnkeyNotFinal = postCertificateRow.getTurnkeyNotFinal();
            double num=0.0;
            if (repertory !=null) {
                if ("0".equals(turnkeyNotFinal)||(StringUtils.isEmpty(turnkeyNotFinal)&&(StringUtils.isEmpty(postCertificateRow.getStockStatuShip())||"1".equals(postCertificateRow.getStockStatuShip())))) {
                    num=repertory.getUnrestrictedStock();
                }
                if ("1".equals(turnkeyNotFinal)){
                    num=repertory.getProcessStock();
                }
                if (StringUtils.isEmpty(turnkeyNotFinal)&&"2".equals(postCertificateRow.getStockStatuShip())){
                    num=repertory.getFreezeStock();
                }
            }

            if (num<Math.abs(postCertificateRow.getPostQuantity())) {
                throw new ServiceException("过账凭证行&"+postCertificateRow.getVoucherRowNum()+"&短缺库存，无法取消，请检查！");
            }
        });

    }

    @Override
    @Transactional
    public void post(PostCertificateVo postCertificateVo) {
        PostCertificateHead head = postCertificateVo.getPostCertificateHead();
        String maxNum = postCertificateHeadMapperMapper.getMaxNum(DateUtils.dateTimeNow(DateUtils.YYYY));
        if (StringUtils.isEmpty(maxNum)) {
            maxNum="0";
        }
        Integer integer = Integer.valueOf(maxNum);
        integer=integer+1;
        head.setVoucherNum(integer);
        head.setVoucherVintage(DateUtils.dateTimeNow(DateUtils.YYYY));
        head.setEnteringDate(new Date());
        head.setEnteringTime(Time.valueOf(DateUtils.dateTimeNow(DateUtils.HH_MM_SS)));
        head.setUsername(SecurityUtils.getUsername());
        postCertificateHeadMapperMapper.insert(head);


        List<PostCertificateRow> postCertificateRows = postCertificateVo.getPostCertificateRows();
        long i=1L;
        for (PostCertificateRow postCertificateRow : postCertificateRows) {
            postCertificateRowService.update(Wrappers.lambdaUpdate(PostCertificateRow.class)
                    .set(PostCertificateRow::getWriteOff,"1")
                    .eq(PostCertificateRow::getVoucherNum,postCertificateRow.getVoucherNum())
                    .eq(PostCertificateRow::getVoucherVintage,postCertificateRow.getVoucherVintage())
                    .eq(PostCertificateRow::getVoucherRowNum,postCertificateRow.getVoucherRowNum())
            );
            postCertificateRow.setVoucherNum(integer);
            postCertificateRow.setVoucherRowNum(i);
            postCertificateRow.setVoucherVintage(DateUtils.dateTimeNow(DateUtils.YYYY));


            postCertificateRow.setWriteOff("1");
            i=i+1l;

            //修改发料表
            if ("543".equals(postCertificateRow.getMoveType())){
                WorkOrderStoreIssue workOrderStoreIssue=new WorkOrderStoreIssue();
                workOrderStoreIssue.setWorkOrderNum(postCertificateRow.getWorkOrderNum());
                workOrderStoreIssue.setWorkOrderRowNum(postCertificateRow.getWorkOrderRowNum());
                workOrderStoreIssue.setWorkOrderChildrenNum(postCertificateRow.getWorkOrderChildrenNum());
                workOrderStoreIssue.setMaterialCode(postCertificateRow.getMaterialCode());
                workOrderStoreIssue.setFactoryCode(postCertificateRow.getFactoryCodeReceive());
                workOrderStoreIssue.setStockPCode(postCertificateRow.getStockPCodeReceive());
                workOrderStoreIssue.setBatchNumber(postCertificateRow.getBatchNumberReceive());
                workOrderStoreIssue.setPiece(postCertificateRow.getPieceReceive());
                workOrderStoreIssue.setBinNum(postCertificateRow.getBinNumReceive());
                workOrderStoreIssue.setQuantityConsume(Math.abs(postCertificateRow.getPostQuantity())*-1);
                workOrderStoreIssueService.updataQuantityConsume(Arrays.asList(workOrderStoreIssue));
            }

        }


        Map<String, List<PostCertificateRow>> map = this.grouping(postCertificateRows);
        List<PostCertificateRow> oneGroup = map.get("oneGroup");
        List<PostCertificateRow> twoGroup = map.get("twoGroup");
        List<PostCertificateRow> threeGroup = map.get("threeGroup");
        List<PostCertificateRow> oneThreeGroup =Lists.newArrayList();
        oneThreeGroup.addAll(oneGroup);
        oneThreeGroup.addAll(threeGroup);

        for (PostCertificateRow postCertificateRow : twoGroup) {
            //更新发出非限制库存数量
            repertoryService.updataShipUnrestrictedStock(postCertificateRow);
            postCertificateRow.setPostQuantity(Math.abs(postCertificateRow.getPostQuantity()));
            postCertificateRow.setInvoicesReference("8");
            MoveType moveType = moveTypeService.getOne(new QueryWrapper<MoveType>().lambda().eq(MoveType::getMoveType, postCertificateRow.getMoveType()));
            postCertificateRow.setMoveType(moveType!=null?moveType.getOppositeMoveType():"");
            postCertificateRowService.save(postCertificateRow);
        }
        for (PostCertificateRow postCertificateRow : oneThreeGroup) {
            //更新发出非限制库存数量
            repertoryService.updataReceiveUnrestrictedStock(postCertificateRow);
        }

        for (PostCertificateRow postCertificateRow : threeGroup) {
            repertoryService.updataShipUnrestrictedStockByStockStatu(postCertificateRow);
            postCertificateRow.setPostQuantity(Math.abs(postCertificateRow.getPostQuantity()));
            postCertificateRow.setInvoicesReference("8");
            MoveType moveType = moveTypeService.getOne(new QueryWrapper<MoveType>().lambda().eq(MoveType::getMoveType, postCertificateRow.getMoveType()));
            postCertificateRow.setMoveType(moveType!=null?moveType.getOppositeMoveType():"");
            postCertificateRowService.save(postCertificateRow);
        }

        for (PostCertificateRow postCertificateRow : oneGroup) {
            postCertificateRow.setPostQuantity(Math.abs(postCertificateRow.getPostQuantity())*-1);
            postCertificateRow.setInvoicesReference("8");
            MoveType moveType = moveTypeService.getOne(new QueryWrapper<MoveType>().lambda().eq(MoveType::getMoveType, postCertificateRow.getMoveType()));
            postCertificateRow.setMoveType(moveType!=null?moveType.getOppositeMoveType():"");
            postCertificateRowService.save(postCertificateRow);
        }

    }

    public Map<String,List<PostCertificateRow>> grouping(List<PostCertificateRow> postCertificateRows){
        List<PostCertificateRow> oneGroup= Lists.newArrayList();
        List<PostCertificateRow> twoGroup= Lists.newArrayList();
        List<PostCertificateRow> threeGroup= Lists.newArrayList();
        for (PostCertificateRow postCertificateRow : postCertificateRows) {
            if (StringUtils.isEmpty(postCertificateRow.getFactoryCodeReceive())&&
                    StringUtils.isEmpty(postCertificateRow.getStockPCodeReceive())&&
                    StringUtils.isEmpty(postCertificateRow.getStockStatuReceive())&&
                    StringUtils.isEmpty(postCertificateRow.getBinNumReceive())&&
                    StringUtils.isEmpty(postCertificateRow.getPieceReceive())&&
                    StringUtils.isEmpty(postCertificateRow.getBinNumReceive())
            ){
                oneGroup.add(postCertificateRow);
            }else if (StringUtils.isEmpty(postCertificateRow.getFactoryCodeShip())&&
                    StringUtils.isEmpty(postCertificateRow.getStockPCodeShip())&&
                    StringUtils.isEmpty(postCertificateRow.getStockStatuShip())&&
                    StringUtils.isEmpty(postCertificateRow.getBinNumShip())&&
                    StringUtils.isEmpty(postCertificateRow.getPieceShip())&&
                    StringUtils.isEmpty(postCertificateRow.getBinNumShip())){
                twoGroup.add(postCertificateRow);
            }else {
                threeGroup.add(postCertificateRow);
            }
        }
        Map<String,List<PostCertificateRow>> map = Maps.newHashMap();
        map.put("oneGroup",oneGroup);
        map.put("twoGroup",twoGroup);
        map.put("threeGroup",threeGroup);
        return map;
    }
}

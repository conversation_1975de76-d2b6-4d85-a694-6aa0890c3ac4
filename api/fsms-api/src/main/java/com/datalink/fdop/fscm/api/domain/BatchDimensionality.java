package com.datalink.fdop.fscm.api.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(schema = "zjdata", value = "f_d_batch_dimensionality")
@ExcelIgnoreUnannotated
@ApiModel
public class BatchDimensionality {
  @ApiModelProperty(value = "管理维度")
  private String manageDimension;

}

package com.datalink.fdop.auth.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.domain.BaseField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/4/20 15:15
 */
//@Document("AuthObject")
@Data
@TableName("auth_object")
@ApiModel("权限对象")
public class AuthObject extends BaseField implements Serializable {
    private static final long serialVersionUID = -4158329917172969346L;
    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "父节点id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;




    @Override
    public String toString() {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{");
        ageString.append("id:"+(getId() != null ? getId():""));
        ageString.append(",pid: " + (getPid() != null?getPid() : ""));
        ageString.append((StringUtils.isNotEmpty(getName())?",name: " +"'"+ getName() + "'":""));
        ageString.append(",code: " +(StringUtils.isNotEmpty(getCode())?"'"+ getCode() + "'":""));
        ageString.append((StringUtils.isNotEmpty(getDescription())?",description: " +"'"+ getDescription() + "'":""));
        ageString.append("}");
        return ageString.toString();
    }
}

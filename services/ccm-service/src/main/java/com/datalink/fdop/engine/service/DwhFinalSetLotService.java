package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalSetLot;

import java.util.List;

public interface DwhFinalSetLotService extends IService<DwhFinalSetLot> {

    PageDataInfo<DwhFinalSetLot> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo);

    List<DwhFinalSetLot> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo);
}
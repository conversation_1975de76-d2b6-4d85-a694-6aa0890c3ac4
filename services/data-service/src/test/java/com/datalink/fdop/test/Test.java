package com.datalink.fdop.test;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.DateTimeFormatterBuilder;

import java.text.ParseException;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoField;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Test {

    // public static void main(String[] args) {
//        String sql = "select  gcc.segment7 as pro_no,group_concat(distinct arca.receipt_number,';') as receipt_number" +
//                "from ods.ODS_GTMS_AR_CASH_RECEIPTS_ALL arca" +
//                "inner join (select * from ods.ODS_GTMS_XLA_TRANSACTION_ENTITIES where entity_code = 'RECEIPTS') xt" +
//                "on arca.cash_receipt_id = xt.source_id_int_1" +
//                "inner join ods.ODS_GTMS_XLA_AE_HEADERS xah" +
//                "on xt.entity_id = xah.entity_id and xt.application_id = xah.application_id" +
//                "inner join ods.ODS_GTMS_XLA_AE_LINES xal" +
//                "on xah.ae_header_id = xal.ae_header_id" +
//                "inner join ods.ODS_GTMS_GL_IMPORT_REFERENCES gir" +
//                "on gir.gl_sl_link_table = xal.gl_sl_link_table and gir.gl_sl_link_id = xal.gl_sl_link_id" +
//                "inner join ods.ODS_GTMS_GL_JE_HEADERS gjh" +
//                "on gir.je_header_id = gjh.je_header_id" +
//                "inner join ods.ODS_GTMS_GL_JE_LINES gjl" +
//                "on gjh.je_header_id = gjl.je_header_id" +
//                "INNER join ods.ODS_GTMS_GL_CODE_COMBINATIONS gcc" +
//                "on gjl.code_combination_id = gcc.code_combination_id" +
//                "inner join ods.ODS_GTMS_AR_PAYMENT_SCHEDULES_ALL apsa" +
//                "on apsa.cash_receipt_id = arca.cash_receipt_id" +
//                "group by gcc.segment7";
//        Pattern pattern = Pattern.compile("(?<!['\"])(;)(?!['\"])");
//        String[] sqlStatements = pattern.split(sql);
//        for (String sqlStatement : sqlStatements) {
//            System.out.println(sqlStatement);
//        }
//
//        String value = "'upload,LAYER_INDEX_UPLOAD'";
//        String s = value.replaceAll(",", "','");
//        System.out.println(s);

    //String body = "{\"年份\":\"2024\",\"受教育人数\":2,\"月份\":\"4\",\"本单位受教育人次\":2,\"ID\":1,\"组织安全教育培训次数\":3,\"入场三级安全教育人次\":4}";

//        String body = "{\"success\": true,\"message\": \"查询成功\",\"data\": [{\"ID\": 1,\"年份\": \"2024\",\"月份\": \"4\",\"受教育人数\": 2, \"本单位受教育人次\": 2,\"外来单位受教育人次\": 1,\"入厂三级安全教育人次\": 3,\"组织安全教育培训次数\": 3}],\"total\": 2}";
//
//        body = JSONPath.read(body, "$.data").toString();
//        List<Map> mapList = new ArrayList<>();
//        mapList.add(JSONObject.parseObject(body, Map.class));

    //}

    /**
     * 获取邮件接收时间
     * 从content中提取文件时间（手动补偿机制），提取格式为：Data Refreshed Time:2024-12-30 06:51:18(UTC+8)，如果没有获取到时间则用 item.getDateTimeReceived()
     *
     * @param content 邮件内容
     * @return 提取到的时间
     */
    private static Date getEmailReceivedDateByContent(String content) throws ParseException {
        Date emailReceivedDate = null;
        if (StringUtils.isNotBlank(content)) {
            String[] lines = content.split("\n");
            for (String line : lines) {
                if (line.contains("Data Refreshed Time")) {
                    // 正则表达式用于提取日期时间和时区信息
                    Pattern pattern = Pattern.compile("Data Refreshed Time:\\s*(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2})");
                    Matcher matcher = pattern.matcher(line);

                    if (matcher.find()) {  // 使用 find() 而不是 matches()
                        // 提取匹配到的日期时间部分
                        String dateTimePart = matcher.group(1);

                        // 打印调试信息
                        System.out.println("Extracted Date Time: " + dateTimePart);

                        // 尝试解析日期时间
                        emailReceivedDate = DateUtils.parse(dateTimePart, DateConstants.YYYY_MM_DD_HH_MM_SS);
                        break;
                    } else {
                        // 如果没有找到匹配的日期时间，抛出异常
                        throw new ParseException("No valid date found in the line: " + line, 0);
                    }
                }
            }
        }
        return emailReceivedDate;
    }

    public static void main(String[] args) throws ParseException {
        String content = "Dear TSMC customer,\n" +
                "\n" +
                "Please find attached file for the All-in-One WIP Report report.\n" +
                "\n" +
                "Subscription Name: All-in-One WIP(with Order)\n" +
                "Data Refreshed Time: 2024-11-12 06:49:13(UTC+8)\n" +
                "Frequency: [Daily] 8am\n" +
                "Subscriber: <EMAIL>\n";
        Date date = getEmailReceivedDateByContent(content);
        System.out.println(date);
    }

}

package com.datalink.fdop.data.controller;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.data.annotation.Restrictive;
import com.datalink.fdop.data.api.domain.DataDocumentation;
import com.datalink.fdop.data.api.domain.DataServe;
import com.datalink.fdop.data.service.DataPreviewService;
import com.datalink.fdop.data.service.DataServeService;
import com.datalink.fdop.data.utils.MapExcelExportUtil;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.element.api.RemoteEntityTableService;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.permissions.api.model.vo.PermissionsElementVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/5/16 14:01
 */
@RestController
@RequestMapping("/data/detail")
@Api(tags = "数据分享api")
public class DataPreviewController {


    @Autowired
    private DataPreviewService previewService;

    @Autowired
    private RemoteEntityTableService entityTableService;

    @Autowired
    private RemoteDriveService remoteDriveService;

    @Autowired
    private DataServeService dataServeService;

    /**
     * 数据预览
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "数据预览")
    @PostMapping("/fetchResult")
    @Log(title = "数据分享模块", businessType = BusinessType.OTHER)
    public R<Map<String, Object>> fetchResult(@Validated @RequestBody DataServe params,
                                              @RequestParam(defaultValue = "1", value = "page", required = false) Integer page,
                                              @RequestParam(defaultValue = "10", value = "limit", required = false) Integer limit) {
        String type = params.getType();
        Long dataSourceId = params.getDatasourceId();
        String sql = params.getQuerySql();
        if (type.equals("entity")) {
            R<DataEntityTable> r = entityTableService.selectById(params.getEntityId());
            if (r.getCode() != 200) {
                throw new ServiceException("获取实体失败");
            }
            DataEntityTable entityTable = r.getData();
            //获取实体sql
            sql = previewService.getEntityData(entityTable);
            //获取数据源id
            dataSourceId = entityTable.getDataSourceId();
        }

        // 获取数据源信息
        R<DataSource> dataSourceR = remoteDriveService.queryDataSource(dataSourceId);
        if (dataSourceR.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(dataSourceR.getMsg());
        }
        DataSource dataSource = dataSourceR.getData();
        // TODO:如果是iceberg的数据源，则使用trino代替
        if ("iceberg".equalsIgnoreCase(dataSource.getType())) {
            // 获取trino数据源
            String trinoCode = "trino";
            R<DataSource> trinoDataSourceR = remoteDriveService.queryDataSource(trinoCode);
            if (trinoDataSourceR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(trinoDataSourceR.getMsg());
            }
            dataSource = trinoDataSourceR.getData();
            dataSourceId = trinoDataSourceR.getData().getId();
        }
        //检查数据源以及sql是否为空
        sql = previewService.checkSql(sql, dataSourceId);
        // 排序信息动态参数转换
        String orderBy = previewService.replaceParamObject(params.getOrderBy(), null, previewService.getShareParams(params.getShareParams()), true);
        Map<String, Object> orderByMap = null;
        if (StringUtils.isNotEmpty(orderBy)) {
            orderByMap = JSONObject.parseObject(orderBy, Feature.OrderedField);
        }
        // 新增mvelParams
        Map<String, Object> mvelParams = new HashMap<>();
        // 替换参数
        sql = previewService.getParamSql(sql, params.getShareParams(), dataSource.getType(), mvelParams);
        // 解析mvel表达式过滤if条件
        sql = previewService.mvelParseSql(sql, mvelParams);
        String originSql = sql;
        // 列权限动态参数转换
        String columnPermissionInfo = previewService.replaceParamObject(params.getColumnPermission(), null, previewService.getShareParams(params.getShareParams()), true);
        //行权限参数
        Map<String, Object> columnPermissions = JSONObject.parseObject(columnPermissionInfo, Feature.OrderedField);
        //校验多主键是否全部添加
        previewService.checkMorePrimary(columnPermissions);

        Boolean flag = false;
        if (columnPermissions != null) {
            for (String key : columnPermissions.keySet()) {
                if (StringUtils.isNotEmpty(columnPermissions.get(key).toString())) {
                    flag = true;
                    break;
                }
            }
        }
        //用户id
        Long userId = null;
        if (params.getUserId() != null) {
            userId = params.getUserId();
        }
        List<PermissionsElementVo> permissionsElementVos = previewService.getPermissionsElementVo(userId);

        //获取执行SQL  执行查询总数SQL
        Map<String, Object> sqlMap = previewService.getSql(sql, dataSource, columnPermissions, page, limit, userId, permissionsElementVos, orderByMap, flag);


        if (dataSource.getType().equalsIgnoreCase("postgresql")) {
            if (sql.contains("`")) {
                sql = sql.replace("`", "");
            }
        }

        previewService.checkParam(sql);

        Map<String, Object> result = null;

        result = previewService.getSqlData(dataSourceId, (String) sqlMap.get("sql"), originSql, params.getShareParams());
        result.put("count", previewService.findTotal(sql, sqlMap, dataSourceId));
        return R.ok(result, "查询成功");
    }


    /**
     * 分享的地址
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "外部请求接口")
    @Restrictive
    @PostMapping("/open/{id}")
    public R<Map> open(@PathVariable("id") Long id, HttpServletRequest request) throws Exception {
        try {
            if (id == null) {
                return R.fail("服务API调用错误，服务APIID为空");
            }
            // Map<String, String[]> parameterMap = request.getParameterMap();
            //存储所有参数
            Map<String, Object> map = new HashMap<>();
            //获取访问接口
            String requestURI = request.getRequestURI();
            map.put("uri", requestURI);
            //获取自定义参数
            // String urlParameter = request.getQueryString();
            //获取参数
            Map<String, List<Object>> parameterMap = previewService.getParameterMap(id, request);

            map.put("params", parameterMap);
            Map result = previewService.openFetchResult(id, map);
            return R.ok(result);
        } catch (ServiceException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }


    /**
     * 根据code查询服务API数据
     *
     * @param code 服务API编码
     * @throws Exception
     */
    @ApiOperation(value = "外部请求接口")
    @Log(title = "数据分享模块", businessType = BusinessType.OTHER)
    @Restrictive
    @PostMapping("/openByCode/{code}")
    public R<Map> openByCode(@PathVariable("code") String code, HttpServletRequest request) throws Exception {
        try {
            if (code == null) {
                return R.fail("code 不能为空");
            }
            //存储所有参数
            Map<String, Object> map = new HashMap<>();
            //获取访问接口
            String requestURI = request.getRequestURI();
            map.put("uri", requestURI);
            DataServe dataServe = dataServeService.selectByCode(code);
            if (dataServe == null) {
                return R.fail("服务API不存在");
            }
            Long id = dataServe.getId();
            // 获取参数
            Map<String, List<Object>> parameterMap = previewService.getParameterMap(id, request);
            map.put("params", parameterMap);
            Map result = previewService.openFetchResult(id, map);
            return R.ok(result);
        } catch (ServiceException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    @PostMapping("/getAddr")
    @Log(title = "数据分享模块", businessType = BusinessType.OTHER)
    public R getAddr(@RequestParam Long id) {
        return R.ok("http://IP:8080/api/data/detail/open/" + id);
    }

    @ApiOperation(value = "下载接口文档")
    @PostMapping("/documentation")
    public void documentation(@RequestBody DataDocumentation dataDocumentation, HttpServletResponse response, HttpServletRequest httpServletRequest) throws Exception {
        httpServletRequest.getHeader("Authorization").split(" ");
        String token = Arrays.asList(httpServletRequest.getHeader("Authorization").split(" ")).get(1);
        String data = previewService.documentation(dataDocumentation, token);
        previewService.fileDownLoad(data, response);
    }

    @ApiOperation(value = "清空服务API配置缓存")
    @PostMapping("/clearCache/{id}")
    public R clearCache(@PathVariable("id") Long id) {
        previewService.clearCache(id);
        return R.ok("清空成功");
    }


    /**
     * 根据code导出服务API数据
     *
     * @param code 服务API编码
     * @throws Exception
     */
    @ApiOperation(value = "导出服务API数据")
    @Log(title = "导出服务API数据", businessType = BusinessType.OTHER)
    @Restrictive
    @PostMapping("/exportData/{code}")
    public void exportData(@PathVariable("code") String code, HttpServletRequest request,
                           @RequestBody(required = false) List<DynamicColumn> dynamicColumns,
                           HttpServletResponse response) throws Exception {
        try {
            if (code == null) {
                return;
            }
            //存储所有参数
            Map<String, Object> map = new HashMap<>();
            //获取访问接口
            String requestURI = request.getRequestURI();
            map.put("uri", requestURI);
            DataServe dataServe = dataServeService.selectByCode(code);
            if (dataServe == null) {
                return;
            }
            Long id = dataServe.getId();
            // 获取参数
            Map<String, List<Object>> parameterMap = previewService.getParameterMap(id, request);
            map.put("params", parameterMap);
            Map result = previewService.openFetchResult(id, map);
            List<Map<String, Object>> dataList = null;
            if (result.get("data") != null) {
                dataList = (List<Map<String, Object>>) result.get("data");
            }
            MapExcelExportUtil.exportMapDataWithDynamicColumns(response, dataList, dynamicColumns, "报表数据");

        } catch (ServiceException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

}

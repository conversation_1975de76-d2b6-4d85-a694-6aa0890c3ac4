<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.datart.mapper.DataChartMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.datart.domain.DataChart"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="createChartAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:f_d_datachart_menu), (node:f_d_datachart)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'f_d_datachart_menu',endTable:'f_d_datachart'}]->(node)-[:node_menu_edge
        {startTable:'f_d_datachart',endTable:'f_d_datachart_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertChart" parameterType="com.datalink.fdop.datart.domain.DataChart" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:f_d_datachart ${@com.datalink.fdop.datart.utils.DomainAgeUtils@getDataChartAgeStr(dataChart)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createChartAndEntityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (chart:f_d_datachart), (entity:d_e_data_entity)
                                   WHERE chart.id = ${id} AND entity.id = ${dataEntityId}
                                   CREATE (chart)-[cee:f_d_datachart_entity_edge
                                   {startTable:'f_d_datachart', endTable:'d_e_data_entity'}]->(entity)-[:f_d_datachart_entity_edge
        {startTable:'d_e_data_entity',endTable:'f_d_datachart'}]->(chart)
        RETURN id(cee), properties(cee)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.datart.domain.DataChart" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_datachart)
        WHERE node.id = ${id}
        <set>
            <if test="pid != null">node.pid = ${pid},</if>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="description != null and description != ''">node.description = '${description}',</if>
            <if test="viewId != null and viewId != ''">node.viewId = '${viewId}',</if>
            <if test="config != null and config != ''">node.config = '${config}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_datachart)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteChartAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteChartAndEntityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (chart:f_d_datachart) -[cee:f_d_datachart_entity_edge]-(entity:d_e_data_entity)
            WHERE chart.id = ${id} AND entity.id = ${dataEntityId}
            DELETE cee
            properties(cee) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_datachart)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.datart.domain.DataChart" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_datachart)
        <where>
            <if test="dataChart.id != null">AND node.id = ${dataChart.id}</if>
            <if test="dataChart.code != null and dataChart.code != ''">AND node.code =~'.*${dataChart.code}.*'
            </if>
            <if test="dataChart.name != null and dataChart.name != ''">AND node.name =~'.*${dataChart.name}.*'
            </if>
            <if test="dataChart.description != null and dataChart.description != ''">AND node.description
                =~'.*${dataChart.description}.*'
            </if>
            <if test="dataChart.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",dataChart.searchCondition)})
            </if>
        </where>
        WITH node
        ORDER BY node.createTime DESC
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectListByIds" parameterType="com.datalink.fdop.datart.domain.DataChart" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_datachart)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.status &lt;&gt; 0
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectAllChart" parameterType="com.datalink.fdop.datart.domain.DataChart"
            resultType="com.datalink.fdop.datart.domain.DataChart">
        WITH chart AS (
        <if test="pids.contains(-1L)">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_datachart)
            WHERE node.pid = -1
            <if test="searchCondition != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description, '顶级菜单' as menuName
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_datachart) -[:node_menu_edge]->(menu:f_d_datachart_menu)
        WHERE node.pid in
        <foreach collection="pids" item="pid" open="[" separator="," close="]">
            ${pid}
        </foreach>
        <if test="searchCondition != null ">AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
        </if>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as menuName
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT)
        ) SELECT * FROM chart ORDER BY chart.code ${sort}
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_datachart) WHERE node.id = ${id} RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_datachart) WHERE node.code = '${code}' RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_datachart) WHERE node.code = '${code}' AND node.id &lt;&gt; ${id} RETURN id(node),
                               properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:f_d_datachart_menu) -[nme:node_menu_edge]->(nodeE:f_d_datachart)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="selectChartTree" resultType="com.datalink.fdop.datart.domain.DataChartTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (nodeS:f_d_datachart) -[]->(menuE:f_d_datachart_menu)
        <where>
            <if test="code != null and code != ''">nodeS.code =~'.*${code}.*'</if>
        </where>
        RETURN nodeS.id, nodeS.pid, nodeS.code, nodeS.name, nodeS.description, 'NODE' as menuType
        ORDER BY nodeS.code ${sort}
        $$) as (id ag_catalog.agtype,pid ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType
        TEXT)
    </select>

    <select id="selectByDataEntityId" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_datachart) -[]->(entity:d_e_data_entity)
            WHERE entity.id = ${dataEntityId}
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper> 
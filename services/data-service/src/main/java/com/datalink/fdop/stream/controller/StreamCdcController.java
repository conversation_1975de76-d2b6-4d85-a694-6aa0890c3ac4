package com.datalink.fdop.stream.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.DbType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.stream.api.RemoteStreamService;
import com.datalink.fdop.stream.api.domain.StreamCdc;
import com.datalink.fdop.stream.service.DebeziumService;
import com.datalink.fdop.stream.service.IStreamCdcService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@RequestMapping("/stream/cdc")
@RestController
@Api(tags = "流cdc-api")
public class StreamCdcController extends BaseController {

    @Autowired
    private IStreamCdcService streamCdcService;

    @Autowired
    private RemoteStreamService remoteStreamService;

    @Autowired
    private DebeziumService debeziumService;

    public static List<String> debeziumJsonList = Arrays.asList("name", "config");

    public static final String MYSQL_CLASS = "io.debezium.connector.mysql.MySqlConnector";

    public static final String ORACLE_CLASS = "io.debezium.connector.oracle.OracleConnector";

    public static final String POSTGRESQL_CLASS = "io.debezium.connector.postgresql.PostgresConnector";

    public static final String SQLSERVER_CLASS = "io.debezium.connector.sqlserver.SqlServerConnector";

//    @Autowired
//    private KubernetesUtils kubernetesUtils;

    @ApiOperation("创建流")
    @Log(title = "流", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody StreamCdc streamCdc) {
        return R.toResult(streamCdcService.create(streamCdc));
    }

    @ApiOperation("修改流")
    @Log(title = "流", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody StreamCdc streamCdc) {
        return R.toResult(streamCdcService.update(streamCdc));
    }

    @ApiOperation("删除流")
    @Log(title = "流", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.LEASE_SPECIFY_THE_FLOW_TO_DELETE);
        }
        for (Long id : ids) {
            StreamCdc streamCdc = streamCdcService.selectById(id);
            try {
                if (streamCdc != null && streamCdc.getType() != null) {
                    JSONObject jsonObject = JSON.parseObject(streamCdc.getTaskInfo());
                    String name = jsonObject.getString("name");
                    name = getName(name);
                    jsonObject.put("name", name);
                    List list = debeziumService.getConnectorList();
                    if (list.contains(name)) {
                        debeziumService.deleteConnector(name);
                        streamCdcService.offline(name);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return R.toResult(streamCdcService.delete(ids));
    }

    @ApiOperation("根据id查询流信息")
    @Log(title = "流")
    @GetMapping(value = "/selectById/{id}")
    public R<StreamCdc> selectById(@PathVariable("id") Long id) {
        return R.ok(streamCdcService.selectById(id));
    }


    @ApiOperation("启动")
    @Log(title = "流")
    @GetMapping(value = "/online")
    public R online(@RequestParam String code) {
        return R.toResult(streamCdcService.online(code));
    }


    @ApiOperation("上线")
    @PostMapping("/connectors/{id}")
    public R createConnector(@PathVariable(value = "id") Long id) {
        StreamCdc streamCdc = streamCdcService.selectById(id);
        if (streamCdc == null) {
            return R.fail("实时数据采集配置不存在！");
        }
        if (streamCdc.getType().equalsIgnoreCase(DbType.DORIS.getCode())) {
            return streamCdcService.dorisOnline(streamCdc);
        } else {
            JSONObject jsonObject = JSON.parseObject(streamCdc.getTaskInfo());
            if (StringUtils.isEmpty(streamCdc.getTaskInfo())) {
                return R.fail("实时数据采集配置未保存，不能上线！");
            }
            if (this.checkJson(jsonObject)) {
                return R.fail("JSON有误,请进行校验成功后上线");
            }
            String name = jsonObject.getString("name");
            List list = new ArrayList();
            Boolean flag = true;
            while (flag) {
                try {
                    list = debeziumService.getConnectorList();
                    flag = false;
                } catch (Exception e) {
                    logger.error(e.getLocalizedMessage());
                }
            }
            if (list.contains(name)) {
                return R.fail("已存在该名称的connector");
            }
            JSONObject config = jsonObject.getJSONObject("config");
            config.put("name", name);
            String clazz = config.getString("connector.class");
            if (POSTGRESQL_CLASS.equals(clazz)) {
                config.put("slot.name", name.toLowerCase().replaceAll("-", ""));
            } else if (ORACLE_CLASS.equals(clazz)) {
                config.put("lob.enabled", "true");
                config.put("query.fetch.size", "10000");
                config.put("log.mining.query.filter.mode", "in");
                config.put("poll.interval.ms", "100");
                config.put("log.mining.sleep.time.default.ms", "100");
                config.put("log.mining.sleep.time.increment.ms", "20");
                config.put("log.mining.archive.log.hours", "1");
            }
            return R.ok(debeziumService.createConnector(jsonObject));
        }
    }


    @ApiOperation("下线")
    @PostMapping("/offline/{id}")
    public R offline(@PathVariable(value = "id") Long id) {
        StreamCdc streamCdc = streamCdcService.selectById(id);
        if (streamCdc == null) {
            return R.fail("实时数据采集配置不存在！");
        }
        if (streamCdc.getType().equalsIgnoreCase(DbType.DORIS.getCode())) {
            return streamCdcService.dorisOffline(streamCdc);
        }
        return R.fail("只有DORIS数据源才有下线功能");
    }


    @ApiOperation("停止")
    @PostMapping("/connectors/{id}/stop")
    public R stopConnector(@PathVariable(value = "id") Long id) {
        StreamCdc streamCdc = streamCdcService.selectById(id);
        if (streamCdc == null || streamCdc.getTaskInfo() == null) {
            return R.fail("任务未提交上线");
        }
        if (streamCdc.getType().equalsIgnoreCase(DbType.DORIS.getCode())) {
            return streamCdcService.dorisOffline(streamCdc);
        } else {
            try {
                JSONObject jsonObject = JSON.parseObject(streamCdc.getTaskInfo());
                String name = jsonObject.getString("name");
                name = getName(name);
                jsonObject.put("name", name);
                List list = debeziumService.getConnectorList();
                if (list.contains(name)) {
                    debeziumService.deleteConnector(name);
                    streamCdcService.offline(name);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return R.ok();
    }

    @ApiOperation("校验")
    @PostMapping("/check")
    public R check(@RequestBody JSONObject jsonObject) {
        if ("DORIS".equals(jsonObject.getString("type"))) {
            return R.ok();
        } else {
            for (String key : debeziumJsonList) {
                if (!jsonObject.containsKey(key)) {
                    return R.fail("没有" + key + "属性");
                }
            }
            String name = jsonObject.getString("name");
            name = getName(name);
            jsonObject.put("name", name);
            JSONObject config = jsonObject.getJSONObject("config");
            config.put("name", name);
            String clazz = config.getString("connector.class");
            JSONObject object;
            List list = new ArrayList();
            Boolean flag = true;
            while (flag) {
                try {
                    list = debeziumService.getConnectorList();
                    flag = false;
                } catch (Exception e) {
                    logger.error(e.getLocalizedMessage());
                }
            }
            if (MYSQL_CLASS.equals(clazz)) {
                object = debeziumService.validateJson("MySql", config);
            } else if (ORACLE_CLASS.equals(clazz)) {
                config.put("lob.enabled", "true");
                config.put("query.fetch.size", "10000");
                config.put("log.mining.query.filter.mode", "in");
                config.put("poll.interval.ms", "100");
                config.put("log.mining.sleep.time.default.ms", "100");
                config.put("log.mining.sleep.time.increment.ms", "20");
                config.put("log.mining.archive.log.hours", "1");
                object = debeziumService.validateJson("Oracle", config);
            } else if (POSTGRESQL_CLASS.equals(clazz)) {
                config.put("slot.name", name.toLowerCase().replaceAll("-", ""));
                object = debeziumService.validateJson("Postgres", config);
            } else if (SQLSERVER_CLASS.equals(clazz)) {
                object = debeziumService.validateJson("SqlServer", config);
            } else {
                return R.fail("connector.class属性有误！");
            }
            return object.getIntValue("error_count") == 0 ? R.ok(object) : R.fail(this.getError(object));
        }
    }

    @ApiOperation("重启")
    @PostMapping("/connectors/{id}/restart")
    public R restartConnector(@PathVariable(value = "id") Long id) {
        StreamCdc streamCdc = streamCdcService.selectById(id);
        if (streamCdc == null || streamCdc.getTaskInfo() == null) {
            return R.fail("connector未提交上线");
        }
        JSONObject jsonObject = JSON.parseObject(streamCdc.getTaskInfo());
        String name = jsonObject.getString("name");
        name = getName(name);
        jsonObject.put("name", name);
        List list = debeziumService.getConnectorList();
        if (list.contains(name)) {
            debeziumService.restartConnector(name);
        }
        return R.ok();
    }

    @ApiOperation("恢复")
    @PutMapping("/connectors/{id}/resume")
    public R resumeConnector(@PathVariable(value = "id") Long id) {
        StreamCdc streamCdc = streamCdcService.selectById(id);
        if (streamCdc == null || streamCdc.getTaskInfo() == null) {
            return R.fail("connector未提交上线");
        }
        if (streamCdc.getType().equalsIgnoreCase(DbType.DORIS.getCode())) {
            return streamCdcService.dorisResume(streamCdc);
        } else {
            JSONObject jsonObject = JSON.parseObject(streamCdc.getTaskInfo());
            String name = jsonObject.getString("name");
            name = getName(name);
            jsonObject.put("name", name);
            List list = debeziumService.getConnectorList();
            if (list.contains(name)) {
                debeziumService.resumeConnector(name);
            }
            return R.ok();
        }
    }


    @ApiOperation("暂停")
    @PutMapping("/connectors/{id}/pause")
    public R pauseConnector(@PathVariable(value = "id") Long id) {
        StreamCdc streamCdc = streamCdcService.selectById(id);
        if (streamCdc == null || streamCdc.getTaskInfo() == null) {
            return R.fail("connector未提交上线");
        }
        if (streamCdc.getType().equalsIgnoreCase(DbType.DORIS.getCode())) {
            return streamCdcService.dorisPause(streamCdc);
        } else {
            JSONObject jsonObject = JSON.parseObject(streamCdc.getTaskInfo());
            String name = jsonObject.getString("name");
            name = getName(name);
            jsonObject.put("name", name);
            List list = debeziumService.getConnectorList();
            if (list.contains(name)) {
                debeziumService.pauseConnector(name);
            }
            return R.ok();
        }
    }


    @ApiOperation("状态")
    @PostMapping("/connectors/{id}/status")
    public R getConnectorTasksStatus(@PathVariable(value = "id") Long id) {
        StreamCdc streamCdc = streamCdcService.selectById(id);
        if (streamCdc == null || streamCdc.getTaskInfo() == null) {
            return R.fail("connector未提交上线");
        }
        int running = 0;
        int failed = 0;
        if (streamCdc.getType().equalsIgnoreCase(DbType.DORIS.getCode())) {
            return R.ok(streamCdcService.showDorisStatus(streamCdc));
        } else {
            JSONObject jsonObject = JSON.parseObject(streamCdc.getTaskInfo());
            String name = jsonObject.getString("name");
            name = getName(name);
            jsonObject.put("name", name);
            JSONObject re = debeziumService.getConnectorStatus(name);
            List<JSONObject> tasks = JSONArray.parseArray(JSONObject.toJSONString(re.get("tasks")), JSONObject.class);
            for (JSONObject objest : tasks) {
                String state = objest.getString("state");
                if ("RUNNING".equals(state)) {
                    running++;
                } else if ("FAILED".equals(state)) {
                    failed++;
                }
            }
            JSONObject result = new JSONObject();
            result.put("RUNNING", running);
            result.put("FAILED", failed);
            result.put("EXECUTE_INFO", tasks);
            return R.ok(result);
        }
    }

    private JSONObject getError(JSONObject jsonObject) {
        JSONObject result = new JSONObject();
        List<String> re = new ArrayList<>();
        List<JSONObject> configs = JSONArray.parseArray(jsonObject.getJSONArray("configs").toJSONString(), JSONObject.class);
        for (JSONObject config : configs) {
            JSONObject value = config.getJSONObject("value");
            String er = value.getString("errors");
            if (JSONArray.parseArray(er).size() != 0) {
                if (er.contains("connectionTimeZone")) {
                    re.add("发生了时区错误，你可以考虑设置这样一个配置属性： \"driver.connectionTimeZone\": \"Asia/Shanghai\"，错误信息如下：" + er);
                } else {
                    re.add(er);
                }
            }
        }
        result.put("error_message", re);
        result.put("error_count", jsonObject.getIntValue("error_count"));
        return result;
    }

    private String getName(String name) {
        name = name.replaceAll("_", "");
        name = "debezium" + name;
        name = name.toLowerCase();
        return name;
    }

    private Boolean checkJson(JSONObject jsonObject) {
        for (String key : debeziumJsonList) {
            if (!jsonObject.containsKey(key)) {
                return true;
            }
        }
        String name = jsonObject.getString("name");
        name = getName(name);
        jsonObject.put("name", name);
        JSONObject config = jsonObject.getJSONObject("config");
        config.put("name", name);
        String clazz = config.getString("connector.class");
        List list = new ArrayList();
        Boolean flag = true;
        while (flag) {
            try {
                list = debeziumService.getConnectorList();
                flag = false;
            } catch (Exception e) {
                logger.error(e.getLocalizedMessage());
            }
        }
        JSONObject object;
        if (!list.isEmpty()) {
            if (MYSQL_CLASS.equals(clazz)) {
                object = debeziumService.validateJson("MySql", config);
            } else if (ORACLE_CLASS.equals(clazz)) {
                config.put("lob.enabled", "true");
                config.put("query.fetch.size", "10000");
                config.put("log.mining.query.filter.mode", "in");
                config.put("poll.interval.ms", "100");
                config.put("log.mining.sleep.time.default.ms", "100");
                config.put("log.mining.sleep.time.increment.ms", "20");
                config.put("log.mining.archive.log.hours", "1");
                object = debeziumService.validateJson("Oracle", config);
            } else if (POSTGRESQL_CLASS.equals(clazz)) {
                config.put("slot.name", name.toLowerCase().replaceAll("-", ""));
                object = debeziumService.validateJson("Postgres", config);
            } else if (SQLSERVER_CLASS.equals(clazz)) {
                object = debeziumService.validateJson("SqlServer", config);
            } else {
                return true;
            }
            return object.getIntValue("error_count") != 0;
        }
        // 连接都没建立的就不需要校验
        return false;
    }
}

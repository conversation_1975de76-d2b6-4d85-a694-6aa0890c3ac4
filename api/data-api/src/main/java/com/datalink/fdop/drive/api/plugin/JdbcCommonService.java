package com.datalink.fdop.drive.api.plugin;

import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.drive.api.domain.dto.FieldValue;
import com.datalink.fdop.drive.api.domain.dto.UniqueField;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/9 14:12
 */
public interface JdbcCommonService extends CommonService {

    /**
     * 测试连接
     *
     * @return
     */
    boolean connect(JdbcTemplate jdbcTemplate);


    // 获取所有数据库
    Map<String, Object> getDatabases(JdbcTemplate jdbcTemplate, String databaseName, Integer pageNo, Integer pageSize);

    /**
     * 获取所有有schema的数据库名称
     * @param jdbcTemplate jdbc操作对象
     * @return 数据库名称
     */
    List<String> getSchemasDatabases(JdbcTemplate jdbcTemplate);

    Map<String, Object> getSchemasByDatabase(JdbcTemplate jdbcTemplate, String dbName, String databaseName, Integer pageNo, Integer pageSize);

    // 获取所有schema
    Map<String, Object> getSchemaName(JdbcTemplate jdbcTemplate, String databaseName, Integer pageNo, Integer pageSize);

    // 获取数据库下的所有表
    Map<String, Object> getTables(JdbcTemplate jdbcTemplate, String databaseName, String tableName, Integer pageNo, Integer pageSize);

    // 获取数据库下的所有表详情（表名称，描述）
    Map<String, Object> getTableDetails(JdbcTemplate jdbcTemplate, String databaseName, String tableName, Integer pageNo, Integer pageSize);

    // 获取数据库下的所有视图
    Map<String, Object> getViews(JdbcTemplate jdbcTemplate, String databaseName, String viewName, Integer pageNo, Integer pageSize);

    // 获取表的所有字段
    List<Field> getFields(JdbcTemplate jdbcTemplate, String databaseName, String tableName);

    /**
     * 获取表的唯一字段(没有主键字段时再去检索一下是否有唯一键)
     */
    List<UniqueField> getUniqueFields(JdbcTemplate jdbcTemplate, String databaseName, String tableName);

    // DDL语句
    void execDdlSql(JdbcTemplate jdbcTemplate, String sql);

    // DQL语句
    List<Map<String, Object>> execDqlSql(JdbcTemplate jdbcTemplate, String sql);

    /**
     * 自定义insert语句
     *
     * @param databaseName
     * @param tableName
     * @param insertField  key为字段名，value为字段值
     * @return
     */
    String insert(String databaseName, String tableName, Map<String, Object> insertField);

    // 自定义批量insert语句
    String insertBatch(String databaseName, String tableName, List<Map<String, Object>> insertFieldList);

    // 自定义update语句
    String update(String databaseName, String tableName, List<FieldValue> setFieldValueList, List<FieldValue> whereFieldValueList);

    // 自定义delete语句
    String delete(String databaseName, String tableName, List<FieldValue> fieldValueList);

    // 执行delete语句
    void delete(JdbcTemplate jdbcTemplate, String databaseName, String tableName, String condition);

    // 验证长度是否合法
    void verifyLength(List<Field> fieldList);

    // 自定义创表语句
    void createTable(JdbcTemplate jdbcTemplate, String databaseName, String tableName, List<Field> fieldList, Map<String, Object> specialMap);

    // 删除表
    void dropTable(JdbcTemplate jdbcTemplate, String databaseName, String tableName);

    // 判断表是否存在
    Boolean isExistTable(JdbcTemplate jdbcTemplate, String databaseName, String tableName);

    void execDMlSql(JdbcTemplate jdbcTemplate, String sql);

    void truncate(JdbcTemplate jdbcTemplate, String databaseName, String tableName);

    void addField(JdbcTemplate jdbcTemplate, String databaseName, String tableName, List<Field> fields);

    void batchInsert(JdbcTemplate jdbcTemplate, String databaseName, String tableName, List<Map<String, Object>> datas);

    void createSchema(JdbcTemplate jdbcTemplate, String databaseName);

}

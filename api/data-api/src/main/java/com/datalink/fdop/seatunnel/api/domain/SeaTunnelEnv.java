package com.datalink.fdop.seatunnel.api.domain;

import com.datalink.fdop.seatunnel.api.enums.SeaTunnelJobMode;
import lombok.Data;

/**
 * SeaTunnel运行环境
 */
@Data
public class SeaTunnelEnv {

    /**
     * 运行模式 BATCH/STREAMING
     */
    private SeaTunnelJobMode jobMode;

    /**
     * SeaTunnel 任务名称
     */
    private String jobName;

    /**
     * 并行度（源与目标同步使用公共参数）
     */
    private Integer parallelism;

    /**
     * checkpoint间隔
     */
    private Integer checkpointInterval;

    /**
     * SeaTunnel配置文件加解密策略
     * 详情用法查看：https://seatunnel.apache.org/docs/2.3.3/connector-v2/Config-Encryption-Decryption/
     */
    private String shadeIdentifier;

    /**
     * SeaTunnel 版本号
     */
    private String version;

}

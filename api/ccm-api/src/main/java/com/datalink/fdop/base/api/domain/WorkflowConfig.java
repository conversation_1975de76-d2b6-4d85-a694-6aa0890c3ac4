package com.datalink.fdop.base.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.Map;

@Data
@TableName(schema = "zjdata", value = "ccm_workflow_config")
@ApiModel
public class WorkflowConfig {

    @ApiModelProperty(value = "CCM功能编码")
    @NotBlank(message = "CCM功能编码不能为空")
    @TableId
    private String ccmFunCode;

    @ApiModelProperty(value = "CCM功能名称")
    private String ccmFunName;

    @ApiModelProperty(value = "数据同步工作流编码")
    private String dataSyncWorkflowCode;

    @ApiModelProperty(value = "数据同步工作流名称")
    private String dataSyncWorkflowName;

    @ApiModelProperty(value = "逻辑计算工作流编码")
    private String logicalWorkflowCode;

    @ApiModelProperty(value = "逻辑计算工作流名称")
    private String logicalWorkflowName;

    @ApiModelProperty(value = "推送数据工作流编码")
    private String pushDataWorkflowCode;

    @ApiModelProperty(value = "推送数据工作流名称")
    private String pushDataWorkflowName;

    @ApiModelProperty(value = "更新为定版工作流编码")
    private String finalVersionWorkflowCode;

    @ApiModelProperty(value = "更新为定版工作流名称")
    private String finalVersionWorkflowName;

    @ApiModelProperty(value = "数据提交工作流编码")
    private String dataSubmitWorkflowCode;

    @ApiModelProperty(value = "数据提交工作流名称")
    private String dataSubmitWorkflowName;

    @ApiModelProperty(value = "CCM功能是否锁定")
    private Boolean ccmFunLock;

    @ApiModelProperty(value = "更改人")
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "更改时间")
    private Date updateTime;

    @ApiModelProperty(value = "工作流参数")
    @TableField(exist = false)
    private Map<String,Object> workflowParams;

}

package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostProd;

import java.util.List;

public interface DwhFinalStdCostProdService extends IService<DwhFinalStdCostProd> {

    PageDataInfo<DwhFinalStdCostProd> overview(
            String plantId, String verId, String controlAreaId, String productId, Integer batchQty, String baseYearMonth,
            String dateFrom, String dateTo, String sort, SearchVo searchVo);

    List<DwhFinalStdCostProd> listAll();

    List<DwhFinalStdCostProd> selectNoPage(String plantId,
                                           String verId,
                                           String controlAreaId,
                                           String productId,
                                           Integer batchQty,
                                           String baseYearMonth,
                                           String dateFrom,
                                           String dateTo,
                                           String sort,
                                           SearchVo searchVo);
}
package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewStdCostCcrate;

import java.util.List;

public interface DwsNewStdCostCcrateService extends IService<DwsNewStdCostCcrate> {

    PageDataInfo<DwsNewStdCostCcrate> overview(
            String controlAreaId, String costCenterId, String costStructureId, String activityId,
            String verId, String valueType, String date,
            String sort, SearchVo searchVo);

    List<DwsNewStdCostCcrate> selectNoPage(String controlAreaId, String costCenterId, String costStructureId, String activityId, String verId, String valueType, String date, String sort, SearchVo searchVo);
}
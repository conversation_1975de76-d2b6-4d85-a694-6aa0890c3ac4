package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwdTraceWip;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdTraceWipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwdTraceWip")
@Api(tags = "同步视图-时点在制状态")
public class DwdTraceWipController extends BaseController {

    @Autowired
    private DwdTraceWipService dwdTraceWipService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "queryParam", value = "高级搜索及查询参数", required = false, dataTypeClass = QueryParam.class, paramType = "body"),
    })
    @Log(title = "同步视图-时点在制状态")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String dateFrom = MapUtils.getAsString(params, "dateFrom");
        String dateTo = MapUtils.getAsString(params, "dateTo");
        PageDataInfo<DwdTraceWip> overview = dwdTraceWipService.overview(plantId, dateFrom, dateTo, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdTraceWip.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "同步视图-时点在制状态", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String dateFrom = MapUtils.getAsString(params, "dateFrom");
        String dateTo = MapUtils.getAsString(params, "dateTo");
        List<DwdTraceWip> list = dwdTraceWipService.selectNoPage(plantId, dateFrom, dateTo, sort, queryParam.getSearchVo());
        ExcelUtil<DwdTraceWip> util = new ExcelUtil<>(DwdTraceWip.class);
        util.exportExcel(response, list, "同步视图-时点在制状态数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdTraceWip> util = new ExcelUtil<>(DwdTraceWip.class);
        util.importTemplateExcel(response, "同步视图-时点在制状态模板");
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwd.dwd_trace_wip", null));
    }
    
}
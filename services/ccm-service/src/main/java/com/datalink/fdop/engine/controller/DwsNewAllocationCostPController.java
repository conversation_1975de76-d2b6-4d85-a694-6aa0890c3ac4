package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.SearchVoUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationCostP;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwsNewAllocationCostPService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwsNewAllocationCostP")
@Api(tags = "逻辑视图-定向分摊")
public class DwsNewAllocationCostPController extends BaseController {

    @Autowired
    private DwsNewAllocationCostPService dwsNewAllocationCostPService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "逻辑视图-定向分摊")
    @PostMapping(value = "/selectTargetedNewAllocation")
    public Object selectTargetedNewAllocation(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");
        PageDataInfo<DwsNewAllocationCostP> overview = dwsNewAllocationCostPService.selectTargetedNewAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwsNewAllocationCostP.class, overview));
    }

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "逻辑视图-费用分摊")
    @PostMapping(value = "/selectCostNewAllocation")
    public Object selectCostNewAllocation(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");
        PageDataInfo<DwsNewAllocationCostP> overview = dwsNewAllocationCostPService.selectCostNewAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwsNewAllocationCostP.class, overview));
    }

    @ApiOperation(value = "导出逻辑视图-定向分摊数据")
    @Log(title = "逻辑视图-定向分摊", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void exportData(HttpServletResponse response,
                           @RequestParam(required = false, defaultValue = "ASC") String sort,
                           @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");
        List<DwsNewAllocationCostP> list = dwsNewAllocationCostPService.selectNoPage(controlAreaId, verId, companyId,
                yearMonthFrom, yearMonthTo, allocationType, sort, queryParam.getSearchVo());
        ExcelUtil<DwsNewAllocationCostP> util = new ExcelUtil<>(DwsNewAllocationCostP.class);
        util.exportExcel(response, list, "逻辑视图-定向分摊");
    }

    @ApiOperation(value = "导出逻辑视图-定向分摊数据")
    @Log(title = "逻辑视图-定向分摊", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTargetedNewAllocationData")
    public void exportTargetedNewAllocationData(HttpServletResponse response) {
        List<DwsNewAllocationCostP> list = dwsNewAllocationCostPService.lambdaQuery().in(DwsNewAllocationCostP::getAllocationType, "assign_eid", "assign_pid").list();
        ExcelUtil<DwsNewAllocationCostP> util = new ExcelUtil<>(DwsNewAllocationCostP.class);
        util.exportExcel(response, list, "逻辑视图-定向分摊");
    }

    @ApiOperation(value = "导出逻辑视图-费用分摊数据")
    @Log(title = "逻辑视图-费用分摊", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCostNewAllocationData")
    public void exportCostNewAllocationData(HttpServletResponse response) {
        List<DwsNewAllocationCostP> list = dwsNewAllocationCostPService.lambdaQuery().eq(DwsNewAllocationCostP::getAllocationType, "assign_non").list();
        ExcelUtil<DwsNewAllocationCostP> util = new ExcelUtil<>(DwsNewAllocationCostP.class);
        util.exportExcel(response, list, "逻辑视图-费用分摊数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsNewAllocationCostP> util = new ExcelUtil<>(DwsNewAllocationCostP.class);
        util.importTemplateExcel(response, "逻辑视图-定向分摊模板");
    }

    @ApiOperation(value = "管理范围（SelectVo）")
    @GetMapping("/manageScope")
    public R<List<SelectVo>> manageScope(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listManageScope("dws.dws_new_allocation_cost_p", searchVo));
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listVerId("dws.dws_new_allocation_cost_p", searchVo));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listCompanyId("dws.dws_new_allocation_cost_p", "node.cost_object_fr_company_id", searchVo));
    }

}
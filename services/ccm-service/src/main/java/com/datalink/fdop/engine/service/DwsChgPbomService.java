package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsChgPbom;

import java.util.List;

public interface DwsChgPbomService extends IService<DwsChgPbom> {

    PageDataInfo<DwsChgPbom> overview(String verId,String  plantId,String productId,String sort, SearchVo searchVo);

    List<DwsChgPbom> listAll();

    List<DwsChgPbom> selectNoPage(String verId,
                                  String plantId,
                                  String productId,
                                  String sort,
                                  SearchVo searchVo);
}

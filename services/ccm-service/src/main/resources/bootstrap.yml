nacos:
  server:
    addr: @nacos_server@
    group: @nacos_server_group@
  config:
    addr: @nacos_server@
    group: @nacos_config_group@

# Tomcat
server:
  port: 9549

# Spring
spring:
  main:
    allow-bean-definition-overriding: true
  jackson:
    time-zone: Asia/Shanghai
  application:
    # 应用名称
    name: ccm-service
  profiles:
    # 环境配置
    active: @profiles.active@
  cloud:
    nacos:
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      discovery:
        # 服务注册地址
        server-addr: ${nacos.server.addr}
        # 服务分组
        group: ${nacos.server.group}
      config:
        # 配置组
        group: ${nacos.config.group}
        # 配置中心地址
        server-addr: ${nacos.server.addr}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - data-id: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.config.group}
          - data-id: application-db.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.config.group}
          - data-id: application-redis.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.config.group}

# swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 标题
  title: 'CCM模块接口文档'
  # 描述
  description: 'CCM模块接口文档'
  # 版本
  version: @docker.imagesName.version@

mybatis-plus:
  type-handlers-package: com.datalink.fdop.engine.handler

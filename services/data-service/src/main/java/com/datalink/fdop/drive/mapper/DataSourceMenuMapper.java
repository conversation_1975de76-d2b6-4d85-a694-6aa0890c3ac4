package com.datalink.fdop.drive.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.drive.api.domain.DataSourceMenu;
import com.datalink.fdop.drive.api.model.DataSourceTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataSourceMenuMapper extends BaseMapper<DataSourceMenu> {

    DataSourceMenu selectByCode(String code);

    int insertDataSourceMenu(@Param("dataSourceMenu") DataSourceMenu dataSourceMenu);

    int createDataSourceMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    DataSourceMenu selectById(Long id);

    int updateById(DataSourceMenu dataElementMenu);

    List<Long> selectIdsByPid(Long pid);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    DataSourceMenu checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    int deleteSourceMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    List<DataSourceTree> selectMenuTree(@Param("sort") String sort, @Param("code") String code);
}

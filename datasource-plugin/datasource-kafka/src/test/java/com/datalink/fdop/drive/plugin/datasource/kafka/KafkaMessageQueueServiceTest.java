package com.datalink.fdop.drive.plugin.datasource.kafka;

import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.drive.plugin.datasource.kafka.service.impl.KafkaMessageQueueServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Kafka消息队列服务测试类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class KafkaMessageQueueServiceTest {

    private KafkaMessageQueueServiceImpl kafkaService;
    private DataSourceBasicInfo dataSourceBasicInfo;

    @Before
    public void setUp() {
        kafkaService = new KafkaMessageQueueServiceImpl();

        // 设置测试用的数据源信息
        dataSourceBasicInfo = new DataSourceBasicInfo();
        dataSourceBasicInfo.setHost("localhost");
        dataSourceBasicInfo.setPort(9092);
        dataSourceBasicInfo.setJdbcUrl("localhost:9092");
        dataSourceBasicInfo.setPluginId("datasource-kafka");
        // 如果需要认证，可以设置用户名和密码
        // dataSourceBasicInfo.setUsername("your-username");
        // dataSourceBasicInfo.setPassword("your-password");
    }

    @Test
    public void testConnect() {
        System.out.println("测试Kafka连接...");
        try {
            boolean connected = kafkaService.connect(dataSourceBasicInfo);
            System.out.println("连接结果: " + connected);
            assert connected : "Kafka连接失败";
        } catch (Exception e) {
            System.err.println("连接测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetDbType() {
        String dbType = kafkaService.getDbType();
        System.out.println("数据库类型: " + dbType);
        assert "kafka".equals(dbType) : "数据库类型不正确";
    }

    @Test
    public void testGetVersion() {
        String version = kafkaService.getVersion();
        System.out.println("版本: " + version);
        assert version != null && !version.isEmpty() : "版本信息为空";
    }

    @Test
    public void testGetDataSourceBasicInfo() {
        DataSourceBasicInfo result = kafkaService.getDataSourceBasicInfo(dataSourceBasicInfo);
        System.out.println("处理后的数据源信息: " + result.getJdbcUrl());
        assert result.getJdbcUrl().startsWith("kafka://") : "JDBC URL格式不正确";
    }

    @Test
    public void testGetTopics() {
        System.out.println("测试获取Topic列表...");
        try {
            Map<String, Object> result = kafkaService.getTopics(dataSourceBasicInfo, null, 1, 10);
            System.out.println("Topic列表: " + result);
            assert result.containsKey("total") : "结果中缺少total字段";
            assert result.containsKey("data") : "结果中缺少data字段";
        } catch (Exception e) {
            System.err.println("获取Topic列表失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetClusterInfo() {
        System.out.println("测试获取集群信息...");
        try {
            Map<String, Object> result = kafkaService.getClusterInfo(dataSourceBasicInfo);
            System.out.println("集群信息: " + result);
            assert result.containsKey("clusterId") : "结果中缺少clusterId字段";
        } catch (Exception e) {
            System.err.println("获取集群信息失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetConsumerGroups() {
        System.out.println("测试获取消费者组列表...");
        try {
            Map<String, Object> result = kafkaService.getConsumerGroups(dataSourceBasicInfo, null, 1, 10);
            System.out.println("消费者组列表: " + result);
            assert result.containsKey("total") : "结果中缺少total字段";
            assert result.containsKey("data") : "结果中缺少data字段";
        } catch (Exception e) {
            System.err.println("获取消费者组列表失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testCreateAndDeleteTopic() {
        String testTopicName = "test-topic-" + System.currentTimeMillis();
        System.out.println("测试创建和删除Topic: " + testTopicName);

        try {
            // 创建Topic
            Map<String, String> configs = new HashMap<>();
            configs.put("cleanup.policy", "delete");
            configs.put("retention.ms", "86400000"); // 1天

            boolean created = kafkaService.createTopic(dataSourceBasicInfo, testTopicName, 1, (short) 1, configs);
            System.out.println("Topic创建结果: " + created);

            if (created) {
                // 检查Topic是否存在
                boolean exists = kafkaService.isTopicExists(dataSourceBasicInfo, testTopicName);
                System.out.println("Topic存在检查: " + exists);
                assert exists : "创建的Topic不存在";

                // 获取Topic信息
                Map<String, Object> topicInfo = kafkaService.getTopicInfo(dataSourceBasicInfo, testTopicName);
                System.out.println("Topic信息: " + topicInfo);

                // 获取分区信息
                List<Map<String, Object>> partitions = kafkaService.getTopicPartitions(dataSourceBasicInfo, testTopicName);
                System.out.println("分区信息: " + partitions);

                // 删除Topic
                boolean deleted = kafkaService.deleteTopic(dataSourceBasicInfo, testTopicName);
                System.out.println("Topic删除结果: " + deleted);
            }
        } catch (Exception e) {
            System.err.println("创建和删除Topic测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetTopicMessages() {
        System.out.println("测试获取Topic消息...");
        try {
            // 首先获取可用的Topic列表
            Map<String, Object> topicsResult = kafkaService.getTopics(dataSourceBasicInfo, null, 1, 10);
            @SuppressWarnings("unchecked")
            List<String> topics = (List<String>) topicsResult.get("data");

            if (!topics.isEmpty()) {
                String topicName = topics.get(0);
                System.out.println("测试Topic: " + topicName);

                // 测试不带jsonPath的消息获取
                System.out.println("1. 测试不带jsonPath的消息获取:");
                List<Map<String, Object>> messages = kafkaService.getTopicMessages(dataSourceBasicInfo, topicName, null, 5);
                System.out.println("获取到 " + messages.size() + " 条消息");

                for (Map<String, Object> message : messages) {
                    System.out.println("消息: " + message);
                    // 验证新的返回格式：只包含key和value，不包含partition、offset等元数据
                    assert message.containsKey("value") : "消息中缺少value字段";
                    assert !message.containsKey("partition") : "消息中不应包含partition字段";
                    assert !message.containsKey("offset") : "消息中不应包含offset字段";
                    assert !message.containsKey("timestamp") : "消息中不应包含timestamp字段";
                }

                // 测试带jsonPath的消息获取
                System.out.println("\n2. 测试带jsonPath的消息获取:");
                String jsonPath = "$.data"; // 假设JSON结构中有data字段
                List<Map<String, Object>> messagesWithPath = kafkaService.getTopicMessages(dataSourceBasicInfo, topicName, jsonPath, 3);
                System.out.println("使用jsonPath [" + jsonPath + "] 获取到 " + messagesWithPath.size() + " 条消息");

                for (Map<String, Object> message : messagesWithPath) {
                    System.out.println("过滤后的消息: " + message);
                }
            } else {
                System.out.println("没有可用的Topic进行测试");
            }
        } catch (Exception e) {
            System.err.println("获取Topic消息失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetTopicFields() {
        System.out.println("测试获取Topic字段...");
        try {
            // 首先获取可用的Topic列表
            Map<String, Object> topicsResult = kafkaService.getTopics(dataSourceBasicInfo, null, 1, 10);
            @SuppressWarnings("unchecked")
            List<String> topics = (List<String>) topicsResult.get("data");

            if (!topics.isEmpty()) {
                String topicName = topics.get(0);
                System.out.println("测试Topic: " + topicName);

                // 测试不带jsonPath的字段解析
                System.out.println("测试不带jsonPath的字段解析...");
                List<Field> fields = kafkaService.getTopicFields(dataSourceBasicInfo, topicName, null);
                System.out.println("解析出 " + fields.size() + " 个字段:");
                for (Field field : fields) {
                    System.out.println("  - " + field.getFieldName() + " (" + field.getFieldType() + "/" + field.getBaseFieldType() + ")");
                }

                // 测试带jsonPath的字段解析
                System.out.println("测试带jsonPath的字段解析...");
                String jsonPath = "$.data"; // 假设JSON结构中有data字段
                List<Field> fieldsWithPath = kafkaService.getTopicFields(dataSourceBasicInfo, topicName, jsonPath);
                System.out.println("使用jsonPath [" + jsonPath + "] 解析出 " + fieldsWithPath.size() + " 个字段:");
                for (Field field : fieldsWithPath) {
                    System.out.println("  - " + field.getFieldName() + " (" + field.getFieldType() + "/" + field.getBaseFieldType() + ")");
                }

            } else {
                System.out.println("没有可用的Topic进行测试");
            }
        } catch (Exception e) {
            System.err.println("获取Topic字段失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 集成测试 - 测试完整的工作流程
     */
    @Test
    public void testFullWorkflow() {
        System.out.println("开始集成测试...");

        try {
            // 1. 测试连接
            System.out.println("1. 测试连接");
            boolean connected = kafkaService.connect(dataSourceBasicInfo);
            System.out.println("连接状态: " + connected);

            if (!connected) {
                System.out.println("连接失败，跳过后续测试");
                return;
            }

            // 2. 获取集群信息
            System.out.println("2. 获取集群信息");
            Map<String, Object> clusterInfo = kafkaService.getClusterInfo(dataSourceBasicInfo);
            System.out.println("集群信息: " + clusterInfo);

            // 3. 获取Topic列表
            System.out.println("3. 获取Topic列表");
            Map<String, Object> topicsResult = kafkaService.getTopics(dataSourceBasicInfo, null, 1, 10);
            System.out.println("Topic数量: " + topicsResult.get("total"));

            // 4. 获取消费者组列表
            System.out.println("4. 获取消费者组列表");
            Map<String, Object> groupsResult = kafkaService.getConsumerGroups(dataSourceBasicInfo, null, 1, 10);
            System.out.println("消费者组数量: " + groupsResult.get("total"));

            System.out.println("集成测试完成");

        } catch (Exception e) {
            System.err.println("集成测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwsNewRecoverEntMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwsNewRecoverEnt">
        SELECT 
            *
        FROM dws.dws_new_recover_ent node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="yearMonth != null and yearMonth != ''">
                and DATE_FORMAT(STR_TO_DATE(node.date, '%Y%m%d'), '%Y-%m') = #{yearMonth}
            </if>
        </where>
        ORDER BY node.ver_id,node.plant_id,node.date,node.timestamp,node.base_order,node.work_order,node.base_lot_id,node.lot_id,node.history_step_no ${sort}
    </select>
    <select id="listAll" resultType="com.datalink.fdop.engine.api.domain.DwsNewRecoverEnt">
        SELECT * FROM dws.dws_new_recover_ent
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwsNewRecoverEnt">
        SELECT
        *
        FROM dws.dws_new_recover_ent node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and node.ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="yearMonth != null and yearMonth != ''">
                and DATE_FORMAT(STR_TO_DATE(node.date, '%Y%m%d'), '%Y-%m') = #{yearMonth}
            </if>
        </where>
        ORDER BY node.ver_id,node.plant_id,node.date,node.timestamp,node.base_order,node.work_order,node.base_lot_id,node.lot_id,node.history_step_no ${sort}
    </select>

</mapper>
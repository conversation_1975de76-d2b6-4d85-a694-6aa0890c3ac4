package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdMaterialMarm;
import com.datalink.fdop.engine.mapper.DwdMaterialMarmMapper;
import com.datalink.fdop.engine.service.DwdMaterialMarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdMaterialMarmServiceImpl extends ServiceImpl<DwdMaterialMarmMapper, DwdMaterialMarm> implements DwdMaterialMarmService {

    @Autowired
    private DwdMaterialMarmMapper dwdMaterialMarmMapper;

    @Override
    public PageDataInfo<DwdMaterialMarm> overview(
                                                  String materialId,String sort, SearchVo searchVo) {
        Page<DwdMaterialMarm> page = PageUtils.getPage(DwdMaterialMarm.class);
        IPage<DwdMaterialMarm> iPage = dwdMaterialMarmMapper.selectAll(materialId,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdMaterialMarm> selectNoPage(String materialId, String sort, SearchVo searchVo) {
        return dwdMaterialMarmMapper.selectNoPage(materialId, sort, searchVo);
    }
}

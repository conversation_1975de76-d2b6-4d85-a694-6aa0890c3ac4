<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CapacityAttributeMapper">

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_capacity_attribute
        WHERE equip_id = #{equipId} and attribute_id = #{attributeId} limit 1
    </select>

    <delete id="deleteById" parameterType="String">
        delete from zjdata.ccm_capacity_attribute
        WHERE equip_id = #{equipId}  and attribute_id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from zjdata.ccm_capacity_attribute
        WHERE (equip_id,attribute_id) IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            (#{item.equipId},#{item.attributeId})
        </foreach>
    </delete>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_capacity_attribute
        SET enable = #{enable}
        WHERE (equip_id,attribute_id) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.equipId},#{item.attributeId})
        </foreach>
    </update>

    <update id="update">
        UPDATE zjdata.ccm_capacity_attribute
        set
            <if test="equipId != null ">equip_id =#{equipId},</if>
            <if test="attributeValue != null ">attribute_value =#{attributeValue},</if>
            <if test="enable != null">enable =#{enable},</if>
        update_by =#{updateBy},update_time=#{updateTime}
        WHERE equip_id = #{equipId}  and attribute_id = #{attributeId}
    </update>


    <select id="pageList" resultType="com.datalink.fdop.base.api.domain.CapacityAttribute">
        select a.*, c.attribute_desc as attributeDesc from zjdata.ccm_capacity_attribute  a
        left join  zjdata.ccm_attribute c on a.attribute_id = c.attribute_id
        <where>
            <if test="searchVo !=null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("a",searchVo)})
            </if>
        </where>
        order by a.attribute_id
        <if test="sort != null and sort != ''">
            ${sort}
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_capacity_attribute (
        attribute_id, attribute_desc, attribute_text,enable, create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.attributeId},#{item.attributeDesc},#{item.attributeText},
            #{item.enable},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>

</mapper>


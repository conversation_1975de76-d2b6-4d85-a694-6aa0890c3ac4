package com.datalink.fdop.element.api.factory;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.element.api.RemoteEntityTableService;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class
RemoteEntityTableFallbackFactory implements FallbackFactory<RemoteEntityTableService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteEntityTableFallbackFactory.class);

    @Override
    public RemoteEntityTableService create(Throwable throwable) {
        log.error("数据元素服务调用失败:{}", throwable.getMessage());
        return new RemoteEntityTableService() {
            @Override
            public R<DataEntityTable> selectById(Long dataElementId) {
                return R.fail("查询数据实体关联表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<DataEntityTableMapping>> selectTableMapping(Long dataEntityId, Long tableId) {
                return R.fail("查询数据实体关联表映射关系失败:" + throwable.getMessage());
            }

            @Override
            public R<List<DataEntityTableMapping>> selectEntityTableMapping(Long dataEntityId) {
                return R.fail("查询数据实体关联表映射关系失败:" + throwable.getMessage());
            }

            @Override
            public R<DataEntityTable> selectByIdAndTenantId(Long dataEntityId, Long tenantId) {
                return R.fail("查询数据实体关联表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<DataEntityTableMapping>> selectTableMappingByTenantId(Long dataEntityId, Long tenantId, Long tableId) {
                return R.fail("查询数据实体关联表映射关系失败:" + throwable.getMessage());
            }

            @Override
            public R<List<DataEntityTableMapping>> selectEntityTableMappingByTenantId(Long dataEntityId, Long tenantId) {
                return R.fail("查询数据实体关联表映射关系失败:" + throwable.getMessage());
            }
        };
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.EvaluateElementMapper">

    <delete id="deleteByKey">
        delete from zjdata.ccm_evaluate_element
        WHERE (evaluate_area_group, evaluate_sort_id, movement_type) IN
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.evaluateAreaGroup},#{item.evaluateSortId},#{item.movementType})
        </foreach>
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_evaluate_element
        WHERE evaluate_area_group = #{evaluateAreaGroup} AND evaluate_sort_id = #{evaluateSortId}
        AND movement_type = #{movementType}
        limit 1
    </select>

    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.EvaluateElement">
        UPDATE zjdata.ccm_evaluate_element
        SET
        <if test="controlAreaId != null">control_area_id = #{controlAreaId},</if>
        <if test="costCenterId != null">cost_center_id = #{costCenterId},</if>
        <if test="origCostElementId != null">orig_cost_element_id = #{origCostElementId},</if>
        <if test="costElementId != null">cost_element_id = #{costElementId},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        WHERE evaluate_area_group = #{evaluateAreaGroup} AND evaluate_sort_id = #{evaluateSortId}
        AND movement_type = #{movementType}
    </update>

    <select id="selectControlAreaId" resultType="String" parameterType="String">
        select control_area_id from zjdata.ccm_control_area where control_area_id = #{id} limit 1
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.EvaluateElement">
        SELECT node.*
        FROM zjdata.ccm_evaluate_element node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
        </where>
        ORDER BY  evaluate_area_group, evaluate_sort_id, movement_type ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_evaluate_element
        SET enable = #{enable}
        WHERE (evaluate_area_group, evaluate_sort_id, movement_type) IN
        <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
            (#{item.evaluateAreaGroup},#{item.evaluateSortId},#{item.movementType})
        </foreach>
    </update>


    <select id="selectEvaluateAreaGroup"  resultType="String">
        SELECT
             evaluate_area_group as evaluateAreaGroup
        FROM zjdata.ccm_evaluate_area
        WHERE enable = true AND evaluate_area_group IS NOT NULL AND evaluate_area_group &lt;&gt; ''
        GROUP BY evaluate_area_group
    </select>

    <select id="selectEvaluateSort"  resultType="String">
        SELECT
             evaluate_sort_id as evaluateSortId
        FROM zjdata.ccm_evaluate_revalue
        WHERE enable = true AND evaluate_sort_id IS NOT NULL AND evaluate_sort_id &lt;&gt; ''
        GROUP BY evaluate_sort_id
    </select>


</mapper>


package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.base.service.AttributeService;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.security.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwsNewCapacity;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwsNewCapacityService;
import com.datalink.fdop.engine.utils.DynamicExcelExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwsNewCapacity")
@Api(tags = "CCMS-产能整合单元")
public class DwsNewCapacityController extends BaseController {

    @Autowired
    private DwsNewCapacityService dwsNewCapacityService;

    @Autowired
    private AttributeService attributeService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "逻辑视图")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params , "verId");
        String plantId = MapUtils.getAsString(params , "plantId");

        PageDataInfo<DwsNewCapacity> overview = dwsNewCapacityService.overview(verId, plantId, sort, queryParam.getSearchVo());
        // 查询动态列配置
        List<DynamicColumn> dynamicColumns = attributeService.selectDynamicColumnList();
        return R.ok(MetaUtils.getMetadata(DwsNewCapacity.class, overview, dynamicColumns));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "逻辑视图", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params , "verId");
        String plantId = MapUtils.getAsString(params , "plantId");
        List<DwsNewCapacity> list = dwsNewCapacityService.selectNoPage(verId, plantId,sort,queryParam.getSearchVo());
        List<DynamicColumn> dynamicColumns = attributeService.selectDynamicColumnList();
        DynamicExcelExportUtil<DwsNewCapacity> excelUtil = new DynamicExcelExportUtil<>(DwsNewCapacity.class);
        excelUtil.exportExcelWithDynamicColumns(response, list, dynamicColumns, "暂存：产能数据");

    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsNewCapacity> util = new ExcelUtil<>(DwsNewCapacity.class);
        util.importTemplateExcel(response, "暂存：产能数据");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dws.dws_new_capacity", null));
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dws.dws_new_capacity", null));
    }

}

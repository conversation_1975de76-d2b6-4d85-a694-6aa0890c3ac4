package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRbom;

import java.util.List;

public interface DwhFinalRbomService extends IService<DwhFinalRbom> {

    PageDataInfo<DwhFinalRbom> overview(String verId, String plantId, String productId, String sort, SearchVo searchVo);

    List<DwhFinalRbom> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo);
}
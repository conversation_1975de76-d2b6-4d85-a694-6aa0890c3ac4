package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.BinPropertyConfig;
import com.datalink.fdop.fscm.service.BinPropertyConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/14 16:03
 */
@RestController
@RequestMapping("/bin/property")
@Transactional
public class BinPropertyConfigController {
    @Autowired
    private BinPropertyConfigService binPropertyConfigService;

    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) SearchVo searchVo) {
        Page<BinPropertyConfig> page = PageUtils.getPage(BinPropertyConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<BinPropertyConfig> pageData=binPropertyConfigService.page(page,new QueryWrapper<BinPropertyConfig>()
                .lambda()
                .orderByAsc(BinPropertyConfig::getAttributeName));
        List<BinPropertyConfig> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
            page.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }



    @ApiOperation(value = "新增修改")
    @Log(title = "fscm",businessType = BusinessType.INSERT_OR_UPDATE)
    @PostMapping("/createOrUpdate")
    public R create(@RequestBody List<BinPropertyConfig> binPropertyConfigs) {
        binPropertyConfigs.forEach(data -> {
            BinPropertyConfig one=null;
            if (data.getId()!=null) {
                one= binPropertyConfigService.getOne(new QueryWrapper<BinPropertyConfig>().lambda()
                        .eq(BinPropertyConfig::getAttributeName,data.getAttributeName())
                        .ne(BinPropertyConfig::getId,data.getId())
                );
            }else {
                one= binPropertyConfigService.getOne(new QueryWrapper<BinPropertyConfig>().lambda()
                        .eq(BinPropertyConfig::getAttributeName,data.getAttributeName())
                );
            }
            if (one!=null) {
                throw new ServiceException("数据已存在");
            }
        });
        if (binPropertyConfigService.saveOrUpdateBatch(binPropertyConfigs)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids) {
        if (binPropertyConfigService.removeBatchByIds(ids)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody(required = false) SearchVo searchVo) throws IOException {
        Page<BinPropertyConfig> page = PageUtils.getPage(BinPropertyConfig.class);
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<BinPropertyConfig> pageData=binPropertyConfigService.page(page,new QueryWrapper<BinPropertyConfig>()
                .lambda()
                .orderByAsc(BinPropertyConfig::getAttributeName));
        List<BinPropertyConfig> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
        }
        ExcelUtils.export3Excel(response,records, BinPropertyConfig.class,"Bin属性配置");
    }
}

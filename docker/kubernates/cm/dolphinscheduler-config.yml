kind: ConfigMap
apiVersion: v1
metadata:
  name: dolphinscheduler-config
  annotations:
    kubesphere.io/creator: admin
    kubesphere.io/description: 海豚调度器配置文件
data:
  common.properties: >-
    #

    # Licensed to the Apache Software Foundation (ASF) under one or more

    # contributor license agreements.  See the NOTICE file distributed with

    # this work for additional information regarding copyright ownership.

    # The ASF licenses this file to You under the Apache License, Version 2.0

    # (the "License"); you may not use this file except in compliance with

    # the License.  You may obtain a copy of the License at

    #

    #     http://www.apache.org/licenses/LICENSE-2.0

    #

    # Unless required by applicable law or agreed to in writing, software

    # distributed under the License is distributed on an "AS IS" BASIS,

    # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

    # See the License for the specific language governing permissions and

    # limitations under the License.

    #


    # user data local directory path, please make sure the directory exists and
    have read write permissions

    data.basedir.path=/tmp/dolphinscheduler


    # resource view suffixs

    #resource.view.suffixs=txt,log,sh,bat,conf,cfg,py,java,sql,xml,hql,properties,json,yml,yaml,ini,js


    # resource storage type: HDFS, S3, OSS, NONE

    resource.storage.type=HDFS

    #resource.storage.type=S3

    # resource store on HDFS/S3 path, resource file will store to this base
    path, self configuration, please make sure the directory exists on hdfs and
    have read write permissions. "/dolphinscheduler" is recommended

    resource.storage.upload.base.path=/fdop


    # the aws access key. if resource.storage.type=s3 or use emr-task, this
    configuration is required

    resource.aws.access.key.id=admin

    # The AWS secret access key. if resource.storage.type=S3 or use EMR-Task,
    This configuration is required

    resource.aws.secret.access.key=admin123456

    # The AWS Region to use. if resource.storage.type=S3 or use EMR-Task, This
    configuration is required

    resource.aws.region=cn-north-1

    # The name of the bucket. You need to create them by yourself. Otherwise,
    the system cannot start. All buckets in Amazon S3 share a single namespace;
    ensure the bucket is given a unique name.

    resource.aws.s3.bucket.name=iceberg

    # You need to set this parameter when private cloud s3. If S3 uses public
    cloud, you only need to set resource.aws.region or set to the endpoint of a
    public cloud such as S3.cn-north-1.amazonaws.com.cn

    resource.aws.s3.endpoint=http://10.10.10.115:9000


    # alibaba cloud access key id, required if you set resource.storage.type=OSS

    resource.alibaba.cloud.access.key.id=<your-access-key-id>

    # alibaba cloud access key secret, required if you set
    resource.storage.type=OSS

    resource.alibaba.cloud.access.key.secret=<your-access-key-secret>

    # alibaba cloud region, required if you set resource.storage.type=OSS

    resource.alibaba.cloud.region=cn-hangzhou

    # oss bucket name, required if you set resource.storage.type=OSS

    resource.alibaba.cloud.oss.bucket.name=dolphinscheduler

    # oss bucket endpoint, required if you set resource.storage.type=OSS

    resource.alibaba.cloud.oss.endpoint=https://oss-cn-hangzhou.aliyuncs.com


    # if resource.storage.type=HDFS, the user must have the permission to create
    directories under the HDFS root path

    resource.hdfs.root.user=hdfs

    # if resource.storage.type=S3, the value like: s3a://dolphinscheduler; if
    resource.storage.type=HDFS and namenode HA is enabled, you need to copy
    core-site.xml and hdfs-site.xml to conf dir

    #resource.hdfs.fs.defaultFS=hdfs://mycluster:8020

    resource.hdfs.fs.defaultFS=file:///

    #resource.hdfs.fs.defaultFS=s3a://iceberg

    #resource.hdfs.fs.defaultFS=hdfs://10.16.6.5:9820

    #resource.hdfs.fs.defaultFS=hdfs://10.16.6.164:9820

    #resource.hdfs.fs.defaultFS=hdfs://10.10.10.116:9820


    # whether to startup kerberos

    hadoop.security.authentication.startup.state=false


    # java.security.krb5.conf path

    java.security.krb5.conf.path=/opt/krb5.conf


    # login user from keytab username

    login.user.keytab.username=<EMAIL>


    # login user from keytab path

    login.user.keytab.path=/opt/hdfs.headless.keytab


    # kerberos expire time, the unit is hour

    kerberos.expire.time=2



    # resourcemanager port, the default value is 8088 if not specified

    resource.manager.httpaddress.port=8088

    # if resourcemanager HA is enabled, please set the HA IPs; if
    resourcemanager is single, keep this value empty

    yarn.resourcemanager.ha.rm.ids=192.168.xx.xx,192.168.xx.xx

    # if resourcemanager HA is enabled or not use resourcemanager, please keep
    the default value; If resourcemanager is single, you only need to replace
    ds1 to actual resourcemanager hostname

    yarn.application.status.address=http://ds1:%s/ws/v1/cluster/apps/%s

    # job history status url when application number threshold is
    reached(default 10000, maybe it was set to 1000)

    yarn.job.history.status.address=http://ds1:19888/ws/v1/history/mapreduce/jobs/%s


    # datasource encryption enable

    datasource.encryption.enable=false


    # datasource encryption salt

    datasource.encryption.salt=!@#$%^&*


    # data quality option

    data-quality.jar.name=dolphinscheduler-data-quality-dev-SNAPSHOT.jar


    #data-quality.error.output.path=/tmp/data-quality-error-data


    # Network IP gets priority, default inner outer


    # Whether hive SQL is executed in the same session

    support.hive.oneSession=false


    # use sudo or not, if set true, executing user is tenant user and deploy
    user needs sudo permissions; if set false, executing user is the deploy user
    and doesn't need sudo permissions

    sudo.enable=true

    setTaskDirToTenant.enable=false


    # network interface preferred like eth0, default: empty

    #dolphin.scheduler.network.interface.preferred=


    # network IP gets priority, default: inner outer

    #dolphin.scheduler.network.priority.strategy=default


    # system env path

    #dolphinscheduler.env.path=dolphinscheduler_env.sh


    # development state

    development.state=false


    # rpc port

    alert.rpc.port=50052


    # set path of conda.sh

    conda.path=/opt/anaconda3/etc/profile.d/conda.sh


    # Task resource limit state

    task.resource.limit.state=false


    # mlflow task plugin preset repository

    ml.mlflow.preset_repository=https://github.com/apache/dolphinscheduler-mlflow

    # mlflow task plugin preset repository version

    ml.mlflow.preset_repository_version="main"
  dolphinscheduler_env.sh: >-
    #

    # Licensed to the Apache Software Foundation (ASF) under one or more

    # contributor license agreements.  See the NOTICE file distributed with

    # this work for additional information regarding copyright ownership.

    # The ASF licenses this file to You under the Apache License, Version 2.0

    # (the "License"); you may not use this file except in compliance with

    # the License.  You may obtain a copy of the License at

    #

    #     http://www.apache.org/licenses/LICENSE-2.0

    #

    # Unless required by applicable law or agreed to in writing, software

    # distributed under the License is distributed on an "AS IS" BASIS,

    # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

    # See the License for the specific language governing permissions and

    # limitations under the License.

    #


    # nacos

    export NACOS_SERVER=${NACOS_SERVER:-************:8848}

    export NACOS_GROUP=${NACOS_GROUP:-DEFAULT_GROUP}


    # JAVA_HOME, will use it to start DolphinScheduler server

    export JAVA_HOME=${JAVA_HOME:-/opt/java/openjdk}

    #export JAVA_HOME=/usr/local/openjdk-8


    # Database related configuration, set database type, username and password

    export DATABASE=${DATABASE:-postgresql}

    export SPRING_PROFILES_ACTIVE=${DATABASE}

    export SPRING_DATASOURCE_URL

    export SPRING_DATASOURCE_USERNAME

    export SPRING_DATASOURCE_PASSWORD


    # DolphinScheduler server related configuration

    export SPRING_CACHE_TYPE=${SPRING_CACHE_TYPE:-none}

    # export SPRING_JACKSON_TIME_ZONE=${SPRING_JACKSON_TIME_ZONE:-UTC}

    export SPRING_JACKSON_TIME_ZONE=${SPRING_JACKSON_TIME_ZONE:-Asia/Shanghai}

    export MASTER_FETCH_COMMAND_NUM=${MASTER_FETCH_COMMAND_NUM:-10}


    # Registry center configuration, determines the type and link of the
    registry center

    export REGISTRY_TYPE=${REGISTRY_TYPE:-zookeeper}

    export
    REGISTRY_ZOOKEEPER_CONNECT_STRING=${REGISTRY_ZOOKEEPER_CONNECT_STRING:-localhost:2181}


    # Tasks related configurations, need to change the configuration if you use
    the related tasks.

    export HADOOP_HOME=${HADOOP_HOME:-/opt/soft/hadoop}

    export HADOOP_CONF_DIR=${HADOOP_CONF_DIR:-/opt/soft/hadoop/etc/hadoop}

    export SPARK_HOME1=${SPARK_HOME1:-/opt/soft/spark1}

    export SPARK_HOME2=${SPARK_HOME2:-/opt/soft/spark2}

    export PYTHON_HOME=${PYTHON_HOME:-/opt/soft/python}

    export HIVE_HOME=${HIVE_HOME:-/opt/soft/hive}

    export FLINK_HOME=${FLINK_HOME:-/opt/soft/flink}

    export DATAX_HOME=${DATAX_HOME:-/opt/soft/datax}

    export SEATUNNEL_HOME=${SEATUNNEL_HOME:-/opt/soft/seatunnel}

    export CHUNJUN_HOME=${CHUNJUN_HOME:-/opt/soft/chunjun}


    export
    PATH=$HADOOP_HOME/bin:$SPARK_HOME1/bin:$SPARK_HOME2/bin:$PYTHON_HOME/bin:$JAVA_HOME/bin:$HIVE_HOME/bin:$FLINK_HOME/bin:$DATAX_HOME/bin:$SEATUNNEL_HOME/bin:$CHUNJUN_HOME/bin:$PATH

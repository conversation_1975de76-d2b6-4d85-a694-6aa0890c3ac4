package com.datalink.fdop.fscm.api.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(schema = "zjdata", value = "f_d_work_order_config")
@ApiModel
@ExcelIgnoreUnannotated
public class WorkOrderConfig {
  @ApiModelProperty(value = "工单类型")
  @ExcelProperty("工单类型")
  private String workOrderType;
  @ApiModelProperty(value = "工单类型描述")
  @ExcelProperty("工单类型描述")
  private String workOrderTypeDesc;
  @ApiModelProperty(value = "id")
  @TableId(type = IdType.ASSIGN_ID)
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

}

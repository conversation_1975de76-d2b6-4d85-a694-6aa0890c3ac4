<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.SettlementRulesMapper">

    <select id="query" resultType="com.datalink.fdop.base.api.domain.SettlementRules">
        select a.*, controlArea.control_area_desc as control_area_desc
        from zjdata.ccm_settlement_rules a
        left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        <if test="searchVo != null and searchVo != ''">
            and ${searchVo}
        </if>
        order by control_area_id, settlement_type ${sort}
    </select>

    <select id="selectByKey" resultType="com.datalink.fdop.base.api.domain.SettlementRules">
        select a.*, controlArea.control_area_desc as control_area_desc
        from zjdata.ccm_settlement_rules a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
            where a.control_area_id = #{controlAreaId} and a.settlement_type = #{settlementType}
        limit 1
    </select>

    <select id="queryAll" resultType="com.datalink.fdop.base.api.domain.SettlementRules">
        select a.*, controlArea.control_area_desc as control_area_desc
        from zjdata.ccm_settlement_rules a
                 left join zjdata.ccm_control_area controlArea on a.control_area_id = controlArea.control_area_id
        order by control_area_id, settlement_type desc
    </select>

    <select id="checkIdUnique" resultType="java.lang.Integer">
        select count(1) from zjdata.ccm_settlement_rules where control_area_id = #{controlAreaId} and settlement_type = #{settlementType}
    </select>

    <update id="update" parameterType="com.datalink.fdop.base.api.domain.SettlementRules">
        update zjdata.ccm_settlement_rules
        set
        <if test="drAccount != null">dr_account = #{drAccount},</if>
        <if test="drCostCenter != null">dr_cost_center = #{drCostCenter},</if>
        <if test="drProject != null">dr_project = #{drProject},</if>
        <if test="crAccount != null">cr_account = #{crAccount},</if>
        <if test="crCostCenter != null">cr_cost_center = #{crCostCenter},</if>
        <if test="crProject != null">cr_project = #{crProject},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where control_area_id = #{controlAreaId} and settlement_type = #{settlementType}
    </update>

    <delete id="delete" parameterType="com.datalink.fdop.base.api.domain.SettlementRules">
        delete from zjdata.ccm_settlement_rules
        where control_area_id = #{controlAreaId} and settlement_type = #{settlementType}
    </delete>

    <update id="updateBatchStatus">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE zjdata.ccm_settlement_rules
            SET enable = #{item.enable}
            WHERE settlement_type = #{item.settlementType}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.ccm_settlement_rules (
        control_area_id, settlement_type, dr_account,dr_cost_center,dr_project, cr_account,cr_cost_center, cr_project,enable,create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.controlAreaId},#{item.settlementType},#{item.drAccount},
            #{item.drCostCenter},#{item.drProject},#{item.crAccount},
            #{item.crCostCenter},#{item.crProject},
            #{item.enable},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>

    <select id="querySettleType" resultType="com.datalink.fdop.base.api.domain.SettlementRules">
        select settlement_type as settlementType
        from zjdata.ccm_settlement_rules node
        where node.settlement_type is not null and node.settlement_type &lt;> ''
            <if test="controlAreaId != null">and control_area_id = #{controlAreaId}</if>
        order by control_area_id, settlement_type desc
    </select>

</mapper>

